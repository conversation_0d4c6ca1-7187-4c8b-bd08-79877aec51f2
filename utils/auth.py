"""
Authentication utilities for API endpoints.
"""
import os
import logging
from typing import Optional, Dict, Any, List, Union
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# OAuth2 scheme for token extraction
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/v1/auth/token",
    auto_error=False
)

# Check if we're in test mode
TEST_MODE = os.environ.get("TEST_MODE", "false").lower() in ("true", "1", "yes")

if TEST_MODE:
    logger.warning("AUTH MODULE: Test mode enabled - authentication will be bypassed")

async def authenticate_token(token: Optional[str] = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """
    Authenticate a user from a JWT token.

    In test mode, this function bypasses authentication and returns a test user ID.

    Args:
        token: JWT token from request

    Returns:
        Dict: User information from token

    Raises:
        HTTPException: If authentication fails (except in test mode)
    """
    # In test mode, still process tokens but don't validate signatures
    if TEST_MODE:
        logger.debug("Test mode: processing token without signature validation")
        if token:
            try:
                # Decode token without verification for test mode
                from utils.security import decode_access_token
                payload = decode_access_token(token)
                return payload
            except Exception as e:
                logger.debug(f"Test mode: Failed to decode token, using fallback: {e}")
        # Fallback for test mode
        return {"sub": "00000000-0000-0000-0000-000000000000", "is_test_user": True}

    # In production, require a token
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"}
        )

    # Here you would normally validate the token
    # For simplicity, we're just checking it exists
    return {"sub": "authenticated-user", "token": token}

class UserInfo:
    """Class representing a user's information."""
    def __init__(self, id: str, username: Optional[str] = None, email: Optional[str] = None,
                is_test_user: bool = False):
        self.id = id
        self.username = username or f"user_{id}"
        self.email = email
        self.is_test_user = is_test_user

async def get_current_user(token_info: Dict[str, Any] = Depends(authenticate_token)) -> UserInfo:
    """
    Get the current user based on the token info.

    Args:
        token_info: Token information from authenticate_token

    Returns:
        UserInfo: User information

    Raises:
        HTTPException: If user cannot be found
    """
    # In production, this would look up the user in a database
    user_id = token_info.get("sub")
    if not user_id:
        logger.error("No user ID found in token")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"}
        )

    is_test_user = token_info.get("is_test_user", False)

    # Create a user object
    user = UserInfo(
        id=user_id,
        username=f"user_{user_id}" if is_test_user else None,
        email=f"{user_id}@example.com" if is_test_user else None,
        is_test_user=is_test_user
    )

    logger.debug(f"User authenticated: {user.id}")
    return user

async def get_current_superuser(current_user: UserInfo = Depends(get_current_user)) -> UserInfo:
    """
    Get the current superuser.

    Args:
        current_user: Current user from get_current_user

    Returns:
        UserInfo: User information

    Raises:
        HTTPException: If user is not a superuser
    """
    # In test mode, all users are superusers
    if TEST_MODE:
        return current_user

    # In production, this would check if the user is a superuser
    # For now, we'll just check if the user ID is "admin"
    if current_user.id != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )

    return current_user

async def get_current_user_id(current_user: UserInfo = Depends(get_current_user)) -> str:
    """
    Get the current user ID.

    Args:
        current_user: Current user from get_current_user

    Returns:
        str: User ID
    """
    return current_user.id