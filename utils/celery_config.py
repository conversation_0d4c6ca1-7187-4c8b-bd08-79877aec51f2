"""
Celery configuration settings for TurdParty.

This module contains all the configuration settings for Celery.
"""
import os
from datetime import timed<PERSON><PERSON>
from kombu import Exchange, Queue

# Broker settings
BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')

# Task serialization format
TASK_SERIALIZER = 'json'
RESULT_SERIALIZER = 'json'
ACCEPT_CONTENT = ['json']

# Task execution settings
TASK_TRACK_STARTED = True
TASK_TIME_LIMIT = 30 * 60  # 30 minutes
TASK_SOFT_TIME_LIMIT = 25 * 60  # 25 minutes
WORKER_DISABLE_RATE_LIMITS = True
WORKER_HIJACK_ROOT_LOGGER = False

# Task result settings
TASK_IGNORE_RESULT = False
TASK_STORE_ERRORS_EVEN_IF_IGNORED = True
TASK_EAGER_PROPAGATES = True

# Concurrency settings
WORKER_CONCURRENCY = int(os.getenv('CELERY_CONCURRENCY', '4'))
WORKER_MAX_TASKS_PER_CHILD = 1000

# Queue settings
TASK_CREATE_MISSING_QUEUES = True
TASK_DEFAULT_DELIVERY_MODE = 'persistent'

# Periodic task settings
BEAT_SCHEDULE = {
    'cleanup-expired-tasks': {
        'task': 'api.tasks.monitoring.cleanup_expired_tasks',
        'schedule': timedelta(hours=1),
        'options': {'queue': 'monitoring'},
    },
    'check-vm-status': {
        'task': 'api.tasks.vm_ops.check_vm_status',
        'schedule': timedelta(minutes=5),
        'options': {'queue': 'vm_ops'},
    },
}

# Task routing
TASK_ROUTES = {
    'api.tasks.file_ops.*': {'queue': 'file_ops'},
    'api.tasks.vm_ops.*': {'queue': 'vm_ops'},
    'api.tasks.analysis.*': {'queue': 'analysis'},
    'api.tasks.monitoring.*': {'queue': 'monitoring'},
}

# Logging
WORKER_LOG_FORMAT = '[%(asctime)s: %(levelname)s/%(processName)s] %(message)s'
WORKER_TASK_LOG_FORMAT = '[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s'
