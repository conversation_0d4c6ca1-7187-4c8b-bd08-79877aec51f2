"""Application configuration module."""
import os
import logging
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, validator, BaseSettings

# Fix the import path for Base
from db.base import Base

logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    """Application settings."""

    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    PROJECT_NAME: str = "turdparty"
    API_V1_STR: str = "/api/v1"
    API_VERSION: str = "0.1.0"
    SECRET_KEY: str = os.getenv("SECRET_KEY", "development_secret_key")
    ALGORITHM: str = "HS256"
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    TESTING: bool = os.getenv("TESTING", "false").lower() == "true"

    # 60 minutes * 24 hours * 8 days = 8 days
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # CORS Configuration
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """Validate CORS origins."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        return v

    # Database Settings
    DATABASE_URL: str = os.environ.get(
        "DATABASE_URL", "********************************************/app"
    )
    SYNC_DATABASE_URL: str = os.getenv(
        "SYNC_DATABASE_URL", "********************************************/app"
    )
    DATABASE_URL_ASYNC: str = ""

    @validator("DATABASE_URL", "SYNC_DATABASE_URL", pre=True)
    def update_test_database_url(cls, v: str, values: Dict[str, Any]) -> str:
        """Update database URL for test environment."""
        if os.getenv("TESTING") == "true":
            # Replace the database name with test_* prefix
            if "postgres" in v or "postgresql" in v:
                parts = v.rsplit("/", 1)
                return f"{parts[0]}/test_{parts[1]}"
        return v

    @validator("DATABASE_URL_ASYNC", always=True)
    def update_async_url(cls, v: str, values: Dict[str, Any]) -> str:
        """Update the async database URL when the main URL changes."""
        url = values.get("DATABASE_URL", "")
        if not url.startswith("postgresql+asyncpg://"):
            url = url.replace("postgresql://", "postgresql+asyncpg://")
            url = url.replace("postgres://", "postgresql+asyncpg://")
        return url

    # SQLAlchemy Settings
    SQLALCHEMY_DATABASE_URI: str = os.getenv(
        "SQLALCHEMY_DATABASE_URI", "********************************************/app"
    )
    SQLALCHEMY_POOL_SIZE: int = int(os.getenv("SQLALCHEMY_POOL_SIZE", "5"))
    SQLALCHEMY_MAX_OVERFLOW: int = int(os.getenv("SQLALCHEMY_MAX_OVERFLOW", "10"))
    SQLALCHEMY_POOL_TIMEOUT: int = int(os.getenv("SQLALCHEMY_POOL_TIMEOUT", "30"))

    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def update_test_sqlalchemy_uri(cls, v: str, values: Dict[str, Any]) -> str:
        """Update SQLAlchemy URI for test environment."""
        if os.getenv("TESTING") == "true":
            if "postgres" in v or "postgresql" in v:
                parts = v.rsplit("/", 1)
                return f"{parts[0]}/test_{parts[1]}"
        return v

    @property
    def DATABASE_URL_SYNC(self) -> str:
        """Get the synchronous database URL with proper driver."""
        url = self.SYNC_DATABASE_URL
        if "asyncpg" in url:
            url = url.replace("postgresql+asyncpg://", "postgresql://")
        return url

    # SSH Settings
    SSH_KEY_PATH: str = os.getenv("SSH_KEY_PATH", "~/.ssh/replit")
    DEFAULT_VAGRANT_SSH_USER: str = os.getenv("DEFAULT_VAGRANT_SSH_USER", "vagrant")
    DEFAULT_VAGRANT_SERVER: str = os.getenv("DEFAULT_VAGRANT_SERVER", "localhost")
    DEFAULT_VAGRANT_SSH_KEY: str = os.getenv("VAGRANT_SSH_KEY", "")

    # MinIO Settings
    MINIO_ACCESS_KEY: str = os.getenv("MINIO_ACCESS_KEY", "minioadmin")
    MINIO_SECRET_KEY: str = os.getenv("MINIO_SECRET_KEY", "minioadmin")
    MINIO_ENDPOINT: str = os.getenv("MINIO_ENDPOINT", "minio:9000")
    MINIO_USE_SSL: bool = os.getenv("MINIO_USE_SSL", "false").lower() == "true"

    # File Upload Settings
    FILE_UPLOAD_DIR: str = os.getenv("FILE_UPLOAD_DIR", "/app/uploads")
    MAX_UPLOAD_SIZE: int = int(os.getenv("MAX_UPLOAD_SIZE", "104857600"))  # 100MB
    ALLOWED_UPLOAD_EXTENSIONS: List[str] = [
        ".txt", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".csv",
        ".jpg", ".jpeg", ".png", ".gif", ".zip", ".tar", ".gz"
    ]

    # Gemini Settings
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")

    # VirusTotal Settings
    VIRUSTOTAL_API_KEY: str = os.getenv("VIRUSTOTAL_API_KEY", "")

    # Test Mode Setting
    TEST_MODE: bool = True
    VERSION: str = os.getenv("VERSION", "1.0.0")

    # Set API_TEST_MODE environment variable
    @validator("TEST_MODE", always=True)
    def set_api_test_mode(cls, v: bool) -> bool:
        """Set API_TEST_MODE environment variable."""
        os.environ["API_TEST_MODE"] = "true"
        return v

    class Config:
        case_sensitive = True
        env_file = ".env"

# Initialize settings
settings = Settings()

# Add a function to get settings
def get_settings() -> Settings:
    """Get application settings."""
    return settings

if settings.TEST_MODE:
    logger.warning("Running in TEST MODE - authentication bypassed")
