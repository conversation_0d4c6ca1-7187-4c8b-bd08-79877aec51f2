"""Security utilities for authentication and authorization."""

import logging
import secrets
import hashlib
import hmac
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Union, List
from ipaddress import ip_address, ip_network

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordBearer
import jwt
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession

from utils.db import get_db_session
from utils.config import settings
from db.schemas.token import TokenData

logger = logging.getLogger(__name__)

# Password hashing with stronger configuration
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__rounds=12  # Increased rounds for better security
)

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")

# JWT configuration - Consider upgrading to RS256 in production
ALGORITHM = "HS256"

# Security constants
MAX_LOGIN_ATTEMPTS = 5
LOGIN_ATTEMPT_WINDOW = timedelta(minutes=15)
TOKEN_BLACKLIST = set()  # In production, use Redis or database

# Allowed IP ranges for admin operations (configure as needed)
ADMIN_IP_RANGES = [
    ip_network("*********/8"),  # localhost
    ip_network("10.0.0.0/8"),   # private networks
    ip_network("**********/12"),
    ip_network("***********/16"),
]


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash with timing attack protection.

    Args:
        plain_password: The plain text password
        hashed_password: The hashed password

    Returns:
        True if the password matches the hash, False otherwise
    """
    try:
        # Use constant-time comparison to prevent timing attacks
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error(f"Password verification error: {str(e)}")
        return False


def get_password_hash(password: str) -> str:
    """
    Hash a password with secure random salt.

    Args:
        password: The plain text password

    Returns:
        The hashed password
    """
    if not password or len(password) < 8:
        raise ValueError("Password must be at least 8 characters long")

    return pwd_context.hash(password)


def validate_password_strength(password: str) -> bool:
    """
    Validate password strength according to security policy.

    Args:
        password: The password to validate

    Returns:
        True if password meets requirements, False otherwise
    """
    if len(password) < 8:
        return False

    # Check for at least one uppercase, lowercase, digit, and special character
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)

    return has_upper and has_lower and has_digit and has_special


def generate_secure_token(length: int = 32) -> str:
    """
    Generate a cryptographically secure random token.

    Args:
        length: Length of the token in bytes

    Returns:
        Hex-encoded secure random token
    """
    return secrets.token_hex(length)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token with enhanced security.

    Args:
        data: Data to encode in the token
        expires_delta: Token expiration time delta

    Returns:
        The encoded JWT token
    """
    to_encode = data.copy()

    # Add security claims
    now = datetime.utcnow()
    expire = now + (expires_delta or timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES))

    to_encode.update({
        "exp": expire,
        "iat": now,  # Issued at
        "nbf": now,  # Not before
        "jti": generate_secure_token(16),  # JWT ID for tracking
        "iss": settings.PROJECT_NAME,  # Issuer
    })

    try:
        encoded_jwt = jwt.encode(
            to_encode, settings.SECRET_KEY, algorithm=ALGORITHM
        )
        return encoded_jwt
    except Exception as e:
        logger.error(f"Error creating access token: {str(e)}")
        raise


def is_token_blacklisted(jti: str) -> bool:
    """
    Check if a token is blacklisted.

    Args:
        jti: JWT ID

    Returns:
        True if token is blacklisted, False otherwise
    """
    return jti in TOKEN_BLACKLIST


def blacklist_token(jti: str) -> None:
    """
    Add a token to the blacklist.

    Args:
        jti: JWT ID to blacklist
    """
    TOKEN_BLACKLIST.add(jti)
    logger.info(f"Token {jti} added to blacklist")


def decode_token(token: str) -> Dict[str, Any]:
    """
    Decode a JWT token.

    Args:
        token: The JWT token

    Returns:
        The decoded token payload

    Raises:
        HTTPException: If the token is invalid
    """
    # For testing purposes, return a mock payload
    if settings.TEST_MODE:
        logger.warning("TEST MODE: Bypassing token validation")
        return {"sub": 1, "exp": datetime.utcnow() + timedelta(days=1)}

    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[ALGORITHM]
        )
        return payload
    except jwt.InvalidTokenError as e:
        logger.warning(f"JWT error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Unexpected error decoding token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get the current authenticated user.

    Args:
        token: JWT token from request
        db: Database session

    Returns:
        The current user

    Raises:
        HTTPException: If authentication fails
    """
    try:
        payload = decode_token(token)
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
            )
        
        user = await get_user(db, user_id)
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
            )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting current user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
        )