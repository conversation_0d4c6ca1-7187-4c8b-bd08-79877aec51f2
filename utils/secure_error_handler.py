"""Secure error handling for the API."""

import logging
import traceback
from typing import Dict, Any, Optional
from uuid import uuid4

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from sqlalchemy.exc import SQLAlchemyError
from pydantic import ValidationError

from utils.config import settings

logger = logging.getLogger(__name__)


class SecureErrorResponse:
    """Utility class for creating secure error responses."""
    
    @staticmethod
    def create_error_response(
        status_code: int,
        error_code: str,
        message: str,
        request_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a standardized error response.
        
        Args:
            status_code: HTTP status code
            error_code: Internal error code
            message: User-friendly error message
            request_id: Request ID for tracking
            details: Additional error details (only in development)
            
        Returns:
            Standardized error response dictionary
        """
        response = {
            "error": {
                "code": error_code,
                "message": message,
                "status_code": status_code,
            },
            "success": False,
            "timestamp": "2024-01-01T00:00:00Z",  # Use actual timestamp
        }
        
        if request_id:
            response["request_id"] = request_id
        
        # Only include detailed error information in development
        if settings.DEBUG and details:
            response["error"]["details"] = details
        
        return response


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """
    Handle HTTP exceptions with secure error responses.
    
    Args:
        request: The request that caused the exception
        exc: The HTTP exception
        
    Returns:
        JSON response with error details
    """
    request_id = request.headers.get("X-Request-ID", str(uuid4()))
    
    # Log the error
    logger.warning(
        f"HTTP Exception: {exc.status_code} - {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "detail": exc.detail,
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method,
            "client_ip": request.client.host if request.client else "unknown",
        }
    )
    
    # Map status codes to user-friendly messages
    status_messages = {
        400: "Bad request. Please check your input.",
        401: "Authentication required.",
        403: "Access denied.",
        404: "Resource not found.",
        405: "Method not allowed.",
        409: "Conflict with existing resource.",
        413: "Request too large.",
        422: "Invalid input data.",
        429: "Too many requests. Please try again later.",
        500: "Internal server error.",
        502: "Service temporarily unavailable.",
        503: "Service temporarily unavailable.",
    }
    
    # Use generic message for security, specific message only in development
    if settings.DEBUG:
        message = str(exc.detail)
    else:
        message = status_messages.get(exc.status_code, "An error occurred.")
    
    error_response = SecureErrorResponse.create_error_response(
        status_code=exc.status_code,
        error_code=f"HTTP_{exc.status_code}",
        message=message,
        request_id=request_id,
        details={"original_detail": str(exc.detail)} if settings.DEBUG else None
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    Handle request validation errors.
    
    Args:
        request: The request that caused the exception
        exc: The validation exception
        
    Returns:
        JSON response with validation error details
    """
    request_id = request.headers.get("X-Request-ID", str(uuid4()))
    
    # Log validation error
    logger.warning(
        f"Validation Error: {exc.errors()}",
        extra={
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method,
            "client_ip": request.client.host if request.client else "unknown",
            "validation_errors": exc.errors(),
        }
    )
    
    # Create user-friendly validation error message
    if settings.DEBUG:
        details = {"validation_errors": exc.errors()}
        message = "Validation failed. Please check the provided data."
    else:
        details = None
        message = "Invalid input data."
    
    error_response = SecureErrorResponse.create_error_response(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        error_code="VALIDATION_ERROR",
        message=message,
        request_id=request_id,
        details=details
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response
    )


async def database_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """
    Handle database errors securely.
    
    Args:
        request: The request that caused the exception
        exc: The database exception
        
    Returns:
        JSON response with generic error message
    """
    request_id = request.headers.get("X-Request-ID", str(uuid4()))
    
    # Log the full error for debugging
    logger.error(
        f"Database Error: {str(exc)}",
        extra={
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method,
            "client_ip": request.client.host if request.client else "unknown",
            "exception_type": type(exc).__name__,
        },
        exc_info=True
    )
    
    # Never expose database errors to users
    error_response = SecureErrorResponse.create_error_response(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code="DATABASE_ERROR",
        message="A database error occurred. Please try again later.",
        request_id=request_id,
        details={"exception_type": type(exc).__name__} if settings.DEBUG else None
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle unexpected exceptions securely.
    
    Args:
        request: The request that caused the exception
        exc: The unexpected exception
        
    Returns:
        JSON response with generic error message
    """
    request_id = request.headers.get("X-Request-ID", str(uuid4()))
    
    # Log the full error with stack trace
    logger.error(
        f"Unexpected Error: {str(exc)}",
        extra={
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method,
            "client_ip": request.client.host if request.client else "unknown",
            "exception_type": type(exc).__name__,
            "stack_trace": traceback.format_exc(),
        },
        exc_info=True
    )
    
    # Generic error message for security
    error_response = SecureErrorResponse.create_error_response(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code="INTERNAL_ERROR",
        message="An unexpected error occurred. Please try again later.",
        request_id=request_id,
        details={
            "exception_type": type(exc).__name__,
            "stack_trace": traceback.format_exc()
        } if settings.DEBUG else None
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response
    )


def setup_exception_handlers(app):
    """
    Set up all exception handlers for the FastAPI app.
    
    Args:
        app: FastAPI application instance
    """
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(SQLAlchemyError, database_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("Exception handlers configured successfully")
