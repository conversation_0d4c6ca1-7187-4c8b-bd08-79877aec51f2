"""Security middleware for the FastAPI application."""

import logging
import time
import uuid
from typing import Dict, Set, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta

from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

logger = logging.getLogger(__name__)


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers to all responses."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; "
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
                "img-src 'self' data: https:; "
                "font-src 'self' https: https://cdn.jsdelivr.net; "
                "connect-src 'self' https:; "
                "frame-ancestors 'none';"
            ),
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": (
                "geolocation=(), microphone=(), camera=(), "
                "payment=(), usb=(), magnetometer=(), gyroscope=()"
            ),
        }
    
    async def dispatch(self, request: Request, call_next):
        """Add security headers to response."""
        response = await call_next(request)
        
        # Add security headers
        for header, value in self.security_headers.items():
            response.headers[header] = value
        
        # Remove server header to avoid information disclosure
        if "server" in response.headers:
            del response.headers["server"]
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware with different limits for different endpoints."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        # Store request counts per IP
        self.request_counts: Dict[str, deque] = defaultdict(deque)
        self.blocked_ips: Dict[str, datetime] = {}
        
        # Rate limits: (requests, window_seconds, block_duration_seconds)
        self.rate_limits = {
            "/api/v1/auth/": (5, 60, 300),  # 5 requests per minute, block for 5 minutes
            "/api/v1/file_upload/": (10, 60, 60),  # 10 uploads per minute
            "/api/v1/vm_injection/": (20, 60, 60),  # 20 VM operations per minute
            "default": (100, 60, 60),  # Default: 100 requests per minute
        }
    
    def get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def get_rate_limit_for_path(self, path: str) -> tuple:
        """Get rate limit configuration for a given path."""
        for prefix, limit in self.rate_limits.items():
            if prefix != "default" and path.startswith(prefix):
                return limit
        return self.rate_limits["default"]
    
    def is_rate_limited(self, ip: str, path: str) -> bool:
        """Check if IP is rate limited for the given path."""
        # Check if IP is currently blocked
        if ip in self.blocked_ips:
            if datetime.now() < self.blocked_ips[ip]:
                return True
            else:
                # Block period expired, remove from blocked list
                del self.blocked_ips[ip]
        
        max_requests, window_seconds, block_duration = self.get_rate_limit_for_path(path)
        now = time.time()
        window_start = now - window_seconds
        
        # Clean old requests outside the window
        requests = self.request_counts[ip]
        while requests and requests[0] < window_start:
            requests.popleft()
        
        # Check if limit exceeded
        if len(requests) >= max_requests:
            # Block the IP
            self.blocked_ips[ip] = datetime.now() + timedelta(seconds=block_duration)
            logger.warning(f"Rate limit exceeded for IP {ip} on path {path}. Blocked for {block_duration} seconds.")
            return True
        
        # Add current request
        requests.append(now)
        return False
    
    async def dispatch(self, request: Request, call_next):
        """Check rate limits before processing request."""
        client_ip = self.get_client_ip(request)
        path = request.url.path
        
        if self.is_rate_limited(client_ip, path):
            logger.warning(f"Rate limit exceeded for {client_ip} on {path}")
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "error": "Rate limit exceeded",
                    "message": "Too many requests. Please try again later.",
                    "retry_after": 60
                },
                headers={"Retry-After": "60"}
            )
        
        return await call_next(request)


class RequestValidationMiddleware(BaseHTTPMiddleware):
    """Middleware for request validation and sanitization."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.max_request_size = 100 * 1024 * 1024  # 100MB
        self.suspicious_patterns = [
            # SQL injection patterns
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
            # XSS patterns
            r"(<script|javascript:|vbscript:|onload=|onerror=)",
            # Path traversal
            r"(\.\./|\.\.\\|%2e%2e%2f|%2e%2e%5c)",
            # Command injection
            r"(\b(eval|exec|system|shell_exec|passthru)\b)",
        ]
    
    async def dispatch(self, request: Request, call_next):
        """Validate and sanitize requests."""
        # Check request size
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.max_request_size:
            logger.warning(f"Request too large: {content_length} bytes from {request.client}")
            return JSONResponse(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                content={"error": "Request too large"}
            )
        
        # Validate request headers
        user_agent = request.headers.get("user-agent", "")
        if not user_agent or len(user_agent) > 500:
            logger.warning(f"Suspicious user agent from {request.client}: {user_agent}")
        
        # Check for suspicious patterns in URL
        url_str = str(request.url)
        for pattern in self.suspicious_patterns:
            import re
            if re.search(pattern, url_str, re.IGNORECASE):
                logger.warning(f"Suspicious pattern detected in URL: {url_str}")
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content={"error": "Invalid request"}
                )
        
        return await call_next(request)
