"""Input validation utilities for enhanced security."""

import re
import magic
import hashlib
from typing import List, Optional, Dict, Any
from pathlib import Path

from fastapi import HTTPException, status, UploadFile
from pydantic import BaseModel, validator


class FileValidationConfig:
    """Configuration for file upload validation."""
    
    # Maximum file size (100MB)
    MAX_FILE_SIZE = 100 * 1024 * 1024
    
    # Allowed MIME types
    ALLOWED_MIME_TYPES = {
        'text/plain',
        'text/csv',
        'application/json',
        'application/pdf',
        'application/zip',
        'application/x-zip-compressed',
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    }
    
    # Allowed file extensions
    ALLOWED_EXTENSIONS = {
        '.txt', '.csv', '.json', '.pdf', '.zip',
        '.jpg', '.jpeg', '.png', '.gif',
        '.doc', '.docx', '.xls', '.xlsx'
    }
    
    # Dangerous file extensions to always block
    DANGEROUS_EXTENSIONS = {
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr',
        '.vbs', '.js', '.jar', '.sh', '.ps1', '.php',
        '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl'
    }


class InputSanitizer:
    """Utility class for input sanitization."""
    
    @staticmethod
    def sanitize_string(value: str, max_length: int = 1000) -> str:
        """
        Sanitize string input to prevent injection attacks.
        
        Args:
            value: Input string to sanitize
            max_length: Maximum allowed length
            
        Returns:
            Sanitized string
            
        Raises:
            HTTPException: If input is invalid
        """
        if not isinstance(value, str):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Input must be a string"
            )
        
        if len(value) > max_length:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Input too long. Maximum length is {max_length}"
            )
        
        # Remove null bytes and control characters
        sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', value)
        
        # Remove potentially dangerous patterns
        dangerous_patterns = [
            r'<script[^>]*>.*?</script>',  # Script tags
            r'javascript:',  # JavaScript URLs
            r'vbscript:',   # VBScript URLs
            r'on\w+\s*=',   # Event handlers
        ]
        
        for pattern in dangerous_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE | re.DOTALL)
        
        return sanitized.strip()
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        Sanitize filename to prevent path traversal and other attacks.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        if not filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Filename cannot be empty"
            )
        
        # Remove path components
        filename = Path(filename).name
        
        # Remove dangerous characters
        sanitized = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '_', filename)
        
        # Remove leading/trailing dots and spaces
        sanitized = sanitized.strip('. ')
        
        # Ensure filename is not empty after sanitization
        if not sanitized:
            sanitized = "unnamed_file"
        
        # Limit length
        if len(sanitized) > 255:
            name, ext = Path(sanitized).stem, Path(sanitized).suffix
            max_name_length = 255 - len(ext)
            sanitized = name[:max_name_length] + ext
        
        return sanitized
    
    @staticmethod
    def validate_email(email: str) -> str:
        """
        Validate and sanitize email address.
        
        Args:
            email: Email address to validate
            
        Returns:
            Validated email address
            
        Raises:
            HTTPException: If email is invalid
        """
        email = email.strip().lower()
        
        # Basic email regex
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if not re.match(email_pattern, email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid email format"
            )
        
        if len(email) > 254:  # RFC 5321 limit
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email address too long"
            )
        
        return email


class FileValidator:
    """Validator for uploaded files."""
    
    def __init__(self, config: FileValidationConfig = None):
        self.config = config or FileValidationConfig()
    
    async def validate_file(self, file: UploadFile) -> Dict[str, Any]:
        """
        Validate uploaded file for security and compliance.
        
        Args:
            file: Uploaded file to validate
            
        Returns:
            Dictionary with validation results and file metadata
            
        Raises:
            HTTPException: If file validation fails
        """
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Filename is required"
            )
        
        # Sanitize filename
        sanitized_filename = InputSanitizer.sanitize_filename(file.filename)
        
        # Check file extension
        file_ext = Path(sanitized_filename).suffix.lower()
        
        if file_ext in self.config.DANGEROUS_EXTENSIONS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type {file_ext} is not allowed for security reasons"
            )
        
        if file_ext not in self.config.ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type {file_ext} is not allowed"
            )
        
        # Read file content for validation
        content = await file.read()
        await file.seek(0)  # Reset file pointer
        
        # Check file size
        file_size = len(content)
        if file_size > self.config.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File too large. Maximum size is {self.config.MAX_FILE_SIZE} bytes"
            )
        
        if file_size == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File is empty"
            )
        
        # Validate MIME type using python-magic
        try:
            detected_mime = magic.from_buffer(content, mime=True)
        except Exception:
            detected_mime = "application/octet-stream"
        
        if detected_mime not in self.config.ALLOWED_MIME_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"MIME type {detected_mime} is not allowed"
            )
        
        # Calculate file hash for integrity
        file_hash = hashlib.sha256(content).hexdigest()
        
        # Basic malware detection (check for suspicious patterns)
        self._check_malware_patterns(content)
        
        return {
            "original_filename": file.filename,
            "sanitized_filename": sanitized_filename,
            "file_size": file_size,
            "mime_type": detected_mime,
            "file_hash": file_hash,
            "extension": file_ext,
        }
    
    def _check_malware_patterns(self, content: bytes) -> None:
        """
        Basic malware pattern detection.
        
        Args:
            content: File content to check
            
        Raises:
            HTTPException: If suspicious patterns are found
        """
        # Convert to string for pattern matching (handle encoding errors)
        try:
            content_str = content.decode('utf-8', errors='ignore').lower()
        except:
            content_str = str(content).lower()
        
        # Suspicious patterns that might indicate malware
        suspicious_patterns = [
            b'\x4d\x5a',  # PE executable header
            b'\x7f\x45\x4c\x46',  # ELF executable header
            'eval(',
            'exec(',
            'system(',
            'shell_exec(',
            'passthru(',
            'base64_decode(',
            'gzinflate(',
            'str_rot13(',
        ]
        
        for pattern in suspicious_patterns:
            if isinstance(pattern, bytes):
                if pattern in content:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="File contains suspicious content"
                    )
            else:
                if pattern in content_str:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="File contains suspicious content"
                    )


# Pydantic models with enhanced validation
class SecureBaseModel(BaseModel):
    """Base model with security validations."""
    
    @validator('*', pre=True)
    def sanitize_strings(cls, v):
        """Sanitize all string fields."""
        if isinstance(v, str):
            return InputSanitizer.sanitize_string(v)
        return v
