# Consolidated VM Endpoints

## Description

This PR implements the consolidated VM endpoints that provide a unified interface for all Vagrant VM operations. These endpoints replace the previously scattered and duplicated endpoints across different routes.

## Changes

- Created a new router in `api/routes/consolidated_vagrant.py` with all VM-related endpoints
- Added redirects from old endpoints to new consolidated ones for backward compatibility
- Updated tests to use the new consolidated endpoints
- Added comprehensive documentation for the new endpoints

## Testing

- [ ] Unit tests for all consolidated endpoints
- [ ] Integration tests for backward compatibility
- [ ] Manual testing of all endpoints
- [ ] Verified redirects from old endpoints to new ones

## Documentation

- [ ] Added detailed documentation in `.ai/docs/2-technical-design/api/consolidated_vm_endpoints.md`
- [ ] Created a migration guide in `.ai/docs/2-technical-design/api/vm_endpoints_migration_guide.md`
- [ ] Updated the changelog to reflect the changes
- [ ] Added monitoring plan in `.ai/docs/2-technical-design/api/endpoint_monitoring_plan.md`

## Deployment Considerations

- This change maintains backward compatibility, so existing code will continue to work
- New code should use the consolidated endpoints
- The old endpoints will be deprecated in a future release

## Related Issues

- Closes #XXX (Replace with the actual issue number)

## Screenshots

(Add screenshots of the Swagger UI showing the new endpoints)
