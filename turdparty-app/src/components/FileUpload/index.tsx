import React, { useState, useEffect } from 'react';
import { Upload, Form, Input, message, Progress, Alert, Button, Typography, Card, Space, Statistic } from 'antd';
import { InboxOutlined, FolderOutlined, ReloadOutlined, InfoCircleOutlined, ClockCircleOutlined, BugOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import axios from 'axios';
import API_ENDPOINTS from '../../utils/apiConfig';
import { useAuth } from '../../hooks/useAuth';
import './styles.css';

const { Dragger } = Upload;
const { Text, Title, Paragraph } = Typography;

export interface FileUploadProps {
  onUploadSuccess?: (response: any) => void;
  onUploadError?: (error: any) => void;
  maxFileSize?: number; // in bytes
  acceptedFileTypes?: string[];
  multiple?: boolean;
  directory?: boolean;
  description?: string;
  token: string | null;
}

const DEFAULT_MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
const DEFAULT_ACCEPTED_TYPES = ['*/*']; // Accept all file types by default

export const FileUpload: React.FC<FileUploadProps> = ({
  onUploadSuccess,
  onUploadError,
  maxFileSize = DEFAULT_MAX_FILE_SIZE,
  acceptedFileTypes = DEFAULT_ACCEPTED_TYPES,
  multiple = false,
  directory = false,
  description: defaultDescription = '',
  token,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStartTime, setUploadStartTime] = useState<number | null>(null);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | null>(null);
  const [uploadSpeed, setUploadSpeed] = useState<number | null>(null);
  const [totalUploadBytes, setTotalUploadBytes] = useState(0);
  const [uploadedBytes, setUploadedBytes] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isDarkMode, setIsDarkMode] = useState<boolean>(localStorage.getItem('darkMode') === 'true');
  const [refreshingToken, setRefreshingToken] = useState(false);
  const [showDebugInfo, setShowDebugInfo] = useState(false);
  const [form] = Form.useForm();
  const { refreshToken, authError } = useAuth();
  const [isDevelopment, setIsDevelopment] = useState<boolean>(process.env.NODE_ENV === 'development');
  const [isSimulating, setIsSimulating] = useState<boolean>(false);

  // Listen for dark mode changes
  useEffect(() => {
    const handleDarkModeChange = () => {
      setIsDarkMode(localStorage.getItem('darkMode') === 'true');
    };

    window.addEventListener('darkModeChange', handleDarkModeChange);
    return () => {
      window.removeEventListener('darkModeChange', handleDarkModeChange);
    };
  }, []);

  // Update error message if auth error is present
  useEffect(() => {
    if (authError) {
      setError(`Authentication error: ${authError}`);
    }
  }, [authError]);

  // Monitor token status and set error if missing
  useEffect(() => {
    if (!token && fileList.length > 0) {
      setError('Authentication token is missing. Please use the "Get Authentication Token" button below.');
    } else if (token && error && error.includes('Authentication token is missing')) {
      setError(null);
    }
  }, [token, fileList.length, error]);

  // Calculate total file size
  useEffect(() => {
    const totalSize = fileList.reduce((total, file) => {
      return total + (file.size || 0);
    }, 0);
    setTotalUploadBytes(totalSize);
  }, [fileList]);

  // Update time remaining and upload speed
  useEffect(() => {
    if (uploading && uploadStartTime && uploadProgress > 0 && uploadProgress < 100) {
      const elapsedTime = (Date.now() - uploadStartTime) / 1000; // in seconds
      const speedBytesPerSecond = uploadedBytes / elapsedTime;

      // Only update speed if we have meaningful data
      if (speedBytesPerSecond > 0) {
        setUploadSpeed(speedBytesPerSecond);

        const remainingBytes = totalUploadBytes - uploadedBytes;
        const estimatedSeconds = remainingBytes / speedBytesPerSecond;
        setEstimatedTimeRemaining(estimatedSeconds);
      }
    } else if (!uploading || uploadProgress === 100) {
      setUploadSpeed(null);
      setEstimatedTimeRemaining(null);
    }
  }, [uploading, uploadProgress, uploadStartTime, uploadedBytes, totalUploadBytes]);

  const validateFile = (file: File): boolean => {
    // Check file size
    if (file.size > maxFileSize) {
      message.error(`File ${file.name} is too large. Maximum size is ${maxFileSize / 1024 / 1024}MB`);
      return false;
    }

    // Check file type if specific types are required
    if (acceptedFileTypes.length > 0 && acceptedFileTypes[0] !== '*/*') {
      const fileType = file.type || '';
      const isAccepted = acceptedFileTypes.some(type => {
        if (type.endsWith('/*')) {
          return fileType.startsWith(type.replace('/*', ''));
        }
        return type === fileType;
      });

      if (!isAccepted) {
        message.error(`File ${file.name} is not an accepted file type`);
        return false;
      }
    }

    return true;
  };

  const handleRefreshToken = async () => {
    setRefreshingToken(true);
    setError(null);

    try {
      console.log('[FileUpload] Attempting to refresh token');
      const success = await refreshToken();
      console.log('[FileUpload] Token refresh result:', success);

      if (success) {
        message.success('Successfully refreshed authentication token');
        // Don't force reload - this causes issues
        setError(null);
        // Check if we have fileList items to enable upload
        if (fileList.length > 0) {
          message.info('Token refreshed. You can now upload your files.');
        }
      } else {
        setError('Failed to refresh authentication token. Please try again or reload the page.');
        message.error('Failed to refresh authentication token');
      }
    } catch (err) {
      console.error('[FileUpload] Error refreshing token:', err);
      setError('Error refreshing authentication token. Please reload the page and try again.');
    } finally {
      setRefreshingToken(false);
    }
  };

  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.error('Please select at least one file');
      return;
    }

    if (!token) {
      setError('Authentication token is missing. Please try refreshing the page or log in again.');
      message.error('Authentication token is missing');
      if (onUploadError) {
        onUploadError(new Error('Authentication token is missing'));
      }
      return;
    }

    const description = form.getFieldValue('description');
    const formData = new FormData();

    // Reset states
    setUploading(true);
    setUploadProgress(0);
    setUploadedBytes(0);
    setUploadStartTime(Date.now());
    setError(null);

    try {
      // Use different endpoints based on upload type
      const endpoint = multiple ? API_ENDPOINTS.FILE_UPLOAD.FOLDER : API_ENDPOINTS.FILE_UPLOAD.BASE;

      console.log(`[FileUpload] Uploading to endpoint: ${endpoint}`);
      console.log(`[FileUpload] Files to upload:`, fileList.map(f => ({ name: f.name, size: f.size, type: f.type })));

      // Append files and paths
      fileList.forEach((file, index) => {
        console.log(`[FileUpload] Appending file to FormData:`, { name: file.name, type: file.type || 'unknown' });
        // Extract the actual File object from UploadFile
        if (file.originFileObj) {
          formData.append('file', file.originFileObj);
        } else if (file instanceof File) {
          formData.append('file', file);
        } else {
          console.error('[FileUpload] Unable to extract file from', file);
        }

        if (multiple) {
          formData.append('paths', file.name);
        }
      });

      // Add description if provided
      if (description) {
        formData.append('description', description);
        console.log(`[FileUpload] Added description to form data: ${description.substring(0, 30)}${description.length > 30 ? '...' : ''}`);
      }

      // Log form data info without iterating
      console.log('[FileUpload] FormData prepared with files and description');

      console.log(`[FileUpload] Starting upload with token: ${token.substring(0, 15)}...`);
      console.log(`[FileUpload] Request URL: ${endpoint}`);

      // Make sure the endpoint has a trailing slash
      const finalEndpoint = endpoint.endsWith('/') ? endpoint : `${endpoint}/`;
      console.log(`[FileUpload] Final endpoint with trailing slash: ${finalEndpoint}`);

      // Add retry logic for network issues
      let retries = 0;
      const maxRetries = 3;
      let response;

      while (retries < maxRetries) {
        try {
          response = await axios.post(finalEndpoint, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
        timeout: 60000, // Increase timeout to 60 seconds
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setUploadProgress(percent);
            setUploadedBytes(progressEvent.loaded);
            console.log(`[FileUpload] Upload progress: ${percent}%, ${progressEvent.loaded}/${progressEvent.total} bytes`);
          }
        },
          });

          // If we get here, the request was successful
          console.log(`[FileUpload] Upload successful: Status ${response.status}`);
          console.log(`[FileUpload] Response data:`, response.data);

          message.success(multiple ? 'Files uploaded successfully' : 'File uploaded successfully');
          setFileList([]);
          form.resetFields();

          if (onUploadSuccess) {
            onUploadSuccess(response.data);
          }

          // Exit the retry loop
          break;
        } catch (err: any) {
          console.error(`[FileUpload] Upload attempt ${retries + 1} failed:`, err);

          // Only retry on network errors or 5xx server errors
          if ((err.message && err.message.includes('Network Error')) ||
              (err.response && err.response.status >= 500)) {
            retries++;

            if (retries < maxRetries) {
              const backoffTime = Math.pow(2, retries) * 1000; // Exponential backoff
              console.log(`[FileUpload] Retrying in ${backoffTime}ms...`);
              await new Promise(resolve => setTimeout(resolve, backoffTime));
              continue;
            }
          }

          // If we get here, either it's not a retryable error or we've exhausted retries
          throw err;
        }
      }
    } catch (error: any) {
      console.error('[FileUpload] Upload error:', error);

      // Log detailed error information
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('[FileUpload] Response status:', error.response.status);
        console.error('[FileUpload] Response headers:', error.response.headers);
        console.error('[FileUpload] Response data:', error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('[FileUpload] No response received from server. Request:', error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('[FileUpload] Error setting up request:', error.message);
      }
      console.error('[FileUpload] Error config:', error.config);

      let errorMessage = 'Failed to upload file(s)';

      // Enhanced error message based on status codes
      if (error.response) {
        const status = error.response.status;
        if (status === 400) {
          errorMessage = error.response.data?.detail || 'Invalid request format. Please check file and try again.';
        } else if (status === 401) {
          errorMessage = 'Authentication error: Your session has expired. Please refresh your token.';
        } else if (status === 403) {
          errorMessage = 'You do not have permission to upload files.';
        } else if (status === 404) {
          errorMessage = 'Upload endpoint not found. This may be a configuration issue.';
        } else if (status === 413) {
          errorMessage = 'File too large. Please try uploading a smaller file.';
        } else if (status === 500) {
          errorMessage = 'Server error occurred while processing your upload.';
        } else if (status >= 500) {
          errorMessage = 'Server is experiencing issues. Please try again later.';
        } else {
          errorMessage = error.response.data?.detail || error.message || 'Failed to upload file(s)';
        }
      } else if (error.message && error.message.includes('timeout')) {
        errorMessage = 'Upload timed out. This may be due to a large file or slow connection.';
      } else if (error.message && error.message.includes('Network Error')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      }

      setError(errorMessage);
      message.error(errorMessage);

      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const simulateUploadProgress = () => {
    if (fileList.length === 0) {
      message.warning('Please select at least one file first');
      return;
    }

    // Calculate total size
    const totalSize = fileList.reduce((total, file) => total + (file.size || 0), 0);

    // Reset states
    setUploading(true);
    setIsSimulating(true);
    setUploadProgress(0);
    setUploadedBytes(0);
    setUploadStartTime(Date.now());
    setError(null);

    let progress = 0;
    const totalSimulationTime = 10000; // 10 seconds
    const interval = 100; // update every 100ms
    const steps = totalSimulationTime / interval;
    const increment = 100 / steps;

    const simulationInterval = setInterval(() => {
      progress += increment;
      const simulatedProgress = Math.min(Math.round(progress), 100);
      setUploadProgress(simulatedProgress);

      // Calculate simulated uploaded bytes
      const simulatedBytes = Math.round((simulatedProgress / 100) * totalSize);
      setUploadedBytes(simulatedBytes);

      if (simulatedProgress >= 100) {
        clearInterval(simulationInterval);
        setTimeout(() => {
          setUploading(false);
          setIsSimulating(false);
          message.success('Upload simulation completed!');
        }, 500);
      }
    }, interval);
  };

  const uploadProps: UploadProps = {
    name: 'file',
    multiple,
    directory,
    fileList,
    accept: acceptedFileTypes.join(','),
    beforeUpload: (file) => {
      if (!validateFile(file)) {
        return false;
      }
      console.log(`[FileUpload] File selected:`, { name: file.name, size: file.size, type: file.type });
      setFileList(prev => [...prev, file]);
      return false; // Prevent automatic upload
    },
    onRemove: (file) => {
      console.log(`[FileUpload] File removed:`, { name: file.name });
      setFileList(prev => prev.filter(f => f.uid !== file.uid));
      return true;
    },
    progress: {
      strokeColor: {
        '0%': '#108ee9',
        '100%': '#87d068',
      },
      strokeWidth: 3,
      format: percent => `${percent}%`,
    },
  };

  return (
    <div className={`file-upload-component ${isDarkMode ? 'dark-mode' : ''}`}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
        <Title level={4} style={{ margin: 0 }}>Upload Files</Title>
        <Space>
          {isDevelopment && (
            <Button
              type="default"
              icon={<BugOutlined />}
              onClick={() => setShowDebugInfo(!showDebugInfo)}
            >
              {showDebugInfo ? 'Hide Debug' : 'Show Debug'}
            </Button>
          )}

          <Button
            type="text"
            icon={<InfoCircleOutlined />}
            onClick={() => setShowDebugInfo(!showDebugInfo)}
          >
            {showDebugInfo ? 'Hide Info' : 'Show Info'}
          </Button>
        </Space>
      </div>

      {showDebugInfo && (
        <Card size="small" title="Debug Information" style={{ marginBottom: '16px' }}>
          <Paragraph>
            <strong>Token Status:</strong> {token ? 'Present' : 'Missing'}
          </Paragraph>
          {token && (
            <Paragraph>
              <strong>Token Preview:</strong> {token.substring(0, 20)}...
            </Paragraph>
          )}
          <Paragraph>
            <strong>Auth Error:</strong> {authError || 'None'}
          </Paragraph>
          <Paragraph>
            <strong>Upload Options:</strong> Multiple: {multiple.toString()}, Directory: {directory.toString()}
          </Paragraph>
          <Paragraph>
            <strong>Files Selected:</strong> {fileList.length}
          </Paragraph>
          <Space direction="vertical">
            {fileList.map((file, index) => (
              <Text key={file.uid}>
                {index + 1}. {file.name} ({typeof file.size === 'number' ? Math.round(file.size / 1024) : 'Unknown'} KB)
              </Text>
            ))}
          </Space>
        </Card>
      )}

      {error && (
        <Alert
          message="Upload Error"
          description={
            <div>
              {error}
              {error.includes('Authentication') && (
                <div style={{ marginTop: '10px' }}>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={handleRefreshToken}
                    loading={refreshingToken}
                  >
                    Refresh Authentication
                  </Button>
                </div>
              )}
            </div>
          }
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          className="upload-error-alert"
        />
      )}

      <Form form={form} layout="vertical" initialValues={{ description: defaultDescription }}>
        <Form.Item>
          <Dragger {...uploadProps} disabled={uploading}>
            <p className="ant-upload-drag-icon">
              {directory ? <FolderOutlined /> : <InboxOutlined />}
            </p>
            <p className="ant-upload-text">
              {directory ? 'Click or drag folder to this area to upload' : 'Click or drag file to this area to upload'}
            </p>
            <p className="ant-upload-hint">
              {multiple ? 'Support for multiple file uploads' : 'Support for single file upload'}
              {acceptedFileTypes[0] !== '*/*' && ` (Accepted types: ${acceptedFileTypes.join(', ')})`}
              {`. Maximum size: ${maxFileSize / 1024 / 1024}MB`}
            </p>
          </Dragger>
        </Form.Item>

        <Form.Item name="description" label="Description">
          <Input.TextArea
            rows={4}
            placeholder="Enter a description for this upload"
            disabled={uploading}
          />
        </Form.Item>

        {uploading && (
          <Card className="upload-progress-card">
            <Progress
              percent={uploadProgress}
              status={uploadProgress < 100 ? "active" : "success"}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
              strokeWidth={8}
            />

            <div className="upload-stats">
              <Space size="large">
                <Statistic
                  title="Progress"
                  value={uploadProgress}
                  suffix="%"
                  precision={1}
                />

                <Statistic
                  title="Uploaded"
                  value={uploadedBytes / 1024 / 1024}
                  suffix="MB"
                  precision={2}
                />

                <Statistic
                  title="Total Size"
                  value={totalUploadBytes / 1024 / 1024}
                  suffix="MB"
                  precision={2}
                />

                {uploadSpeed && (
                  <Statistic
                    title="Speed"
                    value={uploadSpeed / 1024 / 1024}
                    suffix="MB/s"
                    precision={2}
                  />
                )}

                {estimatedTimeRemaining && (
                  <Statistic
                    title="Time Remaining"
                    value={estimatedTimeRemaining > 60
                      ? Math.ceil(estimatedTimeRemaining / 60)
                      : Math.ceil(estimatedTimeRemaining)}
                    suffix={estimatedTimeRemaining > 60 ? "min" : "sec"}
                    prefix={<ClockCircleOutlined />}
                  />
                )}
              </Space>
            </div>
          </Card>
        )}

        <Form.Item>
          <Space>
            <Button
              type="primary"
              onClick={handleUpload}
              disabled={uploading || fileList.length === 0}
              loading={uploading && !isSimulating}
            >
              {uploading ? 'Uploading...' : 'Upload'}
            </Button>

            {!token && (
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRefreshToken}
                loading={refreshingToken}
              >
                Get Authentication Token
              </Button>
            )}

            {isDevelopment && !uploading && (
              <Button
                type="dashed"
                icon={<BugOutlined />}
                onClick={simulateUploadProgress}
                disabled={fileList.length === 0}
              >
                Test Progress
              </Button>
            )}
          </Space>
        </Form.Item>
      </Form>
    </div>
  );
};

// Add default export to support both import styles
export default FileUpload;