{"timestamp": "2025-06-09T17:13:40.042816", "api_url": "http://localhost:3050", "total_duration": 8.070839881896973, "overall_statistics": {"total_test_suites": 4, "successful_suites": 1, "suite_success_rate": 25.0, "total_tests": 44, "successful_tests": 37, "failed_tests": 7, "error_tests": 0, "skipped_tests": 20, "overall_success_rate": 84.0909090909091}, "suite_results": {"Core API Functionality": {"name": "Core API Functionality", "duration": 0.01834893226623535, "tests_run": 19, "failures": 2, "errors": 0, "skipped": 10, "success_rate": 89.47368421052632, "successful": false, "failure_details": [{"test": "test_01_health_check (unit.test_api_coverage.APITestCase.test_01_health_check)", "error": "Traceback (most recent call last):\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 789, in urlopen\n    response = self._make_request(\n               ^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 536, in _make_request\n    response = conn.getresponse()\n               ^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 507, in getresponse\n    httplib_response = super().getresponse()\n                       ^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 1428, in getresponse\n    response.begin()\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 331, in begin\n    version, status, reason = self._read_status()\n                              ^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 292, in _read_status\n    line = str(self.fp.readline(_MAXLINE + 1), \"iso-8859-1\")\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/socket.py\", line 720, in readinto\n    return self._sock.recv_into(b)\n           ^^^^^^^^^^^^^^^^^^^^^^^\nConnectionResetError: [Errno 104] Connection reset by peer\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/adapters.py\", line 683, in send\n    resp = conn.urlopen(\n           ^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 843, in urlopen\n    retries = retries.increment(\n              ^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/util/retry.py\", line 474, in increment\n    raise reraise(type(error), error, _stacktrace)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/util/util.py\", line 38, in reraise\n    raise value.with_traceback(tb)\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 789, in urlopen\n    response = self._make_request(\n               ^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 536, in _make_request\n    response = conn.getresponse()\n               ^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 507, in getresponse\n    httplib_response = super().getresponse()\n                       ^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 1428, in getresponse\n    response.begin()\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 331, in begin\n    version, status, reason = self._read_status()\n                              ^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 292, in _read_status\n    line = str(self.fp.readline(_MAXLINE + 1), \"iso-8859-1\")\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/socket.py\", line 720, in readinto\n    return self._sock.recv_into(b)\n           ^^^^^^^^^^^^^^^^^^^^^^^\nurllib3.exceptions.ProtocolError: ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/dev/10Baht/turdparty/tests/unit/test_api_coverage.py\", line 149, in test_01_health_check\n    resp = requests.get(f\"{API_BASE}/health/\", timeout=TEST_TIMEOUT)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/api.py\", line 73, in get\n    return request(\"get\", url, params=params, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/api.py\", line 59, in request\n    return session.request(method=method, url=url, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/sessions.py\", line 589, in request\n    resp = self.send(prep, **send_kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/sessions.py\", line 703, in send\n    r = adapter.send(request, **kwargs)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/adapters.py\", line 698, in send\n    raise ConnectionError(err, request=request)\nrequests.exceptions.ConnectionError: ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/dev/10Baht/turdparty/tests/unit/test_api_coverage.py\", line 157, in test_01_health_check\n    self.fail(f\"Health check endpoint failed: {str(e)}\")\nAssertionError: Health check endpoint failed: ('Connection aborted.', ConnectionResetError(104, 'Connection reset by peer'))\n"}, {"test": "test_17_performance_and_load (unit.test_api_coverage.APITestCase.test_17_performance_and_load)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/dev/10Baht/turdparty/tests/unit/test_api_coverage.py\", line 724, in test_17_performance_and_load\n    self.assertGreater(len(successful_requests), 0, \"At least one request should succeed\")\nAssertionError: 0 not greater than 0 : At least one request should succeed\n"}], "error_details": []}, "Async Tasks & Celery Integration": {"name": "Async Tasks & Celery Integration", "duration": 0.0018050670623779297, "tests_run": 7, "failures": 0, "errors": 0, "skipped": 7, "success_rate": 100.0, "successful": true, "failure_details": [], "error_details": []}, "Security & Authentication": {"name": "Security & Authentication", "duration": 0.0218045711517334, "tests_run": 8, "failures": 3, "errors": 0, "skipped": 1, "success_rate": 62.5, "successful": false, "failure_details": [{"test": "test_01_authentication_security (unit.test_security_comprehensive.SecurityTestCase.test_01_authentication_security)", "error": "Traceback (most recent call last):\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 199, in _new_conn\n    sock = connection.create_connection(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/util/connection.py\", line 85, in create_connection\n    raise err\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/util/connection.py\", line 73, in create_connection\n    sock.connect(sa)\nConnectionRefusedError: [<PERSON>rrno 111] Connection refused\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 789, in urlopen\n    response = self._make_request(\n               ^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 495, in _make_request\n    conn.request(\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 441, in request\n    self.endheaders()\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 1331, in endheaders\n    self._send_output(message_body, encode_chunked=encode_chunked)\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 1091, in _send_output\n    self.send(msg)\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 1035, in send\n    self.connect()\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 279, in connect\n    self.sock = self._new_conn()\n                ^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 214, in _new_conn\n    raise NewConnectionError(\nurllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f92b3d91a30>: Failed to establish a new connection: [Errno 111] Connection refused\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/adapters.py\", line 683, in send\n    resp = conn.urlopen(\n           ^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 843, in urlopen\n    retries = retries.increment(\n              ^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/util/retry.py\", line 519, in increment\n    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nurllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='localhost', port=3050): Max retries exceeded with url: /api/v1/auth/login (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f92b3d91a30>: Failed to establish a new connection: [Errno 111] Connection refused'))\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/dev/10Baht/turdparty/tests/unit/test_security_comprehensive.py\", line 88, in test_01_authentication_security\n    resp = requests.post(f\"{API_BASE}/auth/login\",\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/api.py\", line 115, in post\n    return request(\"post\", url, data=data, json=json, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/api.py\", line 59, in request\n    return session.request(method=method, url=url, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/sessions.py\", line 589, in request\n    resp = self.send(prep, **send_kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/sessions.py\", line 703, in send\n    r = adapter.send(request, **kwargs)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/adapters.py\", line 716, in send\n    raise ConnectionError(e, request=request)\nrequests.exceptions.ConnectionError: HTTPConnectionPool(host='localhost', port=3050): Max retries exceeded with url: /api/v1/auth/login (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f92b3d91a30>: Failed to establish a new connection: [Errno 111] Connection refused'))\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/dev/10Baht/turdparty/tests/unit/test_security_comprehensive.py\", line 94, in test_01_authentication_security\n    self.fail(f\"Invalid credentials test failed: {str(e)}\")\nAssertionError: Invalid credentials test failed: HTTPConnectionPool(host='localhost', port=3050): Max retries exceeded with url: /api/v1/auth/login (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f92b3d91a30>: Failed to establish a new connection: [Errno 111] Connection refused'))\n"}, {"test": "test_02_authorization_controls (unit.test_security_comprehensive.SecurityTestCase.test_02_authorization_controls)", "error": "Traceback (most recent call last):\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 199, in _new_conn\n    sock = connection.create_connection(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/util/connection.py\", line 85, in create_connection\n    raise err\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/util/connection.py\", line 73, in create_connection\n    sock.connect(sa)\nConnectionRefusedError: [<PERSON>rrno 111] Connection refused\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 789, in urlopen\n    response = self._make_request(\n               ^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 495, in _make_request\n    conn.request(\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 441, in request\n    self.endheaders()\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 1331, in endheaders\n    self._send_output(message_body, encode_chunked=encode_chunked)\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 1091, in _send_output\n    self.send(msg)\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 1035, in send\n    self.connect()\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 279, in connect\n    self.sock = self._new_conn()\n                ^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 214, in _new_conn\n    raise NewConnectionError(\nurllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f92b3d71d60>: Failed to establish a new connection: [Errno 111] Connection refused\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/adapters.py\", line 683, in send\n    resp = conn.urlopen(\n           ^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 843, in urlopen\n    retries = retries.increment(\n              ^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/util/retry.py\", line 519, in increment\n    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nurllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='localhost', port=3050): Max retries exceeded with url: /api/v1/users/me (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f92b3d71d60>: Failed to establish a new connection: [Errno 111] Connection refused'))\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/dev/10Baht/turdparty/tests/unit/test_security_comprehensive.py\", line 112, in test_02_authorization_controls\n    resp = requests.get(f\"{API_BASE}/users/me\", timeout=TEST_TIMEOUT)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/api.py\", line 73, in get\n    return request(\"get\", url, params=params, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/api.py\", line 59, in request\n    return session.request(method=method, url=url, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/sessions.py\", line 589, in request\n    resp = self.send(prep, **send_kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/sessions.py\", line 703, in send\n    r = adapter.send(request, **kwargs)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/adapters.py\", line 716, in send\n    raise ConnectionError(e, request=request)\nrequests.exceptions.ConnectionError: HTTPConnectionPool(host='localhost', port=3050): Max retries exceeded with url: /api/v1/users/me (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f92b3d71d60>: Failed to establish a new connection: [Errno 111] Connection refused'))\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/dev/10Baht/turdparty/tests/unit/test_security_comprehensive.py\", line 116, in test_02_authorization_controls\n    self.fail(f\"Unauthorized access test failed: {str(e)}\")\nAssertionError: Unauthorized access test failed: HTTPConnectionPool(host='localhost', port=3050): Max retries exceeded with url: /api/v1/users/me (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f92b3d71d60>: Failed to establish a new connection: [Errno 111] Connection refused'))\n"}, {"test": "test_04_security_headers (unit.test_security_comprehensive.SecurityTestCase.test_04_security_headers)", "error": "Traceback (most recent call last):\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 199, in _new_conn\n    sock = connection.create_connection(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/util/connection.py\", line 85, in create_connection\n    raise err\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/util/connection.py\", line 73, in create_connection\n    sock.connect(sa)\nConnectionRefusedError: [<PERSON>rrno 111] Connection refused\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 789, in urlopen\n    response = self._make_request(\n               ^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 495, in _make_request\n    conn.request(\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 441, in request\n    self.endheaders()\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 1331, in endheaders\n    self._send_output(message_body, encode_chunked=encode_chunked)\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 1091, in _send_output\n    self.send(msg)\n  File \"/nix/store/dksjvr69ckglyw1k2ss1qgshhcix73p8-python3-3.12.8/lib/python3.12/http/client.py\", line 1035, in send\n    self.connect()\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 279, in connect\n    self.sock = self._new_conn()\n                ^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connection.py\", line 214, in _new_conn\n    raise NewConnectionError(\nurllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x7f92b3d73fe0>: Failed to establish a new connection: [Errno 111] Connection refused\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/adapters.py\", line 683, in send\n    resp = conn.urlopen(\n           ^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 843, in urlopen\n    retries = retries.increment(\n              ^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/a34gs261yr48w107d0c0yxq20nxhjy4x-python3.12-urllib3-2.2.3/lib/python3.12/site-packages/urllib3/util/retry.py\", line 519, in increment\n    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nurllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='localhost', port=3050): Max retries exceeded with url: /api/v1/health/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f92b3d73fe0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/dev/10Baht/turdparty/tests/unit/test_security_comprehensive.py\", line 220, in test_04_security_headers\n    resp = requests.get(f\"{API_BASE}/health/\", timeout=TEST_TIMEOUT)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/api.py\", line 73, in get\n    return request(\"get\", url, params=params, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/api.py\", line 59, in request\n    return session.request(method=method, url=url, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/sessions.py\", line 589, in request\n    resp = self.send(prep, **send_kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/sessions.py\", line 703, in send\n    r = adapter.send(request, **kwargs)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/nix/store/gkslpflfbpdy1mhsgw9xqvpid0dj6j4j-python3.12-requests-2.32.3/lib/python3.12/site-packages/requests/adapters.py\", line 716, in send\n    raise ConnectionError(e, request=request)\nrequests.exceptions.ConnectionError: HTTPConnectionPool(host='localhost', port=3050): Max retries exceeded with url: /api/v1/health/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f92b3d73fe0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/dev/10Baht/turdparty/tests/unit/test_security_comprehensive.py\", line 256, in test_04_security_headers\n    self.fail(f\"Security headers test failed: {str(e)}\")\nAssertionError: Security headers test failed: HTTPConnectionPool(host='localhost', port=3050): Max retries exceeded with url: /api/v1/health/ (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f92b3d73fe0>: Failed to establish a new connection: [Errno 111] Connection refused'))\n"}], "error_details": []}, "Multilingual & Internationalization": {"name": "Multilingual & Internationalization", "duration": 0.02838897705078125, "tests_run": 10, "failures": 2, "errors": 0, "skipped": 2, "success_rate": 80.0, "successful": false, "failure_details": [{"test": "test_02_european_language_family_coverage (unit.test_multilingual_api.MultilingualAPITestCase.test_02_european_language_family_coverage)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/dev/10Baht/turdparty/tests/unit/test_multilingual_api.py\", line 172, in test_02_european_language_family_coverage\n    self.assertGreaterEqual(coverage_percent, 80.0,\nAssertionError: 0.0 not greater than or equal to 80.0 : Germanic family should have at least 80% language coverage\n"}, {"test": "test_03_eu_compliance_testing (unit.test_multilingual_api.MultilingualAPITestCase.test_03_eu_compliance_testing)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/dev/10Baht/turdparty/tests/unit/test_multilingual_api.py\", line 194, in test_03_eu_compliance_testing\n    self.assertGreaterEqual(eu_compliance, 95.0, \"Should have at least 95% EU official language compliance\")\nAssertionError: 0.0 not greater than or equal to 95.0 : Should have at least 95% EU official language compliance\n"}], "error_details": []}}, "api_coverage_analysis": {"Authentication & Authorization": {"tested": true, "success_rate": 62.5, "status": "Needs Improvement"}, "File Management": {"tested": true, "success_rate": 89.47368421052632, "status": "Good"}, "VM Management": {"tested": true, "success_rate": 89.47368421052632, "status": "Good"}, "Async Task Processing": {"tested": true, "success_rate": 100.0, "status": "Excellent"}, "Multilingual Support": {"tested": true, "success_rate": 80.0, "status": "Needs Improvement"}, "Security Features": {"tested": true, "success_rate": 62.5, "status": "Needs Improvement"}, "Error Handling": {"tested": true, "success_rate": 89.47368421052632, "status": "Good"}, "Performance Testing": {"tested": true, "success_rate": 89.47368421052632, "status": "Good"}}, "recommendations": ["Review and fix issues in Core API Functionality", "Improve Core API Functionality success rate (currently 89.5%)", "Address 2 test failures in Core API Functionality", "Review and fix issues in Security & Authentication", "Improve Security & Authentication success rate (currently 62.5%)", "Address 3 test failures in Security & Authentication", "Review and fix issues in Multilingual & Internationalization", "Improve Multilingual & Internationalization success rate (currently 80.0%)", "Address 2 test failures in Multilingual & Internationalization", "⚠️ Some test suites failed. Review and address issues before production deployment."]}