"""
Behave Environment Configuration for TurdParty User Experience Flow Testing.

This module sets up the testing environment for Behavior-Driven Development (BDD)
testing using Behave, focusing on user experience flows and end-to-end scenarios.
"""

import os
import time
import json
import requests
import tempfile
from datetime import datetime
from typing import Dict, Any, Optional
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Configuration
API_URL = os.environ.get("API_URL", "http://localhost:3050")
FRONTEND_URL = os.environ.get("FRONTEND_URL", "http://localhost:3000")
API_VERSION = os.environ.get("API_VERSION", "v1")
API_BASE = f"{API_URL}/api/{API_VERSION}"
TEST_TIMEOUT = int(os.environ.get("TEST_TIMEOUT", "30"))
HEADLESS = os.environ.get("HEADLESS", "true").lower() == "true"

# Test data directories
TEST_DATA_DIR = os.path.join(os.path.dirname(__file__), "test_data")
SCREENSHOTS_DIR = os.path.join(os.path.dirname(__file__), "screenshots")
REPORTS_DIR = os.path.join(os.path.dirname(__file__), "reports")

def before_all(context):
    """Set up the testing environment before all scenarios."""
    print("🚀 Setting up Behave testing environment for TurdParty...")
    
    # Create necessary directories
    os.makedirs(TEST_DATA_DIR, exist_ok=True)
    os.makedirs(SCREENSHOTS_DIR, exist_ok=True)
    os.makedirs(REPORTS_DIR, exist_ok=True)
    
    # Initialize context variables
    context.api_url = API_URL
    context.frontend_url = FRONTEND_URL
    context.api_base = API_BASE
    context.test_timeout = TEST_TIMEOUT
    context.test_data = {}
    context.screenshots_dir = SCREENSHOTS_DIR
    context.reports_dir = REPORTS_DIR
    
    # Create test files
    context.test_files = create_test_files()
    
    # Initialize API session
    context.api_session = requests.Session()
    context.auth_token = get_auth_token(context.api_session)
    
    if context.auth_token:
        context.api_session.headers.update({
            "Authorization": f"Bearer {context.auth_token}"
        })
        print(f"✅ Authentication successful")
    else:
        print("⚠️ Authentication failed - some tests may be limited")
    
    # Test API connectivity
    try:
        response = context.api_session.get(f"{API_BASE}/health/", timeout=TEST_TIMEOUT)
        if response.status_code == 200:
            print(f"✅ API connectivity verified at {API_URL}")
        else:
            print(f"⚠️ API health check returned {response.status_code}")
    except Exception as e:
        print(f"⚠️ API connectivity issue: {str(e)}")
    
    print("🎯 Behave environment setup complete")

def before_scenario(context, scenario):
    """Set up before each scenario."""
    print(f"\n🎬 Starting scenario: {scenario.name}")
    
    # Set up browser for UI scenarios
    if 'ui' in scenario.tags or 'frontend' in scenario.tags or 'browser' in scenario.tags:
        context.driver = setup_browser()
        print("🌐 Browser initialized for UI testing")
    
    # Initialize scenario-specific data
    context.scenario_data = {
        'name': scenario.name,
        'tags': scenario.tags,
        'start_time': time.time(),
        'uploaded_files': [],
        'created_vms': [],
        'async_tasks': []
    }

def after_scenario(context, scenario):
    """Clean up after each scenario."""
    duration = time.time() - context.scenario_data.get('start_time', time.time())
    status = "✅ PASSED" if scenario.status == "passed" else "❌ FAILED"
    
    print(f"🏁 Scenario completed: {scenario.name} - {status} ({duration:.2f}s)")
    
    # Take screenshot if scenario failed and browser is available
    if scenario.status == "failed" and hasattr(context, 'driver'):
        screenshot_path = os.path.join(
            context.screenshots_dir, 
            f"failed_{scenario.name.replace(' ', '_')}_{int(time.time())}.png"
        )
        try:
            context.driver.save_screenshot(screenshot_path)
            print(f"📸 Failure screenshot saved: {screenshot_path}")
        except Exception as e:
            print(f"⚠️ Could not save screenshot: {str(e)}")
    
    # Clean up browser
    if hasattr(context, 'driver'):
        try:
            context.driver.quit()
            print("🌐 Browser closed")
        except Exception as e:
            print(f"⚠️ Error closing browser: {str(e)}")
    
    # Clean up test resources
    cleanup_scenario_resources(context)

def after_all(context):
    """Clean up after all scenarios."""
    print("\n🧹 Cleaning up Behave testing environment...")
    
    # Clean up test files
    cleanup_test_files(context.test_files)
    
    # Generate final report
    generate_final_report(context)
    
    print("✅ Behave testing environment cleanup complete")

def setup_browser():
    """Set up Chrome browser with appropriate options."""
    chrome_options = Options()
    
    if HEADLESS:
        chrome_options.add_argument("--headless")
    
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")
    
    # Set download directory
    prefs = {
        "download.default_directory": tempfile.gettempdir(),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.implicitly_wait(10)
        return driver
    except Exception as e:
        print(f"❌ Failed to initialize Chrome browser: {str(e)}")
        raise

def get_auth_token(session):
    """Get authentication token for API testing."""
    try:
        # Try test token first
        response = session.post(f"{API_BASE}/auth/test-token", timeout=TEST_TIMEOUT)
        if response.status_code == 200:
            return response.json().get("access_token")
        
        # Try login with default credentials
        login_data = {
            "username": os.environ.get("TEST_USERNAME", "<EMAIL>"),
            "password": os.environ.get("TEST_PASSWORD", "password123")
        }
        response = session.post(f"{API_BASE}/auth/login", json=login_data, timeout=TEST_TIMEOUT)
        if response.status_code == 200:
            return response.json().get("access_token")
        
        return None
    except Exception as e:
        print(f"⚠️ Authentication error: {str(e)}")
        return None

def create_test_files():
    """Create test files for upload scenarios."""
    test_files = {}
    
    # Create text file
    text_file = os.path.join(TEST_DATA_DIR, "test_document.txt")
    with open(text_file, 'w') as f:
        f.write("This is a test document for TurdParty BDD testing.\n")
        f.write("Created for user experience flow validation.\n")
        f.write(f"Timestamp: {datetime.now().isoformat()}\n")
    test_files['text'] = text_file
    
    # Create JSON file
    json_file = os.path.join(TEST_DATA_DIR, "test_config.json")
    with open(json_file, 'w') as f:
        json.dump({
            "test_type": "bdd_user_flow",
            "created_at": datetime.now().isoformat(),
            "description": "Test configuration for BDD scenarios"
        }, f, indent=2)
    test_files['json'] = json_file
    
    # Create binary file (simulated AppImage)
    binary_file = os.path.join(TEST_DATA_DIR, "test_app.AppImage")
    with open(binary_file, 'wb') as f:
        f.write(b"#!/bin/bash\n")
        f.write(b"echo 'Test AppImage for BDD testing'\n")
        f.write(b"exit 0\n")
    os.chmod(binary_file, 0o755)
    test_files['appimage'] = binary_file
    
    # Create large file for performance testing
    large_file = os.path.join(TEST_DATA_DIR, "large_test_file.txt")
    with open(large_file, 'w') as f:
        for i in range(1000):
            f.write(f"Line {i}: This is a large test file for performance testing scenarios.\n")
    test_files['large'] = large_file
    
    return test_files

def cleanup_scenario_resources(context):
    """Clean up resources created during scenario execution."""
    if not hasattr(context, 'scenario_data'):
        return
    
    scenario_data = context.scenario_data
    
    # Clean up uploaded files
    for file_id in scenario_data.get('uploaded_files', []):
        try:
            context.api_session.delete(f"{API_BASE}/file_upload/{file_id}", timeout=TEST_TIMEOUT)
        except Exception as e:
            print(f"⚠️ Could not clean up file {file_id}: {str(e)}")
    
    # Clean up created VMs
    for vm_id in scenario_data.get('created_vms', []):
        try:
            context.api_session.delete(f"{API_BASE}/vagrant_vm/{vm_id}", timeout=TEST_TIMEOUT)
        except Exception as e:
            print(f"⚠️ Could not clean up VM {vm_id}: {str(e)}")

def cleanup_test_files(test_files):
    """Clean up test files created for scenarios."""
    for file_type, file_path in test_files.items():
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            print(f"⚠️ Could not clean up test file {file_path}: {str(e)}")

def generate_final_report(context):
    """Generate final BDD test report."""
    report_file = os.path.join(context.reports_dir, f"bdd_report_{int(time.time())}.json")
    
    report_data = {
        "timestamp": datetime.now().isoformat(),
        "environment": {
            "api_url": context.api_url,
            "frontend_url": context.frontend_url,
            "headless": HEADLESS
        },
        "authentication": {
            "token_available": bool(context.auth_token)
        },
        "test_files_created": len(context.test_files),
        "screenshots_directory": context.screenshots_dir
    }
    
    try:
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)
        print(f"📊 Final BDD report saved: {report_file}")
    except Exception as e:
        print(f"⚠️ Could not save final report: {str(e)}")

# Helper functions for step implementations
def wait_for_element(driver, locator, timeout=TEST_TIMEOUT):
    """Wait for element to be present and visible."""
    return WebDriverWait(driver, timeout).until(
        EC.presence_of_element_located(locator)
    )

def wait_for_clickable(driver, locator, timeout=TEST_TIMEOUT):
    """Wait for element to be clickable."""
    return WebDriverWait(driver, timeout).until(
        EC.element_to_be_clickable(locator)
    )

def take_screenshot(context, name):
    """Take a screenshot with a descriptive name."""
    if hasattr(context, 'driver'):
        timestamp = int(time.time())
        screenshot_path = os.path.join(
            context.screenshots_dir,
            f"{name}_{timestamp}.png"
        )
        try:
            context.driver.save_screenshot(screenshot_path)
            print(f"📸 Screenshot saved: {screenshot_path}")
            return screenshot_path
        except Exception as e:
            print(f"⚠️ Could not save screenshot: {str(e)}")
    return None
