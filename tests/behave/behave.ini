[behave]
# Behave configuration for TurdParty User Experience Flow Testing

# Test discovery and execution
paths = features
step_definitions = steps

# Output formatting
format = pretty
outfiles = reports/behave_results.txt
junit = true
junit_directory = reports/junit

# Logging configuration
logging_level = INFO
logging_format = %(levelname)-8s %(name)-10s %(message)s
logging_datefmt = %Y-%m-%d %H:%M:%S

# Tag configuration for test organization
default_tags = -@wip -@skip
tags = 

# Browser and environment configuration
# These can be overridden by environment variables
browser = chrome
headless = true
timeout = 30

# Test data and reporting
screenshots = true
screenshot_dir = screenshots
reports_dir = reports

# Parallel execution (when supported)
processes = 1

# Behavior-driven development settings
show_source = true
show_timings = true
show_skipped = false

# Color output
color = true

# Stop on first failure (useful for debugging)
stop = false

# Dry run mode (syntax checking)
dry_run = false

# Summary reporting
summary = true

# Include scenario outlines in summary
include_re = .*

# Exclude patterns
exclude_re = 

# User data directory for custom configurations
userdata_defines = 
    api_url=http://localhost:3050
    frontend_url=http://localhost:3000
    test_timeout=30
    headless=true
    screenshot_on_failure=true
    
# Stage definitions for different environments
# Usage: behave -D stage=development
stage.development.api_url = http://localhost:3050
stage.development.frontend_url = http://localhost:3000
stage.development.headless = false

stage.testing.api_url = http://test.turdparty.localhost
stage.testing.frontend_url = http://test-frontend.turdparty.localhost
stage.testing.headless = true

stage.staging.api_url = https://staging-api.turdparty.com
stage.staging.frontend_url = https://staging.turdparty.com
stage.staging.headless = true

stage.production.api_url = https://api.turdparty.com
stage.production.frontend_url = https://turdparty.com
stage.production.headless = true
