#!/usr/bin/env python3
"""
Comprehensive Behave Test Runner for TurdParty User Experience Flow Testing.

This script provides a comprehensive test runner for Behave BDD tests,
with support for different environments, test filtering, reporting,
and integration with the existing test infrastructure.
"""

import os
import sys
import time
import json
import argparse
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Add the tests directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configuration
BEHAVE_DIR = os.path.dirname(os.path.abspath(__file__))
FEATURES_DIR = os.path.join(BEHAVE_DIR, "features")
REPORTS_DIR = os.path.join(BEHAVE_DIR, "reports")
SCREENSHOTS_DIR = os.path.join(BEHAVE_DIR, "screenshots")

def log(message: str, level: str = "INFO"):
    """Print message with timestamp and level."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

class BehaveTestRunner:
    """Comprehensive Behave test runner with advanced features."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.test_results = {}
        self.reports_dir = REPORTS_DIR
        self.screenshots_dir = SCREENSHOTS_DIR
        
        # Ensure directories exist
        os.makedirs(self.reports_dir, exist_ok=True)
        os.makedirs(self.screenshots_dir, exist_ok=True)
    
    def run_behave_tests(self, 
                        tags: Optional[List[str]] = None,
                        features: Optional[List[str]] = None,
                        environment: str = "development",
                        headless: bool = True,
                        parallel: bool = False,
                        dry_run: bool = False) -> Dict:
        """Run Behave tests with specified configuration."""
        
        log("🎭 Starting Behave User Experience Flow Testing...")
        log("=" * 80)
        log("🎯 Behavior-Driven Development Testing for TurdParty Platform")
        log("🌍 User Experience | Multilingual | End-to-End Workflows")
        log("=" * 80)
        
        self.start_time = time.time()
        
        # Build behave command
        behave_cmd = self._build_behave_command(
            tags=tags,
            features=features,
            environment=environment,
            headless=headless,
            parallel=parallel,
            dry_run=dry_run
        )
        
        log(f"🚀 Executing Behave command: {' '.join(behave_cmd)}")
        
        try:
            # Run behave tests
            result = subprocess.run(
                behave_cmd,
                cwd=BEHAVE_DIR,
                capture_output=True,
                text=True,
                timeout=3600  # 1 hour timeout
            )
            
            self.end_time = time.time()
            
            # Process results
            test_results = self._process_behave_results(result)
            
            # Generate reports
            self._generate_comprehensive_report(test_results)
            
            return test_results
            
        except subprocess.TimeoutExpired:
            log("❌ Behave tests timed out after 1 hour", "ERROR")
            return {"error": "timeout", "duration": 3600}
        except Exception as e:
            log(f"❌ Error running Behave tests: {str(e)}", "ERROR")
            return {"error": str(e)}
    
    def _build_behave_command(self,
                             tags: Optional[List[str]] = None,
                             features: Optional[List[str]] = None,
                             environment: str = "development",
                             headless: bool = True,
                             parallel: bool = False,
                             dry_run: bool = False) -> List[str]:
        """Build the behave command with all necessary arguments."""
        
        cmd = ["behave"]
        
        # Add configuration file
        behave_ini = os.path.join(BEHAVE_DIR, "behave.ini")
        if os.path.exists(behave_ini):
            cmd.extend(["-c", behave_ini])
        
        # Add environment configuration
        cmd.extend(["-D", f"stage={environment}"])
        cmd.extend(["-D", f"headless={str(headless).lower()}"])
        
        # Add tags if specified
        if tags:
            for tag in tags:
                cmd.extend(["--tags", tag])
        
        # Add specific features if specified
        if features:
            cmd.extend(features)
        else:
            cmd.append(FEATURES_DIR)
        
        # Add output formatting
        cmd.extend(["--format", "pretty"])
        cmd.extend(["--format", "json", "--outfile", os.path.join(self.reports_dir, "behave_results.json")])
        cmd.extend(["--format", "junit", "--outdir", os.path.join(self.reports_dir, "junit")])
        
        # Add additional options
        cmd.extend(["--show-timings"])
        cmd.extend(["--show-source"])
        
        if dry_run:
            cmd.append("--dry-run")
        
        if parallel and not dry_run:
            # Note: Parallel execution requires behave-parallel package
            cmd.extend(["--processes", "4"])
        
        return cmd
    
    def _process_behave_results(self, result: subprocess.CompletedProcess) -> Dict:
        """Process Behave test results and extract meaningful information."""
        
        duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        # Basic result information
        test_results = {
            "timestamp": datetime.now().isoformat(),
            "duration": duration,
            "return_code": result.returncode,
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
        
        # Try to parse JSON results if available
        json_results_file = os.path.join(self.reports_dir, "behave_results.json")
        if os.path.exists(json_results_file):
            try:
                with open(json_results_file, 'r') as f:
                    json_results = json.load(f)
                test_results["detailed_results"] = json_results
                
                # Extract summary statistics
                test_results["summary"] = self._extract_summary_stats(json_results)
                
            except Exception as e:
                log(f"⚠️ Could not parse JSON results: {str(e)}", "WARNING")
        
        # Parse stdout for additional information
        if result.stdout:
            test_results["parsed_output"] = self._parse_behave_output(result.stdout)
        
        return test_results
    
    def _extract_summary_stats(self, json_results: List[Dict]) -> Dict:
        """Extract summary statistics from Behave JSON results."""
        
        total_features = len(json_results)
        total_scenarios = 0
        passed_scenarios = 0
        failed_scenarios = 0
        skipped_scenarios = 0
        total_steps = 0
        passed_steps = 0
        failed_steps = 0
        skipped_steps = 0
        
        for feature in json_results:
            scenarios = feature.get("elements", [])
            total_scenarios += len(scenarios)
            
            for scenario in scenarios:
                scenario_status = scenario.get("status", "unknown")
                if scenario_status == "passed":
                    passed_scenarios += 1
                elif scenario_status == "failed":
                    failed_scenarios += 1
                elif scenario_status == "skipped":
                    skipped_scenarios += 1
                
                steps = scenario.get("steps", [])
                total_steps += len(steps)
                
                for step in steps:
                    step_status = step.get("result", {}).get("status", "unknown")
                    if step_status == "passed":
                        passed_steps += 1
                    elif step_status == "failed":
                        failed_steps += 1
                    elif step_status == "skipped":
                        skipped_steps += 1
        
        return {
            "features": {
                "total": total_features
            },
            "scenarios": {
                "total": total_scenarios,
                "passed": passed_scenarios,
                "failed": failed_scenarios,
                "skipped": skipped_scenarios,
                "success_rate": (passed_scenarios / total_scenarios * 100) if total_scenarios > 0 else 0
            },
            "steps": {
                "total": total_steps,
                "passed": passed_steps,
                "failed": failed_steps,
                "skipped": skipped_steps,
                "success_rate": (passed_steps / total_steps * 100) if total_steps > 0 else 0
            }
        }
    
    def _parse_behave_output(self, output: str) -> Dict:
        """Parse Behave stdout output for additional information."""
        
        lines = output.split('\n')
        parsed = {
            "features_run": 0,
            "scenarios_run": 0,
            "steps_run": 0,
            "failures": [],
            "warnings": []
        }
        
        for line in lines:
            line = line.strip()
            
            # Look for summary lines
            if "feature" in line.lower() and "passed" in line.lower():
                # Extract feature count
                try:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if "feature" in part.lower() and i > 0:
                            parsed["features_run"] = int(parts[i-1])
                            break
                except (ValueError, IndexError):
                    pass
            
            # Look for failure information
            if "FAILED" in line or "ERROR" in line:
                parsed["failures"].append(line)
            
            # Look for warnings
            if "WARNING" in line or "WARN" in line:
                parsed["warnings"].append(line)
        
        return parsed
    
    def _generate_comprehensive_report(self, test_results: Dict):
        """Generate comprehensive test report."""
        
        # Generate text report
        self._generate_text_report(test_results)
        
        # Generate HTML report if possible
        self._generate_html_report(test_results)
        
        # Print summary to console
        self._print_test_summary(test_results)
    
    def _generate_text_report(self, test_results: Dict):
        """Generate detailed text report."""
        
        report_file = os.path.join(self.reports_dir, f"behave_report_{int(time.time())}.txt")
        
        try:
            with open(report_file, 'w') as f:
                f.write("=" * 100 + "\n")
                f.write("BEHAVE USER EXPERIENCE FLOW TESTING REPORT\n")
                f.write("=" * 100 + "\n\n")
                
                f.write(f"Timestamp: {test_results.get('timestamp', 'Unknown')}\n")
                f.write(f"Duration: {test_results.get('duration', 0):.2f} seconds\n")
                f.write(f"Success: {'✅ PASSED' if test_results.get('success') else '❌ FAILED'}\n")
                f.write(f"Return Code: {test_results.get('return_code', 'Unknown')}\n\n")
                
                # Summary statistics
                if "summary" in test_results:
                    summary = test_results["summary"]
                    f.write("SUMMARY STATISTICS:\n")
                    f.write("-" * 50 + "\n")
                    f.write(f"Features: {summary['features']['total']}\n")
                    f.write(f"Scenarios: {summary['scenarios']['total']} total, ")
                    f.write(f"{summary['scenarios']['passed']} passed, ")
                    f.write(f"{summary['scenarios']['failed']} failed, ")
                    f.write(f"{summary['scenarios']['skipped']} skipped\n")
                    f.write(f"Scenario Success Rate: {summary['scenarios']['success_rate']:.1f}%\n")
                    f.write(f"Steps: {summary['steps']['total']} total, ")
                    f.write(f"{summary['steps']['passed']} passed, ")
                    f.write(f"{summary['steps']['failed']} failed, ")
                    f.write(f"{summary['steps']['skipped']} skipped\n")
                    f.write(f"Step Success Rate: {summary['steps']['success_rate']:.1f}%\n\n")
                
                # Output
                if test_results.get("stdout"):
                    f.write("BEHAVE OUTPUT:\n")
                    f.write("-" * 50 + "\n")
                    f.write(test_results["stdout"])
                    f.write("\n\n")
                
                # Errors
                if test_results.get("stderr"):
                    f.write("ERRORS:\n")
                    f.write("-" * 50 + "\n")
                    f.write(test_results["stderr"])
                    f.write("\n\n")
            
            log(f"📄 Text report saved: {report_file}")
            
        except Exception as e:
            log(f"⚠️ Could not generate text report: {str(e)}", "WARNING")
    
    def _generate_html_report(self, test_results: Dict):
        """Generate HTML report if possible."""
        # This would require additional HTML generation logic
        # For now, we'll just log that it's not implemented
        log("📝 HTML report generation not yet implemented")
    
    def _print_test_summary(self, test_results: Dict):
        """Print test summary to console."""
        
        log("=" * 100)
        log("🎭 BEHAVE USER EXPERIENCE FLOW TESTING SUMMARY")
        log("=" * 100)
        
        duration = test_results.get('duration', 0)
        success = test_results.get('success', False)
        
        log(f"⏱️ Total Duration: {duration:.2f} seconds")
        log(f"🎯 Overall Result: {'✅ PASSED' if success else '❌ FAILED'}")
        
        if "summary" in test_results:
            summary = test_results["summary"]
            log(f"📊 Features Tested: {summary['features']['total']}")
            log(f"🎬 Scenarios: {summary['scenarios']['total']} total, {summary['scenarios']['passed']} passed, {summary['scenarios']['failed']} failed")
            log(f"📈 Scenario Success Rate: {summary['scenarios']['success_rate']:.1f}%")
            log(f"👣 Steps: {summary['steps']['total']} total, {summary['steps']['passed']} passed, {summary['steps']['failed']} failed")
            log(f"📈 Step Success Rate: {summary['steps']['success_rate']:.1f}%")
        
        log("=" * 100)
        
        if success:
            log("🎉 BEHAVE USER EXPERIENCE TESTING: SUCCESSFUL!")
            log("   All user experience flows validated and working correctly")
        else:
            log("⚠️ BEHAVE USER EXPERIENCE TESTING: ISSUES DETECTED")
            log("   Some user experience flows require attention")
        
        log("=" * 100)

def main():
    """Main function to run Behave tests with command line arguments."""
    
    parser = argparse.ArgumentParser(description="TurdParty Behave User Experience Flow Testing")
    
    parser.add_argument("--tags", nargs="+", help="Behave tags to run (e.g., @ui @critical)")
    parser.add_argument("--features", nargs="+", help="Specific feature files to run")
    parser.add_argument("--environment", default="development", 
                       choices=["development", "testing", "staging", "production"],
                       help="Environment to test against")
    parser.add_argument("--headless", action="store_true", default=True,
                       help="Run browser tests in headless mode")
    parser.add_argument("--headed", action="store_false", dest="headless",
                       help="Run browser tests with visible browser")
    parser.add_argument("--parallel", action="store_true",
                       help="Run tests in parallel (requires behave-parallel)")
    parser.add_argument("--dry-run", action="store_true",
                       help="Perform syntax check without executing tests")
    
    args = parser.parse_args()
    
    # Initialize test runner
    runner = BehaveTestRunner()
    
    # Run tests
    results = runner.run_behave_tests(
        tags=args.tags,
        features=args.features,
        environment=args.environment,
        headless=args.headless,
        parallel=args.parallel,
        dry_run=args.dry_run
    )
    
    # Exit with appropriate code
    success = results.get("success", False)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
