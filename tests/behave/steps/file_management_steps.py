"""
Step definitions for file management and analysis workflow scenarios.

This module implements the step definitions for Behave scenarios related to
file upload, management, processing, and analysis workflows.
"""

import os
import time
import json
import requests
from behave import given, when, then, step
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# File Management step definitions

@given('I am authenticated on the TurdParty platform')
def step_authenticated_on_platform(context):
    """Ensure user is authenticated on the platform."""
    if not hasattr(context, 'auth_token') or not context.auth_token:
        # Try to get authentication token
        try:
            response = context.api_session.post(f"{context.api_base}/auth/test-token")
            if response.status_code == 200:
                context.auth_token = response.json().get("access_token")
                context.api_session.headers.update({
                    "Authorization": f"Bearer {context.auth_token}"
                })
                print("✅ Authentication successful")
            else:
                print("⚠️ Using platform without authentication")
        except Exception as e:
            print(f"⚠️ Authentication failed: {str(e)}")

@given('the file management system is available')
def step_file_management_available(context):
    """Verify that the file management system is accessible."""
    try:
        response = context.api_session.get(f"{context.api_base}/file_upload", timeout=context.test_timeout)
        assert response.status_code in [200, 401], f"File management system check failed: {response.status_code}"
        print("✅ File management system is available")
    except Exception as e:
        raise AssertionError(f"File management system is not available: {str(e)}")

@given('I am on the file upload page')
def step_on_file_upload_page(context):
    """Navigate to the file upload page."""
    upload_url = f"{context.frontend_url}/file_upload"
    context.driver.get(upload_url)
    
    try:
        WebDriverWait(context.driver, context.test_timeout).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        print(f"✅ Navigated to file upload page: {upload_url}")
        take_screenshot(context, "file_upload_page_loaded")
    except TimeoutException:
        raise AssertionError("File upload page did not load within timeout")

@when('I select a file to upload')
def step_select_file_to_upload(context):
    """Select a file for upload based on the provided specifications."""
    file_specs = {}
    for row in context.table:
        file_specs['filename'] = row['filename']
        file_specs['type'] = row['type']
        file_specs['size'] = row['size']
    
    # Determine which test file to use based on type
    if file_specs['type'] == 'binary':
        selected_file = context.test_files['appimage']
    elif file_specs['type'] == 'document':
        selected_file = context.test_files['json']
    else:
        selected_file = context.test_files['text']
    
    context.selected_file = selected_file
    context.file_specs = file_specs
    print(f"✅ Selected file: {selected_file} (type: {file_specs['type']})")

@when('I provide file metadata')
def step_provide_file_metadata(context):
    """Provide metadata for the file upload."""
    metadata = {}
    for row in context.table:
        metadata[row['description']] = row['source']
        metadata['priority'] = row['priority']
    
    try:
        # Find and fill description field
        description_selectors = [
            "textarea[name='description']",
            "textarea[placeholder*='description']",
            "input[name='description']",
            ".description-field"
        ]
        
        description_field = None
        for selector in description_selectors:
            try:
                description_field = context.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except NoSuchElementException:
                continue
        
        if description_field:
            description_field.clear()
            description_field.send_keys(list(metadata.keys())[0])
            print("✅ File description provided")
        
        context.file_metadata = metadata
        take_screenshot(context, "file_metadata_provided")
        
    except Exception as e:
        take_screenshot(context, "file_metadata_failed")
        raise AssertionError(f"Failed to provide file metadata: {str(e)}")

@when('I click the upload button')
def step_click_upload_button(context):
    """Click the upload button to start file upload."""
    try:
        # First, set the file input if it exists
        file_input_selectors = [
            "input[type='file']",
            ".file-input",
            "[data-testid='file-input']"
        ]
        
        file_input = None
        for selector in file_input_selectors:
            try:
                file_input = context.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except NoSuchElementException:
                continue
        
        if file_input and hasattr(context, 'selected_file'):
            file_input.send_keys(context.selected_file)
            print("✅ File selected for upload")
            time.sleep(1)  # Wait for file to be processed
        
        # Find and click upload button
        upload_button_selectors = [
            "button[type='submit']",
            ".upload-button",
            "button:contains('Upload')",
            "[data-testid='upload-button']"
        ]
        
        upload_button = None
        for selector in upload_button_selectors:
            try:
                if ":contains(" in selector:
                    upload_button = context.driver.find_element(By.XPATH, 
                        "//button[contains(text(), 'Upload')]")
                else:
                    upload_button = context.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except NoSuchElementException:
                continue
        
        if upload_button:
            upload_button.click()
            print("✅ Upload button clicked")
            take_screenshot(context, "upload_button_clicked")
        else:
            print("⚠️ Upload button not found, file may auto-upload")
        
    except Exception as e:
        take_screenshot(context, "upload_button_click_failed")
        raise AssertionError(f"Failed to click upload button: {str(e)}")

@then('the file should be uploaded successfully')
def step_file_uploaded_successfully(context):
    """Verify that the file was uploaded successfully."""
    try:
        # Wait for upload completion indicators
        success_indicators = [
            ".upload-success",
            ".success-message",
            ".ant-message-success",
            "//*[contains(text(), 'Success')]",
            "//*[contains(text(), 'Uploaded')]"
        ]
        
        success_found = False
        for selector in success_indicators:
            try:
                if selector.startswith("//"):
                    element = WebDriverWait(context.driver, 30).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                else:
                    element = WebDriverWait(context.driver, 30).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                if element.is_displayed():
                    success_found = True
                    print(f"✅ Upload success indicator found: {element.text}")
                    break
            except TimeoutException:
                continue
        
        if not success_found:
            # Alternative: check if we're redirected or if upload progress completed
            current_url = context.driver.current_url
            if "success" in current_url.lower() or "files" in current_url.lower():
                success_found = True
                print("✅ Upload success inferred from URL change")
        
        assert success_found, "No upload success indicators found"
        take_screenshot(context, "file_upload_success")
        
    except Exception as e:
        take_screenshot(context, "file_upload_verification_failed")
        raise AssertionError(f"Failed to verify file upload: {str(e)}")

@then('I should see a success confirmation')
def step_see_success_confirmation(context):
    """Verify that a success confirmation message is displayed."""
    try:
        confirmation_selectors = [
            ".confirmation-message",
            ".success-notification",
            ".alert-success",
            "//*[contains(text(), 'successfully')]",
            "//*[contains(text(), 'completed')]"
        ]
        
        confirmation_found = False
        for selector in confirmation_selectors:
            try:
                if selector.startswith("//"):
                    element = context.driver.find_element(By.XPATH, selector)
                else:
                    element = context.driver.find_element(By.CSS_SELECTOR, selector)
                if element.is_displayed():
                    confirmation_found = True
                    print(f"✅ Success confirmation found: {element.text}")
                    break
            except NoSuchElementException:
                continue
        
        assert confirmation_found, "No success confirmation message found"
        take_screenshot(context, "success_confirmation_visible")
        
    except Exception as e:
        take_screenshot(context, "success_confirmation_check_failed")
        raise AssertionError(f"Failed to find success confirmation: {str(e)}")

@then('the file should appear in my files list')
def step_file_appears_in_list(context):
    """Verify that the uploaded file appears in the files list."""
    try:
        # Navigate to files list if not already there
        files_url = f"{context.frontend_url}/files"
        if context.driver.current_url != files_url:
            context.driver.get(files_url)
            time.sleep(2)
        
        # Look for file list elements
        file_list_selectors = [
            ".file-list",
            ".files-table",
            ".ant-table",
            "[data-testid='files-list']"
        ]
        
        file_list_found = False
        for selector in file_list_selectors:
            try:
                file_list = WebDriverWait(context.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                if file_list.is_displayed():
                    file_list_found = True
                    print("✅ Files list found")
                    break
            except TimeoutException:
                continue
        
        if not file_list_found:
            # Check if there are any file entries
            file_entries = context.driver.find_elements(By.CSS_SELECTOR, 
                "tr, .file-item, .file-entry")
            if len(file_entries) > 0:
                file_list_found = True
                print(f"✅ Found {len(file_entries)} file entries")
        
        assert file_list_found, "Files list not found or empty"
        take_screenshot(context, "files_list_visible")
        
    except Exception as e:
        take_screenshot(context, "files_list_check_failed")
        raise AssertionError(f"Failed to verify files list: {str(e)}")

# Drag and drop upload steps

@when('I drag and drop a file onto the upload area')
def step_drag_drop_file(context):
    """Simulate drag and drop file upload."""
    file_specs = {}
    for row in context.table:
        file_specs['filename'] = row['filename']
        file_specs['type'] = row['type']
    
    # Select appropriate test file
    if file_specs['type'] == 'document':
        selected_file = context.test_files['json']
    else:
        selected_file = context.test_files['text']
    
    try:
        # Find upload area
        upload_area_selectors = [
            ".upload-area",
            ".drag-drop-area",
            ".ant-upload-drag",
            "[data-testid='upload-area']"
        ]
        
        upload_area = None
        for selector in upload_area_selectors:
            try:
                upload_area = context.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except NoSuchElementException:
                continue
        
        if upload_area:
            # For web automation, we'll use the file input instead of actual drag/drop
            file_input = context.driver.find_element(By.CSS_SELECTOR, "input[type='file']")
            file_input.send_keys(selected_file)
            print(f"✅ File drag-dropped (simulated): {selected_file}")
        else:
            raise AssertionError("Upload area not found")
        
        context.selected_file = selected_file
        context.file_specs = file_specs
        take_screenshot(context, "file_drag_dropped")
        
    except Exception as e:
        take_screenshot(context, "drag_drop_failed")
        raise AssertionError(f"Failed to drag and drop file: {str(e)}")

@when('I fill in the required metadata')
def step_fill_required_metadata(context):
    """Fill in required metadata for the uploaded file."""
    try:
        # Wait for metadata form to appear
        time.sleep(2)
        
        # Find description field
        description_field = None
        description_selectors = [
            "textarea[name='description']",
            "input[name='description']",
            ".description-input"
        ]
        
        for selector in description_selectors:
            try:
                description_field = context.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except NoSuchElementException:
                continue
        
        if description_field:
            description_field.clear()
            description_field.send_keys("Drag and drop test file upload")
            print("✅ Required metadata filled")
        
        take_screenshot(context, "metadata_filled")
        
    except Exception as e:
        take_screenshot(context, "metadata_fill_failed")
        raise AssertionError(f"Failed to fill required metadata: {str(e)}")

@then('the file should be uploaded automatically')
def step_file_uploaded_automatically(context):
    """Verify that the file is uploaded automatically after drag and drop."""
    try:
        # Wait for automatic upload to complete
        upload_indicators = [
            ".upload-progress",
            ".uploading",
            ".processing"
        ]
        
        # First wait for upload to start
        upload_started = False
        for selector in upload_indicators:
            try:
                WebDriverWait(context.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                upload_started = True
                print("✅ Automatic upload started")
                break
            except TimeoutException:
                continue
        
        # Then wait for completion
        success_indicators = [
            ".upload-success",
            ".success-message",
            "//*[contains(text(), 'Success')]"
        ]
        
        success_found = False
        for selector in success_indicators:
            try:
                if selector.startswith("//"):
                    WebDriverWait(context.driver, 30).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                else:
                    WebDriverWait(context.driver, 30).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                success_found = True
                print("✅ Automatic upload completed successfully")
                break
            except TimeoutException:
                continue
        
        assert success_found or upload_started, "No evidence of automatic upload"
        take_screenshot(context, "automatic_upload_complete")
        
    except Exception as e:
        take_screenshot(context, "automatic_upload_verification_failed")
        raise AssertionError(f"Failed to verify automatic upload: {str(e)}")

@then('I should see upload progress')
def step_see_upload_progress(context):
    """Verify that upload progress is displayed."""
    try:
        progress_indicators = [
            ".progress-bar",
            ".upload-progress",
            ".ant-progress",
            "[role='progressbar']"
        ]
        
        progress_found = False
        for selector in progress_indicators:
            try:
                progress_element = WebDriverWait(context.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                if progress_element.is_displayed():
                    progress_found = True
                    print("✅ Upload progress indicator found")
                    break
            except TimeoutException:
                continue
        
        # Alternative: look for percentage text
        if not progress_found:
            percentage_elements = context.driver.find_elements(By.XPATH, 
                "//*[contains(text(), '%')]")
            if percentage_elements:
                progress_found = True
                print("✅ Upload progress percentage found")
        
        assert progress_found, "No upload progress indicators found"
        take_screenshot(context, "upload_progress_visible")
        
    except Exception as e:
        take_screenshot(context, "upload_progress_check_failed")
        raise AssertionError(f"Failed to find upload progress: {str(e)}")

def take_screenshot(context, name):
    """Helper function to take screenshots."""
    if hasattr(context, 'driver'):
        timestamp = int(time.time())
        screenshot_path = f"{context.screenshots_dir}/{name}_{timestamp}.png"
        try:
            context.driver.save_screenshot(screenshot_path)
            print(f"📸 Screenshot saved: {screenshot_path}")
        except Exception as e:
            print(f"⚠️ Could not save screenshot: {str(e)}")
