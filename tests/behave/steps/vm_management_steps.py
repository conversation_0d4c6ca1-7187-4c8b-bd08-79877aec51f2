"""
Step definitions for VM management and analysis workflow scenarios.

This module implements the step definitions for Behave scenarios related to
virtual machine creation, management, monitoring, and malware analysis workflows.
"""

import time
import json
import requests
from behave import given, when, then, step
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# VM Management step definitions

@given('I have appropriate permissions for VM operations')
def step_have_vm_permissions(context):
    """Verify that the user has appropriate permissions for VM operations."""
    try:
        # Check VM management endpoint accessibility
        response = context.api_session.get(f"{context.api_base}/vagrant_vm", timeout=context.test_timeout)
        assert response.status_code in [200, 401], f"VM management check failed: {response.status_code}"
        
        if response.status_code == 200:
            print("✅ VM management permissions verified")
        else:
            print("⚠️ VM management may require additional permissions")
            
    except Exception as e:
        print(f"⚠️ VM permissions check failed: {str(e)}")

@given('the VM management system is available')
def step_vm_management_available(context):
    """Verify that the VM management system is accessible."""
    try:
        response = context.api_session.get(f"{context.api_base}/vagrant_vm", timeout=context.test_timeout)
        assert response.status_code in [200, 401], f"VM management system check failed: {response.status_code}"
        print("✅ VM management system is available")
    except Exception as e:
        raise AssertionError(f"VM management system is not available: {str(e)}")

@given('I am on the VM management page')
def step_on_vm_management_page(context):
    """Navigate to the VM management page."""
    vm_url = f"{context.frontend_url}/vms"
    context.driver.get(vm_url)
    
    try:
        WebDriverWait(context.driver, context.test_timeout).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        print(f"✅ Navigated to VM management page: {vm_url}")
        take_screenshot(context, "vm_management_page_loaded")
    except TimeoutException:
        raise AssertionError("VM management page did not load within timeout")

@when('I click "Create New VM"')
def step_click_create_new_vm(context):
    """Click the Create New VM button."""
    try:
        create_button_selectors = [
            "button:contains('Create New VM')",
            "button:contains('New VM')",
            "button:contains('Create VM')",
            ".create-vm-button",
            "[data-testid='create-vm-button']"
        ]
        
        create_button = None
        for selector in create_button_selectors:
            try:
                if ":contains(" in selector:
                    create_button = context.driver.find_element(By.XPATH, 
                        "//button[contains(text(), 'Create') and contains(text(), 'VM')]")
                else:
                    create_button = context.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except NoSuchElementException:
                continue
        
        assert create_button is not None, "Create New VM button not found"
        create_button.click()
        print("✅ Create New VM button clicked")
        take_screenshot(context, "create_vm_button_clicked")
        
        # Wait for form to appear
        time.sleep(2)
        
    except Exception as e:
        take_screenshot(context, "create_vm_button_click_failed")
        raise AssertionError(f"Failed to click Create New VM button: {str(e)}")

@when('I configure the VM settings')
def step_configure_vm_settings(context):
    """Configure VM settings based on the provided table."""
    vm_config = {}
    for row in context.table:
        vm_config[row['setting']] = row['value']
    
    try:
        # Configure VM name
        if 'name' in vm_config:
            name_field = find_form_field(context.driver, 'name', ['name', 'vm_name', 'vmName'])
            if name_field:
                name_field.clear()
                name_field.send_keys(vm_config['name'])
                print(f"✅ VM name set: {vm_config['name']}")
        
        # Configure template
        if 'template' in vm_config:
            template_field = find_form_field(context.driver, 'template', ['template', 'os_template', 'osTemplate'])
            if template_field:
                if template_field.tag_name == 'select':
                    select = Select(template_field)
                    select.select_by_visible_text(vm_config['template'])
                else:
                    template_field.clear()
                    template_field.send_keys(vm_config['template'])
                print(f"✅ VM template set: {vm_config['template']}")
        
        # Configure memory
        if 'memory' in vm_config:
            memory_field = find_form_field(context.driver, 'memory', ['memory', 'ram', 'memorySize'])
            if memory_field:
                memory_field.clear()
                memory_value = vm_config['memory'].replace(' MB', '').replace('MB', '')
                memory_field.send_keys(memory_value)
                print(f"✅ VM memory set: {vm_config['memory']}")
        
        # Configure CPU cores
        if 'cpu_cores' in vm_config:
            cpu_field = find_form_field(context.driver, 'cpu', ['cpu', 'cpus', 'cpu_cores', 'cpuCores'])
            if cpu_field:
                cpu_field.clear()
                cpu_field.send_keys(vm_config['cpu_cores'])
                print(f"✅ VM CPU cores set: {vm_config['cpu_cores']}")
        
        # Configure disk size
        if 'disk_size' in vm_config:
            disk_field = find_form_field(context.driver, 'disk', ['disk', 'disk_size', 'diskSize', 'storage'])
            if disk_field:
                disk_field.clear()
                disk_value = vm_config['disk_size'].replace(' GB', '').replace('GB', '')
                disk_field.send_keys(disk_value)
                print(f"✅ VM disk size set: {vm_config['disk_size']}")
        
        # Configure network mode
        if 'network_mode' in vm_config:
            network_field = find_form_field(context.driver, 'network', ['network', 'network_mode', 'networkMode'])
            if network_field:
                if network_field.tag_name == 'select':
                    select = Select(network_field)
                    select.select_by_visible_text(vm_config['network_mode'])
                else:
                    network_field.clear()
                    network_field.send_keys(vm_config['network_mode'])
                print(f"✅ VM network mode set: {vm_config['network_mode']}")
        
        context.vm_config = vm_config
        take_screenshot(context, "vm_settings_configured")
        
    except Exception as e:
        take_screenshot(context, "vm_settings_configuration_failed")
        raise AssertionError(f"Failed to configure VM settings: {str(e)}")

@when('I click "Create VM"')
def step_click_create_vm(context):
    """Click the Create VM button to submit the configuration."""
    try:
        create_submit_selectors = [
            "button[type='submit']",
            "button:contains('Create VM')",
            "button:contains('Create')",
            ".submit-button",
            ".create-button"
        ]
        
        create_submit_button = None
        for selector in create_submit_selectors:
            try:
                if ":contains(" in selector:
                    create_submit_button = context.driver.find_element(By.XPATH, 
                        "//button[contains(text(), 'Create')]")
                else:
                    create_submit_button = context.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except NoSuchElementException:
                continue
        
        assert create_submit_button is not None, "Create VM submit button not found"
        create_submit_button.click()
        print("✅ Create VM submit button clicked")
        take_screenshot(context, "create_vm_submit_clicked")
        
    except Exception as e:
        take_screenshot(context, "create_vm_submit_failed")
        raise AssertionError(f"Failed to click Create VM submit button: {str(e)}")

@then('the VM should be created successfully')
def step_vm_created_successfully(context):
    """Verify that the VM was created successfully."""
    try:
        # Wait for creation success indicators
        success_indicators = [
            ".vm-creation-success",
            ".success-message",
            ".ant-message-success",
            "//*[contains(text(), 'VM created')]",
            "//*[contains(text(), 'successfully')]"
        ]
        
        success_found = False
        for selector in success_indicators:
            try:
                if selector.startswith("//"):
                    element = WebDriverWait(context.driver, 60).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                else:
                    element = WebDriverWait(context.driver, 60).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                if element.is_displayed():
                    success_found = True
                    print(f"✅ VM creation success indicator found: {element.text}")
                    break
            except TimeoutException:
                continue
        
        if not success_found:
            # Check if we're redirected to VM list or details
            current_url = context.driver.current_url
            if "vm" in current_url.lower() and "create" not in current_url.lower():
                success_found = True
                print("✅ VM creation success inferred from URL change")
        
        assert success_found, "No VM creation success indicators found"
        take_screenshot(context, "vm_creation_success")
        
    except Exception as e:
        take_screenshot(context, "vm_creation_verification_failed")
        raise AssertionError(f"Failed to verify VM creation: {str(e)}")

@then('I should see the VM in my VMs list')
def step_see_vm_in_list(context):
    """Verify that the created VM appears in the VMs list."""
    try:
        # Navigate to VMs list if not already there
        vms_url = f"{context.frontend_url}/vms"
        if "vms" not in context.driver.current_url.lower():
            context.driver.get(vms_url)
            time.sleep(2)
        
        # Look for VM list elements
        vm_list_selectors = [
            ".vm-list",
            ".vms-table",
            ".ant-table",
            "[data-testid='vms-list']"
        ]
        
        vm_list_found = False
        for selector in vm_list_selectors:
            try:
                vm_list = WebDriverWait(context.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                if vm_list.is_displayed():
                    vm_list_found = True
                    print("✅ VMs list found")
                    break
            except TimeoutException:
                continue
        
        if not vm_list_found:
            # Check if there are any VM entries
            vm_entries = context.driver.find_elements(By.CSS_SELECTOR, 
                "tr, .vm-item, .vm-entry")
            if len(vm_entries) > 0:
                vm_list_found = True
                print(f"✅ Found {len(vm_entries)} VM entries")
        
        # Look for the specific VM name if configured
        if hasattr(context, 'vm_config') and 'name' in context.vm_config:
            vm_name = context.vm_config['name']
            vm_name_elements = context.driver.find_elements(By.XPATH, 
                f"//*[contains(text(), '{vm_name}')]")
            if vm_name_elements:
                print(f"✅ Found VM with name: {vm_name}")
        
        assert vm_list_found, "VMs list not found or empty"
        take_screenshot(context, "vms_list_visible")
        
    except Exception as e:
        take_screenshot(context, "vms_list_check_failed")
        raise AssertionError(f"Failed to verify VMs list: {str(e)}")

@then('the VM status should be "{expected_status}"')
def step_vm_status_should_be(context, expected_status):
    """Verify that the VM status matches the expected status."""
    try:
        # Look for status indicators
        status_selectors = [
            ".vm-status",
            ".status-badge",
            ".ant-tag",
            "[data-testid='vm-status']"
        ]
        
        status_found = False
        for selector in status_selectors:
            try:
                status_elements = context.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in status_elements:
                    if element.is_displayed() and expected_status.lower() in element.text.lower():
                        status_found = True
                        print(f"✅ VM status found: {element.text}")
                        break
                if status_found:
                    break
            except NoSuchElementException:
                continue
        
        if not status_found:
            # Look for status text anywhere on the page
            status_elements = context.driver.find_elements(By.XPATH, 
                f"//*[contains(text(), '{expected_status}')]")
            if status_elements:
                status_found = True
                print(f"✅ VM status found in page text: {expected_status}")
        
        assert status_found, f"VM status '{expected_status}' not found"
        take_screenshot(context, f"vm_status_{expected_status.lower()}")
        
    except Exception as e:
        take_screenshot(context, "vm_status_check_failed")
        raise AssertionError(f"Failed to verify VM status: {str(e)}")

# VM Templates step definitions

@when('I view available templates')
def step_view_available_templates(context):
    """View the available VM templates."""
    try:
        # Look for template selection area
        template_selectors = [
            ".template-selection",
            ".vm-templates",
            "select[name='template']",
            "[data-testid='template-selector']"
        ]
        
        template_area = None
        for selector in template_selectors:
            try:
                template_area = context.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except NoSuchElementException:
                continue
        
        assert template_area is not None, "Template selection area not found"
        
        # If it's a select dropdown, click to open it
        if template_area.tag_name == 'select':
            template_area.click()
            time.sleep(1)
        
        print("✅ VM templates area accessed")
        take_screenshot(context, "vm_templates_viewed")
        
    except Exception as e:
        take_screenshot(context, "vm_templates_view_failed")
        raise AssertionError(f"Failed to view available templates: {str(e)}")

@then('I should see multiple OS options')
def step_see_multiple_os_options(context):
    """Verify that multiple OS template options are available."""
    expected_templates = {}
    for row in context.table:
        expected_templates[row['template_name']] = {
            'os_type': row['os_type'],
            'description': row['description']
        }
    
    try:
        # Look for template options
        template_option_selectors = [
            "option",
            ".template-option",
            ".vm-template-card",
            "[data-testid='template-option']"
        ]
        
        template_options = []
        for selector in template_option_selectors:
            try:
                options = context.driver.find_elements(By.CSS_SELECTOR, selector)
                template_options.extend(options)
            except NoSuchElementException:
                continue
        
        assert len(template_options) > 0, "No template options found"
        
        # Check for expected templates
        found_templates = []
        for option in template_options:
            option_text = option.text or option.get_attribute('value') or ''
            for template_name in expected_templates.keys():
                if template_name.lower() in option_text.lower():
                    found_templates.append(template_name)
                    print(f"✅ Found template: {template_name}")
        
        print(f"✅ Found {len(template_options)} total template options")
        print(f"✅ Matched {len(found_templates)} expected templates")
        take_screenshot(context, "vm_templates_options_visible")
        
    except Exception as e:
        take_screenshot(context, "vm_templates_options_check_failed")
        raise AssertionError(f"Failed to verify template options: {str(e)}")

def find_form_field(driver, field_type, field_names):
    """Helper function to find form fields by various possible names."""
    for name in field_names:
        selectors = [
            f"input[name='{name}']",
            f"select[name='{name}']",
            f"textarea[name='{name}']",
            f"[data-testid='{name}']",
            f".{name}-field input",
            f".{name}-field select"
        ]
        
        for selector in selectors:
            try:
                field = driver.find_element(By.CSS_SELECTOR, selector)
                if field.is_displayed():
                    return field
            except NoSuchElementException:
                continue
    
    return None

def take_screenshot(context, name):
    """Helper function to take screenshots."""
    if hasattr(context, 'driver'):
        timestamp = int(time.time())
        screenshot_path = f"{context.screenshots_dir}/{name}_{timestamp}.png"
        try:
            context.driver.save_screenshot(screenshot_path)
            print(f"📸 Screenshot saved: {screenshot_path}")
        except Exception as e:
            print(f"⚠️ Could not save screenshot: {str(e)}")
