"""
Step definitions for authentication and authorization scenarios.

This module implements the step definitions for Behave scenarios related to
user authentication, login, logout, and authorization testing.
"""

import time
import requests
from behave import given, when, then, step
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Authentication step definitions

@given('the TurdParty platform is running')
def step_platform_running(context):
    """Verify that the TurdParty platform is accessible."""
    try:
        response = requests.get(f"{context.api_base}/health/", timeout=context.test_timeout)
        assert response.status_code == 200, f"Platform health check failed: {response.status_code}"
        print(f"✅ Platform is running at {context.api_url}")
    except Exception as e:
        raise AssertionError(f"Platform is not accessible: {str(e)}")

@given('the authentication system is available')
def step_auth_system_available(context):
    """Verify that the authentication system is working."""
    try:
        response = requests.post(f"{context.api_base}/auth/test-token", timeout=context.test_timeout)
        assert response.status_code in [200, 401], f"Auth system check failed: {response.status_code}"
        print("✅ Authentication system is available")
    except Exception as e:
        raise AssertionError(f"Authentication system is not available: {str(e)}")

@given('I am on the login page')
def step_on_login_page(context):
    """Navigate to the login page."""
    login_url = f"{context.frontend_url}/login"
    context.driver.get(login_url)
    
    # Wait for page to load
    try:
        WebDriverWait(context.driver, context.test_timeout).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        print(f"✅ Navigated to login page: {login_url}")
        context.take_screenshot = lambda name: take_screenshot(context, name)
        context.take_screenshot("login_page_loaded")
    except TimeoutException:
        raise AssertionError("Login page did not load within timeout")

@when('I enter valid credentials')
def step_enter_valid_credentials(context):
    """Enter valid login credentials."""
    credentials = {}
    for row in context.table:
        credentials[row['username']] = row['password']
    
    try:
        # Find username field
        username_selectors = [
            "input[name='username']",
            "input[name='email']", 
            "input[type='email']",
            "input[placeholder*='username']",
            "input[placeholder*='email']"
        ]
        
        username_field = None
        for selector in username_selectors:
            try:
                username_field = context.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except NoSuchElementException:
                continue
        
        assert username_field is not None, "Could not find username field"
        username_field.clear()
        username_field.send_keys(list(credentials.keys())[0])
        
        # Find password field
        password_selectors = [
            "input[name='password']",
            "input[type='password']",
            "input[placeholder*='password']"
        ]
        
        password_field = None
        for selector in password_selectors:
            try:
                password_field = context.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except NoSuchElementException:
                continue
        
        assert password_field is not None, "Could not find password field"
        password_field.clear()
        password_field.send_keys(list(credentials.values())[0])
        
        context.entered_credentials = credentials
        print("✅ Valid credentials entered")
        context.take_screenshot("credentials_entered")
        
    except Exception as e:
        context.take_screenshot("credential_entry_failed")
        raise AssertionError(f"Failed to enter credentials: {str(e)}")

@when('I enter invalid credentials')
def step_enter_invalid_credentials(context):
    """Enter invalid login credentials."""
    credentials = {}
    for row in context.table:
        credentials[row['username']] = row['password']
    
    try:
        # Find and fill username field
        username_field = context.driver.find_element(By.CSS_SELECTOR, 
            "input[name='username'], input[name='email'], input[type='email']")
        username_field.clear()
        username_field.send_keys(list(credentials.keys())[0])
        
        # Find and fill password field
        password_field = context.driver.find_element(By.CSS_SELECTOR, 
            "input[name='password'], input[type='password']")
        password_field.clear()
        password_field.send_keys(list(credentials.values())[0])
        
        context.entered_credentials = credentials
        print("✅ Invalid credentials entered")
        context.take_screenshot("invalid_credentials_entered")
        
    except Exception as e:
        context.take_screenshot("invalid_credential_entry_failed")
        raise AssertionError(f"Failed to enter invalid credentials: {str(e)}")

@when('I click the login button')
def step_click_login_button(context):
    """Click the login button to submit credentials."""
    try:
        # Find login button
        login_button_selectors = [
            "button[type='submit']",
            "input[type='submit']",
            "button:contains('Login')",
            "button:contains('Sign In')",
            ".login-button",
            ".submit-button"
        ]
        
        login_button = None
        for selector in login_button_selectors:
            try:
                if ":contains(" in selector:
                    # Handle text-based selectors
                    login_button = context.driver.find_element(By.XPATH, 
                        f"//button[contains(text(), 'Login') or contains(text(), 'Sign In')]")
                else:
                    login_button = context.driver.find_element(By.CSS_SELECTOR, selector)
                break
            except NoSuchElementException:
                continue
        
        assert login_button is not None, "Could not find login button"
        
        # Click the button
        login_button.click()
        print("✅ Login button clicked")
        context.take_screenshot("login_button_clicked")
        
        # Wait a moment for the request to process
        time.sleep(2)
        
    except Exception as e:
        context.take_screenshot("login_button_click_failed")
        raise AssertionError(f"Failed to click login button: {str(e)}")

@then('I should be redirected to the dashboard')
def step_redirected_to_dashboard(context):
    """Verify redirection to dashboard after successful login."""
    try:
        # Wait for redirect and check URL
        WebDriverWait(context.driver, context.test_timeout).until(
            lambda driver: "dashboard" in driver.current_url.lower() or 
                          "home" in driver.current_url.lower() or
                          driver.current_url != f"{context.frontend_url}/login"
        )
        
        current_url = context.driver.current_url
        assert "login" not in current_url.lower(), f"Still on login page: {current_url}"
        print(f"✅ Redirected to: {current_url}")
        context.take_screenshot("dashboard_loaded")
        
    except TimeoutException:
        context.take_screenshot("redirect_failed")
        raise AssertionError("Was not redirected from login page within timeout")

@then('I should see a welcome message')
def step_see_welcome_message(context):
    """Verify that a welcome message is displayed."""
    try:
        # Look for welcome message elements
        welcome_selectors = [
            "//*[contains(text(), 'Welcome')]",
            "//*[contains(text(), 'Hello')]",
            "//*[contains(text(), 'Dashboard')]",
            ".welcome-message",
            ".user-greeting"
        ]
        
        welcome_found = False
        for selector in welcome_selectors:
            try:
                if selector.startswith("//"):
                    element = context.driver.find_element(By.XPATH, selector)
                else:
                    element = context.driver.find_element(By.CSS_SELECTOR, selector)
                if element.is_displayed():
                    welcome_found = True
                    print(f"✅ Welcome message found: {element.text}")
                    break
            except NoSuchElementException:
                continue
        
        assert welcome_found, "No welcome message found on the page"
        context.take_screenshot("welcome_message_visible")
        
    except Exception as e:
        context.take_screenshot("welcome_message_check_failed")
        raise AssertionError(f"Failed to find welcome message: {str(e)}")

@then('my session should be authenticated')
def step_session_authenticated(context):
    """Verify that the session is properly authenticated."""
    try:
        # Check for authentication indicators
        auth_indicators = [
            ".user-menu",
            ".logout-button", 
            ".user-profile",
            "//*[contains(text(), 'Logout')]",
            "//*[contains(text(), 'Sign Out')]"
        ]
        
        auth_found = False
        for selector in auth_indicators:
            try:
                if selector.startswith("//"):
                    element = context.driver.find_element(By.XPATH, selector)
                else:
                    element = context.driver.find_element(By.CSS_SELECTOR, selector)
                if element.is_displayed():
                    auth_found = True
                    print("✅ Authentication indicators found")
                    break
            except NoSuchElementException:
                continue
        
        assert auth_found, "No authentication indicators found"
        context.take_screenshot("session_authenticated")
        
    except Exception as e:
        context.take_screenshot("session_auth_check_failed")
        raise AssertionError(f"Failed to verify session authentication: {str(e)}")

@then('I should see an error message')
def step_see_error_message(context):
    """Verify that an error message is displayed for failed login."""
    try:
        # Look for error message elements
        error_selectors = [
            ".error-message",
            ".alert-danger",
            ".notification-error",
            "//*[contains(text(), 'Invalid')]",
            "//*[contains(text(), 'Error')]",
            "//*[contains(text(), 'Failed')]"
        ]
        
        error_found = False
        for selector in error_selectors:
            try:
                if selector.startswith("//"):
                    element = context.driver.find_element(By.XPATH, selector)
                else:
                    element = context.driver.find_element(By.CSS_SELECTOR, selector)
                if element.is_displayed():
                    error_found = True
                    print(f"✅ Error message found: {element.text}")
                    break
            except NoSuchElementException:
                continue
        
        assert error_found, "No error message found for failed login"
        context.take_screenshot("error_message_visible")
        
    except Exception as e:
        context.take_screenshot("error_message_check_failed")
        raise AssertionError(f"Failed to find error message: {str(e)}")

@then('I should remain on the login page')
def step_remain_on_login_page(context):
    """Verify that user remains on login page after failed login."""
    try:
        current_url = context.driver.current_url
        assert "login" in current_url.lower(), f"Not on login page: {current_url}"
        print(f"✅ Remained on login page: {current_url}")
        context.take_screenshot("remained_on_login")
        
    except Exception as e:
        context.take_screenshot("login_page_check_failed")
        raise AssertionError(f"Failed to verify login page: {str(e)}")

@then('my session should not be authenticated')
def step_session_not_authenticated(context):
    """Verify that the session is not authenticated after failed login."""
    try:
        # Check that authentication indicators are NOT present
        auth_indicators = [
            ".user-menu",
            ".logout-button",
            ".user-profile"
        ]
        
        for selector in auth_indicators:
            try:
                element = context.driver.find_element(By.CSS_SELECTOR, selector)
                assert not element.is_displayed(), f"Authentication indicator found: {selector}"
            except NoSuchElementException:
                pass  # Good, element not found
        
        print("✅ Session is not authenticated")
        context.take_screenshot("session_not_authenticated")
        
    except Exception as e:
        context.take_screenshot("session_not_auth_check_failed")
        raise AssertionError(f"Failed to verify session not authenticated: {str(e)}")

# API Authentication steps

@given('I have valid API credentials')
def step_have_valid_api_credentials(context):
    """Set up valid API credentials for testing."""
    context.api_credentials = {
        "username": "<EMAIL>",
        "password": "password123"
    }
    print("✅ Valid API credentials prepared")

@when('I request an authentication token')
def step_request_auth_token(context):
    """Request an authentication token via API."""
    try:
        response = context.api_session.post(
            f"{context.api_base}/auth/login",
            json=context.api_credentials,
            timeout=context.test_timeout
        )
        
        context.auth_response = response
        if response.status_code == 200:
            context.received_token = response.json().get("access_token")
            print("✅ Authentication token requested successfully")
        else:
            print(f"⚠️ Token request failed: {response.status_code}")
            
    except Exception as e:
        raise AssertionError(f"Failed to request authentication token: {str(e)}")

@then('I should receive a valid JWT token')
def step_receive_valid_jwt_token(context):
    """Verify that a valid JWT token was received."""
    assert hasattr(context, 'auth_response'), "No authentication response found"
    assert context.auth_response.status_code == 200, f"Auth failed: {context.auth_response.status_code}"
    
    response_data = context.auth_response.json()
    assert "access_token" in response_data, "No access token in response"
    
    token = response_data["access_token"]
    assert token is not None and len(token) > 0, "Empty or null token received"
    
    # Basic JWT format check (should have 3 parts separated by dots)
    token_parts = token.split('.')
    assert len(token_parts) == 3, f"Invalid JWT format: {len(token_parts)} parts"
    
    print(f"✅ Valid JWT token received (length: {len(token)})")

@then('the token should have proper expiration')
def step_token_proper_expiration(context):
    """Verify that the token has proper expiration settings."""
    assert hasattr(context, 'auth_response'), "No authentication response found"
    
    response_data = context.auth_response.json()
    
    # Check for expiration information
    if "expires_in" in response_data:
        expires_in = response_data["expires_in"]
        assert expires_in > 0, "Token expiration should be positive"
        print(f"✅ Token expires in {expires_in} seconds")
    
    if "token_type" in response_data:
        token_type = response_data["token_type"]
        assert token_type.lower() == "bearer", f"Expected Bearer token, got {token_type}"
        print(f"✅ Token type is {token_type}")

@then('I should be able to access protected endpoints')
def step_access_protected_endpoints(context):
    """Verify that the token allows access to protected endpoints."""
    assert hasattr(context, 'received_token'), "No token available for testing"
    
    headers = {"Authorization": f"Bearer {context.received_token}"}
    
    try:
        response = requests.get(
            f"{context.api_base}/users/me",
            headers=headers,
            timeout=context.test_timeout
        )
        
        assert response.status_code == 200, f"Protected endpoint access failed: {response.status_code}"
        print("✅ Successfully accessed protected endpoint")
        
    except Exception as e:
        raise AssertionError(f"Failed to access protected endpoint: {str(e)}")

def take_screenshot(context, name):
    """Helper function to take screenshots."""
    if hasattr(context, 'driver'):
        timestamp = int(time.time())
        screenshot_path = f"{context.screenshots_dir}/{name}_{timestamp}.png"
        try:
            context.driver.save_screenshot(screenshot_path)
            print(f"📸 Screenshot saved: {screenshot_path}")
        except Exception as e:
            print(f"⚠️ Could not save screenshot: {str(e)}")
