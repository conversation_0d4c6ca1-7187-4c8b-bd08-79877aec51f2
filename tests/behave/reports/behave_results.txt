Feature: End-to-End Cybersecurity Analysis Workflows # features/end_to_end_workflows.feature:1
  As a cybersecurity professional
  I want to perform complete malware analysis workflows
  So that I can efficiently investigate and analyze security threats
  Background:   # features/end_to_end_workflows.feature:6

  @e2e @critical @malware_analysis
  Scenario: Complete malware analysis workflow           # features/end_to_end_workflows.feature:11
    Given I am authenticated on the TurdParty platform   # steps/file_management_steps.py:20
    And all system components are operational            # None
    Given I have received a suspicious file for analysis # None
    When I perform the complete analysis workflow        # None
      | step                   | action                            | expected_outcome                  |
      | initial_assessment     | upload and scan file              | file accepted and queued          |
      | static_analysis        | examine file structure            | metadata and signatures extracted |
      | vm_preparation         | create isolated analysis VM       | secure analysis environment ready |
      | dynamic_analysis       | execute file in controlled env    | behavior patterns captured        |
      | network_monitoring     | monitor network communications    | traffic patterns recorded         |
      | system_impact_analysis | assess system modifications       | changes documented                |
      | threat_classification  | classify threat type and severity | threat level determined           |
      | report_generation      | compile comprehensive report      | detailed analysis report ready    |
      | evidence_preservation  | archive analysis artifacts        | forensic evidence secured         |
    Then I should have a complete threat assessment      # None
    And all evidence should be properly documented       # None
    And the analysis should meet forensic standards      # None

  @e2e @collaboration @team_workflow
  Scenario: Collaborative team analysis workflow       # features/end_to_end_workflows.feature:29
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And all system components are operational          # None
    Given I am part of a cybersecurity analysis team   # None
    And we have a complex threat to investigate        # None
    When we perform collaborative analysis             # None
      | team_member | role               | responsibility                    |
      | analyst_1   | lead_investigator  | coordinate analysis and reporting |
      | analyst_2   | malware_expert     | perform detailed malware analysis |
      | analyst_3   | network_specialist | analyze network communications    |
      | analyst_4   | forensics_expert   | preserve evidence and artifacts   |
    And we use the platform's collaboration features   # None
      | feature           | usage                              |
      | shared_workspace  | access common analysis environment |
      | real_time_updates | monitor analysis progress          |
      | comment_system    | discuss findings and observations  |
      | role_permissions  | control access to sensitive data   |
    Then the team should work efficiently together     # None
    And all findings should be properly coordinated    # None
    And the final report should reflect team consensus # None

  @e2e @incident_response @time_critical
  Scenario: Emergency incident response workflow       # features/end_to_end_workflows.feature:49
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And all system components are operational          # None
    Given we have detected an active security incident # None
    And immediate analysis is required                 # None
    When I initiate emergency response procedures      # None
      | urgency_level | response_time | analysis_depth | reporting_speed |
      | critical      | < 5 minutes   | rapid_triage   | real_time       |
    And I perform rapid threat assessment              # None
      | assessment_type    | time_limit | key_indicators              |
      | initial_triage     | 2 minutes  | file type, size, source     |
      | quick_scan         | 5 minutes  | known signatures, hashes    |
      | behavioral_preview | 10 minutes | immediate execution effects |
      | threat_level       | 15 minutes | severity and impact scope   |
    Then I should have actionable intelligence quickly # None
    And containment recommendations should be provided # None
    And the incident response team should be notified  # None

  @e2e @bulk_processing @automation
  Scenario: Bulk file processing and analysis          # features/end_to_end_workflows.feature:66
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And all system components are operational          # None
    Given I have multiple suspicious files to analyze  # None
    When I initiate bulk processing workflow           # None
      | batch_size | file_types                       | processing_mode |
      | 50         | executables, documents, archives | automated       |
    And the system processes files automatically       # None
      | processing_stage   | automation_level | human_intervention  |
      | file_validation    | full             | none                |
      | initial_scanning   | full             | exception_handling  |
      | vm_allocation      | full             | resource_monitoring |
      | analysis_execution | full             | anomaly_review      |
      | result_compilation | full             | quality_assurance   |
    Then all files should be processed efficiently     # None
    And results should be properly categorized         # None
    And exceptions should be flagged for manual review # None

  @e2e @compliance @audit_trail
  Scenario: Compliance and audit trail workflow                 # features/end_to_end_workflows.feature:83
    Given I am authenticated on the TurdParty platform          # steps/file_management_steps.py:20
    And all system components are operational                   # None
    Given I need to maintain compliance with security standards # None
    When I perform analysis with full audit trail               # None
      | compliance_standard | requirement             | implementation       |
      | ISO_27001           | evidence_preservation   | automated_archiving  |
      | NIST_CSF            | incident_documentation  | structured_reporting |
      | GDPR                | data_protection         | privacy_controls     |
      | SOX                 | financial_data_security | access_logging       |
    And I ensure proper documentation                           # None
      | documentation_type | content                       | retention_period |
      | analysis_logs      | detailed_activity_records     | 7_years          |
      | evidence_chain     | custody_and_handling_records  | permanent        |
      | access_records     | user_activity_and_permissions | 3_years          |
      | system_changes     | configuration_modifications   | 5_years          |
    Then all compliance requirements should be met              # None
    And audit trails should be complete and tamper-proof        # None
    And documentation should be easily retrievable              # None

  @e2e @performance @scalability
  Scenario: High-volume analysis performance workflow        # features/end_to_end_workflows.feature:102
    Given I am authenticated on the TurdParty platform       # steps/file_management_steps.py:20
    And all system components are operational                # None
    Given the platform needs to handle high analysis volumes # None
    When I test scalability under load                       # None
      | load_scenario     | concurrent_analyses | file_size_range | expected_performance |
      | normal_operations | 10                  | 1MB - 50MB      | < 30 min per file    |
      | peak_load         | 25                  | 1MB - 100MB     | < 45 min per file    |
      | stress_test       | 50                  | 1MB - 200MB     | < 60 min per file    |
    And I monitor system performance                         # None
      | metric            | normal_threshold | peak_threshold | alert_threshold |
      | cpu_utilization   | < 70%            | < 85%          | > 90%           |
      | memory_usage      | < 80%            | < 90%          | > 95%           |
      | disk_io           | < 75%            | < 85%          | > 90%           |
      | network_bandwidth | < 60%            | < 80%          | > 85%           |
    Then the system should maintain acceptable performance   # None
    And no analyses should fail due to resource constraints  # None
    And scaling should be automatic and transparent          # None

  @e2e @integration @external_systems
  Scenario: External system integration workflow               # features/end_to_end_workflows.feature:120
    Given I am authenticated on the TurdParty platform         # steps/file_management_steps.py:20
    And all system components are operational                  # None
    Given the platform integrates with external security tools # None
    When I perform analysis with external integrations         # None
      | external_system     | integration_type | data_exchange       |
      | threat_intelligence | api_feeds        | IOC_updates         |
      | siem_platform       | log_forwarding   | analysis_results    |
      | ticketing_system    | case_management  | incident_tracking   |
      | sandbox_services    | analysis_offload | behavioral_analysis |
    And I verify integration functionality                     # None
      | integration_test     | expected_behavior                     |
      | data_synchronization | consistent_information_across_systems |
      | error_handling       | graceful_degradation_on_failures      |
      | authentication       | secure_api_communication              |
      | rate_limiting        | respect_external_system_limits        |
    Then all integrations should work seamlessly               # None
    And data should flow correctly between systems             # None
    And the user experience should be unified                  # None

  @e2e @disaster_recovery @business_continuity
  Scenario: Disaster recovery and business continuity workflow      # features/end_to_end_workflows.feature:139
    Given I am authenticated on the TurdParty platform              # steps/file_management_steps.py:20
    And all system components are operational                       # None
    Given the platform must maintain operations during disruptions  # None
    When I test disaster recovery procedures                        # None
      | disaster_scenario | impact_level | recovery_target | data_loss_tolerance |
      | server_failure    | high         | < 4 hours       | < 15 minutes        |
      | network_outage    | medium       | < 2 hours       | none                |
      | data_corruption   | high         | < 8 hours       | < 1 hour            |
      | security_breach   | critical     | < 1 hour        | none                |
    And I verify recovery capabilities                              # None
      | recovery_component  | test_procedure                | success_criteria     |
      | data_backup         | restore_from_backup           | 100%_data_integrity  |
      | system_failover     | switch_to_backup_systems      | < 5_minute_downtime  |
      | user_notification   | alert_users_of_service_status | timely_communication |
      | service_restoration | return_to_normal_operations   | full_functionality   |
    Then recovery procedures should work as designed                # None
    And business operations should continue with minimal disruption # None
    And all data should be preserved and accessible                 # None

  @e2e @mobile_access @remote_work
  Scenario: Mobile and remote access workflow                # features/end_to_end_workflows.feature:158
    Given I am authenticated on the TurdParty platform       # steps/file_management_steps.py:20
    And all system components are operational                # None
    Given cybersecurity professionals work remotely          # None
    When I access the platform from mobile devices           # None
      | device_type | operating_system | browser | screen_size |
      | smartphone  | iOS              | Safari  | 375x667     |
      | smartphone  | Android          | Chrome  | 360x640     |
      | tablet      | iOS              | Safari  | 768x1024    |
      | tablet      | Android          | Chrome  | 800x1280    |
    And I perform essential analysis tasks                   # None
      | task                | mobile_capability | expected_experience    |
      | file_upload         | full              | intuitive_interface    |
      | analysis_monitoring | full              | real_time_updates      |
      | report_viewing      | full              | readable_formatting    |
      | team_collaboration  | full              | seamless_communication |
    Then the mobile experience should be fully functional    # None
    And the interface should be responsive and user-friendly # None
    And security should be maintained across all devices     # None

  @e2e @training @knowledge_transfer
  Scenario: Training and knowledge transfer workflow        # features/end_to_end_workflows.feature:177
    Given I am authenticated on the TurdParty platform      # steps/file_management_steps.py:20
    And all system components are operational               # None
    Given new team members need platform training           # None
    When I conduct comprehensive training sessions          # None
      | training_module   | duration | skill_level  | learning_objectives           |
      | platform_basics   | 2 hours  | beginner     | navigation_and_basic_features |
      | file_analysis     | 4 hours  | intermediate | upload_and_analysis_workflows |
      | vm_management     | 3 hours  | intermediate | vm_creation_and_monitoring    |
      | advanced_features | 6 hours  | advanced     | automation_and_integration    |
    And I provide hands-on practice                         # None
      | practice_scenario  | complexity | guidance_level | success_metrics  |
      | simple_malware     | low        | high           | task_completion  |
      | complex_threat     | medium     | medium         | analysis_quality |
      | team_collaboration | high       | low            | independent_work |
    Then trainees should achieve competency                 # None
    And knowledge transfer should be effective              # None
    And the team should be ready for independent operations # None

  @e2e @quality_assurance @continuous_improvement
  Scenario: Quality assurance and continuous improvement workflow  # features/end_to_end_workflows.feature:195
    Given I am authenticated on the TurdParty platform             # steps/file_management_steps.py:20
    And all system components are operational                      # None
    Given the platform requires ongoing quality assurance          # None
    When I implement quality monitoring                            # None
      | quality_metric     | measurement_method | target_threshold | improvement_trigger |
      | analysis_accuracy  | expert_validation  | > 95%            | < 90%               |
      | processing_speed   | automated_timing   | < 30 min         | > 45 min            |
      | user_satisfaction  | feedback_surveys   | > 4.5/5          | < 4.0/5             |
      | system_reliability | uptime_monitoring  | > 99.5%          | < 99%               |
    And I track improvement opportunities                          # None
      | improvement_area         | monitoring_approach | implementation_cycle |
      | user_experience          | usability_testing   | quarterly            |
      | performance_optimization | benchmarking        | monthly              |
      | feature_enhancement      | user_feedback       | bi_annually          |
      | security_updates         | vulnerability_scans | continuously         |
    Then quality should continuously improve                       # None
    And user needs should be consistently met                      # None
    And the platform should evolve with changing requirements      # None

Feature: File Management and Analysis Workflow # features/file_management_workflow.feature:1
  As a cybersecurity analyst
  I want to upload, manage, and analyze files through the TurdParty platform
  So that I can efficiently process malware samples and suspicious files
  Background:   # features/file_management_workflow.feature:6

  @file_upload @ui @critical
  Scenario: Upload a single file through web interface  # features/file_management_workflow.feature:11
    Given I am authenticated on the TurdParty platform  # steps/file_management_steps.py:20
    And the file management system is available         # steps/file_management_steps.py:38
    Given I am on the file upload page                  # steps/file_management_steps.py:48
    When I select a file to upload                      # steps/file_management_steps.py:63
      | filename    | type   | size   |
      | malware.exe | binary | 2.5 MB |
    And I provide file metadata                         # steps/file_management_steps.py:84
      | description | Suspicious executable from email attachment |
      | source      | Email investigation                         |
      | priority    | High                                        |
    And I click the upload button                       # steps/file_management_steps.py:121
    Then the file should be uploaded successfully       # steps/file_management_steps.py:176
    And I should see a success confirmation             # steps/file_management_steps.py:221
    And the file should appear in my files list         # steps/file_management_steps.py:254

  @file_upload @ui @drag_drop
  Scenario: Upload file using drag and drop            # features/file_management_workflow.feature:26
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And the file management system is available        # steps/file_management_steps.py:38
    Given I am on the file upload page                 # steps/file_management_steps.py:48
    When I drag and drop a file onto the upload area   # steps/file_management_steps.py:302
      | filename   | type     |
      | sample.pdf | document |
    And I fill in the required metadata                # steps/file_management_steps.py:349
    Then the file should be uploaded automatically     # steps/file_management_steps.py:382
    And I should see upload progress                   # steps/file_management_steps.py:437
    And the file should be processed correctly         # None

  @file_upload @api @bulk
  Scenario: Bulk file upload via API                    # features/file_management_workflow.feature:37
    Given I am authenticated on the TurdParty platform  # steps/file_management_steps.py:20
    And the file management system is available         # steps/file_management_steps.py:38
    Given I have API access to the file upload endpoint # None
    When I upload multiple files simultaneously         # None
      | filename    | type     | description            |
      | sample1.exe | binary   | Malware sample 1       |
      | sample2.dll | library  | Suspicious DLL         |
      | sample3.doc | document | Macro-enabled document |
    Then all files should be uploaded successfully      # None
    And each file should have a unique identifier       # None
    And the files should be queued for processing       # None

  @file_management @ui @search
  Scenario: Search and filter uploaded files           # features/file_management_workflow.feature:49
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And the file management system is available        # steps/file_management_steps.py:38
    Given I have uploaded multiple files               # None
    And I am on the files management page              # None
    When I search for files using criteria             # None
      | criteria    | value       |
      | filename    | malware     |
      | file_type   | executable  |
      | upload_date | last 7 days |
    Then I should see only matching files              # None
    And the results should be properly sorted          # None
    And I should be able to refine my search           # None

  @file_management @ui @details
  Scenario: View detailed file information             # features/file_management_workflow.feature:62
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And the file management system is available        # steps/file_management_steps.py:38
    Given I have uploaded a file                       # None
    And I am on the files list page                    # None
    When I click on a file to view details             # None
    Then I should see comprehensive file information   # None
      | field           | expected                      |
      | filename        | original filename             |
      | file_size       | size in human-readable format |
      | upload_date     | timestamp of upload           |
      | file_hash       | SHA-256 hash                  |
      | mime_type       | detected MIME type            |
      | analysis_status | current processing status     |

  @file_processing @async @analysis
  Scenario: Automated file analysis workflow           # features/file_management_workflow.feature:76
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And the file management system is available        # steps/file_management_steps.py:38
    Given I have uploaded a suspicious file            # None
    When the file enters the analysis queue            # None
    Then the system should automatically perform       # None
      | analysis_type    | description                    |
      | hash_check       | Check against known malware DB |
      | static_analysis  | Examine file structure         |
      | metadata_extract | Extract file metadata          |
      | signature_scan   | Scan for known signatures      |
    And I should receive progress updates              # None
    And the results should be stored for review        # None

  @file_management @ui @download
  Scenario: Download processed files and reports       # features/file_management_workflow.feature:89
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And the file management system is available        # steps/file_management_steps.py:38
    Given I have a file that has been analyzed         # None
    And I am viewing the file details                  # None
    When I request to download                         # None
      | item_type       | format |
      | original_file   | binary |
      | analysis_report | PDF    |
      | metadata_export | JSON   |
    Then the download should start immediately         # None
    And the file should be complete and uncorrupted    # None

  @file_management @security @permissions
  Scenario: File access permissions and sharing         # features/file_management_workflow.feature:101
    Given I am authenticated on the TurdParty platform  # steps/file_management_steps.py:20
    And the file management system is available         # steps/file_management_steps.py:38
    Given I have uploaded a file                        # None
    And I want to share it with team members            # None
    When I set file permissions                         # None
      | user_type | permissions         |
      | team_lead | read, write, delete |
      | analyst   | read, comment       |
      | viewer    | read only           |
    Then the permissions should be enforced             # None
    And users should only see files they have access to # None

  @file_upload @validation @security
  Scenario: File type validation and security checks   # features/file_management_workflow.feature:113
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And the file management system is available        # steps/file_management_steps.py:38
    Given I am uploading files to the platform         # None
    When I attempt to upload various file types        # None
      | filename     | type       | expected_result |
      | document.pdf | document   | accepted        |
      | script.js    | script     | quarantined     |
      | image.jpg    | image      | accepted        |
      | malware.exe  | executable | quarantined     |
      | archive.zip  | archive    | scanned         |
    Then the system should handle each appropriately   # None
    And security policies should be enforced           # None

  @file_management @ui @multilingual
  Scenario Outline: File management in different languages -- @1.1   # features/file_management_workflow.feature:135
    Given I am authenticated on the TurdParty platform               # steps/file_management_steps.py:20
    And the file management system is available                      # steps/file_management_steps.py:38
    Given the interface language is set to "English"                 # steps/multilingual_steps.py:95
    And I am on the file management pageEnglish                             # steps/multilingual_steps.py:202
    When I perform file operations                                   # None
    Then all interface elements should be in "English"               # None
    And file operations should work correctly                        # None

  @file_management @ui @multilingual
  Scenario Outline: File management in different languages -- @1.2   # features/file_management_workflow.feature:136
    Given I am authenticated on the TurdParty platform               # steps/file_management_steps.py:20
    And the file management system is available                      # steps/file_management_steps.py:38
    Given the interface language is set to "German"                  # steps/multilingual_steps.py:95
    And I am on the file management pageGerman                             # steps/multilingual_steps.py:202
    When I perform file operations                                   # None
    Then all interface elements should be in "German"                # None
    And file operations should work correctly                        # None

  @file_management @ui @multilingual
  Scenario Outline: File management in different languages -- @1.3   # features/file_management_workflow.feature:137
    Given I am authenticated on the TurdParty platform               # steps/file_management_steps.py:20
    And the file management system is available                      # steps/file_management_steps.py:38
    Given the interface language is set to "French"                  # steps/multilingual_steps.py:95
    And I am on the file management pageFrench                             # steps/multilingual_steps.py:202
    When I perform file operations                                   # None
    Then all interface elements should be in "French"                # None
    And file operations should work correctly                        # None

  @file_management @ui @multilingual
  Scenario Outline: File management in different languages -- @1.4   # features/file_management_workflow.feature:138
    Given I am authenticated on the TurdParty platform               # steps/file_management_steps.py:20
    And the file management system is available                      # steps/file_management_steps.py:38
    Given the interface language is set to "Spanish"                 # steps/multilingual_steps.py:95
    And I am on the file management pageSpanish                             # steps/multilingual_steps.py:202
    When I perform file operations                                   # None
    Then all interface elements should be in "Spanish"               # None
    And file operations should work correctly                        # None

  @file_management @ui @multilingual
  Scenario Outline: File management in different languages -- @1.5   # features/file_management_workflow.feature:139
    Given I am authenticated on the TurdParty platform               # steps/file_management_steps.py:20
    And the file management system is available                      # steps/file_management_steps.py:38
    Given the interface language is set to "Croatian"                # steps/multilingual_steps.py:95
    And I am on the file management pageCroatian                             # steps/multilingual_steps.py:202
    When I perform file operations                                   # None
    Then all interface elements should be in "Croatian"              # None
    And file operations should work correctly                        # None

  @file_processing @performance @large_files
  Scenario: Large file upload and processing           # features/file_management_workflow.feature:142
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And the file management system is available        # steps/file_management_steps.py:38
    Given I need to upload a large file                # None
    When I upload a file larger than 100MB             # None
      | filename     | size  | type     |
      | large_vm.ova | 500MB | VM image |
    Then the upload should handle the large size       # None
    And I should see accurate progress indicators      # None
    And the file should be processed efficiently       # None
    And memory usage should remain reasonable          # None

  @file_management @integration @vm_workflow
  Scenario: Complete file-to-VM analysis workflow      # features/file_management_workflow.feature:153
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And the file management system is available        # steps/file_management_steps.py:38
    Given I have uploaded a suspicious executable      # None
    When I initiate the complete analysis workflow     # None
      | step              | action                        |
      | file_upload       | upload suspicious file        |
      | initial_scan      | perform quick security scan   |
      | vm_preparation    | prepare isolated analysis VM  |
      | file_injection    | inject file into analysis VM  |
      | behavior_analysis | monitor file execution        |
      | report_generation | generate comprehensive report |
    Then each step should complete successfully        # None
    And I should have a complete analysis report       # None
    And the VM should be properly cleaned up           # None

  @file_management @api @automation
  Scenario: Automated file processing pipeline         # features/file_management_workflow.feature:168
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And the file management system is available        # steps/file_management_steps.py:38
    Given I have an automated analysis pipeline        # None
    When files are uploaded via API                    # None
    Then the system should automatically               # None
      | action             | timing        |
      | validate_file      | immediate     |
      | queue_for_analysis | within 1 min  |
      | start_processing   | within 5 min  |
      | generate_report    | within 30 min |
      | notify_completion  | immediate     |
    And the pipeline should handle errors gracefully   # None
    And I should receive status updates throughout     # None

  @file_management @backup @recovery
  Scenario: File backup and recovery procedures        # features/file_management_workflow.feature:182
    Given I am authenticated on the TurdParty platform # steps/file_management_steps.py:20
    And the file management system is available        # steps/file_management_steps.py:38
    Given I have important analysis files              # None
    When I request file backup                         # None
    Then the system should create secure backups       # None
    And I should be able to restore files if needed    # None
    And backup integrity should be verified            # None
    And recovery procedures should be tested           # None

Feature: Multilingual User Experience # features/multilingual_user_experience.feature:1
  As an international cybersecurity professional
  I want to use the TurdParty platform in my native language
  So that I can efficiently perform malware analysis without language barriers
  Background:   # features/multilingual_user_experience.feature:6

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @1.1 Germanic Languages  # features/multilingual_user_experience.feature:26
    Given the TurdParty platform supports multiple languages                            # steps/multilingual_steps.py:76
    And I am authenticated on the platformGerman                                              # steps/multilingual_steps.py:95
    Given the interface language is set to "German"                                     # steps/multilingual_steps.py:166
    When I perform a complete malware analysisGermanlow                                 # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "German"                                   # steps/multilingual_steps.py:250
    And all functionality should workGermanctly                                         # steps/multilingual_steps.py:286
    And error messages should be in "German"                                            # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @1.2 Germanic Languages  # features/multilingual_user_experience.feature:27
    Given the TurdParty platform supports multiple languages                            # steps/multilingual_steps.py:76
    And I am authenticated on the platformEnglish                                              # steps/multilingual_steps.py:95
    Given the interface language is set to "English"                                    # steps/multilingual_steps.py:166
    When I perform a complete malware analysisEnglishow                                 # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "English"                                  # steps/multilingual_steps.py:250
    And all functionality should workEnglishtly                                         # steps/multilingual_steps.py:286
    And error messages should be in "English"                                           # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @1.3 Germanic Languages  # features/multilingual_user_experience.feature:28
    Given the TurdParty platform supports multiple languages                            # steps/multilingual_steps.py:76
    And I am authenticated on the platformDutch                                              # steps/multilingual_steps.py:95
    Given the interface language is set to "Dutch"                                      # steps/multilingual_steps.py:166
    When I perform a complete malware analysisDutchflow                                 # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "Dutch"                                    # steps/multilingual_steps.py:250
    And all functionality should workDutchectly                                         # steps/multilingual_steps.py:286
    And error messages should be in "Dutch"                                             # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @1.4 Germanic Languages  # features/multilingual_user_experience.feature:29
    Given the TurdParty platform supports multiple languages                            # steps/multilingual_steps.py:76
    And I am authenticated on the platformSwedish                                              # steps/multilingual_steps.py:95
    Given the interface language is set to "Swedish"                                    # steps/multilingual_steps.py:166
    When I perform a complete malware analysisSwedishow                                 # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "Swedish"                                  # steps/multilingual_steps.py:250
    And all functionality should workSwedishtly                                         # steps/multilingual_steps.py:286
    And error messages should be in "Swedish"                                           # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @1.5 Germanic Languages  # features/multilingual_user_experience.feature:30
    Given the TurdParty platform supports multiple languages                            # steps/multilingual_steps.py:76
    And I am authenticated on the platformDanish                                              # steps/multilingual_steps.py:95
    Given the interface language is set to "Danish"                                     # steps/multilingual_steps.py:166
    When I perform a complete malware analysisDanishlow                                 # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "Danish"                                   # steps/multilingual_steps.py:250
    And all functionality should workDanishctly                                         # steps/multilingual_steps.py:286
    And error messages should be in "Danish"                                            # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @2.1 Romance Languages  # features/multilingual_user_experience.feature:34
    Given the TurdParty platform supports multiple languages                           # steps/multilingual_steps.py:76
    And I am authenticated on the platformFrench                                             # steps/multilingual_steps.py:95
    Given the interface language is set to "French"                                    # steps/multilingual_steps.py:166
    When I perform a complete malware analysisFrenchlow                                # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "French"                                  # steps/multilingual_steps.py:250
    And all functionality should workFrenchctly                                        # steps/multilingual_steps.py:286
    And error messages should be in "French"                                           # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @2.2 Romance Languages  # features/multilingual_user_experience.feature:35
    Given the TurdParty platform supports multiple languages                           # steps/multilingual_steps.py:76
    And I am authenticated on the platformItalian                                             # steps/multilingual_steps.py:95
    Given the interface language is set to "Italian"                                   # steps/multilingual_steps.py:166
    When I perform a complete malware analysisItalianow                                # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "Italian"                                 # steps/multilingual_steps.py:250
    And all functionality should workItaliantly                                        # steps/multilingual_steps.py:286
    And error messages should be in "Italian"                                          # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @2.3 Romance Languages  # features/multilingual_user_experience.feature:36
    Given the TurdParty platform supports multiple languages                           # steps/multilingual_steps.py:76
    And I am authenticated on the platformSpanish                                             # steps/multilingual_steps.py:95
    Given the interface language is set to "Spanish"                                   # steps/multilingual_steps.py:166
    When I perform a complete malware analysisSpanishow                                # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "Spanish"                                 # steps/multilingual_steps.py:250
    And all functionality should workSpanishtly                                        # steps/multilingual_steps.py:286
    And error messages should be in "Spanish"                                          # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @2.4 Romance Languages  # features/multilingual_user_experience.feature:37
    Given the TurdParty platform supports multiple languages                           # steps/multilingual_steps.py:76
    And I am authenticated on the platformPortuguese                                             # steps/multilingual_steps.py:95
    Given the interface language is set to "Portuguese"                                # steps/multilingual_steps.py:166
    When I perform a complete malware analysisPortuguese                                # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "Portuguese"                              # steps/multilingual_steps.py:250
    And all functionality should workPortuguese                                        # steps/multilingual_steps.py:286
    And error messages should be in "Portuguese"                                       # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @3.1 Slavic Languages  # features/multilingual_user_experience.feature:41
    Given the TurdParty platform supports multiple languages                          # steps/multilingual_steps.py:76
    And I am authenticated on the platformCroatian                                            # steps/multilingual_steps.py:95
    Given the interface language is set to "Croatian"                                 # steps/multilingual_steps.py:166
    When I perform a complete malware analysisCroatianw                               # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "Croatian"                               # steps/multilingual_steps.py:250
    And all functionality should workCroatianly                                       # steps/multilingual_steps.py:286
    And error messages should be in "Croatian"                                        # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @3.2 Slavic Languages  # features/multilingual_user_experience.feature:42
    Given the TurdParty platform supports multiple languages                          # steps/multilingual_steps.py:76
    And I am authenticated on the platformSerbian                                            # steps/multilingual_steps.py:95
    Given the interface language is set to "Serbian"                                  # steps/multilingual_steps.py:166
    When I perform a complete malware analysisSerbianow                               # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "Serbian"                                # steps/multilingual_steps.py:250
    And all functionality should workSerbiantly                                       # steps/multilingual_steps.py:286
    And error messages should be in "Serbian"                                         # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @3.3 Slavic Languages  # features/multilingual_user_experience.feature:43
    Given the TurdParty platform supports multiple languages                          # steps/multilingual_steps.py:76
    And I am authenticated on the platformPolish                                            # steps/multilingual_steps.py:95
    Given the interface language is set to "Polish"                                   # steps/multilingual_steps.py:166
    When I perform a complete malware analysisPolishlow                               # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "Polish"                                 # steps/multilingual_steps.py:250
    And all functionality should workPolishctly                                       # steps/multilingual_steps.py:286
    And error messages should be in "Polish"                                          # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @3.4 Slavic Languages  # features/multilingual_user_experience.feature:44
    Given the TurdParty platform supports multiple languages                          # steps/multilingual_steps.py:76
    And I am authenticated on the platformCzech                                            # steps/multilingual_steps.py:95
    Given the interface language is set to "Czech"                                    # steps/multilingual_steps.py:166
    When I perform a complete malware analysisCzechflow                               # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "Czech"                                  # steps/multilingual_steps.py:250
    And all functionality should workCzechectly                                       # steps/multilingual_steps.py:286
    And error messages should be in "Czech"                                           # None

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages -- @3.5 Slavic Languages  # features/multilingual_user_experience.feature:45
    Given the TurdParty platform supports multiple languages                          # steps/multilingual_steps.py:76
    And I am authenticated on the platformBulgarian                                            # steps/multilingual_steps.py:95
    Given the interface language is set to "Bulgarian"                                # steps/multilingual_steps.py:166
    When I perform a complete malware analysisBulgarian                               # steps/multilingual_steps.py:202
      | step               | action                          |
      | login              | authenticate with credentials   |
      | file_upload        | upload suspicious file          |
      | vm_creation        | create analysis virtual machine |
      | analysis_execution | run malware analysis            |
      | report_generation  | generate analysis report        |
    Then all interface elements should be in "Bulgarian"                              # steps/multilingual_steps.py:250
    And all functionality should workBulgariany                                       # steps/multilingual_steps.py:286
    And error messages should be in "Bulgarian"                                       # None

  @multilingual @ui @language_switching
  Scenario: Dynamic language switching during session        # features/multilingual_user_experience.feature:48
    Given the TurdParty platform supports multiple languages # steps/multilingual_steps.py:76
    And I am authenticated on the Germanrm                   # steps/multilingual_steps.py:325
    Given I am logged into the platformGerman                      # steps/multilingual_steps.py:343
    And the current language is "EFrench"                    # steps/multilingual_steps.py:325
    When I switch the language to "German"                   # None
    Then the interface should immediately update to German   # None
    And my session should remain active                      # None
    And all previously entered data should be preserved      # None
    When I switch the language to "French"                   # None
    Then the interface should update to French               # None
    And functionality should continue to work correctly      # None

  @multilingual @forms @validation
  Scenario Outline: Form validation messages in multiple languages -- @1.1   # features/multilingual_user_experience.feature:72
    Given the TurdParty platform supports multiple languages                 # steps/multilingual_steps.py:76
    And I am authenticated on the platformEnglish                                   # steps/multilingual_steps.py:95
    Given the interface language is set to "English"                         # None
    And I am on the file upload form                                         # None
    When I submit the form with invalid data                                 # None
      | field       | invalid_value |
      | filename    | (empty)       |
      | description | (empty)       |
    Then I should see validation errors in "English"                         # None
    And the error messages should be culturally appropriate                  # None

  @multilingual @forms @validation
  Scenario Outline: Form validation messages in multiple languages -- @1.2   # features/multilingual_user_experience.feature:73
    Given the TurdParty platform supports multiple languages                 # steps/multilingual_steps.py:76
    And I am authenticated on the platformGerman                                   # steps/multilingual_steps.py:95
    Given the interface language is set to "German"                          # None
    And I am on the file upload form                                         # None
    When I submit the form with invalid data                                 # None
      | field       | invalid_value |
      | filename    | (empty)       |
      | description | (empty)       |
    Then I should see validation errors in "German"                          # None
    And the error messages should be culturally appropriate                  # None

  @multilingual @forms @validation
  Scenario Outline: Form validation messages in multiple languages -- @1.3   # features/multilingual_user_experience.feature:74
    Given the TurdParty platform supports multiple languages                 # steps/multilingual_steps.py:76
    And I am authenticated on the platformFrench                                   # steps/multilingual_steps.py:95
    Given the interface language is set to "French"                          # None
    And I am on the file upload form                                         # None
    When I submit the form with invalid data                                 # None
      | field       | invalid_value |
      | filename    | (empty)       |
      | description | (empty)       |
    Then I should see validation errors in "French"                          # None
    And the error messages should be culturally appropriate                  # None

  @multilingual @forms @validation
  Scenario Outline: Form validation messages in multiple languages -- @1.4   # features/multilingual_user_experience.feature:75
    Given the TurdParty platform supports multiple languages                 # steps/multilingual_steps.py:76
    And I am authenticated on the platformSpanish                                   # steps/multilingual_steps.py:95
    Given the interface language is set to "Spanish"                         # None
    And I am on the file upload form                                         # None
    When I submit the form with invalid data                                 # None
      | field       | invalid_value |
      | filename    | (empty)       |
      | description | (empty)       |
    Then I should see validation errors in "Spanish"                         # None
    And the error messages should be culturally appropriate                  # None

  @multilingual @forms @validation
  Scenario Outline: Form validation messages in multiple languages -- @1.5   # features/multilingual_user_experience.feature:76
    Given the TurdParty platform supports multiple languages                 # steps/multilingual_steps.py:76
    And I am authenticated on the platformCroatian                                   # steps/multilingual_steps.py:95
    Given the interface language is set to "Croatian"                        # None
    And I am on the file upload form                                         # None
    When I submit the form with invalid data                                 # None
      | field       | invalid_value |
      | filename    | (empty)       |
      | description | (empty)       |
    Then I should see validation errors in "Croatian"                        # None
    And the error messages should be culturally appropriate                  # None

  @multilingual @content @localization
  Scenario Outline: Content localization and cultural adaptation -- @1.1   # features/multilingual_user_experience.feature:97
    Given the TurdParty platform supports multiple languages               # steps/multilingual_steps.py:76
    And I am authenticated on the platformGerman                                 # steps/multilingual_steps.py:95
    Given the interface language is set to "German"                        # None
    When I view different sections of the platform                         # None
      | section            | content_type       |
      | dashboard          | welcome_messages   |
      | help_documentation | user_guides        |
      | error_pages        | error_explanations |
      | tooltips           | help_text          |
    Then all content should be properly localized for "German"             # None
    And cultural conventions should be respected                           # None
      | convention       | expectation                |
      | date_format      | appropriate for locale     |
      | number_format    | correct decimal separators |
      | currency_display | local currency conventions |
      | time_format      | 12/24 hour based on locale |

  @multilingual @content @localization
  Scenario Outline: Content localization and cultural adaptation -- @1.2   # features/multilingual_user_experience.feature:98
    Given the TurdParty platform supports multiple languages               # steps/multilingual_steps.py:76
    And I am authenticated on the platformFrench                                 # steps/multilingual_steps.py:95
    Given the interface language is set to "French"                        # None
    When I view different sections of the platform                         # None
      | section            | content_type       |
      | dashboard          | welcome_messages   |
      | help_documentation | user_guides        |
      | error_pages        | error_explanations |
      | tooltips           | help_text          |
    Then all content should be properly localized for "French"             # None
    And cultural conventions should be respected                           # None
      | convention       | expectation                |
      | date_format      | appropriate for locale     |
      | number_format    | correct decimal separators |
      | currency_display | local currency conventions |
      | time_format      | 12/24 hour based on locale |

  @multilingual @content @localization
  Scenario Outline: Content localization and cultural adaptation -- @1.3   # features/multilingual_user_experience.feature:99
    Given the TurdParty platform supports multiple languages               # steps/multilingual_steps.py:76
    And I am authenticated on the platformSpanish                                 # steps/multilingual_steps.py:95
    Given the interface language is set to "Spanish"                       # None
    When I view different sections of the platform                         # None
      | section            | content_type       |
      | dashboard          | welcome_messages   |
      | help_documentation | user_guides        |
      | error_pages        | error_explanations |
      | tooltips           | help_text          |
    Then all content should be properly localized for "Spanish"            # None
    And cultural conventions should be respected                           # None
      | convention       | expectation                |
      | date_format      | appropriate for locale     |
      | number_format    | correct decimal separators |
      | currency_display | local currency conventions |
      | time_format      | 12/24 hour based on locale |

  @multilingual @content @localization
  Scenario Outline: Content localization and cultural adaptation -- @1.4   # features/multilingual_user_experience.feature:100
    Given the TurdParty platform supports multiple languages               # steps/multilingual_steps.py:76
    And I am authenticated on the platformCroatian                                 # steps/multilingual_steps.py:95
    Given the interface language is set to "Croatian"                      # None
    When I view different sections of the platform                         # None
      | section            | content_type       |
      | dashboard          | welcome_messages   |
      | help_documentation | user_guides        |
      | error_pages        | error_explanations |
      | tooltips           | help_text          |
    Then all content should be properly localized for "Croatian"           # None
    And cultural conventions should be respected                           # None
      | convention       | expectation                |
      | date_format      | appropriate for locale     |
      | number_format    | correct decimal separators |
      | currency_display | local currency conventions |
      | time_format      | 12/24 hour based on locale |

  @multilingual @accessibility @screen_readers
  Scenario Outline: Screen reader compatibility in multiple languages -- @1.1   # features/multilingual_user_experience.feature:113
    Given the TurdParty platform supports multiple languages                    # steps/multilingual_steps.py:76
    And I am authenticated on the platformEnglish                                      # steps/multilingual_steps.py:95
    Given the interface language is set to "English"                            # None
    And I am using a screen reader                                              # None
    When I navigate through the platform                                        # None
    Then screen reader announcements should be in "English"                     # None
    And ARIA labels should be properly translated                               # None
    And navigation should be logical in the language context                    # None

  @multilingual @accessibility @screen_readers
  Scenario Outline: Screen reader compatibility in multiple languages -- @1.2   # features/multilingual_user_experience.feature:114
    Given the TurdParty platform supports multiple languages                    # steps/multilingual_steps.py:76
    And I am authenticated on the platformGerman                                      # steps/multilingual_steps.py:95
    Given the interface language is set to "German"                             # None
    And I am using a screen reader                                              # None
    When I navigate through the platform                                        # None
    Then screen reader announcements should be in "German"                      # None
    And ARIA labels should be properly translated                               # None
    And navigation should be logical in the language context                    # None

  @multilingual @accessibility @screen_readers
  Scenario Outline: Screen reader compatibility in multiple languages -- @1.3   # features/multilingual_user_experience.feature:115
    Given the TurdParty platform supports multiple languages                    # steps/multilingual_steps.py:76
    And I am authenticated on the platformFrench                                      # steps/multilingual_steps.py:95
    Given the interface language is set to "French"                             # None
    And I am using a screen reader                                              # None
    When I navigate through the platform                                        # None
    Then screen reader announcements should be in "French"                      # None
    And ARIA labels should be properly translated                               # None
    And navigation should be logical in the language context                    # None

  @multilingual @accessibility @screen_readers
  Scenario Outline: Screen reader compatibility in multiple languages -- @1.4   # features/multilingual_user_experience.feature:116
    Given the TurdParty platform supports multiple languages                    # steps/multilingual_steps.py:76
    And I am authenticated on the platformSpanish                                      # steps/multilingual_steps.py:95
    Given the interface language is set to "Spanish"                            # None
    And I am using a screen reader                                              # None
    When I navigate through the platform                                        # None
    Then screen reader announcements should be in "Spanish"                     # None
    And ARIA labels should be properly translated                               # None
    And navigation should be logical in the language context                    # None

  @multilingual @api @content_negotiation
  Scenario: API content negotiation based on language preferences  # features/multilingual_user_experience.feature:119
    Given the TurdParty platform supports multiple languages       # steps/multilingual_steps.py:76
    And I am authenticated on the platform                         # None
    Given I am making API requests                                 # None
    When I set the Accept-Language header to different values      # None
      | accept_language_header  | expected_response_language |
      | en-US,en;q=0.9          | English                    |
      | de-DE,de;q=0.9,en;q=0.8 | German                     |
      | fr-FR,fr;q=0.9,en;q=0.7 | French                     |
      | es-ES,es;q=0.9,en;q=0.7 | Spanish                    |
      | hr-HR,hr;q=0.9,en;q=0.7 | Croatian                   |
    Then the API responses should respect the language preference  # None
    And error messages should be in the requested language         # None
    And content should be appropriately localized                  # None

  @multilingual @performance @language_loading
  Scenario: Performance impact of multilingual features      # features/multilingual_user_experience.feature:133
    Given the TurdParty platform supports multiple languages # steps/multilingual_steps.py:76
    And I am authenticated on the platform                   # None
    Given the platform supports 37 languages                 # None
    When I switch between different languages rapidly        # None
      | sequence | language_1 | language_2 | language_3 |
      | 1        | English    | German     | French     |
      | 2        | Spanish    | Croatian   | Italian    |
      | 3        | Polish     | Swedish    | Portuguese |
    Then language switching should be fast and responsive    # None
    And there should be no memory leaks                      # None
    And the user experience should remain smooth             # None

  @multilingual @unicode @character_support
  Scenario: Unicode and special character support                # features/multilingual_user_experience.feature:145
    Given the TurdParty platform supports multiple languages     # steps/multilingual_steps.py:76
    And I am authenticated on the platform                       # None
    Given I am using the platform in different languages         # None
    When I enter text with special characters                    # None
      | language | special_characters           | context           |
      | German   | ä, ö, ü, ß, Ä, Ö, Ü          | file descriptions |
      | French   | é, è, ê, ë, à, ç, ù, û, ô, î | metadata fields   |
      | Spanish  | ñ, á, é, í, ó, ú, ü          | comments          |
      | Croatian | č, ć, đ, š, ž, Č, Ć, Đ, Š, Ž | analysis notes    |
      | Polish   | ą, ć, ę, ł, ń, ó, ś, ź, ż    | report titles     |
    Then the characters should be displayed correctly            # None
    And they should be stored and retrieved properly             # None
    And search functionality should work with special characters # None

  @multilingual @rtl @bidirectional
  Scenario: Right-to-left language support preparation       # features/multilingual_user_experience.feature:159
    Given the TurdParty platform supports multiple languages # steps/multilingual_steps.py:76
    And I am authenticated on the platform                   # None
    Given the platform is designed for international use     # None
    When I test the interface layout                         # None
    Then the design should be RTL-ready                      # None
    And text alignment should be flexible                    # None
    And UI components should support bidirectional text      # None

  @multilingual @translation_quality @consistency
  Scenario Outline: Translation quality and consistency -- @1.1   # features/multilingual_user_experience.feature:178
    Given the TurdParty platform supports multiple languages      # steps/multilingual_steps.py:76
    And I am authenticated on the platformGerman                        # steps/multilingual_steps.py:95
    Given the interface language is set to "German"               # None
    When I review translations across the platform                # None
    Then technical terms should be consistently translated        # None
    And the tone should be professional and appropriate           # None
    And there should be no untranslated strings                   # None
    And context-specific translations should be accurate          # None

  @multilingual @translation_quality @consistency
  Scenario Outline: Translation quality and consistency -- @1.2   # features/multilingual_user_experience.feature:179
    Given the TurdParty platform supports multiple languages      # steps/multilingual_steps.py:76
    And I am authenticated on the platformFrench                        # steps/multilingual_steps.py:95
    Given the interface language is set to "French"               # None
    When I review translations across the platform                # None
    Then technical terms should be consistently translated        # None
    And the tone should be professional and appropriate           # None
    And there should be no untranslated strings                   # None
    And context-specific translations should be accurate          # None

  @multilingual @translation_quality @consistency
  Scenario Outline: Translation quality and consistency -- @1.3   # features/multilingual_user_experience.feature:180
    Given the TurdParty platform supports multiple languages      # steps/multilingual_steps.py:76
    And I am authenticated on the platformSpanish                        # steps/multilingual_steps.py:95
    Given the interface language is set to "Spanish"              # None
    When I review translations across the platform                # None
    Then technical terms should be consistently translated        # None
    And the tone should be professional and appropriate           # None
    And there should be no untranslated strings                   # None
    And context-specific translations should be accurate          # None

  @multilingual @translation_quality @consistency
  Scenario Outline: Translation quality and consistency -- @1.4   # features/multilingual_user_experience.feature:181
    Given the TurdParty platform supports multiple languages      # steps/multilingual_steps.py:76
    And I am authenticated on the platformCroatian                        # steps/multilingual_steps.py:95
    Given the interface language is set to "Croatian"             # None
    When I review translations across the platform                # None
    Then technical terms should be consistently translated        # None
    And the tone should be professional and appropriate           # None
    And there should be no untranslated strings                   # None
    And context-specific translations should be accurate          # None

  @multilingual @help_documentation @support
  Scenario Outline: Multilingual help and documentation -- @1.1   # features/multilingual_user_experience.feature:198
    Given the TurdParty platform supports multiple languages      # steps/multilingual_steps.py:76
    And I am authenticated on the platformGerman                        # steps/multilingual_steps.py:95
    Given the interface language is set to "German"               # None
    When I access help documentation                              # None
      | help_section      | content_type           |
      | user_guide        | step_by_step_tutorials |
      | api_documentation | technical_reference    |
      | troubleshooting   | problem_solutions      |
      | faq               | common_questions       |
    Then all documentation should be available in "German"        # None
    And examples should be culturally relevant                    # None
    And contact information should be localized                   # None

  @multilingual @help_documentation @support
  Scenario Outline: Multilingual help and documentation -- @1.2   # features/multilingual_user_experience.feature:199
    Given the TurdParty platform supports multiple languages      # steps/multilingual_steps.py:76
    And I am authenticated on the platformFrench                        # steps/multilingual_steps.py:95
    Given the interface language is set to "French"               # None
    When I access help documentation                              # None
      | help_section      | content_type           |
      | user_guide        | step_by_step_tutorials |
      | api_documentation | technical_reference    |
      | troubleshooting   | problem_solutions      |
      | faq               | common_questions       |
    Then all documentation should be available in "French"        # None
    And examples should be culturally relevant                    # None
    And contact information should be localized                   # None

  @multilingual @help_documentation @support
  Scenario Outline: Multilingual help and documentation -- @1.3   # features/multilingual_user_experience.feature:200
    Given the TurdParty platform supports multiple languages      # steps/multilingual_steps.py:76
    And I am authenticated on the platformSpanish                        # steps/multilingual_steps.py:95
    Given the interface language is set to "Spanish"              # None
    When I access help documentation                              # None
      | help_section      | content_type           |
      | user_guide        | step_by_step_tutorials |
      | api_documentation | technical_reference    |
      | troubleshooting   | problem_solutions      |
      | faq               | common_questions       |
    Then all documentation should be available in "Spanish"       # None
    And examples should be culturally relevant                    # None
    And contact information should be localized                   # None

  @multilingual @help_documentation @support
  Scenario Outline: Multilingual help and documentation -- @1.4   # features/multilingual_user_experience.feature:201
    Given the TurdParty platform supports multiple languages      # steps/multilingual_steps.py:76
    And I am authenticated on the platformCroatian                        # steps/multilingual_steps.py:95
    Given the interface language is set to "Croatian"             # None
    When I access help documentation                              # None
      | help_section      | content_type           |
      | user_guide        | step_by_step_tutorials |
      | api_documentation | technical_reference    |
      | troubleshooting   | problem_solutions      |
      | faq               | common_questions       |
    Then all documentation should be available in "Croatian"      # None
    And examples should be culturally relevant                    # None
    And contact information should be localized                   # None

  @multilingual @error_handling @user_feedback
  Scenario Outline: Multilingual error handling and user feedback -- @1.1   # features/multilingual_user_experience.feature:218
    Given the TurdParty platform supports multiple languages                # steps/multilingual_steps.py:76
    And I am authenticated on the platformGerman                                  # steps/multilingual_steps.py:95
    Given the interface language is set to "German"                         # None
    When errors occur during platform usage                                 # None
      | error_type       | trigger_condition           |
      | validation_error | invalid form submission     |
      | network_error    | connection timeout          |
      | permission_error | unauthorized access attempt |
      | system_error     | internal server error       |
    Then error messages should be clear and helpful in "German"             # None
    And suggested solutions should be provided                              # None
    And error codes should be language-independent                          # None

  @multilingual @error_handling @user_feedback
  Scenario Outline: Multilingual error handling and user feedback -- @1.2   # features/multilingual_user_experience.feature:219
    Given the TurdParty platform supports multiple languages                # steps/multilingual_steps.py:76
    And I am authenticated on the platformFrench                                  # steps/multilingual_steps.py:95
    Given the interface language is set to "French"                         # None
    When errors occur during platform usage                                 # None
      | error_type       | trigger_condition           |
      | validation_error | invalid form submission     |
      | network_error    | connection timeout          |
      | permission_error | unauthorized access attempt |
      | system_error     | internal server error       |
    Then error messages should be clear and helpful in "French"             # None
    And suggested solutions should be provided                              # None
    And error codes should be language-independent                          # None

  @multilingual @error_handling @user_feedback
  Scenario Outline: Multilingual error handling and user feedback -- @1.3   # features/multilingual_user_experience.feature:220
    Given the TurdParty platform supports multiple languages                # steps/multilingual_steps.py:76
    And I am authenticated on the platformSpanish                                  # steps/multilingual_steps.py:95
    Given the interface language is set to "Spanish"                        # None
    When errors occur during platform usage                                 # None
      | error_type       | trigger_condition           |
      | validation_error | invalid form submission     |
      | network_error    | connection timeout          |
      | permission_error | unauthorized access attempt |
      | system_error     | internal server error       |
    Then error messages should be clear and helpful in "Spanish"            # None
    And suggested solutions should be provided                              # None
    And error codes should be language-independent                          # None

  @multilingual @error_handling @user_feedback
  Scenario Outline: Multilingual error handling and user feedback -- @1.4   # features/multilingual_user_experience.feature:221
    Given the TurdParty platform supports multiple languages                # steps/multilingual_steps.py:76
    And I am authenticated on the platformCroatian                                  # steps/multilingual_steps.py:95
    Given the interface language is set to "Croatian"                       # None
    When errors occur during platform usage                                 # None
      | error_type       | trigger_condition           |
      | validation_error | invalid form submission     |
      | network_error    | connection timeout          |
      | permission_error | unauthorized access attempt |
      | system_error     | internal server error       |
    Then error messages should be clear and helpful in "Croatian"           # None
    And suggested solutions should be provided                              # None
    And error codes should be language-independent                          # None

  @multilingual @integration @complete_workflow
  Scenario: Complete multilingual cybersecurity workflow          # features/multilingual_user_experience.feature:224
    Given the TurdParty platform supports multiple languages      # steps/multilingual_steps.py:76
    And I am authenticated on the platformGerman                        # steps/multilingual_steps.py:95
    Given I am a German-speaking cybersecurity analyst            # None
    And the interface language is set to "German"                 # None
    When I perform a complete malware analysis                    # None
      | workflow_step       | german_interface_element     | expected_behavior              |
      | login               | "Anmelden" button            | successful authentication      |
      | file_upload         | "Datei hochladen" section    | file upload with German labels |
      | vm_creation         | "VM erstellen" dialog        | VM creation in German          |
      | analysis_monitoring | "Analyse überwachen" panel   | real-time updates in German    |
      | report_download     | "Bericht herunterladen" link | German-language report         |
    Then the entire workflow should be seamless in German         # None
    And all technical terms should be properly translated         # None
    And the user experience should feel native to German speakers # None

  @multilingual @compliance @eu_requirements
  Scenario: EU language compliance verification                   # features/multilingual_user_experience.feature:239
    Given the TurdParty platform supports multiple languages      # steps/multilingual_steps.py:76
    And I am authenticated on the platform                        # None
    Given the platform claims 96% EU language compliance          # None
    When I test EU official languages                             # None
      | eu_language | iso_code | compliance_level |
      | German      | de       | full             |
      | French      | fr       | full             |
      | Italian     | it       | full             |
      | Spanish     | es       | full             |
      | Polish      | pl       | full             |
      | Dutch       | nl       | full             |
      | Croatian    | hr       | full             |
      | Czech       | cs       | full             |
      | Hungarian   | hu       | full             |
      | Portuguese  | pt       | full             |
    Then each language should have complete interface translation # None
    And core functionality should work in all languages           # None
    And the platform should meet EU accessibility requirements    # None

Feature: User Authentication and Authorization # features/user_authentication.feature:1
  As a cybersecurity professional
  I want to securely authenticate and access the TurdParty platform
  So that I can perform malware analysis tasks with proper authorization
  Background:   # features/user_authentication.feature:6

  @authentication @ui @critical
  Scenario: Successful user login through web interface  # features/user_authentication.feature:11
    Given the TurdParty platform is running              # steps/authentication_steps.py:18
    And the authentication system is available           # steps/authentication_steps.py:28
    Given I am on the login page                         # steps/authentication_steps.py:38
    When I enter valid credentials                       # steps/authentication_steps.py:55
      | username | <EMAIL> |
      | password | password123       |
    And I click the login button                         # steps/authentication_steps.py:139
    Then I should be redirected to the dashboard         # steps/authentication_steps.py:180
    And I should see a welcome message                   # steps/authentication_steps.py:200
    And my session should be authenticated               # steps/authentication_steps.py:234

  @authentication @ui @critical
  Scenario: Failed login with invalid credentials  # features/user_authentication.feature:22
    Given the TurdParty platform is running        # steps/authentication_steps.py:18
    And the authentication system is available     # steps/authentication_steps.py:28
    Given I am on the login page                   # steps/authentication_steps.py:38
    When I enter invalid credentials               # steps/authentication_steps.py:111
      | username | <EMAIL> |
      | password | wrongpassword       |
    And I click the login button                   # steps/authentication_steps.py:139
    Then I should see an error message             # steps/authentication_steps.py:268
    And I should remain on the login page          # steps/authentication_steps.py:303
    And my session should not be authenticated     # steps/authentication_steps.py:316

  @authentication @api @critical
  Scenario: API token authentication                   # features/user_authentication.feature:33
    Given the TurdParty platform is running            # steps/authentication_steps.py:18
    And the authentication system is available         # steps/authentication_steps.py:28
    Given I have valid API credentials                 # steps/authentication_steps.py:343
    When I request an authentication token             # steps/authentication_steps.py:352
    Then I should receive a valid JWT token            # steps/authentication_steps.py:372
    And the token should have proper expiration        # steps/authentication_steps.py:390
    And I should be able to access protected endpoints # steps/authentication_steps.py:408

  @authentication @api @security
  Scenario: Token expiration handling              # features/user_authentication.feature:41
    Given the TurdParty platform is running        # steps/authentication_steps.py:18
    And the authentication system is available     # steps/authentication_steps.py:28
    Given I have an expired authentication token   # None
    When I try to access a protected endpoint      # None
    Then I should receive an unauthorized response # None
    And I should be prompted to re-authenticate    # None

  @authentication @ui @security
  Scenario: Session timeout handling                    # features/user_authentication.feature:48
    Given the TurdParty platform is running             # steps/authentication_steps.py:18
    And the authentication system is available          # steps/authentication_steps.py:28
    Given I am logged into the web interface            # None
    And my session has been idle for the timeout period # None
    When I try to perform an action                     # None
    Then I should be redirected to the login page       # None
    And I should see a session timeout message          # None

  @authentication @ui @multilingual
  Scenario Outline: Login interface in multiple languages -- @1.1   # features/user_authentication.feature:65
    Given the TurdParty platform is running                         # steps/authentication_steps.py:18
    And the authentication system is available                      # steps/authentication_steps.py:28
    Given I am on the login page                                    # steps/authentication_steps.py:38
    And the interface language is set to "English"                  # steps/multilingual_steps.py:95
    When I view the login form                                      # None
    Then all text should be displayed in "English"                  # None
    And the form should function correctly                          # None

  @authentication @ui @multilingual
  Scenario Outline: Login interface in multiple languages -- @1.2   # features/user_authentication.feature:66
    Given the TurdParty platform is running                         # steps/authentication_steps.py:18
    And the authentication system is available                      # steps/authentication_steps.py:28
    Given I am on the login page                                    # steps/authentication_steps.py:38
    And the interface language is set to "German"                   # steps/multilingual_steps.py:95
    When I view the login form                                      # None
    Then all text should be displayed in "German"                   # None
    And the form should function correctly                          # None

  @authentication @ui @multilingual
  Scenario Outline: Login interface in multiple languages -- @1.3   # features/user_authentication.feature:67
    Given the TurdParty platform is running                         # steps/authentication_steps.py:18
    And the authentication system is available                      # steps/authentication_steps.py:28
    Given I am on the login page                                    # steps/authentication_steps.py:38
    And the interface language is set to "French"                   # steps/multilingual_steps.py:95
    When I view the login form                                      # None
    Then all text should be displayed in "French"                   # None
    And the form should function correctly                          # None

  @authentication @ui @multilingual
  Scenario Outline: Login interface in multiple languages -- @1.4   # features/user_authentication.feature:68
    Given the TurdParty platform is running                         # steps/authentication_steps.py:18
    And the authentication system is available                      # steps/authentication_steps.py:28
    Given I am on the login page                                    # steps/authentication_steps.py:38
    And the interface language is set to "Spanish"                  # steps/multilingual_steps.py:95
    When I view the login form                                      # None
    Then all text should be displayed in "Spanish"                  # None
    And the form should function correctly                          # None

  @authentication @ui @multilingual
  Scenario Outline: Login interface in multiple languages -- @1.5   # features/user_authentication.feature:69
    Given the TurdParty platform is running                         # steps/authentication_steps.py:18
    And the authentication system is available                      # steps/authentication_steps.py:28
    Given I am on the login page                                    # steps/authentication_steps.py:38
    And the interface language is set to "Croatian"                 # steps/multilingual_steps.py:95
    When I view the login form                                      # None
    Then all text should be displayed in "Croatian"                 # None
    And the form should function correctly                          # None

  @authentication @security @api
  Scenario: Brute force protection                        # features/user_authentication.feature:72
    Given the TurdParty platform is running               # steps/authentication_steps.py:18
    And the authentication system is available            # steps/authentication_steps.py:28
    Given I am using the API authentication endpoint      # None
    When I make multiple failed login attempts            # None
      | attempt | username          | password |
      | 1       | <EMAIL> | wrong1   |
      | 2       | <EMAIL> | wrong2   |
      | 3       | <EMAIL> | wrong3   |
      | 4       | <EMAIL> | wrong4   |
      | 5       | <EMAIL> | wrong5   |
    Then I should be rate limited                         # None
    And subsequent requests should be blocked temporarily # None

  @authentication @ui @accessibility
  Scenario: Login form accessibility                      # features/user_authentication.feature:85
    Given the TurdParty platform is running               # steps/authentication_steps.py:18
    And the authentication system is available            # steps/authentication_steps.py:28
    Given I am on the login page                          # steps/authentication_steps.py:38
    When I navigate using only keyboard                   # None
    Then I should be able to access all form elements     # None
    And the form should have proper ARIA labels           # None
    And screen reader announcements should be appropriate # None

  @authentication @api @security
  Scenario: Password security requirements           # features/user_authentication.feature:93
    Given the TurdParty platform is running          # steps/authentication_steps.py:18
    And the authentication system is available       # steps/authentication_steps.py:28
    Given I am creating a new user account           # None
    When I try to set a weak password                # None
      | password | weakness   |
      | 123      | too short  |
      | password | too common |
      | abc123   | too simple |
    Then I should receive password strength feedback # None
    And the account should not be created            # None

  @authentication @ui @responsive
  Scenario: Login on mobile devices             # features/user_authentication.feature:104
    Given the TurdParty platform is running     # steps/authentication_steps.py:18
    And the authentication system is available  # steps/authentication_steps.py:28
    Given I am using a mobile device            # steps/authentication_steps.py:38
    And I am on the login page                  # None
    When I enter my credentials                 # None
    And I submit the form                       # None
    Then the login should work correctly        # None
    And the interface should be mobile-friendly # None

  @authentication @integration @critical
  Scenario: End-to-end authentication flow          # features/user_authentication.feature:113
    Given the TurdParty platform is running         # steps/authentication_steps.py:18
    And the authentication system is available      # steps/authentication_steps.py:28
    Given I am a new user                           # None
    When I complete the full authentication process # None
      | step               | action                         |
      | registration       | create account with valid data |
      | email verification | verify email address           |
      | initial login      | log in with new credentials    |
      | profile setup      | complete user profile          |
      | dashboard access   | access main dashboard          |
    Then I should have full platform access         # None
    And all features should be available to me      # None

  @authentication @security @advanced
  Scenario: Multi-factor authentication           # features/user_authentication.feature:126
    Given the TurdParty platform is running       # steps/authentication_steps.py:18
    And the authentication system is available    # steps/authentication_steps.py:28
    Given I have MFA enabled on my account        # None
    When I log in with username and password      # None
    Then I should be prompted for a second factor # None
    When I provide the correct MFA code           # None
    Then I should be successfully authenticated   # None
    And I should have access to all features      # None

  @authentication @api @performance
  Scenario: Authentication performance under load                           # features/user_authentication.feature:135
    Given the TurdParty platform is running                                 # steps/authentication_steps.py:18
    And the authentication system is available                              # steps/authentication_steps.py:28
    Given the authentication system is under normal load                    # None
    When multiple users authenticate simultaneously                         # None
    Then all authentication requests should complete within acceptable time # None
    And the system should remain responsive                                 # None
    And no authentication requests should fail due to load                  # None

Feature: Virtual Machine Management and Analysis Workflow # features/vm_management_workflow.feature:1
  As a malware analyst
  I want to create, configure, and manage analysis VMs
  So that I can safely analyze suspicious files in isolated environments
  Background:   # features/vm_management_workflow.feature:6

  @vm_management @ui @creation
  Scenario: Create a new analysis VM                     # features/vm_management_workflow.feature:12
    Given I am authenticated on the TurdParty platform   # steps/file_management_steps.py:20
    And the VM management system is available            # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations # steps/vm_management_steps.py:19
    Given I am on the VM management page                 # steps/vm_management_steps.py:45
    When I click "Create New VM"                         # steps/vm_management_steps.py:60
    And I configure the VM settings                      # steps/vm_management_steps.py:96
      | setting      | value                 |
      | name         | Analysis-VM-001       |
      | template     | Windows 10 Enterprise |
      | memory       | 4096 MB               |
      | cpu_cores    | 2                     |
      | disk_size    | 50 GB                 |
      | network_mode | isolated              |
    And I click "Create VM"                              # steps/vm_management_steps.py:169
    Then the VM should be created successfully           # steps/vm_management_steps.py:202
    And I should see the VM in my VMs list               # steps/vm_management_steps.py:247
    And the VM status should be "Provisioning"           # steps/vm_management_steps.py:301

  @vm_management @ui @templates
  Scenario: Select from available VM templates               # features/vm_management_workflow.feature:29
    Given I am authenticated on the TurdParty platform       # steps/file_management_steps.py:20
    And the VM management system is available                # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations     # steps/vm_management_steps.py:19
    Given I am creating a new VM                             # steps/vm_management_steps.py:344
    When I view available templates                          # steps/vm_management_steps.py:378
    Then I should see multiple OS options                    # None
      | template_name    | os_type | description                   |
      | Windows 10 Pro   | Windows | Standard Windows analysis env |
      | Windows 7 SP1    | Windows | Legacy Windows environment    |
      | Ubuntu 20.04 LTS | Linux   | Linux malware analysis        |
      | Kali Linux       | Linux   | Security testing environment  |
      | macOS Monterey   | macOS   | macOS malware analysis        |
    And I should be able to select any template              # None
    And template-specific configurations should be available # None

  @vm_management @lifecycle @operations
  Scenario: VM lifecycle management                      # features/vm_management_workflow.feature:43
    Given I am authenticated on the TurdParty platform   # steps/file_management_steps.py:20
    And the VM management system is available            # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations # steps/vm_management_steps.py:19
    Given I have a created VM                            # None
    When I perform VM operations                         # None
      | operation | expected_result       | next_status |
      | start     | VM boots successfully | Running     |
      | pause     | VM pauses execution   | Paused      |
      | resume    | VM resumes execution  | Running     |
      | stop      | VM shuts down cleanly | Stopped     |
      | restart   | VM reboots            | Running     |
    Then each operation should complete successfully     # None
    And the VM status should update correctly            # None
    And I should receive appropriate notifications       # None

  @vm_management @ui @monitoring
  Scenario: Monitor VM performance and status            # features/vm_management_workflow.feature:57
    Given I am authenticated on the TurdParty platform   # steps/file_management_steps.py:20
    And the VM management system is available            # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations # steps/vm_management_steps.py:19
    Given I have a running VM                            # None
    And I am on the VM details page                      # None
    When I view the VM monitoring dashboard              # None
    Then I should see real-time metrics                  # None
      | metric           | display_format |
      | cpu_usage        | percentage     |
      | memory_usage     | MB / total MB  |
      | disk_usage       | GB / total GB  |
      | network_activity | bytes in/out   |
      | uptime           | hours:minutes  |
    And the metrics should update automatically          # None
    And I should be able to view historical data         # None

  @vm_management @security @isolation
  Scenario: VM network isolation and security            # features/vm_management_workflow.feature:72
    Given I am authenticated on the TurdParty platform   # steps/file_management_steps.py:20
    And the VM management system is available            # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations # steps/vm_management_steps.py:19
    Given I am configuring a VM for malware analysis     # None
    When I set the network configuration                 # None
      | setting            | value         |
      | network_mode       | isolated      |
      | internet_access    | blocked       |
      | internal_network   | analysis_vlan |
      | monitoring_enabled | true          |
    Then the VM should be properly isolated              # None
    And malware cannot escape the VM environment         # None
    And network traffic should be monitored              # None

  @vm_injection @file_analysis @integration
  Scenario: Inject file into VM for analysis             # features/vm_management_workflow.feature:85
    Given I am authenticated on the TurdParty platform   # steps/file_management_steps.py:20
    And the VM management system is available            # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations # steps/vm_management_steps.py:19
    Given I have a running analysis VM                   # None
    And I have uploaded a suspicious file                # None
    When I initiate file injection                       # None
      | file_name   | injection_method | target_location |
      | malware.exe | secure_copy      | C:\\Analysis\\  |
    Then the file should be transferred securely         # None
    And the VM should remain isolated                    # None
    And I should be able to monitor file execution       # None

  @vm_management @snapshots @recovery
  Scenario: VM snapshot management                       # features/vm_management_workflow.feature:96
    Given I am authenticated on the TurdParty platform   # steps/file_management_steps.py:20
    And the VM management system is available            # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations # steps/vm_management_steps.py:19
    Given I have a configured analysis VM                # None
    When I create VM snapshots                           # None
      | snapshot_name     | description               | timing         |
      | clean_baseline    | Clean VM before analysis  | before_inject  |
      | post_injection    | After file injection      | after_inject   |
      | analysis_complete | After analysis completion | after_analysis |
    Then snapshots should be created successfully        # None
    And I should be able to restore to any snapshot      # None
    And VM state should be preserved accurately          # None

  @vm_management @api @automation
  Scenario: Automated VM provisioning via API            # features/vm_management_workflow.feature:108
    Given I am authenticated on the TurdParty platform   # steps/file_management_steps.py:20
    And the VM management system is available            # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations # steps/vm_management_steps.py:19
    Given I have API access to VM management             # None
    When I create VMs programmatically                   # None
      | vm_count | template     | purpose           |
      | 5        | Windows 10   | Batch analysis    |
      | 2        | Ubuntu 20.04 | Linux malware     |
      | 1        | Kali Linux   | Forensic analysis |
    Then all VMs should be provisioned correctly         # None
    And each VM should have unique identifiers           # None
    And VMs should be ready for analysis tasks           # None

  @vm_management @performance @scaling
  Scenario: VM performance optimization and scaling       # features/vm_management_workflow.feature:120
    Given I am authenticated on the TurdParty platform    # steps/file_management_steps.py:20
    And the VM management system is available             # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations  # steps/vm_management_steps.py:19
    Given I need to analyze multiple files simultaneously # None
    When I scale VM resources                             # None
      | scenario           | vm_count | cpu_per_vm | memory_per_vm |
      | light_analysis     | 10       | 1          | 2048 MB       |
      | intensive_analysis | 5        | 4          | 8192 MB       |
      | bulk_processing    | 20       | 2          | 4096 MB       |
    Then the system should allocate resources efficiently # None
    And VM performance should meet requirements           # None
    And resource utilization should be optimized          # None

  @vm_management @ui @multilingual
  Scenario Outline: VM management in different languages -- @1.1   # features/vm_management_workflow.feature:141
    Given I am authenticated on the TurdParty platform             # steps/file_management_steps.py:20
    And the VM management system is available                      # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations           # steps/vm_management_steps.py:19
    Given the interface language is set to "English"               # steps/multilingual_steps.py:95
    And I am on the VM management page                             # steps/vm_management_steps.py:45
    When I perform VM operations                                   # None
    Then all VM-related text should be in "English"                # None
    And VM operations should work correctly                        # None

  @vm_management @ui @multilingual
  Scenario Outline: VM management in different languages -- @1.2   # features/vm_management_workflow.feature:142
    Given I am authenticated on the TurdParty platform             # steps/file_management_steps.py:20
    And the VM management system is available                      # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations           # steps/vm_management_steps.py:19
    Given the interface language is set to "German"                # steps/multilingual_steps.py:95
    And I am on the VM management page                             # steps/vm_management_steps.py:45
    When I perform VM operations                                   # None
    Then all VM-related text should be in "German"                 # None
    And VM operations should work correctly                        # None

  @vm_management @ui @multilingual
  Scenario Outline: VM management in different languages -- @1.3   # features/vm_management_workflow.feature:143
    Given I am authenticated on the TurdParty platform             # steps/file_management_steps.py:20
    And the VM management system is available                      # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations           # steps/vm_management_steps.py:19
    Given the interface language is set to "French"                # steps/multilingual_steps.py:95
    And I am on the VM management page                             # steps/vm_management_steps.py:45
    When I perform VM operations                                   # None
    Then all VM-related text should be in "French"                 # None
    And VM operations should work correctly                        # None

  @vm_management @ui @multilingual
  Scenario Outline: VM management in different languages -- @1.4   # features/vm_management_workflow.feature:144
    Given I am authenticated on the TurdParty platform             # steps/file_management_steps.py:20
    And the VM management system is available                      # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations           # steps/vm_management_steps.py:19
    Given the interface language is set to "Spanish"               # steps/multilingual_steps.py:95
    And I am on the VM management page                             # steps/vm_management_steps.py:45
    When I perform VM operations                                   # None
    Then all VM-related text should be in "Spanish"                # None
    And VM operations should work correctly                        # None

  @vm_management @ui @multilingual
  Scenario Outline: VM management in different languages -- @1.5   # features/vm_management_workflow.feature:145
    Given I am authenticated on the TurdParty platform             # steps/file_management_steps.py:20
    And the VM management system is available                      # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations           # steps/vm_management_steps.py:19
    Given the interface language is set to "Croatian"              # steps/multilingual_steps.py:95
    And I am on the VM management page                             # steps/vm_management_steps.py:45
    When I perform VM operations                                   # None
    Then all VM-related text should be in "Croatian"               # None
    And VM operations should work correctly                        # None

  @vm_management @cleanup @maintenance
  Scenario: Automated VM cleanup and maintenance         # features/vm_management_workflow.feature:148
    Given I am authenticated on the TurdParty platform   # steps/file_management_steps.py:20
    And the VM management system is available            # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations # steps/vm_management_steps.py:19
    Given I have completed analysis tasks                # None
    When the cleanup process runs                        # None
    Then unused VMs should be identified                 # None
    And temporary VMs should be destroyed                # None
    And VM resources should be reclaimed                 # None
    And cleanup logs should be generated                 # None
    And the system should be ready for new tasks         # None

  @vm_management @backup @disaster_recovery
  Scenario: VM backup and disaster recovery              # features/vm_management_workflow.feature:158
    Given I am authenticated on the TurdParty platform   # steps/file_management_steps.py:20
    And the VM management system is available            # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations # steps/vm_management_steps.py:19
    Given I have important analysis VMs                  # None
    When I initiate VM backup procedures                 # None
    Then VM configurations should be backed up           # None
    And VM snapshots should be preserved                 # None
    And backup integrity should be verified              # None
    And recovery procedures should be tested             # None
    And backup restoration should work correctly         # None

  @vm_management @integration @complete_workflow
  Scenario: Complete malware analysis workflow with VMs    # features/vm_management_workflow.feature:168
    Given I am authenticated on the TurdParty platform     # steps/file_management_steps.py:20
    And the VM management system is available              # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations   # steps/vm_management_steps.py:19
    Given I need to perform comprehensive malware analysis # None
    When I execute the complete workflow                   # None
      | step                 | action                           | expected_outcome           |
      | vm_preparation       | create and configure analysis VM | VM ready for analysis      |
      | baseline_snapshot    | take clean VM snapshot           | baseline state preserved   |
      | file_injection       | inject malware into VM           | file transferred securely  |
      | execution_monitoring | monitor malware execution        | behavior captured          |
      | network_analysis     | analyze network communications   | traffic patterns recorded  |
      | system_changes       | document system modifications    | changes catalogued         |
      | artifact_collection  | collect analysis artifacts       | evidence preserved         |
      | vm_restoration       | restore VM to baseline           | VM ready for next analysis |
    Then each step should complete successfully            # None
    And comprehensive analysis data should be available    # None
    And the VM should be ready for reuse                   # None

  @vm_management @security @advanced
  Scenario: Advanced VM security and containment                      # features/vm_management_workflow.feature:185
    Given I am authenticated on the TurdParty platform                # steps/file_management_steps.py:20
    And the VM management system is available                         # steps/vm_management_steps.py:35
    And I have appropriate permissions for VM operations              # steps/vm_management_steps.py:19
    Given I am analyzing highly dangerous malware                     # None
    When I configure maximum security settings                        # None
      | security_feature     | setting            |
      | network_isolation    | complete_isolation |
      | file_system_monitor  | enabled            |
      | process_monitoring   | enabled            |
      | memory_protection    | enabled            |
      | anti_escape_measures | enabled            |
    Then the VM should be maximally secured                           # None
    And malware cannot escape containment                             # None
    And all activities should be monitored                            # None
    And security alerts should be generated for suspicious activities # None

