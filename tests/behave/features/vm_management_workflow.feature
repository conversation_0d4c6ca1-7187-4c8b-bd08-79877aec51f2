Feature: Virtual Machine Management and Analysis Workflow
  As a malware analyst
  I want to create, configure, and manage analysis VMs
  So that I can safely analyze suspicious files in isolated environments

  Background:
    Given I am authenticated on the TurdParty platform
    And the VM management system is available
    And I have appropriate permissions for VM operations

  @vm_management @ui @creation
  Scenario: Create a new analysis VM
    Given I am on the VM management page
    When I click "Create New VM"
    And I configure the VM settings
      | setting          | value                    |
      | name             | Analysis-VM-001          |
      | template         | Windows 10 Enterprise    |
      | memory           | 4096 MB                  |
      | cpu_cores        | 2                        |
      | disk_size        | 50 GB                    |
      | network_mode     | isolated                 |
    And I click "Create VM"
    Then the VM should be created successfully
    And I should see the VM in my VMs list
    And the VM status should be "Provisioning"

  @vm_management @ui @templates
  Scenario: Select from available VM templates
    Given I am creating a new VM
    When I view available templates
    Then I should see multiple OS options
      | template_name        | os_type    | description                    |
      | Windows 10 Pro       | Windows    | Standard Windows analysis env  |
      | Windows 7 SP1        | Windows    | Legacy Windows environment     |
      | Ubuntu 20.04 LTS     | Linux      | Linux malware analysis         |
      | Kali Linux           | Linux      | Security testing environment   |
      | macOS Monterey       | macOS      | macOS malware analysis         |
    And I should be able to select any template
    And template-specific configurations should be available

  @vm_management @lifecycle @operations
  Scenario: VM lifecycle management
    Given I have a created VM
    When I perform VM operations
      | operation | expected_result        | next_status |
      | start     | VM boots successfully  | Running     |
      | pause     | VM pauses execution    | Paused      |
      | resume    | VM resumes execution   | Running     |
      | stop      | VM shuts down cleanly  | Stopped     |
      | restart   | VM reboots             | Running     |
    Then each operation should complete successfully
    And the VM status should update correctly
    And I should receive appropriate notifications

  @vm_management @ui @monitoring
  Scenario: Monitor VM performance and status
    Given I have a running VM
    And I am on the VM details page
    When I view the VM monitoring dashboard
    Then I should see real-time metrics
      | metric           | display_format    |
      | cpu_usage        | percentage        |
      | memory_usage     | MB / total MB     |
      | disk_usage       | GB / total GB     |
      | network_activity | bytes in/out      |
      | uptime           | hours:minutes     |
    And the metrics should update automatically
    And I should be able to view historical data

  @vm_management @security @isolation
  Scenario: VM network isolation and security
    Given I am configuring a VM for malware analysis
    When I set the network configuration
      | setting              | value                |
      | network_mode         | isolated             |
      | internet_access      | blocked              |
      | internal_network     | analysis_vlan        |
      | monitoring_enabled   | true                 |
    Then the VM should be properly isolated
    And malware cannot escape the VM environment
    And network traffic should be monitored

  @vm_injection @file_analysis @integration
  Scenario: Inject file into VM for analysis
    Given I have a running analysis VM
    And I have uploaded a suspicious file
    When I initiate file injection
      | file_name        | injection_method | target_location    |
      | malware.exe      | secure_copy      | C:\Analysis\       |
    Then the file should be transferred securely
    And the VM should remain isolated
    And I should be able to monitor file execution

  @vm_management @snapshots @recovery
  Scenario: VM snapshot management
    Given I have a configured analysis VM
    When I create VM snapshots
      | snapshot_name    | description                    | timing        |
      | clean_baseline   | Clean VM before analysis       | before_inject |
      | post_injection   | After file injection           | after_inject  |
      | analysis_complete| After analysis completion      | after_analysis|
    Then snapshots should be created successfully
    And I should be able to restore to any snapshot
    And VM state should be preserved accurately

  @vm_management @api @automation
  Scenario: Automated VM provisioning via API
    Given I have API access to VM management
    When I create VMs programmatically
      | vm_count | template      | purpose           |
      | 5        | Windows 10    | Batch analysis    |
      | 2        | Ubuntu 20.04  | Linux malware     |
      | 1        | Kali Linux    | Forensic analysis |
    Then all VMs should be provisioned correctly
    And each VM should have unique identifiers
    And VMs should be ready for analysis tasks

  @vm_management @performance @scaling
  Scenario: VM performance optimization and scaling
    Given I need to analyze multiple files simultaneously
    When I scale VM resources
      | scenario          | vm_count | cpu_per_vm | memory_per_vm |
      | light_analysis    | 10       | 1          | 2048 MB       |
      | intensive_analysis| 5        | 4          | 8192 MB       |
      | bulk_processing   | 20       | 2          | 4096 MB       |
    Then the system should allocate resources efficiently
    And VM performance should meet requirements
    And resource utilization should be optimized

  @vm_management @ui @multilingual
  Scenario Outline: VM management in different languages
    Given the interface language is set to "<language>"
    And I am on the VM management page
    When I perform VM operations
    Then all VM-related text should be in "<language>"
    And VM operations should work correctly
    
    Examples:
      | language |
      | English  |
      | German   |
      | French   |
      | Spanish  |
      | Croatian |

  @vm_management @cleanup @maintenance
  Scenario: Automated VM cleanup and maintenance
    Given I have completed analysis tasks
    When the cleanup process runs
    Then unused VMs should be identified
    And temporary VMs should be destroyed
    And VM resources should be reclaimed
    And cleanup logs should be generated
    And the system should be ready for new tasks

  @vm_management @backup @disaster_recovery
  Scenario: VM backup and disaster recovery
    Given I have important analysis VMs
    When I initiate VM backup procedures
    Then VM configurations should be backed up
    And VM snapshots should be preserved
    And backup integrity should be verified
    And recovery procedures should be tested
    And backup restoration should work correctly

  @vm_management @integration @complete_workflow
  Scenario: Complete malware analysis workflow with VMs
    Given I need to perform comprehensive malware analysis
    When I execute the complete workflow
      | step                    | action                           | expected_outcome           |
      | vm_preparation          | create and configure analysis VM | VM ready for analysis      |
      | baseline_snapshot       | take clean VM snapshot           | baseline state preserved   |
      | file_injection          | inject malware into VM           | file transferred securely  |
      | execution_monitoring    | monitor malware execution        | behavior captured          |
      | network_analysis        | analyze network communications   | traffic patterns recorded  |
      | system_changes          | document system modifications    | changes catalogued         |
      | artifact_collection     | collect analysis artifacts       | evidence preserved         |
      | vm_restoration          | restore VM to baseline           | VM ready for next analysis |
    Then each step should complete successfully
    And comprehensive analysis data should be available
    And the VM should be ready for reuse

  @vm_management @security @advanced
  Scenario: Advanced VM security and containment
    Given I am analyzing highly dangerous malware
    When I configure maximum security settings
      | security_feature     | setting              |
      | network_isolation    | complete_isolation   |
      | file_system_monitor  | enabled              |
      | process_monitoring   | enabled              |
      | memory_protection    | enabled              |
      | anti_escape_measures | enabled              |
    Then the VM should be maximally secured
    And malware cannot escape containment
    And all activities should be monitored
    And security alerts should be generated for suspicious activities
