Feature: Multilingual User Experience
  As an international cybersecurity professional
  I want to use the TurdParty platform in my native language
  So that I can efficiently perform malware analysis without language barriers

  Background:
    Given the TurdParty platform supports multiple languages
    And I am authenticated on the platform

  @multilingual @ui @european_languages
  Scenario Outline: Complete workflow in European languages
    Given the interface language is set to "<language>"
    When I perform a complete malware analysis workflow
      | step                | action                           |
      | login               | authenticate with credentials    |
      | file_upload         | upload suspicious file           |
      | vm_creation         | create analysis virtual machine  |
      | analysis_execution  | run malware analysis             |
      | report_generation   | generate analysis report         |
    Then all interface elements should be in "<language>"
    And all functionality should work correctly
    And error messages should be in "<language>"
    
    Examples: Germanic Languages
      | language      |
      | German        |
      | English       |
      | Dutch         |
      | Swedish       |
      | Danish        |
    
    Examples: Romance Languages
      | language      |
      | French        |
      | Italian       |
      | Spanish       |
      | Portuguese    |
    
    Examples: Slavic Languages
      | language      |
      | Croatian      |
      | Serbian       |
      | Polish        |
      | Czech         |
      | Bulgarian     |

  @multilingual @ui @language_switching
  Scenario: Dynamic language switching during session
    Given I am logged into the platform
    And the current language is "English"
    When I switch the language to "German"
    Then the interface should immediately update to German
    And my session should remain active
    And all previously entered data should be preserved
    When I switch the language to "French"
    Then the interface should update to French
    And functionality should continue to work correctly

  @multilingual @forms @validation
  Scenario Outline: Form validation messages in multiple languages
    Given the interface language is set to "<language>"
    And I am on the file upload form
    When I submit the form with invalid data
      | field       | invalid_value |
      | filename    | (empty)       |
      | description | (empty)       |
    Then I should see validation errors in "<language>"
    And the error messages should be culturally appropriate
    
    Examples:
      | language  |
      | English   |
      | German    |
      | French    |
      | Spanish   |
      | Croatian  |

  @multilingual @content @localization
  Scenario Outline: Content localization and cultural adaptation
    Given the interface language is set to "<language>"
    When I view different sections of the platform
      | section           | content_type        |
      | dashboard         | welcome_messages    |
      | help_documentation| user_guides         |
      | error_pages       | error_explanations  |
      | tooltips          | help_text           |
    Then all content should be properly localized for "<language>"
    And cultural conventions should be respected
      | convention        | expectation                    |
      | date_format       | appropriate for locale         |
      | number_format     | correct decimal separators     |
      | currency_display  | local currency conventions     |
      | time_format       | 12/24 hour based on locale     |
    
    Examples:
      | language  |
      | German    |
      | French    |
      | Spanish   |
      | Croatian  |

  @multilingual @accessibility @screen_readers
  Scenario Outline: Screen reader compatibility in multiple languages
    Given the interface language is set to "<language>"
    And I am using a screen reader
    When I navigate through the platform
    Then screen reader announcements should be in "<language>"
    And ARIA labels should be properly translated
    And navigation should be logical in the language context
    
    Examples:
      | language  |
      | English   |
      | German    |
      | French    |
      | Spanish   |

  @multilingual @api @content_negotiation
  Scenario: API content negotiation based on language preferences
    Given I am making API requests
    When I set the Accept-Language header to different values
      | accept_language_header    | expected_response_language |
      | en-US,en;q=0.9           | English                    |
      | de-DE,de;q=0.9,en;q=0.8  | German                     |
      | fr-FR,fr;q=0.9,en;q=0.7  | French                     |
      | es-ES,es;q=0.9,en;q=0.7  | Spanish                    |
      | hr-HR,hr;q=0.9,en;q=0.7  | Croatian                   |
    Then the API responses should respect the language preference
    And error messages should be in the requested language
    And content should be appropriately localized

  @multilingual @performance @language_loading
  Scenario: Performance impact of multilingual features
    Given the platform supports 37 languages
    When I switch between different languages rapidly
      | sequence | language_1 | language_2 | language_3 |
      | 1        | English    | German     | French     |
      | 2        | Spanish    | Croatian   | Italian    |
      | 3        | Polish     | Swedish    | Portuguese |
    Then language switching should be fast and responsive
    And there should be no memory leaks
    And the user experience should remain smooth

  @multilingual @unicode @character_support
  Scenario: Unicode and special character support
    Given I am using the platform in different languages
    When I enter text with special characters
      | language  | special_characters                    | context           |
      | German    | ä, ö, ü, ß, Ä, Ö, Ü                 | file descriptions |
      | French    | é, è, ê, ë, à, ç, ù, û, ô, î        | metadata fields   |
      | Spanish   | ñ, á, é, í, ó, ú, ü                 | comments          |
      | Croatian  | č, ć, đ, š, ž, Č, Ć, Đ, Š, Ž       | analysis notes    |
      | Polish    | ą, ć, ę, ł, ń, ó, ś, ź, ż          | report titles     |
    Then the characters should be displayed correctly
    And they should be stored and retrieved properly
    And search functionality should work with special characters

  @multilingual @rtl @bidirectional
  Scenario: Right-to-left language support preparation
    Given the platform is designed for international use
    When I test the interface layout
    Then the design should be RTL-ready
    And text alignment should be flexible
    And UI components should support bidirectional text
    # Note: This is preparation for future Arabic/Hebrew support

  @multilingual @translation_quality @consistency
  Scenario Outline: Translation quality and consistency
    Given the interface language is set to "<language>"
    When I review translations across the platform
    Then technical terms should be consistently translated
    And the tone should be professional and appropriate
    And there should be no untranslated strings
    And context-specific translations should be accurate
    
    Examples:
      | language  |
      | German    |
      | French    |
      | Spanish   |
      | Croatian  |

  @multilingual @help_documentation @support
  Scenario Outline: Multilingual help and documentation
    Given the interface language is set to "<language>"
    When I access help documentation
      | help_section        | content_type           |
      | user_guide          | step_by_step_tutorials |
      | api_documentation   | technical_reference    |
      | troubleshooting     | problem_solutions      |
      | faq                 | common_questions       |
    Then all documentation should be available in "<language>"
    And examples should be culturally relevant
    And contact information should be localized
    
    Examples:
      | language  |
      | German    |
      | French    |
      | Spanish   |
      | Croatian  |

  @multilingual @error_handling @user_feedback
  Scenario Outline: Multilingual error handling and user feedback
    Given the interface language is set to "<language>"
    When errors occur during platform usage
      | error_type          | trigger_condition           |
      | validation_error    | invalid form submission      |
      | network_error       | connection timeout           |
      | permission_error    | unauthorized access attempt  |
      | system_error        | internal server error        |
    Then error messages should be clear and helpful in "<language>"
    And suggested solutions should be provided
    And error codes should be language-independent
    
    Examples:
      | language  |
      | German    |
      | French    |
      | Spanish   |
      | Croatian  |

  @multilingual @integration @complete_workflow
  Scenario: Complete multilingual cybersecurity workflow
    Given I am a German-speaking cybersecurity analyst
    And the interface language is set to "German"
    When I perform a complete malware analysis
      | workflow_step           | german_interface_element     | expected_behavior              |
      | login                   | "Anmelden" button            | successful authentication      |
      | file_upload             | "Datei hochladen" section    | file upload with German labels |
      | vm_creation             | "VM erstellen" dialog        | VM creation in German          |
      | analysis_monitoring     | "Analyse überwachen" panel   | real-time updates in German    |
      | report_download         | "Bericht herunterladen" link | German-language report         |
    Then the entire workflow should be seamless in German
    And all technical terms should be properly translated
    And the user experience should feel native to German speakers

  @multilingual @compliance @eu_requirements
  Scenario: EU language compliance verification
    Given the platform claims 96% EU language compliance
    When I test EU official languages
      | eu_language | iso_code | compliance_level |
      | German      | de       | full             |
      | French      | fr       | full             |
      | Italian     | it       | full             |
      | Spanish     | es       | full             |
      | Polish      | pl       | full             |
      | Dutch       | nl       | full             |
      | Croatian    | hr       | full             |
      | Czech       | cs       | full             |
      | Hungarian   | hu       | full             |
      | Portuguese  | pt       | full             |
    Then each language should have complete interface translation
    And core functionality should work in all languages
    And the platform should meet EU accessibility requirements
