Feature: File Management and Analysis Workflow
  As a cybersecurity analyst
  I want to upload, manage, and analyze files through the TurdParty platform
  So that I can efficiently process malware samples and suspicious files

  Background:
    Given I am authenticated on the TurdParty platform
    And the file management system is available

  @file_upload @ui @critical
  Scenario: Upload a single file through web interface
    Given I am on the file upload page
    When I select a file to upload
      | filename        | type      | size    |
      | malware.exe     | binary    | 2.5 MB  |
    And I provide file metadata
      | description | Suspicious executable from email attachment |
      | source      | Email investigation                         |
      | priority    | High                                        |
    And I click the upload button
    Then the file should be uploaded successfully
    And I should see a success confirmation
    And the file should appear in my files list

  @file_upload @ui @drag_drop
  Scenario: Upload file using drag and drop
    Given I am on the file upload page
    When I drag and drop a file onto the upload area
      | filename     | type    |
      | sample.pdf   | document|
    And I fill in the required metadata
    Then the file should be uploaded automatically
    And I should see upload progress
    And the file should be processed correctly

  @file_upload @api @bulk
  Scenario: Bulk file upload via API
    Given I have API access to the file upload endpoint
    When I upload multiple files simultaneously
      | filename      | type      | description           |
      | sample1.exe   | binary    | Malware sample 1      |
      | sample2.dll   | library   | Suspicious DLL        |
      | sample3.doc   | document  | Macro-enabled document|
    Then all files should be uploaded successfully
    And each file should have a unique identifier
    And the files should be queued for processing

  @file_management @ui @search
  Scenario: Search and filter uploaded files
    Given I have uploaded multiple files
    And I am on the files management page
    When I search for files using criteria
      | criteria    | value           |
      | filename    | malware         |
      | file_type   | executable      |
      | upload_date | last 7 days     |
    Then I should see only matching files
    And the results should be properly sorted
    And I should be able to refine my search

  @file_management @ui @details
  Scenario: View detailed file information
    Given I have uploaded a file
    And I am on the files list page
    When I click on a file to view details
    Then I should see comprehensive file information
      | field           | expected                    |
      | filename        | original filename           |
      | file_size       | size in human-readable format|
      | upload_date     | timestamp of upload         |
      | file_hash       | SHA-256 hash                |
      | mime_type       | detected MIME type          |
      | analysis_status | current processing status   |

  @file_processing @async @analysis
  Scenario: Automated file analysis workflow
    Given I have uploaded a suspicious file
    When the file enters the analysis queue
    Then the system should automatically perform
      | analysis_type    | description                    |
      | hash_check       | Check against known malware DB |
      | static_analysis  | Examine file structure         |
      | metadata_extract | Extract file metadata          |
      | signature_scan   | Scan for known signatures      |
    And I should receive progress updates
    And the results should be stored for review

  @file_management @ui @download
  Scenario: Download processed files and reports
    Given I have a file that has been analyzed
    And I am viewing the file details
    When I request to download
      | item_type        | format |
      | original_file    | binary |
      | analysis_report  | PDF    |
      | metadata_export  | JSON   |
    Then the download should start immediately
    And the file should be complete and uncorrupted

  @file_management @security @permissions
  Scenario: File access permissions and sharing
    Given I have uploaded a file
    And I want to share it with team members
    When I set file permissions
      | user_type    | permissions           |
      | team_lead    | read, write, delete   |
      | analyst      | read, comment         |
      | viewer       | read only             |
    Then the permissions should be enforced
    And users should only see files they have access to

  @file_upload @validation @security
  Scenario: File type validation and security checks
    Given I am uploading files to the platform
    When I attempt to upload various file types
      | filename        | type        | expected_result |
      | document.pdf    | document    | accepted        |
      | script.js       | script      | quarantined     |
      | image.jpg       | image       | accepted        |
      | malware.exe     | executable  | quarantined     |
      | archive.zip     | archive     | scanned         |
    Then the system should handle each appropriately
    And security policies should be enforced

  @file_management @ui @multilingual
  Scenario Outline: File management in different languages
    Given the interface language is set to "<language>"
    And I am on the file management page
    When I perform file operations
    Then all interface elements should be in "<language>"
    And file operations should work correctly
    
    Examples:
      | language |
      | English  |
      | German   |
      | French   |
      | Spanish  |
      | Croatian |

  @file_processing @performance @large_files
  Scenario: Large file upload and processing
    Given I need to upload a large file
    When I upload a file larger than 100MB
      | filename      | size   | type     |
      | large_vm.ova  | 500MB  | VM image |
    Then the upload should handle the large size
    And I should see accurate progress indicators
    And the file should be processed efficiently
    And memory usage should remain reasonable

  @file_management @integration @vm_workflow
  Scenario: Complete file-to-VM analysis workflow
    Given I have uploaded a suspicious executable
    When I initiate the complete analysis workflow
      | step                | action                           |
      | file_upload         | upload suspicious file           |
      | initial_scan        | perform quick security scan      |
      | vm_preparation      | prepare isolated analysis VM     |
      | file_injection      | inject file into analysis VM     |
      | behavior_analysis   | monitor file execution           |
      | report_generation   | generate comprehensive report    |
    Then each step should complete successfully
    And I should have a complete analysis report
    And the VM should be properly cleaned up

  @file_management @api @automation
  Scenario: Automated file processing pipeline
    Given I have an automated analysis pipeline
    When files are uploaded via API
    Then the system should automatically
      | action              | timing        |
      | validate_file       | immediate     |
      | queue_for_analysis  | within 1 min  |
      | start_processing    | within 5 min  |
      | generate_report     | within 30 min |
      | notify_completion   | immediate     |
    And the pipeline should handle errors gracefully
    And I should receive status updates throughout

  @file_management @backup @recovery
  Scenario: File backup and recovery procedures
    Given I have important analysis files
    When I request file backup
    Then the system should create secure backups
    And I should be able to restore files if needed
    And backup integrity should be verified
    And recovery procedures should be tested
