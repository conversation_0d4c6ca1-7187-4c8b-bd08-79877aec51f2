Feature: User Authentication and Authorization
  As a cybersecurity professional
  I want to securely authenticate and access the TurdParty platform
  So that I can perform malware analysis tasks with proper authorization

  Background:
    Given the TurdParty platform is running
    And the authentication system is available

  @authentication @ui @critical
  Scenario: Successful user login through web interface
    Given I am on the login page
    When I enter valid credentials
      | username | <EMAIL> |
      | password | password123       |
    And I click the login button
    Then I should be redirected to the dashboard
    And I should see a welcome message
    And my session should be authenticated

  @authentication @ui @critical
  Scenario: Failed login with invalid credentials
    Given I am on the login page
    When I enter invalid credentials
      | username | <EMAIL> |
      | password | wrongpassword       |
    And I click the login button
    Then I should see an error message
    And I should remain on the login page
    And my session should not be authenticated

  @authentication @api @critical
  Scenario: API token authentication
    Given I have valid API credentials
    When I request an authentication token
    Then I should receive a valid JWT token
    And the token should have proper expiration
    And I should be able to access protected endpoints

  @authentication @api @security
  Scenario: Token expiration handling
    Given I have an expired authentication token
    When I try to access a protected endpoint
    Then I should receive an unauthorized response
    And I should be prompted to re-authenticate

  @authentication @ui @security
  Scenario: Session timeout handling
    Given I am logged into the web interface
    And my session has been idle for the timeout period
    When I try to perform an action
    Then I should be redirected to the login page
    And I should see a session timeout message

  @authentication @ui @multilingual
  Scenario Outline: Login interface in multiple languages
    Given I am on the login page
    And the interface language is set to "<language>"
    When I view the login form
    Then all text should be displayed in "<language>"
    And the form should function correctly
    
    Examples:
      | language |
      | English  |
      | German   |
      | French   |
      | Spanish  |
      | Croatian |

  @authentication @security @api
  Scenario: Brute force protection
    Given I am using the API authentication endpoint
    When I make multiple failed login attempts
      | attempt | username            | password      |
      | 1       | <EMAIL>   | wrong1        |
      | 2       | <EMAIL>   | wrong2        |
      | 3       | <EMAIL>   | wrong3        |
      | 4       | <EMAIL>   | wrong4        |
      | 5       | <EMAIL>   | wrong5        |
    Then I should be rate limited
    And subsequent requests should be blocked temporarily

  @authentication @ui @accessibility
  Scenario: Login form accessibility
    Given I am on the login page
    When I navigate using only keyboard
    Then I should be able to access all form elements
    And the form should have proper ARIA labels
    And screen reader announcements should be appropriate

  @authentication @api @security
  Scenario: Password security requirements
    Given I am creating a new user account
    When I try to set a weak password
      | password | weakness     |
      | 123      | too short    |
      | password | too common   |
      | abc123   | too simple   |
    Then I should receive password strength feedback
    And the account should not be created

  @authentication @ui @responsive
  Scenario: Login on mobile devices
    Given I am using a mobile device
    And I am on the login page
    When I enter my credentials
    And I submit the form
    Then the login should work correctly
    And the interface should be mobile-friendly

  @authentication @integration @critical
  Scenario: End-to-end authentication flow
    Given I am a new user
    When I complete the full authentication process
      | step                    | action                           |
      | registration            | create account with valid data   |
      | email verification      | verify email address             |
      | initial login           | log in with new credentials      |
      | profile setup           | complete user profile            |
      | dashboard access        | access main dashboard            |
    Then I should have full platform access
    And all features should be available to me

  @authentication @security @advanced
  Scenario: Multi-factor authentication
    Given I have MFA enabled on my account
    When I log in with username and password
    Then I should be prompted for a second factor
    When I provide the correct MFA code
    Then I should be successfully authenticated
    And I should have access to all features

  @authentication @api @performance
  Scenario: Authentication performance under load
    Given the authentication system is under normal load
    When multiple users authenticate simultaneously
    Then all authentication requests should complete within acceptable time
    And the system should remain responsive
    And no authentication requests should fail due to load
