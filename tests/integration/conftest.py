"""
Shared fixtures for integration tests.
"""
import os
import pytest
import time
import requests
from unittest.mock import patch

from db.session import SessionLocal
from db.models.task_status import TaskStatus


@pytest.fixture(scope="session")
def db_session():
    """Create a database session for testing."""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture(scope="session")
def api_client():
    """Create an API client for testing."""
    base_url = os.environ.get("API_URL", "http://api:8000")
    
    class APIClient:
        def __init__(self, base_url):
            self.base_url = base_url
            
        def get(self, path, **kwargs):
            return requests.get(f"{self.base_url}{path}", **kwargs)
            
        def post(self, path, **kwargs):
            return requests.post(f"{self.base_url}{path}", **kwargs)
            
        def put(self, path, **kwargs):
            return requests.put(f"{self.base_url}{path}", **kwargs)
            
        def delete(self, path, **kwargs):
            return requests.delete(f"{self.base_url}{path}", **kwargs)
    
    return APIClient(base_url)


@pytest.fixture(scope="session")
def wait_for_task_completion():
    """Wait for a Celery task to complete."""
    def _wait_for_task(task_id, timeout=10, interval=0.5):
        """
        Wait for a task to complete.
        
        Args:
            task_id: The ID of the task to wait for
            timeout: Maximum time to wait in seconds
            interval: Time between checks in seconds
            
        Returns:
            The task status
        """
        api_url = os.environ.get("API_URL", "http://api:8000")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            response = requests.get(f"{api_url}/api/v1/tasks/{task_id}")
            
            if response.status_code == 200:
                task_data = response.json()
                if task_data["status"] in ["SUCCESS", "FAILURE"]:
                    return task_data
            
            time.sleep(interval)
        
        raise TimeoutError(f"Task {task_id} did not complete within {timeout} seconds")
    
    return _wait_for_task


@pytest.fixture(scope="session")
def mock_minio():
    """Mock MinIO client for testing."""
    with patch("tasks.file_tasks.get_minio_client") as mock_get_client:
        mock_client = mock_get_client.return_value
        mock_client.put_object.return_value = None
        mock_client.get_object.return_value.read.return_value = b"Test file content"
        yield mock_client


@pytest.fixture(scope="session")
def mock_vagrant_api():
    """Mock Vagrant API for testing."""
    with patch("tasks.vm_tasks.requests.post") as mock_post, \
         patch("tasks.vm_tasks.requests.get") as mock_get:
        # Mock VM creation
        mock_post.return_value.status_code = 200
        mock_post.return_value.json.return_value = {"vm_id": "test-vm-123", "status": "created"}
        
        # Mock VM status check
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = {"status": "running"}
        
        yield mock_post, mock_get
