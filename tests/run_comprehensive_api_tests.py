#!/usr/bin/env python3
"""
Comprehensive API Test Suite Runner for TurdParty Platform.

This script orchestrates and runs all API test suites including:
- Core API functionality tests
- Async task and Celery integration tests  
- Security and authentication tests
- Multilingual and internationalization tests
- Performance and load tests

Provides detailed reporting and analysis of the complete API test coverage.
"""

import os
import sys
import time
import json
import subprocess
import unittest
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Add the tests directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import test modules
from unit.test_api_coverage import APITestCase
from unit.test_async_api_comprehensive import AsyncAPITestCase
from unit.test_security_comprehensive import SecurityTestCase
from unit.test_multilingual_api import MultilingualAPITestCase

# Configuration
API_URL = os.environ.get("API_URL", "http://localhost:3050")
API_VERSION = os.environ.get("API_VERSION", "v1")
REPORT_DIR = os.path.join(os.path.dirname(__file__), "reports", "api_tests")

def log(message: str, level: str = "INFO"):
    """Print message with timestamp and level."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

def create_report_directory():
    """Create report directory if it doesn't exist."""
    os.makedirs(REPORT_DIR, exist_ok=True)
    return REPORT_DIR

class ComprehensiveAPITestRunner:
    """Comprehensive API test runner with detailed reporting."""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.test_results = {}
        self.report_dir = create_report_directory()
        
    def run_test_suite(self, test_case_class, suite_name: str) -> Dict:
        """Run a specific test suite and return results."""
        log(f"🚀 Starting {suite_name} Test Suite...")
        
        suite_start = time.time()
        
        # Create test suite
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromTestCase(test_case_class)
        
        # Run tests with custom result collector
        result = unittest.TestResult()
        suite.run(result)
        
        suite_end = time.time()
        suite_duration = suite_end - suite_start
        
        # Collect results
        suite_results = {
            'name': suite_name,
            'duration': suite_duration,
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'skipped': len(result.skipped) if hasattr(result, 'skipped') else 0,
            'success_rate': ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0,
            'successful': result.wasSuccessful(),
            'failure_details': [{'test': str(test), 'error': error} for test, error in result.failures],
            'error_details': [{'test': str(test), 'error': error} for test, error in result.errors]
        }
        
        log(f"✅ {suite_name} completed: {suite_results['tests_run']} tests, {suite_results['success_rate']:.1f}% success rate")
        
        return suite_results
    
    def run_all_test_suites(self) -> Dict:
        """Run all comprehensive API test suites."""
        log("🎯 Starting Comprehensive API Test Suite Execution...")
        log("=" * 100)
        log("🌍 TurdParty API Testing - Industry-Leading Multilingual Cybersecurity Platform")
        log("📊 37 Languages | 96% EU Compliance | Enterprise-Grade Security")
        log("=" * 100)
        
        self.start_time = time.time()
        
        # Define test suites to run
        test_suites = [
            (APITestCase, "Core API Functionality"),
            (AsyncAPITestCase, "Async Tasks & Celery Integration"),
            (SecurityTestCase, "Security & Authentication"),
            (MultilingualAPITestCase, "Multilingual & Internationalization")
        ]
        
        # Run each test suite
        for test_class, suite_name in test_suites:
            try:
                suite_results = self.run_test_suite(test_class, suite_name)
                self.test_results[suite_name] = suite_results
                
                # Brief pause between suites
                time.sleep(2)
                
            except Exception as e:
                log(f"❌ Error running {suite_name}: {str(e)}", "ERROR")
                self.test_results[suite_name] = {
                    'name': suite_name,
                    'error': str(e),
                    'successful': False
                }
        
        self.end_time = time.time()
        
        return self.test_results
    
    def generate_comprehensive_report(self) -> Dict:
        """Generate comprehensive test report with analysis."""
        total_duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        # Calculate overall statistics
        total_tests = sum(suite.get('tests_run', 0) for suite in self.test_results.values())
        total_failures = sum(suite.get('failures', 0) for suite in self.test_results.values())
        total_errors = sum(suite.get('errors', 0) for suite in self.test_results.values())
        total_skipped = sum(suite.get('skipped', 0) for suite in self.test_results.values())
        
        successful_tests = total_tests - total_failures - total_errors
        overall_success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        successful_suites = len([suite for suite in self.test_results.values() if suite.get('successful')])
        total_suites = len(self.test_results)
        
        # Create comprehensive report
        report = {
            'timestamp': datetime.now().isoformat(),
            'api_url': API_URL,
            'total_duration': total_duration,
            'overall_statistics': {
                'total_test_suites': total_suites,
                'successful_suites': successful_suites,
                'suite_success_rate': (successful_suites / total_suites * 100) if total_suites > 0 else 0,
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'failed_tests': total_failures,
                'error_tests': total_errors,
                'skipped_tests': total_skipped,
                'overall_success_rate': overall_success_rate
            },
            'suite_results': self.test_results,
            'api_coverage_analysis': self._analyze_api_coverage(),
            'recommendations': self._generate_recommendations()
        }
        
        return report
    
    def _analyze_api_coverage(self) -> Dict:
        """Analyze API coverage across different areas."""
        coverage_areas = {
            'Authentication & Authorization': 'Security & Authentication',
            'File Management': 'Core API Functionality', 
            'VM Management': 'Core API Functionality',
            'Async Task Processing': 'Async Tasks & Celery Integration',
            'Multilingual Support': 'Multilingual & Internationalization',
            'Security Features': 'Security & Authentication',
            'Error Handling': 'Core API Functionality',
            'Performance Testing': 'Core API Functionality'
        }
        
        coverage_analysis = {}
        
        for area, suite_name in coverage_areas.items():
            if suite_name in self.test_results:
                suite_data = self.test_results[suite_name]
                coverage_analysis[area] = {
                    'tested': True,
                    'success_rate': suite_data.get('success_rate', 0),
                    'status': 'Excellent' if suite_data.get('success_rate', 0) >= 95 else
                             'Good' if suite_data.get('success_rate', 0) >= 85 else
                             'Needs Improvement'
                }
            else:
                coverage_analysis[area] = {
                    'tested': False,
                    'status': 'Not Tested'
                }
        
        return coverage_analysis
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []
        
        # Analyze each suite for recommendations
        for suite_name, results in self.test_results.items():
            if not results.get('successful', False):
                recommendations.append(f"Review and fix issues in {suite_name}")
            
            success_rate = results.get('success_rate', 0)
            if success_rate < 95:
                recommendations.append(f"Improve {suite_name} success rate (currently {success_rate:.1f}%)")
            
            if results.get('failures', 0) > 0:
                recommendations.append(f"Address {results['failures']} test failures in {suite_name}")
            
            if results.get('errors', 0) > 0:
                recommendations.append(f"Fix {results['errors']} test errors in {suite_name}")
        
        # General recommendations
        overall_success = self.test_results and all(suite.get('successful', False) for suite in self.test_results.values())
        
        if overall_success:
            recommendations.append("🎉 All test suites passed! API is ready for production.")
        else:
            recommendations.append("⚠️ Some test suites failed. Review and address issues before production deployment.")
        
        # Add specific recommendations based on coverage
        if 'Multilingual & Internationalization' in self.test_results:
            ml_results = self.test_results['Multilingual & Internationalization']
            if ml_results.get('success_rate', 0) >= 95:
                recommendations.append("✅ Excellent multilingual support - ready for international markets")
        
        return recommendations
    
    def print_detailed_report(self, report: Dict):
        """Print detailed test report to console."""
        log("=" * 100)
        log("📊 COMPREHENSIVE API TEST SUITE REPORT")
        log("=" * 100)
        
        # Overall statistics
        stats = report['overall_statistics']
        log(f"🎯 Test Execution Summary:")
        log(f"   Total Duration: {report['total_duration']:.2f} seconds")
        log(f"   Test Suites: {stats['successful_suites']}/{stats['total_test_suites']} successful ({stats['suite_success_rate']:.1f}%)")
        log(f"   Individual Tests: {stats['successful_tests']}/{stats['total_tests']} successful ({stats['overall_success_rate']:.1f}%)")
        log(f"   Failed Tests: {stats['failed_tests']}")
        log(f"   Error Tests: {stats['error_tests']}")
        log(f"   Skipped Tests: {stats['skipped_tests']}")
        
        log("=" * 100)
        log("📋 TEST SUITE BREAKDOWN:")
        
        # Suite-by-suite breakdown
        for suite_name, results in report['suite_results'].items():
            status_icon = "✅" if results.get('successful') else "❌"
            log(f"{status_icon} {suite_name}:")
            log(f"   Tests Run: {results.get('tests_run', 0)}")
            log(f"   Success Rate: {results.get('success_rate', 0):.1f}%")
            log(f"   Duration: {results.get('duration', 0):.2f}s")
            
            if results.get('failures', 0) > 0:
                log(f"   ⚠️ Failures: {results['failures']}")
            if results.get('errors', 0) > 0:
                log(f"   🚨 Errors: {results['errors']}")
            if results.get('skipped', 0) > 0:
                log(f"   ⏭️ Skipped: {results['skipped']}")
        
        log("=" * 100)
        log("🔍 API COVERAGE ANALYSIS:")
        
        # Coverage analysis
        for area, analysis in report['api_coverage_analysis'].items():
            status_icon = "✅" if analysis['tested'] and analysis.get('success_rate', 0) >= 95 else \
                         "⚠️" if analysis['tested'] else "❌"
            log(f"{status_icon} {area}: {analysis['status']}")
        
        log("=" * 100)
        log("💡 RECOMMENDATIONS:")
        
        # Recommendations
        for i, recommendation in enumerate(report['recommendations'], 1):
            log(f"   {i}. {recommendation}")
        
        log("=" * 100)
        
        # Final assessment
        if stats['overall_success_rate'] >= 95:
            log("🎉 COMPREHENSIVE API TEST SUITE: EXCELLENT PERFORMANCE!")
            log("   API is ready for production deployment with confidence.")
        elif stats['overall_success_rate'] >= 85:
            log("✅ COMPREHENSIVE API TEST SUITE: GOOD PERFORMANCE")
            log("   API is functional with minor issues to address.")
        elif stats['overall_success_rate'] >= 70:
            log("⚠️ COMPREHENSIVE API TEST SUITE: ACCEPTABLE PERFORMANCE")
            log("   API has significant issues that should be addressed.")
        else:
            log("❌ COMPREHENSIVE API TEST SUITE: POOR PERFORMANCE")
            log("   API requires major fixes before production deployment.")
        
        log("=" * 100)
    
    def save_report_to_file(self, report: Dict):
        """Save detailed report to JSON file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(self.report_dir, f"comprehensive_api_test_report_{timestamp}.json")
        
        try:
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            log(f"📄 Detailed report saved to: {report_file}")
            return report_file
        except Exception as e:
            log(f"❌ Error saving report: {str(e)}", "ERROR")
            return None

def main():
    """Main function to run comprehensive API tests."""
    log("🚀 Initializing Comprehensive API Test Suite...")
    
    # Check if API is accessible
    try:
        import requests
        resp = requests.get(f"{API_URL}/api/{API_VERSION}/health/", timeout=10)
        if resp.status_code != 200:
            log(f"⚠️ API health check failed: {resp.status_code}", "WARNING")
            log("Continuing with tests anyway...")
    except Exception as e:
        log(f"⚠️ Could not reach API at {API_URL}: {str(e)}", "WARNING")
        log("Continuing with tests anyway...")
    
    # Initialize test runner
    runner = ComprehensiveAPITestRunner()
    
    try:
        # Run all test suites
        test_results = runner.run_all_test_suites()
        
        # Generate comprehensive report
        report = runner.generate_comprehensive_report()
        
        # Print detailed report
        runner.print_detailed_report(report)
        
        # Save report to file
        runner.save_report_to_file(report)
        
        # Return exit code based on overall success
        overall_success = report['overall_statistics']['overall_success_rate'] >= 85
        return 0 if overall_success else 1
        
    except Exception as e:
        log(f"❌ Critical error in test execution: {str(e)}", "ERROR")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
