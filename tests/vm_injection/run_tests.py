#!/usr/bin/env python3

"""
Test runner for VM injection tests.
"""

import os
import sys
import unittest
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_tests():
    """Run all VM injection tests."""
    # Discover all tests in the current directory
    test_suite = unittest.defaultTestLoader.discover(
        start_dir=os.path.dirname(__file__),
        pattern='test_*.py'
    )
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Return the result
    return result

if __name__ == '__main__':
    # Run the tests
    result = run_tests()
    
    # Exit with the appropriate status code
    sys.exit(not result.wasSuccessful())
