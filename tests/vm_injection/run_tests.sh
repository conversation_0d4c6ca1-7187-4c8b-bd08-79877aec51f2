#!/bin/bash

# Script to run VM injection tests

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Running VM injection tests...${NC}"

# Change to the project root directory
cd "$(dirname "$0")/../.."

# Run the tests
python3 tests/vm_injection/run_tests.py

# Get the exit code
EXIT_CODE=$?

# Print the result
if [ $EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}All tests passed!${NC}"
else
    echo -e "${RED}Some tests failed!${NC}"
fi

# Exit with the appropriate status code
exit $EXIT_CODE
