# VM Injection Tests

This directory contains tests for the VM injection functionality.

## Test Structure

The tests are organized as follows:

- `base_test.py`: Base test class for VM injection tests.
- `test_ssh_mode.py`: Tests for VM injection using SSH mode.
- `test_grpc_mode.py`: Tests for VM injection using gRPC mode.
- `test_error_handling.py`: Tests for VM injection error handling.
- `test_vm_injection_service.py`: Tests for the VM injection service.
- `run_tests.py`: Python script to run all tests.
- `run_tests.sh`: Shell script to run all tests.
- `run_tests_docker.sh`: Shell script to run all tests in a Docker container.
- `Dockerfile`: Dockerfile for running tests in a Docker container.

## Running the Tests

### Running Tests Locally

To run the tests locally, use the following command:

```bash
./tests/vm_injection/run_tests.sh
```

### Running Tests in Docker

To run the tests in a Docker container, use the following command:

```bash
./tests/vm_injection/run_tests_docker.sh
```

## Test Coverage

The tests cover the following functionality:

- SSH mode:
  - Connection to the Vagrant service
  - Getting VM status
  - File injection
  - Command execution
  - Sudo command execution
  - File verification

- gRPC mode:
  - Connection to the Vagrant service
  - Fallback to SSH mode
  - VM injection service fallback

- Error handling:
  - Invalid VM ID
  - Invalid file path
  - Invalid target path
  - Invalid command
  - Connection error
  - VM injection service error handling

- VM injection service:
  - Getting all VM injections
  - Getting a VM injection by ID
  - Creating a VM injection
  - Updating a VM injection
  - Getting VM injection status
  - Retrying a VM injection
  - Deleting a VM injection

## Adding New Tests

To add a new test, create a new test file in this directory with the following structure:

```python
#!/usr/bin/env python3

"""
Tests for <test subject>.
"""

import os
import sys
import logging
import unittest
import asyncio
from typing import Dict, Any, Optional

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import the base test class
from tests.vm_injection.base_test import BaseVMInjectionTest

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Test<TestSubject>(BaseVMInjectionTest):
    """Tests for <test subject>."""

    async def test_<test_name>(self):
        """Test <test description>."""
        # Test code here
        pass

if __name__ == '__main__':
    unittest.main()
```

Then, add the test to the `run_tests.py` script if necessary.
