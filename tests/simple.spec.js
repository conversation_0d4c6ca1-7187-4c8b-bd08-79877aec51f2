// @ts-check
const { test, expect } = require('@playwright/test');

// Use the correct frontend IP address
const FRONTEND_URL = 'http://**********:3000';

test('basic UI accessibility test', async ({ page }) => {
  // Navigate to the frontend in the Docker container
  await page.goto(FRONTEND_URL);
  
  // Wait for the page to load
  await page.waitForTimeout(2000);
  
  // Take a screenshot for verification
  await page.screenshot({ path: 'test_screenshots/ui_loaded.png' });
  
  // Check if the page has loaded correctly (should have a title)
  const title = await page.title();
  console.log('Page title:', title);
  expect(title).toBeTruthy();
  
  try {
    // Check if main content is visible
    const mainContent = await page.$('main, .ant-layout-content, #root > div');
    expect(mainContent).toBeTruthy('Main content should be visible');
    
    // Try to find any visible buttons
    const buttons = await page.$$('button');
    console.log(`Found ${buttons.length} buttons on the page`);
    
    // Check if any heading is visible
    const heading = await page.$('h1, h2, h3');
    if (heading) {
      const headingText = await heading.textContent();
      console.log('Found heading:', headingText);
    }
    
    console.log('✅ UI is accessible');
  } catch (e) {
    console.log('❌ Error checking UI components:', e.message);
    throw e;
  }
}); 