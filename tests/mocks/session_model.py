"""
Mock Session model for testing.
"""
from sqlalchemy import Column, String, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime

from db.base_model import BaseModel


class Session(BaseModel):
    """Session model for storing user sessions."""

    __tablename__ = "sessions"

    token = Column(String(255), nullable=False)
    expires_at = Column(DateTime, nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="sessions")
    
    def __repr__(self) -> str:
        """Return string representation of the session."""
        return f"<Session(id={self.id}, user_id={self.user_id}, expires_at={self.expires_at})>"
