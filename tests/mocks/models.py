"""
Mock models for testing.
"""
from sqlalchemy import Column, String, DateTime, ForeignKey, Text, JSON, Integer, Boolean
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from datetime import datetime

from db.base_model import BaseModel


class VagrantVM(BaseModel):
    """VagrantVM model for storing VM information."""

    __tablename__ = "vagrant_vms"

    name = Column(String(100), nullable=False)
    description = Column(String(255), nullable=True)
    status = Column(String(50), nullable=False)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="vagrant_vms")
    
    def __repr__(self) -> str:
        """Return string representation of the VM."""
        return f"<VagrantVM(id={self.id}, name={self.name}, status={self.status})>"


class VMInjection(BaseModel):
    """VMInjection model for storing VM injection information."""

    __tablename__ = "vm_injections"

    status = Column(String(50), nullable=False)
    description = Column(String(255), nullable=True)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="vm_injections")
    
    def __repr__(self) -> str:
        """Return string representation of the VM injection."""
        return f"<VMInjection(id={self.id}, status={self.status})>"


class FileUpload(BaseModel):
    """FileUpload model for storing file upload information."""

    __tablename__ = "file_uploads"

    filename = Column(String(255), nullable=False)
    content_type = Column(String(100), nullable=False)
    size = Column(Integer, nullable=False)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="file_uploads")
    
    def __repr__(self) -> str:
        """Return string representation of the file upload."""
        return f"<FileUpload(id={self.id}, filename={self.filename})>"


class FileSelection(BaseModel):
    """FileSelection model for storing file selection information."""

    __tablename__ = "file_selections"

    name = Column(String(100), nullable=False)
    description = Column(String(255), nullable=True)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="file_selections")
    
    def __repr__(self) -> str:
        """Return string representation of the file selection."""
        return f"<FileSelection(id={self.id}, name={self.name})>"


class Item(BaseModel):
    """Item model for storing item information."""

    __tablename__ = "items"

    title = Column(String(100), nullable=False)
    description = Column(String(255), nullable=True)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="items")
    
    def __repr__(self) -> str:
        """Return string representation of the item."""
        return f"<Item(id={self.id}, title={self.title})>"


class HashReport(BaseModel):
    """HashReport model for storing hash report information."""

    __tablename__ = "hash_reports"

    hash_value = Column(String(64), nullable=False)
    report_data = Column(JSONB, nullable=True)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="hash_reports")
    
    def __repr__(self) -> str:
        """Return string representation of the hash report."""
        return f"<HashReport(id={self.id}, hash_value={self.hash_value})>"
