"""Audit log database model."""

from typing import Optional, Dict, Any
from sqlalchemy import Column, String, DateTime, ForeignKey, Text, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime

from db.base_model import BaseModel


class AuditLog(BaseModel):
    """Audit log model for tracking user actions and system events."""

    __tablename__ = "audit_logs"

    # Event information
    action = Column(String(100), nullable=False, index=True)
    resource_type = Column(String(50), nullable=True, index=True)
    resource_id = Column(String(255), nullable=True, index=True)
    
    # User information
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    
    # Request information
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(String(500), nullable=True)
    
    # Event details
    description = Column(Text, nullable=True)
    event_metadata = Column(JSON, nullable=True)  # Additional structured data
    
    # Status and outcome
    status = Column(String(20), nullable=False, default="success")  # success, failure, error
    error_message = Column(Text, nullable=True)
    
    # Timing
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow, index=True)
    
    # Relationships
    user = relationship("User", back_populates="audit_logs")
    
    def __repr__(self) -> str:
        """Return string representation of the audit log."""
        return f"<AuditLog(id={self.id}, action={self.action}, user_id={self.user_id}, timestamp={self.timestamp})>"
    
    @classmethod
    def log_action(
        cls,
        action: str,
        user_id: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        description: Optional[str] = None,
        event_metadata: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        status: str = "success",
        error_message: Optional[str] = None
    ) -> "AuditLog":
        """Create a new audit log entry."""
        return cls(
            action=action,
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            description=description,
            event_metadata=event_metadata,
            ip_address=ip_address,
            user_agent=user_agent,
            status=status,
            error_message=error_message,
            timestamp=datetime.utcnow()
        )
