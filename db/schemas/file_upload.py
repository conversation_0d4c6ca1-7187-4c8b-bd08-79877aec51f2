"""
Schemas for file upload functionality.

This module defines Pydantic models for handling file uploads, including:
- Base models with common fields
- Create models for uploading new files
- Update models for modifying existing files
- Read models for retrieving file information
"""
from typing import Optional, List, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator, AnyHttpUrl
import uuid
from uuid import UUID


class FileUploadBase(BaseModel):
    """
    Base schema for file uploads.

    Contains the common fields shared across file upload operations.
    """

    filename: str = Field(
        ...,
        description="Name of the uploaded file",
        example="document.pdf",
        min_length=1,
        max_length=255
    )

    content_type: str = Field(
        "application/octet-stream",
        description="MIME type of the file",
        example="application/pdf",
        min_length=1,
        max_length=255
    )

    description: Optional[str] = Field(
        "",
        description="Optional description of the file's contents or purpose",
        example="Financial report for Q4 2023",
        max_length=1000
    )


class FileUploadCreate(FileUploadBase):
    """
    Schema for creating a file upload.

    Used when uploading a new file to the system.
    """

    file_size: int = Field(
        ...,
        description="Size of the file in bytes",
        example=1048576,  # 1 MB
        gt=0
    )

    @validator('file_size')
    def validate_file_size(cls, v):
        """Validate file size is positive and within system limits."""
        if v <= 0:
            raise ValueError("File size must be positive")

        # Set a reasonable max file size (100 MB by default)
        max_size = 100 * 1024 * 1024  # 100 MB
        if v > max_size:
            raise ValueError(f"File size exceeds maximum allowed size of {max_size} bytes")

        return v


class FileUploadUpdate(BaseModel):
    """
    Schema for updating a file upload.

    Used when updating metadata for an existing file.
    Currently only allows updating the description.
    """

    description: Optional[str] = Field(
        None,
        description="Updated description for the file",
        example="Updated financial report with corrections",
        max_length=1000
    )


class FileUploadRead(FileUploadBase):
    """
    Schema for reading a file upload.

    Used when retrieving file information from the API.
    Includes all metadata about the file and a download URL.
    """

    id: UUID = Field(
        ...,
        description="Unique identifier for the file",
        example="f8e7d6c5-b4a3-2c1d-0e9f-8a7b6c5d4e3f"
    )

    file_size: int = Field(
        ...,
        description="Size of the file in bytes",
        example=1048576  # 1 MB
    )

    file_hash: Optional[str] = Field(
        None,
        description="SHA-256 hash of the file contents for integrity verification",
        example="8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92"
    )

    created_on: Optional[datetime] = Field(
        None,
        description="Timestamp when the file was uploaded",
        example="2023-10-23T14:30:15.123Z"
    )

    modified_on: Optional[datetime] = Field(
        None,
        description="Timestamp when the file was last updated",
        example="2023-10-24T09:45:30.456Z"
    )

    download_url: Optional[str] = Field(
        None,
        description="URL where the file can be downloaded",
        example="/api/v1/file_upload/download/f8e7d6c5-b4a3-2c1d-0e9f-8a7b6c5d4e3f"
    )

    class Config:
        """Pydantic config."""

        from_attributes = True  # Enable ORM mode
        schema_extra = {
            "example": {
                "id": "f8e7d6c5-b4a3-2c1d-0e9f-8a7b6c5d4e3f",
                "filename": "report.pdf",
                "content_type": "application/pdf",
                "description": "Annual financial report",
                "file_size": 1048576,
                "file_hash": "8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92",
                "created_at": "2023-10-23T14:30:15.123Z",
                "updated_at": "2023-10-24T09:45:30.456Z",
                "download_url": "/api/v1/file_upload/download/f8e7d6c5-b4a3-2c1d-0e9f-8a7b6c5d4e3f"
            }
        }


class FolderUploadCreate(BaseModel):
    """
    Schema for creating a folder upload.

    Used when uploading multiple files as a folder structure.
    """

    description: Optional[str] = Field(
        "",
        description="Description of the folder contents",
        example="Project documentation files",
        max_length=1000
    )

    files: List[FileUploadCreate] = Field(
        ...,
        description="List of files to upload",
        min_items=1
    )

    paths: List[str] = Field(
        ...,
        description="Relative paths for each file within the folder structure",
        example=["docs/readme.md", "src/main.py", "data/sample.json"],
        min_items=1
    )

    @validator('paths')
    def validate_paths(cls, paths, values):
        """Validate paths match number of files and follow proper format."""
        if 'files' in values and len(paths) != len(values['files']):
            raise ValueError("Number of paths must match number of files")

        # Validate path format
        for path in paths:
            if path.startswith('/'):
                raise ValueError("Paths should be relative and not start with '/'")
            if '..' in path.split('/'):
                raise ValueError("Path traversal is not allowed (no '..' components)")

        return paths


class FolderUploadRead(BaseModel):
    """
    Schema for reading a folder upload.

    Used when retrieving information about a folder upload.
    """

    folder_id: str = Field(
        ...,
        description="Unique identifier for the folder",
        example="a1b2c3d4-e5f6-7890-1234-567890abcdef"
    )

    description: str = Field(
        ...,
        description="Description of the folder contents",
        example="Project documentation files"
    )

    file_count: int = Field(
        ...,
        description="Number of files in the folder",
        example=3,
        ge=0
    )

    files: List[FileUploadRead] = Field(
        ...,
        description="List of files in the folder with their metadata"
    )

    created_at: datetime = Field(
        ...,
        description="Timestamp when the folder was created",
        example="2023-10-23T14:30:15.123Z"
    )

    class Config:
        """Pydantic config."""

        from_attributes = True  # Enable ORM mode
        schema_extra = {
            "example": {
                "folder_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
                "description": "Project documentation files",
                "file_count": 3,
                "files": [
                    {
                        "id": "f8e7d6c5-b4a3-2c1d-0e9f-8a7b6c5d4e3f",
                        "filename": "readme.md",
                        "content_type": "text/markdown",
                        "description": "Project readme",
                        "file_size": 1024,
                        "created_at": "2023-10-23T14:30:15.123Z",
                        "download_url": "/api/v1/file_upload/download/f8e7d6c5-b4a3-2c1d-0e9f-8a7b6c5d4e3f"
                    },
                    {
                        "id": "a9b8c7d6-e5f4-3g2h-1i0j-k9l8m7n6o5p4",
                        "filename": "main.py",
                        "content_type": "text/x-python",
                        "description": "Main Python script",
                        "file_size": 2048,
                        "created_at": "2023-10-23T14:31:20.456Z",
                        "download_url": "/api/v1/file_upload/download/a9b8c7d6-e5f4-3g2h-1i0j-k9l8m7n6o5p4"
                    }
                ],
                "created_at": "2023-10-23T14:30:10.789Z"
            }
        }

# For backward compatibility
FileUploadSchema = FileUploadRead

class FileUploadListSchema(BaseModel):
    """
    Schema for a list of file uploads.

    Used when retrieving a paginated list of file uploads.
    """

    items: List[FileUploadSchema] = Field(
        ...,
        description="List of file uploads"
    )

    total: int = Field(
        ...,
        description="Total number of file uploads matching the query",
        example=42,
        ge=0
    )

    class Config:
        """Pydantic config."""

        schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "f8e7d6c5-b4a3-2c1d-0e9f-8a7b6c5d4e3f",
                        "filename": "report.pdf",
                        "content_type": "application/pdf",
                        "description": "Annual financial report",
                        "file_size": 1048576,
                        "file_hash": "8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92",
                        "created_at": "2023-10-23T14:30:15.123Z",
                        "updated_at": "2023-10-24T09:45:30.456Z",
                        "download_url": "/api/v1/file_upload/download/f8e7d6c5-b4a3-2c1d-0e9f-8a7b6c5d4e3f"
                    },
                    {
                        "id": "a9b8c7d6-e5f4-3g2h-1i0j-k9l8m7n6o5p4",
                        "filename": "data.csv",
                        "content_type": "text/csv",
                        "description": "Raw data export",
                        "file_size": 524288,
                        "file_hash": "a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2",
                        "created_at": "2023-10-22T10:15:30.789Z",
                        "updated_at": "2023-10-22T10:15:30.789Z",
                        "download_url": "/api/v1/file_upload/download/a9b8c7d6-e5f4-3g2h-1i0j-k9l8m7n6o5p4"
                    }
                ],
                "total": 42
            }
        }