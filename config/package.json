{"name": "turdparty-tests", "version": "1.0.0", "description": "End-to-end testing for TurdParty file upload functionality", "main": "index.js", "scripts": {"test": "jest", "ui-test": "node test-file-upload-ui.js", "api-test": "node test-api-upload.js", "e2e-test": "node test-e2e-upload.js", "test:security": "node test-api-upload.js --security", "test:playwright": "npx playwright test tests/file-upload-e2e.test.js", "test:all": "npm run api-test && npm run ui-test && npm run e2e-test", "test:ci": "npx playwright test --reporter=html,list", "start:test": "node server.js"}, "repository": {"type": "git", "url": "git+https://github.com/turdparty/turdparty.git"}, "keywords": ["testing", "file-upload", "e2e", "playwright"], "author": "TurdParty Team", "license": "MIT", "dependencies": {"@grpc/grpc-js": "^1.13.3", "@grpc/proto-loader": "^0.7.15", "axios": "^1.9.0", "form-data": "^4.0.0", "jest": "^29.7.0", "node-fetch": "^2.7.0", "playwright": "^1.42.1", "uuid": "^9.0.1"}, "devDependencies": {"@playwright/test": "^1.52.0"}}