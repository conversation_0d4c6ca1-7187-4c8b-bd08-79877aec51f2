
.PHONY: translate update-translations clean-translations check-translations fix-translations translate-ui translate-docs translation-status translation-quality

# Main translation commands
translate:
	@echo "Running comprehensive translation update..."
	python scripts/translation_manager.py --translate

translate-ui:
	@echo "Translating UI components..."
	python scripts/translation_manager.py --translate-ui

translate-docs:
	@echo "Translating documentation..."
	python scripts/translation_manager.py --translate-docs

# Legacy command for backward compatibility
update-translations:
	@echo "Updating translations from source files..."
	python scripts/translate_docs.py

# Quality and status commands
check-translations:
	@echo "Checking translation quality..."
	python scripts/translation_manager.py --check-quality

fix-translations:
	@echo "Fixing missing translations..."
	python scripts/translation_manager.py --fix-missing

translation-status:
	@echo "Checking translation status..."
	python scripts/translation_manager.py --status

translation-quality:
	@echo "Running translation quality checker..."
	python scripts/translation_quality_checker.py

# Cleanup commands
clean-translations:
	@echo "Cleaning translation artifacts..."
	python scripts/translation_manager.py --clean
	@echo "Translation artifacts cleaned"

# Help command
help:
	@echo "Translation management commands:"
	@echo ""
	@echo "Main Commands:"
	@echo "  make translate           - Run comprehensive translation update (UI + docs)"
	@echo "  make translate-ui        - Translate UI components only"
	@echo "  make translate-docs      - Translate documentation only"
	@echo "  make update-translations - Legacy: Update documentation translations"
	@echo ""
	@echo "Quality & Status:"
	@echo "  make check-translations  - Check translation quality"
	@echo "  make fix-translations    - Fix missing translations"
	@echo "  make translation-status  - Show translation status"
	@echo "  make translation-quality - Run detailed quality checks"
	@echo ""
	@echo "Maintenance:"
	@echo "  make clean-translations  - Clean translation artifacts and cache"
	@echo ""
	@echo "Language-specific examples:"
	@echo "  python scripts/translation_manager.py --translate --language de"
	@echo "  python scripts/translation_manager.py --translate-ui --language fr"

# ==================== API TESTING TARGETS ====================

.PHONY: test test-api test-api-core test-api-async test-api-security test-api-multilingual test-api-quick test-api-production test-api-local test-api-docker test-api-report test-api-monitor

# Basic test targets
test:
	@echo "Running basic tests..."
	cd tests && python -m pytest -v

test-coverage:
	@echo "Running tests with coverage..."
	cd tests && python -m pytest --cov=../src --cov-report=html --cov-report=term -v

# Comprehensive API test targets
test-api:
	@echo "🚀 Running comprehensive API test suite..."
	@echo "Testing 37 languages | 96% EU compliance | Enterprise-grade security"
	cd tests && python run_comprehensive_api_tests.py

test-api-core:
	@echo "🔧 Running core API functionality tests..."
	cd tests && python -m unittest unit.test_api_coverage.APITestCase -v

test-api-async:
	@echo "⚡ Running async API and Celery tests..."
	cd tests && python -m unittest unit.test_async_api_comprehensive.AsyncAPITestCase -v

test-api-security:
	@echo "🔒 Running security and authentication tests..."
	cd tests && python -m unittest unit.test_security_comprehensive.SecurityTestCase -v

test-api-multilingual:
	@echo "🌍 Running multilingual and internationalization tests..."
	cd tests && python -m unittest unit.test_multilingual_api.MultilingualAPITestCase -v

test-api-quick:
	@echo "⚡ Running quick API health check..."
	cd tests && python unit/test_api_coverage.py --comprehensive

# API testing with different configurations
test-api-production:
	@echo "🏭 Running API tests against production configuration..."
	API_URL=https://api.turdparty.com cd tests && python run_comprehensive_api_tests.py

test-api-local:
	@echo "🏠 Running API tests against local development server..."
	API_URL=http://localhost:3050 cd tests && python run_comprehensive_api_tests.py

test-api-docker:
	@echo "🐳 Running API tests against Docker containers..."
	API_URL=http://turdparty_api:3050 cd tests && python run_comprehensive_api_tests.py

# Test reporting and analysis
test-api-report:
	@echo "📊 Generating comprehensive API test report..."
	mkdir -p tests/reports/api_tests
	cd tests && python run_comprehensive_api_tests.py > reports/api_tests/latest_api_test_report.txt 2>&1

test-api-monitor:
	@echo "📈 Running continuous API monitoring tests..."
	cd tests && while true; do python run_comprehensive_api_tests.py; sleep 300; done

# API test help
test-help:
	@echo "API Testing Commands:"
	@echo ""
	@echo "Comprehensive Testing:"
	@echo "  make test-api            - Run all API test suites (recommended)"
	@echo "  make test-api-report     - Generate detailed test report"
	@echo ""
	@echo "Individual Test Suites:"
	@echo "  make test-api-core       - Core API functionality tests"
	@echo "  make test-api-async      - Async tasks and Celery integration"
	@echo "  make test-api-security   - Security and authentication tests"
	@echo "  make test-api-multilingual - Multilingual and i18n tests"
	@echo ""
	@echo "Environment-Specific:"
	@echo "  make test-api-local      - Test against local development server"
	@echo "  make test-api-docker     - Test against Docker containers"
	@echo "  make test-api-production - Test against production environment"
	@echo ""
	@echo "Monitoring:"
	@echo "  make test-api-quick      - Quick health check"
	@echo "  make test-api-monitor    - Continuous monitoring (runs every 5 min)"
