
.PHONY: translate update-translations clean-translations check-translations fix-translations translate-ui translate-docs translation-status translation-quality

# Main translation commands
translate:
	@echo "Running comprehensive translation update..."
	python scripts/translation_manager.py --translate

translate-ui:
	@echo "Translating UI components..."
	python scripts/translation_manager.py --translate-ui

translate-docs:
	@echo "Translating documentation..."
	python scripts/translation_manager.py --translate-docs

# Legacy command for backward compatibility
update-translations:
	@echo "Updating translations from source files..."
	python scripts/translate_docs.py

# Quality and status commands
check-translations:
	@echo "Checking translation quality..."
	python scripts/translation_manager.py --check-quality

fix-translations:
	@echo "Fixing missing translations..."
	python scripts/translation_manager.py --fix-missing

translation-status:
	@echo "Checking translation status..."
	python scripts/translation_manager.py --status

translation-quality:
	@echo "Running translation quality checker..."
	python scripts/translation_quality_checker.py

# Cleanup commands
clean-translations:
	@echo "Cleaning translation artifacts..."
	python scripts/translation_manager.py --clean
	@echo "Translation artifacts cleaned"

# Help command
help:
	@echo "Translation management commands:"
	@echo ""
	@echo "Main Commands:"
	@echo "  make translate           - Run comprehensive translation update (UI + docs)"
	@echo "  make translate-ui        - Translate UI components only"
	@echo "  make translate-docs      - Translate documentation only"
	@echo "  make update-translations - Legacy: Update documentation translations"
	@echo ""
	@echo "Quality & Status:"
	@echo "  make check-translations  - Check translation quality"
	@echo "  make fix-translations    - Fix missing translations"
	@echo "  make translation-status  - Show translation status"
	@echo "  make translation-quality - Run detailed quality checks"
	@echo ""
	@echo "Maintenance:"
	@echo "  make clean-translations  - Clean translation artifacts and cache"
	@echo ""
	@echo "Language-specific examples:"
	@echo "  python scripts/translation_manager.py --translate --language de"
	@echo "  python scripts/translation_manager.py --translate-ui --language fr"

# ==================== API TESTING TARGETS ====================

.PHONY: test test-api test-api-core test-api-async test-api-security test-api-multilingual test-api-quick test-api-production test-api-local test-api-docker test-api-report test-api-monitor

# Basic test targets
test:
	@echo "Running basic tests..."
	cd tests && python -m pytest -v

test-coverage:
	@echo "Running tests with coverage..."
	cd tests && python -m pytest --cov=../src --cov-report=html --cov-report=term -v

# Comprehensive API test targets
test-api:
	@echo "🚀 Running comprehensive API test suite..."
	@echo "Testing 37 languages | 96% EU compliance | Enterprise-grade security"
	cd tests && python run_comprehensive_api_tests.py

test-api-core:
	@echo "🔧 Running core API functionality tests..."
	cd tests && python -m unittest unit.test_api_coverage.APITestCase -v

test-api-async:
	@echo "⚡ Running async API and Celery tests..."
	cd tests && python -m unittest unit.test_async_api_comprehensive.AsyncAPITestCase -v

test-api-security:
	@echo "🔒 Running security and authentication tests..."
	cd tests && python -m unittest unit.test_security_comprehensive.SecurityTestCase -v

test-api-multilingual:
	@echo "🌍 Running multilingual and internationalization tests..."
	cd tests && python -m unittest unit.test_multilingual_api.MultilingualAPITestCase -v

test-api-quick:
	@echo "⚡ Running quick API health check..."
	cd tests && python unit/test_api_coverage.py --comprehensive

# API testing with different configurations
test-api-production:
	@echo "🏭 Running API tests against production configuration..."
	API_URL=https://api.turdparty.com cd tests && python run_comprehensive_api_tests.py

test-api-local:
	@echo "🏠 Running API tests against local development server..."
	API_URL=http://localhost:3050 cd tests && python run_comprehensive_api_tests.py

test-api-docker:
	@echo "🐳 Running API tests against Docker containers..."
	API_URL=http://turdparty_api:3050 cd tests && python run_comprehensive_api_tests.py

# Test reporting and analysis
test-api-report:
	@echo "📊 Generating comprehensive API test report..."
	mkdir -p tests/reports/api_tests
	cd tests && python run_comprehensive_api_tests.py > reports/api_tests/latest_api_test_report.txt 2>&1

test-api-monitor:
	@echo "📈 Running continuous API monitoring tests..."
	cd tests && while true; do python run_comprehensive_api_tests.py; sleep 300; done

# ==================== BEHAVE BDD TESTING TARGETS ====================

.PHONY: test-behave test-behave-ui test-behave-auth test-behave-files test-behave-vms test-behave-multilingual test-behave-e2e test-behave-headless test-behave-headed test-behave-report

# Comprehensive Behave BDD testing
test-behave:
	@echo "🎭 Running comprehensive Behave BDD test suite..."
	@echo "Testing user experience flows and behavior-driven scenarios"
	cd tests/behave && python run_behave_tests.py

test-behave-ui:
	@echo "🌐 Running Behave UI and user interface tests..."
	cd tests/behave && python run_behave_tests.py --tags @ui

test-behave-auth:
	@echo "🔐 Running Behave authentication and authorization tests..."
	cd tests/behave && python run_behave_tests.py --tags @authentication

test-behave-files:
	@echo "📁 Running Behave file management workflow tests..."
	cd tests/behave && python run_behave_tests.py --tags @file_upload,@file_management

test-behave-vms:
	@echo "🖥️ Running Behave VM management workflow tests..."
	cd tests/behave && python run_behave_tests.py --tags @vm_management,@vm_injection

test-behave-multilingual:
	@echo "🌍 Running Behave multilingual user experience tests..."
	cd tests/behave && python run_behave_tests.py --tags @multilingual

test-behave-e2e:
	@echo "🔄 Running Behave end-to-end workflow tests..."
	cd tests/behave && python run_behave_tests.py --tags @e2e

test-behave-critical:
	@echo "🚨 Running Behave critical user flow tests..."
	cd tests/behave && python run_behave_tests.py --tags @critical

# Browser mode testing
test-behave-headless:
	@echo "🤖 Running Behave tests in headless browser mode..."
	cd tests/behave && python run_behave_tests.py --headless

test-behave-headed:
	@echo "👁️ Running Behave tests with visible browser..."
	cd tests/behave && python run_behave_tests.py --headed

# Environment-specific Behave testing
test-behave-local:
	@echo "🏠 Running Behave tests against local development environment..."
	cd tests/behave && python run_behave_tests.py --environment development

test-behave-docker:
	@echo "🐳 Running Behave tests against Docker environment..."
	cd tests/behave && python run_behave_tests.py --environment testing

test-behave-staging:
	@echo "🎪 Running Behave tests against staging environment..."
	cd tests/behave && python run_behave_tests.py --environment staging

# Behave reporting and analysis
test-behave-report:
	@echo "📊 Generating comprehensive Behave test report..."
	mkdir -p tests/behave/reports
	cd tests/behave && python run_behave_tests.py > reports/latest_behave_report.txt 2>&1

test-behave-dry-run:
	@echo "🔍 Running Behave syntax check (dry run)..."
	cd tests/behave && python run_behave_tests.py --dry-run

# API test help
test-help:
	@echo "Testing Commands:"
	@echo ""
	@echo "API Testing:"
	@echo "  make test-api            - Run all API test suites (recommended)"
	@echo "  make test-api-core       - Core API functionality tests"
	@echo "  make test-api-async      - Async tasks and Celery integration"
	@echo "  make test-api-security   - Security and authentication tests"
	@echo "  make test-api-multilingual - Multilingual and i18n tests"
	@echo ""
	@echo "Behave BDD Testing:"
	@echo "  make test-behave         - Run all Behave BDD test suites (recommended)"
	@echo "  make test-behave-ui      - User interface and interaction tests"
	@echo "  make test-behave-auth    - Authentication workflow tests"
	@echo "  make test-behave-files   - File management workflow tests"
	@echo "  make test-behave-vms     - VM management workflow tests"
	@echo "  make test-behave-multilingual - Multilingual user experience tests"
	@echo "  make test-behave-e2e     - End-to-end workflow tests"
	@echo ""
	@echo "Environment-Specific:"
	@echo "  make test-api-local      - Test API against local development server"
	@echo "  make test-behave-local   - Test BDD against local development environment"
	@echo "  make test-api-docker     - Test API against Docker containers"
	@echo "  make test-behave-docker  - Test BDD against Docker environment"
	@echo ""
	@echo "Browser Modes:"
	@echo "  make test-behave-headless - Run BDD tests in headless browser mode"
	@echo "  make test-behave-headed   - Run BDD tests with visible browser"
	@echo ""
	@echo "Reporting:"
	@echo "  make test-api-report     - Generate detailed API test report"
	@echo "  make test-behave-report  - Generate detailed Behave test report"
