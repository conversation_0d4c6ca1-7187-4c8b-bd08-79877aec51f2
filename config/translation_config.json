{"services": {"google": {"enabled": true, "api_key_env": "GOOGLE_TRANSLATE_API_KEY", "project_id_env": "GOOGLE_CLOUD_PROJECT_ID", "priority": 2}, "deepl": {"enabled": false, "api_key_env": "DEEPL_API_KEY", "priority": 1, "supported_languages": ["bg", "cs", "da", "de", "el", "es", "et", "fi", "fr", "hu", "it", "ja", "lt", "lv", "nl", "pl", "pt", "ro", "ru", "sk", "sl", "sv", "zh"]}, "claude": {"enabled": true, "fallback": true, "priority": 3, "api_key_env": "ANTHROPIC_API_KEY"}}, "quality": {"min_length_ratio": 0.3, "max_length_ratio": 3.0, "forbidden_patterns": ["[Translation failed]", "ERROR", "FAILED", "[NEEDS_TRANSLATION]", "[CLAUDE_TRANSLATION_NEEDED]"], "required_patterns": {"preserve_placeholders": true, "preserve_html_tags": true, "preserve_markdown": true}}, "retry": {"max_attempts": 3, "delay_seconds": 1, "exponential_backoff": true}, "languages": {"supported": ["af", "be", "bg", "br", "bs", "ca", "co", "cs", "cy", "da", "de", "el", "es", "et", "eu", "fi", "fo", "fr", "ga", "gd", "gl", "gsw", "hr", "hu", "is", "it", "ja", "kw", "lb", "lt", "lv", "mk", "mt", "nl", "pl", "pt", "rm", "ro", "ru", "sc", "sk", "sl", "sr", "sv", "tr", "uk", "zh", "zu"], "priority_languages": ["de", "fr", "es", "it", "pt", "ru", "zh", "ja"], "language_names": {"af": "Afrikaans", "bg": "Bulgarian", "cs": "Czech", "da": "Danish", "de": "German", "el": "Greek", "en_GB": "British English", "es": "Spanish", "et": "Estonian", "fi": "Finnish", "fr": "French", "gsw": "Swiss German", "hu": "Hungarian", "it": "Italian", "ja": "Japanese", "lt": "Lithuanian", "lv": "Latvian", "nl": "Dutch", "pl": "Polish", "pt": "Portuguese", "ro": "Romanian", "ru": "Russian", "sk": "Slovak", "sl": "Slovenian", "sv": "Swedish", "tr": "Turkish", "uk": "Ukrainian", "zh": "Chinese", "zu": "Zulu"}}, "context": {"ui": {"preserve_interpolation": true, "preserve_pluralization": true, "context_hints": ["This is a user interface element", "Keep it concise and user-friendly", "Maintain consistent terminology"]}, "docs": {"preserve_markdown": true, "preserve_code_blocks": true, "context_hints": ["This is technical documentation", "Maintain technical accuracy", "Keep formatting intact"]}, "api": {"preserve_json_structure": true, "preserve_field_names": true, "context_hints": ["This is API documentation", "Preserve technical terms", "Keep examples functional"]}}, "output": {"preserve_formatting": true, "add_metadata": true, "create_backups": true, "validate_json": true}, "logging": {"level": "INFO", "log_translations": true, "log_failures": true, "create_reports": true}}