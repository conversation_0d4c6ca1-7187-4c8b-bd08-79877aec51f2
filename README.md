# TurdParty 🚀

<div align="center">

![TurdParty Logo](docs/source/_static/logo.png)

[![FastAPI](https://img.shields.io/badge/FastAPI-0.109.2-009688?style=for-the-badge&logo=fastapi&logoColor=white)](https://fastapi.tiangolo.com/)
[![Python](https://img.shields.io/badge/Python-3.11-3776AB?style=for-the-badge&logo=python&logoColor=white)](https://www.python.org/)
[![Celery](https://img.shields.io/badge/Celery-5.3.6-37814A?style=for-the-badge&logo=celery&logoColor=white)](https://docs.celeryq.dev/)
[![Redis](https://img.shields.io/badge/Redis-5.0.1-DC382D?style=for-the-badge&logo=redis&logoColor=white)](https://redis.io/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-14-336791?style=for-the-badge&logo=postgresql&logoColor=white)](https://www.postgresql.org/)
[![MinIO](https://img.shields.io/badge/MinIO-Object_Storage-C72E49?style=for-the-badge&logo=minio&logoColor=white)](https://min.io/)
[![Vagrant](https://img.shields.io/badge/Vagrant-VM_Management-1563FF?style=for-the-badge&logo=vagrant&logoColor=white)](https://www.vagrantup.com/)

</div>

A comprehensive platform for file upload, VM management, file injection, and asynchronous task processing.

## ✨ Features

- **📁 File Upload & Management**: Secure file upload and storage using MinIO
- **🖥️ VM Management**: Create and manage Vagrant VMs through a RESTful API
- **💉 File Injection**: Inject files into VMs for testing and analysis
- **🔄 Asynchronous Processing**: Background task processing with Celery
- **🔍 Task Monitoring**: Track task status and results
- **🔒 Authentication**: Secure API with JWT authentication
- **📊 API Documentation**: Interactive API documentation with Swagger UI

## 🏗️ Architecture

TurdParty follows a modular architecture with clear separation of concerns:

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Frontend   │────▶│  API Server │────▶│  MinIO      │
│             │     │             │     │  Storage    │
└─────────────┘     └─────────────┘     └─────────────┘
                          │                    ▲
                          │                    │
                          ▼                    │
                    ┌─────────────┐     ┌─────────────┐
                    │             │     │             │
                    │  Vagrant    │     │  Celery     │
                    │  VMs        │◀───▶│  Workers    │
                    │             │     │             │
                    └─────────────┘     └─────────────┘
                                              │
                                              │
                                              ▼
                                        ┌─────────────┐
                                        │             │
                                        │  Redis      │
                                        │  Broker     │
                                        │             │
                                        └─────────────┘
```

## 📂 Directory Structure

The project is organized into the following directories:

- **api/**: API-related code
  - **v1/**: Version 1 of the API
    - **routes/**: API route handlers
    - **application.py**: FastAPI application setup

- **config/**: Configuration files
  - **alembic.ini**: Alembic database migration configuration
  - **pyproject.toml**: Python project configuration
  - **requirements.txt**: Python dependencies
  - **package.json**: Node.js dependencies

- **data/**: Data files and analysis results

- **db/**: Database-related code
  - **grpc/**: gRPC-related code for Vagrant service
  - **migrations/**: Database migration scripts
  - **models/**: SQLAlchemy ORM models
  - **repositories/**: Data access layer
  - **schemas/**: Pydantic schemas for API requests/responses
  - **services/**: Business logic services

- **docker/**: Docker-related files
  - **Dockerfile**: Main Dockerfile
  - **docker-compose.yml**: Docker Compose configuration

- **docs/**: Documentation
  - **images/**: Documentation images
  - **MODULE_MAP.md**: Mapping between old and new module structure

- **logs/**: Log files

- **nix/**: Nix configuration files
  - **shell.nix**: Nix shell configuration

- **scripts/**: Utility scripts

- **static/**: Static assets
  - **css/**: CSS files
  - **html/**: HTML templates
  - **js/**: JavaScript files
  - **service_icons.py**: Service icon utilities

- **tests/**: Test files
  - **e2e/**: End-to-end tests
  - **fixtures/**: Test fixtures
  - **integration/**: Integration tests
  - **scripts/**: Test scripts
  - **unit/**: Unit tests

- **utils/**: Utility modules
  - **config.py**: Configuration utilities
  - **logging.py**: Logging utilities
  - **exceptions.py**: Exception classes
  - **security.py**: Security utilities

- **vagrant/**: Vagrant-related files
  - **Vagrantfile**: Vagrant configuration

- **main.py**: Application entry point

## 🚀 Getting Started

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- Vagrant
- Nix (optional, for development)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/forkrul/replit-10baht-TurdParty-simplified.git
   cd turdparty
   ```

2. Install dependencies:
   ```bash
   # Using pip
   pip install -r config/requirements.txt

   # Or using Nix
   nix-shell
   ```

3. Start the services:
   ```bash
   # Start the API server
   python -m uvicorn main:app --reload

   # Start Celery workers
   cd .dockerwrapper
   ./run-celery.sh
   ```

## 🔄 Asynchronous API

TurdParty uses Celery for asynchronous task processing. The following diagram illustrates the flow of requests through the asynchronous API endpoints:

```mermaid
sequenceDiagram
    participant Client
    participant AsyncAPI as Async API Endpoints
    participant TaskStatus as TaskStatus DB
    participant CeleryTask as Celery Task Queue
    participant Worker as Celery Worker
    participant Resource as Resource (File/VM)

    %% File Upload Flow
    rect rgb(240, 248, 255)
        Note over Client, Resource: File Upload Flow
        Client->>AsyncAPI: POST /api/v1/async/file_upload
        AsyncAPI->>TaskStatus: Create TaskStatus (PENDING)
        AsyncAPI->>CeleryTask: Enqueue upload_file task
        AsyncAPI-->>Client: Return task_id (202 Accepted)
        CeleryTask->>Worker: Process upload_file task
        Worker->>TaskStatus: Update status (STARTED)
        Worker->>Resource: Upload file to MinIO
        Worker->>TaskStatus: Update status (SUCCESS)
    end
```

### Async API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/async/file_upload` | POST | Upload a file asynchronously |
| `/api/v1/async/file_selection` | POST | Create a file selection asynchronously |
| `/api/v1/async/vms` | POST | Provision a VM asynchronously |
| `/api/v1/async/vm_injection` | POST | Create a VM injection asynchronously |
| `/api/v1/async/vm_injection/{injection_id}/retry` | POST | Retry a failed VM injection |
| `/api/v1/async/task/{task_id}` | GET | Get the status of an asynchronous task |
| `/api/v1/async/tasks` | GET | Get all tasks for the current user |

## 🧪 Testing

Run the tests with:

```bash
# Run all tests
pytest

# Run specific tests
pytest tests/unit/
pytest tests/integration/

# Run with coverage
pytest --cov=.
```

## 📊 API Documentation

The API documentation is available at:

- Swagger UI: [http://localhost:8000/docs](http://localhost:8000/docs)
- ReDoc: [http://localhost:8000/redoc](http://localhost:8000/redoc)

## 📚 Documentation

Build the documentation with:

```bash
cd docs
nix-shell -p gnumake python3Packages.sphinx python3Packages.sphinx_rtd_theme python3Packages.myst-parser python3Packages.rst2pdf python3Packages.linkify-it-py --run "make html"
```

Then open `docs/build/html/index.html` in your browser.

## 🔧 Development

### Using Nix

For development, we recommend using Nix:

```bash
# Enter development shell
nix-shell

# Run the application
python -m uvicorn main:app --reload
```

### Docker Development

```bash
# Start all services
cd .dockerwrapper
docker compose up -d
```

## 🆕 Recent Updates

1. **Docker Container Namespacing**:
   - Updated all Docker containers to use the `turdparty_` prefix for better namespacing
   - Created dedicated Docker networks for each environment
   - Updated scripts to work with the new container naming convention

2. **Celery Integration**:
   - Added asynchronous task processing with Celery
   - Created task queues for file operations, VM lifecycle, VM injection, and monitoring
   - Implemented task status tracking and monitoring
   - Added integration tests for Celery tasks

3. **Reorganized Code Structure**:
   - Moved gRPC-related code to the `db/grpc` directory
   - Added backward compatibility aliases for schemas
   - Ensured consistent import paths throughout the codebase

4. **Improved Error Handling**:
   - Added consistent error handling in service classes
   - Enhanced logging for better debugging

5. **Enhanced Documentation**:
   - Updated README with more detailed information
   - Added comprehensive Sphinx documentation
   - Added docstrings to key functions and classes
   - Created documentation for Docker namespacing and Celery integration testing

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
