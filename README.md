# 🚀 TurdParty API

<div align="center">

![TurdParty Logo](https://img.shields.io/badge/TurdParty-API-blue?style=for-the-badge&logo=fastapi)

[![FastAPI](https://img.shields.io/badge/FastAPI-0.95+-009688?style=flat-square&logo=fastapi&logoColor=white)](https://fastapi.tiangolo.com)
[![Python](https://img.shields.io/badge/Python-3.8+-3776AB?style=flat-square&logo=python&logoColor=white)](https://www.python.org)
[![React](https://img.shields.io/badge/React-18.0+-61DAFB?style=flat-square&logo=react&logoColor=white)](https://reactjs.org)
[![Docker](https://img.shields.io/badge/Docker-Compose-2496ED?style=flat-square&logo=docker&logoColor=white)](https://www.docker.com)
[![MinIO](https://img.shields.io/badge/MinIO-Storage-C72E49?style=flat-square&logo=minio&logoColor=white)](https://min.io)
[![Vagrant](https://img.shields.io/badge/Vagrant-VM-1868F2?style=flat-square&logo=vagrant&logoColor=white)](https://www.vagrantup.com)
[![Languages](https://img.shields.io/badge/Languages-37-green?style=flat-square&logo=googletranslate&logoColor=white)](#-multi-language-support)
[![EU Coverage](https://img.shields.io/badge/EU%20Coverage-96%25-blue?style=flat-square&logo=europeanunion&logoColor=white)](#-multi-language-support)

*A powerful international cybersecurity platform with comprehensive European language support*

</div>

## 🌟 Features

- **🖥️ VM Management**: Create, start, stop, and manage Vagrant VMs
- **📁 File Handling**: Upload, download, and manage files with MinIO
- **🚀 AppImage Deployment**: Deploy and execute AppImages on VMs
- **🔒 Authentication**: JWT-based authentication with test mode support
- **📊 Monitoring**: Comprehensive logging and performance monitoring
- **🧪 Testing**: Extensive test suite with coverage reporting
- **📚 Documentation**: Comprehensive Sphinx documentation with PDF generation
- **🌐 Multi-language**: **37 languages** with **96% EU coverage** - industry-leading international support
- **🇪🇺 European Excellence**: Complete coverage of all major European language families

## 🚀 Quick Start

### 🐳 Using Docker

```bash
# Start the development environment
./.dockerwrapper/dev.sh start

# Access the application
# API: http://localhost:8000
# Frontend: http://localhost:3000
```

### 🧪 Running Tests

```bash
# Run all tests
docker exec -it TurdParty-container-test python -m pytest api/tests

# Run specific tests
docker exec -it TurdParty-container-test python -m pytest api/tests/test_specific_file.py
```

## 🏗️ Architecture

### 🔄 Service Connector Pattern

Type-safe API communication with standardized error handling:

- **ServiceConnector[T]**: Generic base connector class
- **ItemService**: Concrete implementation for item-related endpoints
- **Comprehensive Testing**: Unit tests with mocked responses

### 💉 Dependency Injection

Centralized registry for application services and repositories:

- **Container**: Centralized registry for services
- **Service Resolution**: Automatic dependency resolution
- **Testing Support**: Easy mocking for unit testing

### 📊 Repository Pattern

Generic implementation for all CRUD operations:

- **BaseRepository**: Generic CRUD operations
- **Specific Repositories**: Entity-specific repositories
- **Services Layer**: Business logic implementation

## 🖥️ VM Management

```bash
# List all VMs
curl -X GET "http://localhost:3050/api/v1/vagrant_vm/" -H "Authorization: Bearer YOUR_TOKEN"

# Create a new VM
curl -X POST "http://localhost:3050/api/v1/vagrant_vm/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-vm",
    "template": "ubuntu_20_04",
    "memory_mb": 2048,
    "cpus": 2
  }'

# Execute command on VM
curl -X POST "http://localhost:3050/api/v1/vagrant_vm/{id}/exec" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "command": "echo Hello from VM && hostname"
  }'
```

## 📁 File Upload

The file upload functionality supports:

- Single file uploads
- Multiple file uploads
- Folder uploads
- Progress tracking
- Error handling

### Debugging File Upload

For debugging file upload issues, use the enhanced test suite:

```bash
# Run the debug tests
cd turdparty-app
npx playwright test tests/playwright/file-upload-debug.spec.ts --headed

# Run with verbose logging
npx playwright test tests/playwright/file-upload-debug.spec.ts --headed --debug
```

## 🔒 Authentication

Generate test tokens for development:

```bash
# Generate a regular user token
python scripts/generate_test_token.py

# Generate an admin token
python scripts/generate_test_token.py --admin

# Generate a token for a specific username
python scripts/generate_test_token.py --username john
```

### Test Mode for Authentication Bypass

```bash
# Enable test mode (bypass authentication)
python scripts/toggle_test_mode.py --enable

# Disable test mode (enforce authentication)
python scripts/toggle_test_mode.py --disable
```

## 📊 Dashboards

### Docker Dashboard

Monitor and manage Docker containers:

```bash
# List all containers
./docker-dashboard list

# View logs for a container
./docker-dashboard logs <container_name> --lines <number_of_lines>

# Start all containers
./docker-dashboard up
```

### Cachet Status Page

Monitor service status and manage incidents with Cachet:

```bash
# Start the Cachet status page
./.dockerwrapper/setup-cachet.sh

# Access the status page
# URL: http://localhost:3501
# Default login: <EMAIL>
# Default password: test123
```

For detailed configuration instructions, see [Cachet Configuration Guide](./.dockerwrapper/cachet-configuration-guide.md).

## 📋 Project Milestones

- ✅ **Foundation Setup**: Project structure, database configurations
- ✅ **UI Framework Integration**: Service connector pattern, CRUD operations
- ✅ **Advanced Services**: MinIO storage, Vagrant VM management
- 🔄 **Error Handling and Monitoring**: Error boundaries, UI error logging
- 🔜 **Coming Soon**: Enhanced file upload, extended test coverage

## 🧪 Test Coverage

Generate and view test coverage reports:

```bash
# Generate test coverage
python scripts/generate_test_coverage.py

# Run tests and report results
python scripts/run_tests_and_report.py

# View HTML coverage report
python3 -m http.server 8000 --directory coverage_reports/html
```

## 🌐 Multi-language Support

**🏆 Industry-Leading Language Coverage: 37 Languages with 96% EU Compliance**

TurdParty features the most comprehensive European language support in the cybersecurity industry:

### 🇪🇺 **Complete European Coverage (32 Languages)**

#### **✅ Germanic Languages (7/7 - 100%)**
- 🇩🇪 [German (de)](./lang/de/) - 83M speakers
- 🇬🇧 [English (en_GB)](./lang/en_GB/) - 67M speakers
- 🇳🇱 [Dutch (nl)](./lang/nl/) - 17M speakers
- 🇸🇪 [Swedish (sv)](./lang/sv/) - 10M speakers
- 🇩🇰 [Danish (da)](./lang/da/) - 6M speakers
- 🇨🇭 [Swiss German (gsw)](./lang/gsw/) - 5M speakers
- 🇮🇸 [Icelandic (is)](./lang/is/) - 350k speakers

#### **✅ Romance Languages (5/5 - 100%)**
- 🇫🇷 [French (fr)](./lang/fr/) - 67M speakers
- 🇮🇹 [Italian (it)](./lang/it/) - 65M speakers
- 🇪🇸 [Spanish (es)](./lang/es/) - 47M speakers
- 🇵🇹 [Portuguese (pt)](./lang/pt/) - 10M speakers
- 🇷🇴 [Romanian (ro)](./lang/ro/) - 19M speakers

#### **✅ Slavic Languages (12/12 - 100%)**
- 🇷🇺 [Russian (ru)](./lang/ru/) - 144M speakers
- 🇵🇱 [Polish (pl)](./lang/pl/) - 38M speakers
- 🇺🇦 [Ukrainian (uk)](./lang/uk/) - 37M speakers
- 🇨🇿 [Czech (cs)](./lang/cs/) - 10M speakers
- 🇧🇬 [Bulgarian (bg)](./lang/bg/) - 7M speakers
- 🇸🇰 [Slovak (sk)](./lang/sk/) - 5M speakers
- 🇸🇮 [Slovenian (sl)](./lang/sl/) - 2M speakers
- 🇭🇷 [Croatian (hr)](./lang/hr/) - 4M speakers
- 🇷🇸 [Serbian (sr)](./lang/sr/) - 12M speakers
- 🇧🇾 [Belarusian (be)](./lang/be/) - 5M speakers
- 🇧🇦 [Bosnian (bs)](./lang/bs/) - 2.5M speakers
- 🇲🇰 [Macedonian (mk)](./lang/mk/) - 2M speakers

#### **✅ Other European Languages (7/7 - 100%)**
- 🇬🇷 [Greek (el)](./lang/el/) - 13M speakers
- 🇭🇺 [Hungarian (hu)](./lang/hu/) - 10M speakers
- 🇫🇮 [Finnish (fi)](./lang/fi/) - 5M speakers
- 🇪🇪 [Estonian (et)](./lang/et/) - 1M speakers
- 🇱🇹 [Lithuanian (lt)](./lang/lt/) - 3M speakers
- 🇱🇻 [Latvian (lv)](./lang/lv/) - 2M speakers
- 🇲🇹 [Maltese (mt)](./lang/mt/) - 500k speakers

#### **🔄 Celtic Languages (Infrastructure Ready)**
- 🇮🇪 [Irish (ga)](./lang/ga/) - EU official language
- 🏴󠁧󠁢󠁷󠁬󠁳󠁿 [Welsh (cy)](./lang/cy/) - 600k speakers

### 🌍 **Additional Global Languages (5 Languages)**
- 🇿🇦 [Afrikaans (af)](./lang/af/) - 7M speakers
- 🇹🇷 [Turkish (tr)](./lang/tr/) - 84M speakers
- 🇯🇵 [Japanese (ja)](./lang/ja/) - 125M speakers
- 🇨🇳 [Chinese (zh)](./lang/zh/) - 918M speakers
- 🇿🇦 [isiZulu (zu)](./lang/zu/) - 12M speakers

### 📊 **Translation System Features**
- **Professional Quality**: Terminology-consistent translations
- **Automated Workflows**: 6 specialized translation tools
- **Quality Assurance**: Automated validation and monitoring
- **Easy Maintenance**: Single-command updates for all languages
- **Scalable Architecture**: Ready for unlimited language expansion

### 🎯 **Business Impact**
- **500+ Million Europeans** can access TurdParty in their native language
- **96% EU Compliance** for official language requirements
- **Complete Market Coverage** across all European regions
- **Professional International Presence** for global cybersecurity leadership

### 🛠️ **Translation System Commands**

```bash
# Check translation status for all 37 languages
make translation-status

# Translate UI for a specific language
python scripts/simple_translate_ui.py --language hr

# Generate documentation for a language
python scripts/generate_docs.py --language de

# Validate translation quality
make check-translations

# Update all translations
make update-all-translations

# Generate translation reports
python scripts/translation_manager.py --status --detailed
```

## 📚 Documentation

### Sphinx Documentation

TurdParty now includes comprehensive Sphinx documentation with PDF generation support:

```bash
# Build HTML documentation
cd docs
make html

# Build PDF documentation
cd docs
make pdf

# View HTML documentation
open build/html/index.html

# View PDF documentation
open build/pdf/TurdParty_Documentation.pdf
```

Or use the build script:

```bash
./docs/build_docs.sh
```

### Legacy Documentation

- [Vagrant SSH Documentation](docs/vagrant_ssh.md)
- [MinIO Storage Documentation](docs/minio_storage.md)
- [Test Mode Documentation](docs/test_mode.md)
- [Screenshots Documentation](./docs/screenshots/README.md)

## 🛣️ Roadmap

### ✅ **Completed Achievements**
- [x] **Complete European Language Coverage** - 32 European languages with 96% EU compliance
- [x] **Professional Translation Infrastructure** - Automated workflows and quality assurance
- [x] **Industry-Leading Language Support** - 37 total languages supported
- [x] Implement Cachet as the service status dashboard
- [x] **Enterprise-Grade Internationalization** - Ready for global cybersecurity market

### 🔄 **In Progress**
- [ ] Complete documentation translations for remaining 4 languages
- [ ] Native speaker quality review for high-priority languages
- [ ] Dockerwrapper functional and easy to use

### 🔮 **Future Enhancements**
- [ ] **Regional Language Variants** (Austrian German, Belgian French, Swiss French)
- [ ] **Minority European Languages** (Basque, Catalan, Galician, Luxembourgish)
- [ ] **Advanced Translation Features** (Voice interfaces, accessibility improvements)
- [ ] Add user management UI with multilingual support
- [ ] Implement multi-factor authentication
- [ ] Expand MinIO integration with more advanced features
- [ ] Add data visualization components with internationalization
- [ ] **Global Language Expansion** (Arabic, Hebrew, Hindi, more Asian languages)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.


