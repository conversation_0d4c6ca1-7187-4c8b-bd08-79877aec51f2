{ pkgs ? import <nixpkgs> {} }:

with pkgs;

mkShell {
  buildInputs = [
    python3
    python3Packages.pytest
    python3Packages.fastapi
    python3Packages.sqlalchemy
    python3Packages.requests
    python3Packages.paramiko
    python3Packages.httpx
    python3Packages.pyjwt
    python3Packages.cryptography
    python3Packages.pydantic
    python3Packages.pydantic-settings
    python3Packages.flask
    python3Packages.psycopg2
    python3Packages.asyncpg
    python3Packages.passlib
    python3Packages.email-validator
    python3Packages.boto3
    python3Packages.pyotp
    python3Packages.qrcode
    python3Packages.python-multipart
    python3Packages.minio
    python3Packages.pip
    python3Packages.pytest-asyncio
    python3Packages.aiosqlite
    python3Packages.coverage
    python3Packages.playwright
    python3Packages.celery
    python3Packages.redis
    python3Packages.flower
  ];

  shellHook = ''
    export NIXPKGS_ALLOW_UNFREE=1
    export SSL_CERT_FILE="${cacert}/etc/ssl/certs/ca-bundle.crt"

    echo ""
    echo "TurdParty Test Environment"
    echo "=========================="
    echo ""
  '';
}
