"""
Models for VM injection feature
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Union
from datetime import datetime
from enum import Enum
import uuid


class InjectionStatus(str, Enum):
    """Injection status enum"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class VMInjectionBaseSchema(BaseModel):
    """Base VM injection schema"""
    vagrant_vm_id: uuid.UUID
    file_selection_id: uuid.UUID
    description: Optional[str] = None


class VMInjectionCreateSchema(VMInjectionBaseSchema):
    """VM injection creation schema"""
    additional_command: Optional[str] = Field(None, description="Additional command to run after injection")


class VMInjectionFrontendSchema(BaseModel):
    """VM injection schema for frontend requests"""
    file_upload_id: Union[uuid.UUID, str]
    template_id: Union[uuid.UUID, str]
    target_path: Optional[str] = Field("/app", description="Target path on the VM")
    permissions: Optional[str] = Field("0755", description="File permissions")
    description: Optional[str] = None
    additional_command: Optional[str] = None


class VMInjectionUpdateSchema(BaseModel):
    """VM injection update schema"""
    description: Optional[str] = None
    additional_command: Optional[str] = None


class VMInjectionSchema(BaseModel):
    """VM injection response schema"""
    id: uuid.UUID
    vagrant_vm_id: uuid.UUID
    file_selection_id: uuid.UUID
    description: Optional[str] = None
    status: str
    additional_command: Optional[str] = None
    error_message: Optional[str] = None
    created_on: datetime
    modified_on: Optional[datetime] = None
    completed_on: Optional[datetime] = None
    owner_id: uuid.UUID

    # Include information about the VM and file
    vm_info: Optional[Dict] = None
    file_info: Optional[Dict] = None

    class Config:
        orm_mode = True


class VMInjectionListSchema(BaseModel):
    """List of VM injections"""
    items: List[VMInjectionSchema]
    total: int

    class Config:
        orm_mode = True


class VMInjectionStatusSchema(BaseModel):
    """VM injection status schema"""
    id: uuid.UUID
    vagrant_vm_id: uuid.UUID
    file_selection_id: uuid.UUID
    status: str
    error_message: Optional[str] = None
    completed_on: Optional[datetime] = None
