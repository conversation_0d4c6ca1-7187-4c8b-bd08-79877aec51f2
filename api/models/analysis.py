"""
Data models for binary analysis with Inspektor Gadget.

These models define the structure for:
- Analysis requests and responses
- Analysis status tracking
- Event data schemas
- Results aggregation
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from uuid import UUID
from datetime import datetime
from enum import Enum


class AnalysisStatus(str, Enum):
    """Analysis status enumeration."""
    CREATED = "created"
    STARTING = "starting"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    STOPPED = "stopped"


class VMTemplate(str, Enum):
    """Supported VM templates."""
    UBUNTU_22_04 = "ubuntu-22.04"
    CENTOS_STREAM_9 = "centos-stream-9"
    DEBIAN_12 = "debian-12"
    FEDORA_39 = "fedora-39"
    ALPINE_3_19 = "alpine-3.19"


class GadgetType(str, Enum):
    """Supported Inspektor Gadget types."""
    TRACE_EXEC = "trace_exec"
    TRACE_TCP = "trace_tcp"
    TRACE_DNS = "trace_dns"
    TRACE_OPEN = "trace_open"
    TRACE_CAPABILITIES = "trace_capabilities"
    TRACE_MOUNT = "trace_mount"
    TRACE_FSSLOWER = "trace_fsslower"
    TRACE_BIND = "trace_bind"
    TRACE_SSL = "trace_ssl"
    TRACE_SIGNAL = "trace_signal"
    AUDIT_SECCOMP = "audit_seccomp"
    TOP_PROCESS = "top_process"
    TOP_FILE = "top_file"
    TOP_TCP = "top_tcp"
    SNAPSHOT_PROCESS = "snapshot_process"
    SNAPSHOT_SOCKET = "snapshot_socket"


class EventCategory(str, Enum):
    """Event category classification."""
    PROCESS = "process"
    NETWORK = "network"
    FILESYSTEM = "filesystem"
    SECURITY = "security"
    PERFORMANCE = "performance"


class Analysis(BaseModel):
    """Analysis model for database storage."""
    id: str = Field(..., description="Unique analysis identifier")
    file_uuid: UUID = Field(..., description="UUID of the analyzed file")
    vm_template: str = Field(..., description="VM template used for analysis")
    config: Dict[str, Any] = Field(default_factory=dict, description="Analysis configuration")
    timeout: int = Field(default=300, ge=60, le=3600, description="Analysis timeout in seconds")
    gadgets: List[str] = Field(default_factory=list, description="List of gadgets to run")
    user_id: str = Field(..., description="ID of the user who started the analysis")
    status: AnalysisStatus = Field(default=AnalysisStatus.CREATED, description="Current analysis status")
    started_at: datetime = Field(default_factory=datetime.utcnow, description="Analysis start time")
    completed_at: Optional[datetime] = Field(None, description="Analysis completion time")
    error_message: Optional[str] = Field(None, description="Error message if analysis failed")
    vm_id: Optional[str] = Field(None, description="ID of the VM used for analysis")
    
    @validator('gadgets')
    def validate_gadgets(cls, v):
        """Validate that all gadgets are supported."""
        valid_gadgets = [gadget.value for gadget in GadgetType]
        for gadget in v:
            if gadget not in valid_gadgets:
                raise ValueError(f"Unsupported gadget: {gadget}")
        return v
    
    @validator('vm_template')
    def validate_vm_template(cls, v):
        """Validate VM template."""
        valid_templates = [template.value for template in VMTemplate]
        if v not in valid_templates:
            raise ValueError(f"Unsupported VM template: {v}")
        return v


class AnalysisRequest(BaseModel):
    """Request model for starting analysis."""
    file_uuid: UUID = Field(..., description="UUID of the file to analyze")
    vm_template: VMTemplate = Field(..., description="VM template to use")
    analysis_config: Dict[str, Any] = Field(default_factory=dict, description="Analysis configuration")
    timeout: int = Field(default=300, ge=60, le=3600, description="Analysis timeout in seconds")
    gadgets: List[GadgetType] = Field(
        default=[
            GadgetType.TRACE_EXEC,
            GadgetType.TRACE_TCP,
            GadgetType.TRACE_OPEN,
            GadgetType.TRACE_DNS,
            GadgetType.TRACE_CAPABILITIES
        ],
        description="List of gadgets to run"
    )
    capture_network: bool = Field(default=True, description="Enable network capture")
    capture_filesystem: bool = Field(default=True, description="Enable filesystem capture")
    capture_security: bool = Field(default=True, description="Enable security event capture")


class AnalysisResponse(BaseModel):
    """Response model for analysis creation."""
    analysis_id: str = Field(..., description="Unique analysis identifier")
    status: AnalysisStatus = Field(..., description="Initial analysis status")
    vm_id: str = Field(..., description="ID of the provisioned VM")
    estimated_completion: datetime = Field(..., description="Estimated completion time")


class EventData(BaseModel):
    """Base model for event data from Inspektor Gadget."""
    timestamp: datetime = Field(..., description="Event timestamp")
    file_uuid: UUID = Field(..., description="Correlation UUID from file upload")
    gadget_type: GadgetType = Field(..., description="Type of gadget that generated the event")
    event_category: EventCategory = Field(..., description="Event category")
    event_type: str = Field(..., description="Specific event type")
    correlation_id: str = Field(..., description="Correlation ID for tracking")
    vm_id: str = Field(..., description="VM where the event occurred")
    
    # Common fields across all events
    pid: Optional[int] = Field(None, description="Process ID")
    uid: Optional[int] = Field(None, description="User ID")
    gid: Optional[int] = Field(None, description="Group ID")
    comm: Optional[str] = Field(None, description="Command name")


class ProcessEvent(EventData):
    """Process execution event data."""
    event_category: EventCategory = Field(default=EventCategory.PROCESS, const=True)
    ppid: Optional[int] = Field(None, description="Parent process ID")
    cmdline: Optional[str] = Field(None, description="Command line arguments")
    cwd: Optional[str] = Field(None, description="Current working directory")
    environment: Optional[List[str]] = Field(None, description="Environment variables")
    exit_code: Optional[int] = Field(None, description="Process exit code")


class NetworkEvent(EventData):
    """Network activity event data."""
    event_category: EventCategory = Field(default=EventCategory.NETWORK, const=True)
    src_ip: Optional[str] = Field(None, description="Source IP address")
    dst_ip: Optional[str] = Field(None, description="Destination IP address")
    src_port: Optional[int] = Field(None, description="Source port")
    dst_port: Optional[int] = Field(None, description="Destination port")
    protocol: Optional[str] = Field(None, description="Network protocol")
    dns_query: Optional[str] = Field(None, description="DNS query name")
    dns_response: Optional[str] = Field(None, description="DNS response")
    bytes_sent: Optional[int] = Field(None, description="Bytes sent")
    bytes_received: Optional[int] = Field(None, description="Bytes received")


class FilesystemEvent(EventData):
    """Filesystem operation event data."""
    event_category: EventCategory = Field(default=EventCategory.FILESYSTEM, const=True)
    filename: Optional[str] = Field(None, description="File path")
    operation: Optional[str] = Field(None, description="File operation type")
    flags: Optional[str] = Field(None, description="File operation flags")
    mode: Optional[str] = Field(None, description="File permissions")
    size: Optional[int] = Field(None, description="File size")
    duration_ms: Optional[float] = Field(None, description="Operation duration in milliseconds")


class SecurityEvent(EventData):
    """Security-related event data."""
    event_category: EventCategory = Field(default=EventCategory.SECURITY, const=True)
    capability: Optional[str] = Field(None, description="Capability being checked")
    seccomp_action: Optional[str] = Field(None, description="Seccomp action taken")
    syscall: Optional[str] = Field(None, description="System call name")
    audit_result: Optional[str] = Field(None, description="Audit result")
    risk_level: Optional[str] = Field(None, description="Risk assessment level")


class AnalysisSummary(BaseModel):
    """Summary statistics for completed analysis."""
    total_events: int = Field(default=0, description="Total number of events captured")
    process_events: int = Field(default=0, description="Number of process events")
    network_events: int = Field(default=0, description="Number of network events")
    file_events: int = Field(default=0, description="Number of file events")
    security_events: int = Field(default=0, description="Number of security events")
    unique_processes: int = Field(default=0, description="Number of unique processes")
    unique_files: int = Field(default=0, description="Number of unique files accessed")
    unique_network_connections: int = Field(default=0, description="Number of unique network connections")
    risk_score: Optional[float] = Field(None, ge=0.0, le=10.0, description="Overall risk score")
    
    # Time-based statistics
    analysis_duration: Optional[int] = Field(None, description="Analysis duration in seconds")
    first_event_time: Optional[datetime] = Field(None, description="Timestamp of first event")
    last_event_time: Optional[datetime] = Field(None, description="Timestamp of last event")
    
    # Top items
    top_processes: Optional[List[Dict[str, Any]]] = Field(None, description="Most active processes")
    top_files: Optional[List[Dict[str, Any]]] = Field(None, description="Most accessed files")
    top_network_destinations: Optional[List[Dict[str, Any]]] = Field(None, description="Most contacted destinations")


class AnalysisResults(BaseModel):
    """Complete analysis results."""
    analysis_id: str = Field(..., description="Analysis identifier")
    file_uuid: UUID = Field(..., description="Original file UUID")
    vm_template: str = Field(..., description="VM template used")
    status: AnalysisStatus = Field(..., description="Final analysis status")
    summary: AnalysisSummary = Field(..., description="Analysis summary statistics")
    elasticsearch_indices: List[str] = Field(..., description="Elasticsearch indices containing the data")
    kibana_dashboard_url: str = Field(..., description="URL to Kibana dashboard")
    started_at: datetime = Field(..., description="Analysis start time")
    completed_at: Optional[datetime] = Field(None, description="Analysis completion time")
    total_duration: Optional[int] = Field(None, description="Total analysis duration in seconds")
    
    # Data export options
    raw_data_available: bool = Field(default=True, description="Whether raw event data is available")
    export_formats: List[str] = Field(
        default=["json", "csv", "elasticsearch"],
        description="Available export formats"
    )
    
    # Quality metrics
    data_completeness: Optional[float] = Field(None, ge=0.0, le=1.0, description="Data completeness ratio")
    capture_errors: Optional[int] = Field(None, description="Number of capture errors")
    
    @validator('total_duration')
    def calculate_duration(cls, v, values):
        """Calculate duration if not provided."""
        if v is None and 'started_at' in values and 'completed_at' in values:
            if values['completed_at']:
                delta = values['completed_at'] - values['started_at']
                return int(delta.total_seconds())
        return v


class AnalysisFilter(BaseModel):
    """Filter options for analysis queries."""
    status: Optional[AnalysisStatus] = Field(None, description="Filter by status")
    vm_template: Optional[VMTemplate] = Field(None, description="Filter by VM template")
    user_id: Optional[str] = Field(None, description="Filter by user ID")
    date_from: Optional[datetime] = Field(None, description="Filter analyses from this date")
    date_to: Optional[datetime] = Field(None, description="Filter analyses to this date")
    file_uuid: Optional[UUID] = Field(None, description="Filter by file UUID")
    
    # Pagination
    limit: int = Field(default=50, ge=1, le=100, description="Maximum number of results")
    offset: int = Field(default=0, ge=0, description="Number of results to skip")
    
    # Sorting
    sort_by: str = Field(default="started_at", description="Field to sort by")
    sort_order: str = Field(default="desc", regex="^(asc|desc)$", description="Sort order")
