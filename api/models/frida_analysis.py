"""
Frida Analysis Database Models

SQLAlchemy models for Frida analysis sessions and related data.
Integrates with existing TurdParty database schema.
"""

from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, JSON, <PERSON><PERSON>ey, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum
from datetime import datetime
from typing import Optional, Dict, Any

from .base import Base


class AnalysisStatus(enum.Enum):
    """Analysis session status enumeration."""
    PENDING = "pending"
    PROVISIONING_VM = "provisioning_vm"
    TRANSFERRING_BINARY = "transferring_binary"
    STARTING_FRIDA = "starting_frida"
    RUNNING = "running"
    STOPPING = "stopping"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class AnalysisType(enum.Enum):
    """Analysis type enumeration."""
    RUNTIME = "runtime"
    INSTALL_FOOTPRINT = "install_footprint"
    NETWORK_ANALYSIS = "network_analysis"
    FILE_ANALYSIS = "file_analysis"
    QUICK_SCAN = "quick_scan"
    MALWARE_ANALYSIS = "malware_analysis"


class FridaAnalysisSession(Base):
    """
    Frida analysis session model.
    
    Tracks the lifecycle and metadata of Frida analysis sessions.
    """
    __tablename__ = "frida_analysis_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Session identification
    session_id = Column(String(255), unique=True, nullable=False, index=True)
    
    # Related entities
    binary_uuid = Column(UUID(as_uuid=True), ForeignKey("file_uploads.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    vm_id = Column(String(255), nullable=True)  # VM identifier from coordinator
    
    # Analysis configuration
    analysis_type = Column(Enum(AnalysisType), nullable=False, default=AnalysisType.RUNTIME)
    profile = Column(String(100), nullable=False, default="default")
    vm_template = Column(String(100), nullable=False, default="ubuntu_22")
    duration = Column(Integer, nullable=False, default=300)  # seconds
    
    # Session status and lifecycle
    status = Column(Enum(AnalysisStatus), nullable=False, default=AnalysisStatus.PENDING)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # VM and Frida details
    vm_ip = Column(String(45), nullable=True)  # IPv4/IPv6 address
    frida_pid = Column(Integer, nullable=True)
    frida_port = Column(Integer, nullable=True, default=27042)
    
    # Results and metadata
    artifacts_count = Column(Integer, nullable=False, default=0)
    events_count = Column(Integer, nullable=False, default=0)
    error_message = Column(Text, nullable=True)
    stop_reason = Column(String(100), nullable=True)
    
    # Configuration and metadata
    config = Column(JSON, nullable=True)
    metadata = Column(JSON, nullable=True)
    
    # Inspector Gadget integration
    inspector_gadget_enabled = Column(Boolean, nullable=False, default=True)
    inspector_gadget_session_id = Column(String(255), nullable=True)
    
    # Relationships
    binary_file = relationship("FileUpload", back_populates="frida_sessions")
    user = relationship("User", back_populates="frida_sessions")
    artifacts = relationship("FridaAnalysisArtifact", back_populates="session", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<FridaAnalysisSession(session_id='{self.session_id}', status='{self.status}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "id": str(self.id),
            "session_id": self.session_id,
            "binary_uuid": str(self.binary_uuid),
            "user_id": str(self.user_id),
            "vm_id": self.vm_id,
            "analysis_type": self.analysis_type.value if self.analysis_type else None,
            "profile": self.profile,
            "vm_template": self.vm_template,
            "duration": self.duration,
            "status": self.status.value if self.status else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "vm_ip": self.vm_ip,
            "frida_pid": self.frida_pid,
            "frida_port": self.frida_port,
            "artifacts_count": self.artifacts_count,
            "events_count": self.events_count,
            "error_message": self.error_message,
            "stop_reason": self.stop_reason,
            "config": self.config,
            "metadata": self.metadata,
            "inspector_gadget_enabled": self.inspector_gadget_enabled,
            "inspector_gadget_session_id": self.inspector_gadget_session_id
        }


class ArtifactType(enum.Enum):
    """Artifact type enumeration."""
    FILE_CHANGES = "file_changes"
    REGISTRY_CHANGES = "registry_changes"
    NETWORK_DATA = "network_data"
    MEMORY_DUMP = "memory_dump"
    SCREENSHOT = "screenshot"
    LOG_FILE = "log_file"
    ANALYSIS_REPORT = "analysis_report"
    FRIDA_SCRIPT = "frida_script"
    INSPECTOR_GADGET_DATA = "inspector_gadget_data"


class FridaAnalysisArtifact(Base):
    """
    Frida analysis artifact model.
    
    Tracks individual artifacts generated during analysis sessions.
    """
    __tablename__ = "frida_analysis_artifacts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Related session
    session_id = Column(UUID(as_uuid=True), ForeignKey("frida_analysis_sessions.id"), nullable=False)
    
    # Artifact details
    artifact_type = Column(Enum(ArtifactType), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Storage details
    minio_bucket = Column(String(255), nullable=False)
    minio_object_name = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=True)
    content_type = Column(String(100), nullable=True)
    checksum = Column(String(64), nullable=True)  # SHA-256
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Metadata
    metadata = Column(JSON, nullable=True)
    
    # Relationships
    session = relationship("FridaAnalysisSession", back_populates="artifacts")
    
    def __repr__(self):
        return f"<FridaAnalysisArtifact(name='{self.name}', type='{self.artifact_type}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "id": str(self.id),
            "session_id": str(self.session_id),
            "artifact_type": self.artifact_type.value if self.artifact_type else None,
            "name": self.name,
            "description": self.description,
            "minio_bucket": self.minio_bucket,
            "minio_object_name": self.minio_object_name,
            "file_size": self.file_size,
            "content_type": self.content_type,
            "checksum": self.checksum,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "metadata": self.metadata
        }


class FridaAnalysisEvent(Base):
    """
    Frida analysis event model.
    
    Stores high-level events and summaries from analysis sessions.
    Detailed events are stored in Elasticsearch.
    """
    __tablename__ = "frida_analysis_events"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Related session
    session_id = Column(UUID(as_uuid=True), ForeignKey("frida_analysis_sessions.id"), nullable=False)
    
    # Event details
    event_type = Column(String(100), nullable=False)
    event_category = Column(String(100), nullable=True)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    
    # Event data
    summary = Column(Text, nullable=True)
    details = Column(JSON, nullable=True)
    
    # Correlation with Inspector Gadget
    inspector_gadget_correlation = Column(JSON, nullable=True)
    
    # Elasticsearch reference
    elasticsearch_id = Column(String(255), nullable=True)
    elasticsearch_index = Column(String(255), nullable=True)
    
    # Relationships
    session = relationship("FridaAnalysisSession")
    
    def __repr__(self):
        return f"<FridaAnalysisEvent(type='{self.event_type}', timestamp='{self.timestamp}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "id": str(self.id),
            "session_id": str(self.session_id),
            "event_type": self.event_type,
            "event_category": self.event_category,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "summary": self.summary,
            "details": self.details,
            "inspector_gadget_correlation": self.inspector_gadget_correlation,
            "elasticsearch_id": self.elasticsearch_id,
            "elasticsearch_index": self.elasticsearch_index
        }


class FridaAnalysisTemplate(Base):
    """
    Frida analysis template model.
    
    Stores custom analysis templates and configurations.
    """
    __tablename__ = "frida_analysis_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Template details
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    version = Column(String(50), nullable=False, default="1.0.0")
    
    # Owner
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    is_public = Column(Boolean, nullable=False, default=False)
    
    # Template configuration
    analysis_type = Column(Enum(AnalysisType), nullable=False)
    vm_template = Column(String(100), nullable=False)
    duration = Column(Integer, nullable=False, default=300)
    
    # Frida configuration
    frida_scripts = Column(JSON, nullable=True)  # List of script configurations
    api_hooks = Column(JSON, nullable=True)  # List of API categories to hook
    monitoring_config = Column(JSON, nullable=True)  # Monitoring settings
    
    # Inspector Gadget integration
    inspector_gadget_config = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    
    # Usage statistics
    usage_count = Column(Integer, nullable=False, default=0)
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self):
        return f"<FridaAnalysisTemplate(name='{self.name}', version='{self.version}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "user_id": str(self.user_id),
            "is_public": self.is_public,
            "analysis_type": self.analysis_type.value if self.analysis_type else None,
            "vm_template": self.vm_template,
            "duration": self.duration,
            "frida_scripts": self.frida_scripts,
            "api_hooks": self.api_hooks,
            "monitoring_config": self.monitoring_config,
            "inspector_gadget_config": self.inspector_gadget_config,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "usage_count": self.usage_count
        }


# Add relationships to existing models
def add_frida_relationships():
    """Add Frida relationships to existing models."""
    try:
        from .file_upload import FileUpload
        from .user import User
        
        # Add relationship to FileUpload model
        if not hasattr(FileUpload, 'frida_sessions'):
            FileUpload.frida_sessions = relationship("FridaAnalysisSession", back_populates="binary_file")
        
        # Add relationship to User model
        if not hasattr(User, 'frida_sessions'):
            User.frida_sessions = relationship("FridaAnalysisSession", back_populates="user")
            
    except ImportError:
        # Models not available yet, relationships will be added later
        pass
