"""
Proxy server for Vagrant gRPC service.

This server listens on all interfaces (0.0.0.0) and forwards requests to the Vagrant gRPC service
running on localhost:40000.
"""
import asyncio
import logging
import os
import sys
import grpc
from concurrent import futures

# Add the parent directory to the path so we can import the generated modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the generated modules
try:
    from api.grpc import vagrant_pb2, vagrant_pb2_grpc
except ImportError:
    print("Could not import generated gRPC modules. Run scripts/generate_grpc.py first.")
    sys.exit(1)

logger = logging.getLogger(__name__)

class VagrantServiceProxy(vagrant_pb2_grpc.VagrantServiceServicer):
    """Proxy for the Vagrant gRPC service."""
    
    def __init__(self, target_addr):
        """Initialize the proxy with the target address."""
        self.target_addr = target_addr
        self.channel = None
        self.stub = None
        logger.info("VagrantServiceProxy initialized with target %s", target_addr)
    
    async def connect(self):
        """Connect to the target service."""
        self.channel = grpc.aio.insecure_channel(self.target_addr)
        self.stub = vagrant_pb2_grpc.VagrantServiceStub(self.channel)
        logger.info("Connected to target service at %s", self.target_addr)
    
    async def Status(self, request, context):
        """Forward Status request to the target service."""
        if not self.stub:
            await self.connect()
        
        try:
            response = await self.stub.Status(request)
            return response
        except Exception as e:
            logger.error("Error forwarding Status request: %s", str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantStatusResponse(
                status="",
                error_message=f"Proxy error: {str(e)}"
            )
    
    async def Up(self, request, context):
        """Forward Up request to the target service."""
        if not self.stub:
            await self.connect()
        
        try:
            response = await self.stub.Up(request)
            return response
        except Exception as e:
            logger.error("Error forwarding Up request: %s", str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=f"Proxy error: {str(e)}"
            )
    
    async def Halt(self, request, context):
        """Forward Halt request to the target service."""
        if not self.stub:
            await self.connect()
        
        try:
            response = await self.stub.Halt(request)
            return response
        except Exception as e:
            logger.error("Error forwarding Halt request: %s", str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=f"Proxy error: {str(e)}"
            )
    
    async def Destroy(self, request, context):
        """Forward Destroy request to the target service."""
        if not self.stub:
            await self.connect()
        
        try:
            response = await self.stub.Destroy(request)
            return response
        except Exception as e:
            logger.error("Error forwarding Destroy request: %s", str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantCommandResponse(
                success=False,
                message="",
                error_message=f"Proxy error: {str(e)}"
            )
    
    async def ListBoxes(self, request, context):
        """Forward ListBoxes request to the target service."""
        if not self.stub:
            await self.connect()
        
        try:
            response = await self.stub.ListBoxes(request)
            return response
        except Exception as e:
            logger.error("Error forwarding ListBoxes request: %s", str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantBoxesResponse(
                boxes=[],
                error_message=f"Proxy error: {str(e)}"
            )
    
    async def GetMachineInfo(self, request, context):
        """Forward GetMachineInfo request to the target service."""
        if not self.stub:
            await self.connect()
        
        try:
            response = await self.stub.GetMachineInfo(request)
            return response
        except Exception as e:
            logger.error("Error forwarding GetMachineInfo request: %s", str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantMachineInfoResponse(
                name="",
                provider="",
                state="",
                directory="",
                network={},
                error_message=f"Proxy error: {str(e)}"
            )
    
    async def ExecuteCommand(self, request, context):
        """Forward ExecuteCommand request to the target service."""
        if not self.stub:
            await self.connect()
        
        try:
            response = await self.stub.ExecuteCommand(request)
            return response
        except Exception as e:
            logger.error("Error forwarding ExecuteCommand request: %s", str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return vagrant_pb2.VagrantExecuteCommandResponse(
                success=False,
                stdout="",
                stderr=f"Proxy error: {str(e)}",
                exit_code=-1
            )

async def serve(proxy_port=50051, target_addr="localhost:40000"):
    """
    Start the proxy server.
    
    Args:
        proxy_port: The port to listen on.
        target_addr: The address of the target Vagrant gRPC service.
    """
    server = grpc.aio.server(futures.ThreadPoolExecutor(max_workers=10))
    vagrant_pb2_grpc.add_VagrantServiceServicer_to_server(
        VagrantServiceProxy(target_addr), server
    )
    
    # Listen on all interfaces
    listen_addr = f"0.0.0.0:{proxy_port}"
    server.add_insecure_port(listen_addr)
    
    logger.info(f"Starting Vagrant gRPC proxy server on {listen_addr}")
    logger.info(f"Forwarding requests to {target_addr}")
    
    await server.start()
    
    try:
        await server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("Server stopping...")
        await server.stop(0)
        logger.info("Server stopped")

if __name__ == "__main__":
    import argparse
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Vagrant gRPC proxy server")
    parser.add_argument(
        "--port", type=int, default=50051,
        help="Port to listen on (default: 50051)"
    )
    parser.add_argument(
        "--target", type=str, default="localhost:40000",
        help="Target Vagrant gRPC service address (default: localhost:40000)"
    )
    
    args = parser.parse_args()
    
    # Start the server
    asyncio.run(serve(args.port, args.target))
