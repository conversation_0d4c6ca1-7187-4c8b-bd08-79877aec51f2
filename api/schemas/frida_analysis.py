"""
Frida Analysis Pydantic Schemas

Request/response schemas for Frida analysis API endpoints.
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum
import uuid


class AnalysisStatusEnum(str, Enum):
    """Analysis session status enumeration."""
    PENDING = "pending"
    PROVISIONING_VM = "provisioning_vm"
    TRANSFERRING_BINARY = "transferring_binary"
    STARTING_FRIDA = "starting_frida"
    RUNNING = "running"
    STOPPING = "stopping"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class AnalysisTypeEnum(str, Enum):
    """Analysis type enumeration."""
    RUNTIME = "runtime"
    INSTALL_FOOTPRINT = "install_footprint"
    NETWORK_ANALYSIS = "network_analysis"
    FILE_ANALYSIS = "file_analysis"
    QUICK_SCAN = "quick_scan"
    MALWARE_ANALYSIS = "malware_analysis"


class ArtifactTypeEnum(str, Enum):
    """Artifact type enumeration."""
    FILE_CHANGES = "file_changes"
    REGISTRY_CHANGES = "registry_changes"
    NETWORK_DATA = "network_data"
    MEMORY_DUMP = "memory_dump"
    SCREENSHOT = "screenshot"
    LOG_FILE = "log_file"
    ANALYSIS_REPORT = "analysis_report"
    FRIDA_SCRIPT = "frida_script"
    INSPECTOR_GADGET_DATA = "inspector_gadget_data"


# Request Schemas

class AnalysisSessionCreateSchema(BaseModel):
    """Schema for creating a new analysis session."""
    binary_uuid: str = Field(..., description="UUID of the binary file to analyze")
    analysis_type: AnalysisTypeEnum = Field(default=AnalysisTypeEnum.RUNTIME, description="Type of analysis to perform")
    profile: str = Field(default="default", description="Analysis profile to use")
    vm_template: str = Field(default="ubuntu_22", description="VM template to use")
    duration: Optional[int] = Field(default=None, ge=60, le=3600, description="Analysis duration in seconds")
    
    @validator('binary_uuid')
    def validate_binary_uuid(cls, v):
        try:
            uuid.UUID(v)
            return v
        except ValueError:
            raise ValueError('Invalid UUID format')


class AnalysisSessionUpdateSchema(BaseModel):
    """Schema for updating analysis session metadata."""
    description: Optional[str] = Field(None, max_length=500, description="Session description")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class AnalysisSessionActionSchema(BaseModel):
    """Schema for performing actions on analysis sessions."""
    action: str = Field(..., description="Action to perform (stop, pause, resume)")
    reason: Optional[str] = Field(default="manual", description="Reason for the action")


# Response Schemas

class AnalysisSessionSchema(BaseModel):
    """Schema for analysis session response."""
    id: str
    session_id: str
    binary_uuid: str
    user_id: str
    vm_id: Optional[str] = None
    analysis_type: AnalysisTypeEnum
    profile: str
    vm_template: str
    duration: int
    status: AnalysisStatusEnum
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    vm_ip: Optional[str] = None
    frida_pid: Optional[int] = None
    frida_port: Optional[int] = None
    artifacts_count: int = 0
    events_count: int = 0
    error_message: Optional[str] = None
    stop_reason: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    inspector_gadget_enabled: bool = True
    inspector_gadget_session_id: Optional[str] = None

    class Config:
        orm_mode = True


class AnalysisSessionListSchema(BaseModel):
    """Schema for paginated analysis session list."""
    items: List[AnalysisSessionSchema]
    total: int
    skip: int = 0
    limit: int = 100


class AnalysisSessionStatusSchema(BaseModel):
    """Schema for analysis session status response."""
    session_id: str
    status: AnalysisStatusEnum
    progress: Optional[float] = Field(None, ge=0.0, le=1.0, description="Progress percentage (0.0 to 1.0)")
    current_phase: Optional[str] = None
    estimated_completion: Optional[datetime] = None
    error_message: Optional[str] = None


class AnalysisArtifactSchema(BaseModel):
    """Schema for analysis artifact response."""
    id: str
    session_id: str
    artifact_type: ArtifactTypeEnum
    name: str
    description: Optional[str] = None
    minio_bucket: str
    minio_object_name: str
    file_size: Optional[int] = None
    content_type: Optional[str] = None
    checksum: Optional[str] = None
    created_at: datetime
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        orm_mode = True


class AnalysisArtifactListSchema(BaseModel):
    """Schema for analysis artifact list response."""
    session_id: str
    artifacts: List[AnalysisArtifactSchema]
    total_count: int


class AnalysisEventSchema(BaseModel):
    """Schema for analysis event response."""
    id: str
    session_id: str
    event_type: str
    event_category: Optional[str] = None
    timestamp: datetime
    summary: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    inspector_gadget_correlation: Optional[Dict[str, Any]] = None
    elasticsearch_id: Optional[str] = None
    elasticsearch_index: Optional[str] = None

    class Config:
        orm_mode = True


class AnalysisEventListSchema(BaseModel):
    """Schema for analysis event list response."""
    session_id: str
    events: List[AnalysisEventSchema]
    total_count: int


class AnalysisProfileSchema(BaseModel):
    """Schema for analysis profile response."""
    name: str
    display_name: str
    description: str
    duration: int
    api_hooks: List[str]
    inspector_gadget_enabled: bool = False
    monitoring: Optional[Dict[str, Any]] = None


class AnalysisProfileListSchema(BaseModel):
    """Schema for analysis profile list response."""
    profiles: List[AnalysisProfileSchema]


class AnalysisTemplateCreateSchema(BaseModel):
    """Schema for creating analysis templates."""
    name: str = Field(..., max_length=255, description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    analysis_type: AnalysisTypeEnum
    vm_template: str = Field(..., description="VM template to use")
    duration: int = Field(default=300, ge=60, le=3600, description="Analysis duration in seconds")
    frida_scripts: Optional[List[Dict[str, Any]]] = Field(None, description="Frida script configurations")
    api_hooks: Optional[List[str]] = Field(None, description="API categories to hook")
    monitoring_config: Optional[Dict[str, Any]] = Field(None, description="Monitoring configuration")
    inspector_gadget_config: Optional[Dict[str, Any]] = Field(None, description="Inspector Gadget configuration")
    is_public: bool = Field(default=False, description="Whether template is public")


class AnalysisTemplateUpdateSchema(BaseModel):
    """Schema for updating analysis templates."""
    name: Optional[str] = Field(None, max_length=255, description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    duration: Optional[int] = Field(None, ge=60, le=3600, description="Analysis duration in seconds")
    frida_scripts: Optional[List[Dict[str, Any]]] = Field(None, description="Frida script configurations")
    api_hooks: Optional[List[str]] = Field(None, description="API categories to hook")
    monitoring_config: Optional[Dict[str, Any]] = Field(None, description="Monitoring configuration")
    inspector_gadget_config: Optional[Dict[str, Any]] = Field(None, description="Inspector Gadget configuration")
    is_public: Optional[bool] = Field(None, description="Whether template is public")


class AnalysisTemplateSchema(BaseModel):
    """Schema for analysis template response."""
    id: str
    name: str
    description: Optional[str] = None
    version: str
    user_id: str
    is_public: bool
    analysis_type: AnalysisTypeEnum
    vm_template: str
    duration: int
    frida_scripts: Optional[List[Dict[str, Any]]] = None
    api_hooks: Optional[List[str]] = None
    monitoring_config: Optional[Dict[str, Any]] = None
    inspector_gadget_config: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    usage_count: int = 0

    class Config:
        orm_mode = True


class AnalysisTemplateListSchema(BaseModel):
    """Schema for analysis template list response."""
    items: List[AnalysisTemplateSchema]
    total: int
    skip: int = 0
    limit: int = 100


class AnalysisSummarySchema(BaseModel):
    """Schema for analysis session summary."""
    session_id: str
    total_events: int
    event_types: List[Dict[str, Union[str, int]]]
    api_functions: List[Dict[str, Union[str, int]]]
    api_categories: List[Dict[str, Union[str, int]]]
    timeline: List[Dict[str, Any]]
    artifacts_summary: Optional[Dict[str, int]] = None
    inspector_gadget_correlation: Optional[Dict[str, Any]] = None


class AnalysisHealthSchema(BaseModel):
    """Schema for Frida analysis system health."""
    status: str
    timestamp: datetime
    components: Dict[str, Dict[str, Any]]
    active_sessions: int = 0
    total_sessions: int = 0
    system_resources: Optional[Dict[str, Any]] = None


class AnalysisSearchSchema(BaseModel):
    """Schema for analysis event search."""
    session_id: Optional[str] = None
    event_type: Optional[str] = None
    api_function: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = Field(default=100, ge=1, le=1000)
    offset: int = Field(default=0, ge=0)


class AnalysisSearchResultSchema(BaseModel):
    """Schema for analysis search results."""
    query: AnalysisSearchSchema
    results: List[Dict[str, Any]]
    total_count: int
    execution_time_ms: Optional[float] = None


class AnalysisStatisticsSchema(BaseModel):
    """Schema for analysis statistics."""
    total_sessions: int
    active_sessions: int
    completed_sessions: int
    failed_sessions: int
    success_rate: float
    average_duration: float
    total_artifacts: int
    total_events: int
    popular_profiles: List[Dict[str, Union[str, int]]]
    recent_activity: List[Dict[str, Any]]
