"""
Frida Analysis API endpoints

Provides comprehensive binary analysis using Frida dynamic instrumentation
integrated with existing TurdParty infrastructure.
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.responses import StreamingResponse, FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import uuid
import json
import logging
from datetime import datetime, timedelta

from db.session import get_db
from utils.auth import get_current_user
from db.schemas.user import UserSchema
from db.models.file_upload import FileUpload
from db.models.vagrant_vm import VagrantVM

# Import our Frida components
from vendor.frida.server.frida_server import FridaAnalysisServer
from vendor.frida.server.session_manager import SessionStatus

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/frida_analysis",
    tags=["frida_analysis"],
    responses={404: {"description": "Analysis session not found"}},
)

# Global Frida server instance
frida_server: Optional[FridaAnalysisServer] = None

async def get_frida_server() -> FridaAnalysisServer:
    """Get or initialize the Frida analysis server."""
    global frida_server
    if frida_server is None:
        frida_server = FridaAnalysisServer()
        await frida_server.start()
    return frida_server

@router.post("/sessions", status_code=status.HTTP_201_CREATED)
async def start_analysis_session(
    binary_uuid: str,
    analysis_type: str = "runtime",
    profile: str = "default",
    vm_template: str = "ubuntu_22",
    duration: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: UserSchema = Depends(get_current_user),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Start a new Frida analysis session.
    
    This endpoint creates a new analysis session for a previously uploaded binary.
    The analysis will be performed in an isolated VM with Frida instrumentation.
    
    Parameters:
    - **binary_uuid**: UUID of the uploaded binary file to analyze
    - **analysis_type**: Type of analysis (runtime, install_footprint, network_analysis, etc.)
    - **profile**: Analysis profile to use (default, malware_analysis, quick_scan, etc.)
    - **vm_template**: VM template to use (ubuntu_22, windows_10, etc.)
    - **duration**: Analysis duration in seconds (optional, uses profile default)
    
    Returns:
    - Analysis session object with session_id and status
    
    Requires authentication and valid binary file.
    """
    try:
        # Verify binary exists and belongs to user
        binary_file = db.query(FileUpload).filter(
            FileUpload.id == binary_uuid,
            FileUpload.owner_id == current_user.id
        ).first()
        
        if not binary_file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Binary file {binary_uuid} not found or access denied"
            )
        
        # Get Frida server
        server = await get_frida_server()
        
        # Start analysis session
        session_id = await server.start_analysis(
            binary_uuid=binary_uuid,
            analysis_type=analysis_type,
            profile=profile,
            vm_template=vm_template,
            duration=duration,
            user_id=str(current_user.id)
        )
        
        logger.info(f"Started Frida analysis session {session_id} for user {current_user.id}")
        
        return {
            "session_id": session_id,
            "binary_uuid": binary_uuid,
            "analysis_type": analysis_type,
            "profile": profile,
            "vm_template": vm_template,
            "status": "starting",
            "created_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start analysis session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start analysis session: {str(e)}"
        )

@router.get("/sessions")
async def list_analysis_sessions(
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None,
    current_user: UserSchema = Depends(get_current_user)
):
    """
    List analysis sessions for the current user.
    
    Parameters:
    - **skip**: Number of records to skip (pagination)
    - **limit**: Maximum number of records to return
    - **status_filter**: Optional status filter (running, completed, failed, etc.)
    
    Returns:
    - List of analysis sessions with metadata
    """
    try:
        server = await get_frida_server()
        
        # Get sessions for user
        sessions = await server.session_manager.list_sessions(
            user_id=str(current_user.id),
            status=SessionStatus(status_filter) if status_filter else None,
            limit=limit
        )
        
        # Apply pagination
        total = len(sessions)
        paginated_sessions = sessions[skip:skip + limit]
        
        return {
            "items": paginated_sessions,
            "total": total,
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"Failed to list analysis sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list analysis sessions: {str(e)}"
        )

@router.get("/sessions/{session_id}")
async def get_analysis_session(
    session_id: str,
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get details of a specific analysis session.
    
    Parameters:
    - **session_id**: UUID of the analysis session
    
    Returns:
    - Analysis session details including status, progress, and metadata
    """
    try:
        server = await get_frida_server()
        
        # Get session details
        session_data = await server.get_session_status(session_id)
        
        # Verify user owns this session
        if session_data.get('user_id') != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis session not found"
            )
        
        return session_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get analysis session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get analysis session: {str(e)}"
        )

@router.delete("/sessions/{session_id}", status_code=status.HTTP_204_NO_CONTENT)
async def stop_analysis_session(
    session_id: str,
    reason: str = "manual",
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Stop a running analysis session.
    
    Parameters:
    - **session_id**: UUID of the analysis session to stop
    - **reason**: Reason for stopping (manual, timeout, error, etc.)
    
    Returns:
    - No content (204)
    """
    try:
        server = await get_frida_server()
        
        # Verify session exists and user owns it
        session_data = await server.get_session_status(session_id)
        if session_data.get('user_id') != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis session not found"
            )
        
        # Stop the session
        await server.stop_analysis(session_id, reason=reason)
        
        logger.info(f"Stopped analysis session {session_id} for user {current_user.id}")
        return None
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stop analysis session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop analysis session: {str(e)}"
        )

@router.get("/sessions/{session_id}/artifacts")
async def list_session_artifacts(
    session_id: str,
    artifact_type: Optional[str] = None,
    current_user: UserSchema = Depends(get_current_user)
):
    """
    List artifacts generated by an analysis session.
    
    Parameters:
    - **session_id**: UUID of the analysis session
    - **artifact_type**: Optional filter by artifact type (file_changes, network_data, etc.)
    
    Returns:
    - List of artifacts with metadata
    """
    try:
        server = await get_frida_server()
        
        # Verify session exists and user owns it
        session_data = await server.get_session_status(session_id)
        if session_data.get('user_id') != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis session not found"
            )
        
        # List artifacts from MinIO
        bucket_name = server.config['storage']['minio']['bucket']
        prefix = f"{session_id}/"
        
        if artifact_type:
            prefix += f"{artifact_type}_"
        
        artifacts = []
        objects = server.minio_client.list_objects(bucket_name, prefix=prefix)
        
        for obj in objects:
            artifact_info = {
                "name": obj.object_name,
                "size": obj.size,
                "last_modified": obj.last_modified.isoformat(),
                "etag": obj.etag,
                "artifact_type": obj.object_name.split('/')[-1].split('_')[0] if '_' in obj.object_name else "unknown"
            }
            artifacts.append(artifact_info)
        
        return {
            "session_id": session_id,
            "artifacts": artifacts,
            "total_count": len(artifacts)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list artifacts for session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list artifacts: {str(e)}"
        )

@router.get("/sessions/{session_id}/artifacts/{artifact_name}")
async def download_session_artifact(
    session_id: str,
    artifact_name: str,
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Download a specific artifact from an analysis session.
    
    Parameters:
    - **session_id**: UUID of the analysis session
    - **artifact_name**: Name of the artifact to download
    
    Returns:
    - File download response
    """
    try:
        server = await get_frida_server()
        
        # Verify session exists and user owns it
        session_data = await server.get_session_status(session_id)
        if session_data.get('user_id') != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis session not found"
            )
        
        # Download artifact from MinIO
        bucket_name = server.config['storage']['minio']['bucket']
        object_name = f"{session_id}/{artifact_name}"
        
        try:
            response = server.minio_client.get_object(bucket_name, object_name)
            
            def generate():
                try:
                    for chunk in response.stream(1024):
                        yield chunk
                finally:
                    response.close()
                    response.release_conn()
            
            return StreamingResponse(
                generate(),
                media_type="application/octet-stream",
                headers={"Content-Disposition": f"attachment; filename={artifact_name}"}
            )
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Artifact {artifact_name} not found"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to download artifact {artifact_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download artifact: {str(e)}"
        )

@router.get("/sessions/{session_id}/events")
async def search_session_events(
    session_id: str,
    event_type: Optional[str] = None,
    api_function: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    limit: int = 100,
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Search runtime events for an analysis session.
    
    Parameters:
    - **session_id**: UUID of the analysis session
    - **event_type**: Optional filter by event type
    - **api_function**: Optional filter by API function
    - **start_time**: Optional start time filter (ISO format)
    - **end_time**: Optional end time filter (ISO format)
    - **limit**: Maximum number of events to return
    
    Returns:
    - List of runtime events from Elasticsearch
    """
    try:
        server = await get_frida_server()
        
        # Verify session exists and user owns it
        session_data = await server.get_session_status(session_id)
        if session_data.get('user_id') != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis session not found"
            )
        
        # Build Elasticsearch query
        query = {"bool": {"must": [{"term": {"session_id": session_id}}]}}
        
        if event_type:
            query["bool"]["must"].append({"term": {"event_type": event_type}})
        
        if api_function:
            query["bool"]["must"].append({"term": {"api_function": api_function}})
        
        # Parse time filters
        start_dt = datetime.fromisoformat(start_time) if start_time else None
        end_dt = datetime.fromisoformat(end_time) if end_time else None
        
        # Search events
        events = await server.elasticsearch_streamer.search_events(
            query=query,
            session_id=session_id,
            start_time=start_dt,
            end_time=end_dt,
            size=limit
        )
        
        return {
            "session_id": session_id,
            "events": events["hits"]["hits"],
            "total_count": events["hits"]["total"]["value"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to search events for session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search events: {str(e)}"
        )

@router.get("/sessions/{session_id}/summary")
async def get_session_summary(
    session_id: str,
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get analysis summary for a session.
    
    Parameters:
    - **session_id**: UUID of the analysis session
    
    Returns:
    - Analysis summary with statistics and aggregations
    """
    try:
        server = await get_frida_server()
        
        # Verify session exists and user owns it
        session_data = await server.get_session_status(session_id)
        if session_data.get('user_id') != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis session not found"
            )
        
        # Get summary from Elasticsearch
        summary = await server.elasticsearch_streamer.get_session_summary(session_id)
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get summary for session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get session summary: {str(e)}"
        )

@router.get("/profiles")
async def list_analysis_profiles(
    current_user: UserSchema = Depends(get_current_user)
):
    """
    List available analysis profiles.
    
    Returns:
    - List of available analysis profiles with descriptions
    """
    try:
        # Load profiles from configuration
        import json
        with open("vendor/frida/config/analysis_profiles.json", "r") as f:
            profiles_config = json.load(f)
        
        profiles = []
        for profile_name, profile_data in profiles_config["profiles"].items():
            profile_info = {
                "name": profile_name,
                "display_name": profile_data["name"],
                "description": profile_data["description"],
                "duration": profile_data["duration"],
                "api_hooks": profile_data["api_hooks"],
                "inspector_gadget_enabled": profile_data.get("inspector_gadget", {}).get("enabled", False)
            }
            profiles.append(profile_info)
        
        return {"profiles": profiles}
        
    except Exception as e:
        logger.error(f"Failed to list analysis profiles: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list analysis profiles: {str(e)}"
        )

@router.get("/health")
async def get_frida_health(
    current_user: UserSchema = Depends(get_current_user)
):
    """
    Get health status of the Frida analysis system.
    
    Returns:
    - Health status of all Frida components
    """
    try:
        server = await get_frida_server()
        health = await server.health_check()
        return health
        
    except Exception as e:
        logger.error(f"Failed to get Frida health: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }
