"""
Custom Swagger UI and ReDoc implementations with dark mode support.
"""
from fastapi import APIRouter
from fastapi.responses import HTMLResponse, JSONResponse, FileResponse
import logging
import os

# Set up logging
logger = logging.getLogger(__name__)

router = APIRouter(tags=["documentation"])

@router.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """
    Custom Swagger UI with dark mode support.
    """
    # Path to the static Swagger UI HTML file
    swagger_path = os.path.join("api", "static", "swagger.html")

    # Check if the file exists
    if not os.path.exists(swagger_path):
        logger.error(f"Swagger UI HTML file not found at {swagger_path}")
        return HTMLResponse(
            content="<h1>API Documentation</h1><p>Documentation is currently unavailable.</p>",
            status_code=500
        )

    # Serve the file
    return FileResponse(swagger_path)

@router.get("/redoc", include_in_schema=False)
async def redoc_html():
    """
    Custom ReDoc with dark mode support.
    """
    # Path to the static ReDoc HTML file
    redoc_path = os.path.join("api", "static", "redoc.html")

    # Check if the file exists
    if not os.path.exists(redoc_path):
        logger.error(f"ReDoc HTML file not found at {redoc_path}")
        return HTMLResponse(
            content="<h1>API Documentation</h1><p>Documentation is currently unavailable.</p>",
            status_code=500
        )

    # Serve the file
    return FileResponse(redoc_path)

@router.get("/openapi.json", include_in_schema=False)
async def get_openapi_json():
    """
    Return the OpenAPI schema as JSON.
    """
    import json

    # Path to the static OpenAPI schema file
    schema_path = os.path.join("api", "static", "openapi.json")

    # Check if the file exists
    if not os.path.exists(schema_path):
        logger.error(f"OpenAPI schema file not found at {schema_path}")
        return JSONResponse(
            content={"error": "OpenAPI schema file not found"},
            status_code=500
        )

    # Read the static OpenAPI schema file
    try:
        with open(schema_path, "r") as f:
            openapi_schema = json.load(f)
        return JSONResponse(content=openapi_schema)
    except Exception as e:
        logger.error(f"Error reading OpenAPI schema: {e}")
        return JSONResponse(
            content={"error": f"Error reading OpenAPI schema: {e}"},
            status_code=500
        )

@router.get("/static-docs", include_in_schema=False)
async def static_swagger_ui():
    """
    Serve a static Swagger UI HTML file.
    """
    # Path to the static Swagger UI HTML file
    swagger_path = os.path.join("api", "static", "swagger.html")

    # Check if the file exists
    if not os.path.exists(swagger_path):
        logger.error(f"Swagger UI HTML file not found at {swagger_path}")
        return HTMLResponse(
            content="<h1>API Documentation</h1><p>Documentation is currently unavailable.</p>",
            status_code=500
        )

    # Serve the file
    return FileResponse(swagger_path)
