"""
File upload routes module with enhanced security.
"""
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import uuid
import logging
from db.session import get_db
from db.models.file_upload import FileUpload
from db.schemas.file_upload import FileUploadSchema, FileUploadListSchema
from db.services.file_upload import FileUploadService
from utils.auth import get_current_user, UserInfo
from db.schemas.user import UserSchema
from utils.input_validation import FileValidator, InputSanitizer

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", response_model=FileUploadSchema, status_code=status.HTTP_201_CREATED)
async def upload_file(
    file: UploadFile = File(...),
    description: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    current_user: UserInfo = Depends(get_current_user)
):
    """
    Upload a file to the server with enhanced security validation.

    This endpoint allows users to upload files to the server. The files are validated
    for security, stored in the configured upload directory, and a record is created
    in the database.

    Parameters:
    - **file**: The file to upload (validated for security)
    - **description**: Optional description of the file

    Returns:
    - File upload object with details about the uploaded file

    Raises:
    - 400: If the file is invalid, too large, or contains suspicious content
    - 413: If the file exceeds size limits
    - 500: If there's an error saving the file

    Requires authentication.
    """
    # Validate file security
    file_validator = FileValidator()
    try:
        validation_result = await file_validator.validate_file(file)
        logger.info(f"File validation successful for {validation_result['sanitized_filename']}")
    except HTTPException as e:
        logger.warning(f"File validation failed for user {current_user.id}: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error during file validation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="File validation failed"
        )

    # Sanitize description if provided
    if description:
        description = InputSanitizer.sanitize_string(description, max_length=500)

    # Create file upload service
    file_upload_service = FileUploadService(db)

    # Create file upload with validation results
    try:
        file_upload = await file_upload_service.create(
            file,
            current_user.id,
            description=description,
            validation_metadata=validation_result
        )

        logger.info(f"File uploaded successfully: {file_upload.id} by user {current_user.id}")
        return file_upload
    except HTTPException as e:
        logger.warning(f"File upload failed for user {current_user.id}: {e.detail}")
        raise e
    except Exception as e:
        logger.error(f"Unexpected error during file upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error uploading file"
        )

@router.get("/", response_model=FileUploadListSchema)
async def get_all_file_uploads(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: UserInfo = Depends(get_current_user)
):
    """
    Get all file uploads for the current user.
    
    This endpoint returns a paginated list of file uploads that belong to the current user.
    
    Parameters:
    - **skip**: Number of records to skip (for pagination)
    - **limit**: Maximum number of records to return (for pagination)
    
    Returns:
    - **items**: List of file upload objects
    - **total**: Total number of file uploads
    
    Requires authentication.
    """
    # Create file upload service
    file_upload_service = FileUploadService(db)
    
    # Get all file uploads
    file_uploads, total = file_upload_service.get_all(skip, limit, current_user.id)
    return {"items": file_uploads, "total": total}

@router.get("/{file_upload_id}", response_model=FileUploadSchema)
async def get_file_upload(
    file_upload_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: UserInfo = Depends(get_current_user)
):
    """
    Get a specific file upload by ID.
    
    This endpoint retrieves detailed information about a specific file upload.
    
    Parameters:
    - **file_upload_id**: UUID of the file upload to retrieve
    
    Returns:
    - File upload object with all details
    
    Raises:
    - 404: If the file upload is not found or doesn't belong to the current user
    
    Requires authentication.
    """
    # Create file upload service
    file_upload_service = FileUploadService(db)
    
    # Get file upload
    file_upload = file_upload_service.get_by_id(file_upload_id, current_user.id)
    if not file_upload:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"File upload with id {file_upload_id} not found"
        )
    return file_upload

@router.delete("/{file_upload_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_file_upload(
    file_upload_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: UserInfo = Depends(get_current_user)
):
    """
    Delete a file upload.
    
    This endpoint deletes a file upload from the database and removes the file from the server.
    
    Parameters:
    - **file_upload_id**: UUID of the file upload to delete
    
    Returns:
    - No content (204)
    
    Raises:
    - 404: If the file upload is not found or doesn't belong to the current user
    - 500: If there's an error deleting the file
    
    Requires authentication.
    """
    # Create file upload service
    file_upload_service = FileUploadService(db)
    
    # Delete file upload
    try:
        file_upload_service.delete(file_upload_id, current_user.id)
        return None
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting file upload: {str(e)}"
        )
