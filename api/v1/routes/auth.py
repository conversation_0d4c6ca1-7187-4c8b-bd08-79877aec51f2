"""
Authentication endpoints with enhanced security.
"""
import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Any

from fastapi import API<PERSON>outer, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from utils.db import get_db_session
from utils.security import (
    create_access_token, verify_password, blacklist_token,
    validate_password_strength, get_password_hash
)
from utils.input_validation import InputSanitizer
from db.services.auth import AuthService
from db.schemas.user import UserCreate, UserResponse
from utils.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()


class LoginRequest(BaseModel):
    """Login request model with validation."""
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=8, max_length=128)


class TokenResponse(BaseModel):
    """Token response model."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int


class LogoutRequest(BaseModel):
    """Logout request model."""
    token: str


@router.post("/login", response_model=TokenResponse, tags=["auth"])
async def login_for_access_token(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db_session)
):
    """
    Authenticate user and return access token.

    Args:
        request: HTTP request object
        form_data: OAuth2 password request form
        db: Database session

    Returns:
        dict: Access token and token type

    Raises:
        HTTPException: If authentication fails
    """
    # Get client IP for logging
    client_ip = request.client.host if request.client else "unknown"

    # Sanitize inputs
    try:
        username = InputSanitizer.sanitize_string(form_data.username, max_length=50)
        password = form_data.password  # Don't sanitize password, just validate length

        if len(password) > 128:
            raise ValueError("Password too long")

    except Exception as e:
        logger.warning(f"Invalid login input from {client_ip}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid input format"
        )

    # Authenticate user
    auth_service = AuthService(db)
    try:
        user = await auth_service.authenticate_user(username, password)
        if not user:
            logger.warning(f"Failed login attempt for {username} from {client_ip}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        if not user.is_active:
            logger.warning(f"Login attempt for inactive user {username} from {client_ip}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is disabled"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Authentication error for {username} from {client_ip}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error"
        )

    # Create access token
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id), "username": user.username},
        expires_delta=access_token_expires
    )

    logger.info(f"Successful login for {username} from {client_ip}")

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    }


@router.post("/logout", tags=["auth"])
async def logout(
    request: Request,
    logout_data: LogoutRequest
):
    """
    Logout endpoint that blacklists the token.

    Args:
        request: HTTP request object
        logout_data: Logout request with token

    Returns:
        dict: Success message
    """
    client_ip = request.client.host if request.client else "unknown"

    try:
        # Extract JWT ID from token and blacklist it
        import jwt
        from utils.config import settings

        payload = jwt.decode(
            logout_data.token,
            settings.SECRET_KEY,
            algorithms=["HS256"]
        )
        jti = payload.get("jti")

        if jti:
            blacklist_token(jti)
            logger.info(f"Token blacklisted for logout from {client_ip}")

    except jwt.InvalidTokenError:
        logger.warning(f"Invalid token provided for logout from {client_ip}")
        # Don't raise error, just log it
    except Exception as e:
        logger.error(f"Error during logout from {client_ip}: {str(e)}")

    return {"message": "Successfully logged out"}

@router.post("/test-token", tags=["auth"])
async def get_test_token():
    """
    Get a test token for development purposes.

    Returns:
        dict: Access token and token type
    """
    # Create a test token with test user data
    user_data = {
        "sub": "00000000-0000-0000-0000-000000000000",
        "username": "testuser",
        "email": "<EMAIL>",
        "is_active": True,
        "is_superuser": True
    }

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(user_data, expires_delta=access_token_expires)

    return {
        "access_token": access_token,
        "token_type": "bearer"
    }
