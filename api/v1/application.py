"""
FastAPI application module with Flask application mounting.
"""
from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.wsgi import WSGIMiddleware
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import RedirectResponse, JSONResponse
from flask import Flask
from api.v1.routes import health, auth, items, users, vagrant, vagrant_vm, vm_injection, mfa, consolidated_vagrant
from api.v1.routes import test
from api.v1.routes import file_upload  # Import file upload router
from api.v1.routes import file_selection  # Import file selection router
from api.v1.routes import async_routes  # Import async routes
from utils.security_middleware import SecurityHeadersMiddleware, RateLimitMiddleware, RequestValidationMiddleware
from utils.middleware import RequestLoggingMiddleware, CorrelationIDMiddleware
from utils.secure_error_handler import setup_exception_handlers
# from api.v1.routes import swagger_ui  # Import our custom Swagger UI router
# from api.v1.routes import local_swagger  # Import our dark mode Swagger UI router
# from api.v1.routes import logs  # Import our logs router
# from api.v1.routes import system  # Import our system router
# from api.v1.routes import file_to_vm  # Import file-to-vm bridge router
# from api.v1.routes import success_criteria  # Import success criteria router
# from api.v1.routes import virustotal  # Import virustotal router
# from api.v1.routes import static_analysis  # Import static analysis router
# from api.v1.routes import static_files  # Import static file server
import logging
# from utils.auth import AuthMiddleware
# from utils.mfa import MFAMiddleware
# from utils.api_version import APIVersionMiddleware
# from utils.monitoring import EndpointMonitoringMiddleware
# from utils.security import SecurityHeadersMiddleware
from utils.test_config import test_settings
from utils.config import settings
from utils.logging_config import setup_logging
from fastapi.openapi.utils import get_openapi
# from api.v1.routes import endpoint_monitoring  # Import endpoint monitoring router

from fastapi.responses import HTMLResponse
from fastapi.responses import FileResponse
logger = setup_logging()

# Create Flask app for UI
flask_app = Flask(__name__)

@flask_app.route('/')
def flask_root():
    """Root endpoint for the Flask application."""
    return {"status": "ok", "message": "UI Server running"}


def get_application() -> FastAPI:
    """
    Create and configure the FastAPI application with mounted Flask app.

    Returns:
        FastAPI: Configured FastAPI application instance
    """
    # Enable test mode to bypass authentication
    if test_settings.TEST_MODE:
        logger.info("Test mode enabled for all requests - authentication bypassed")

    app = FastAPI(
        title="TurdParty API",
        description="Secure API for the TurdParty application with enhanced security features.",
        version="2.0.0",  # Updated version to reflect security improvements
        docs_url=f"{settings.API_V1_STR}/docs" if settings.DEBUG else None,  # Only show docs in debug mode
        redoc_url=f"{settings.API_V1_STR}/redoc" if settings.DEBUG else None,
        swagger_ui_oauth2_redirect_url=f"{settings.API_V1_STR}/docs/oauth2-redirect",
        openapi_url=f"{settings.API_V1_STR}/openapi.json" if settings.DEBUG else None,
        swagger_ui_parameters={
            "defaultModelsExpandDepth": -1,
            "displayRequestDuration": True,
            "filter": True,
            "syntaxHighlight.theme": "monokai",
            "operationsSorter": "alpha",
            "deepLinking": False,  # Disable deep linking for security
            "displayOperationId": False,  # Hide operation IDs
        },
        root_path="",
        # Security-related settings
        openapi_tags=[
            {"name": "auth", "description": "Authentication operations"},
            {"name": "users", "description": "User management"},
            {"name": "file_upload", "description": "Secure file upload operations"},
            {"name": "virtual-machines", "description": "Virtual machine management"},
            {"name": "health", "description": "Health check endpoints"},
        ] if settings.DEBUG else None,
    )

    # Set up secure exception handlers
    setup_exception_handlers(app)

    # Add security middleware (order matters!)
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(RateLimitMiddleware)
    app.add_middleware(RequestValidationMiddleware)
    app.add_middleware(RequestLoggingMiddleware)
    app.add_middleware(CorrelationIDMiddleware)

    # Add CORS middleware with secure configuration
    allowed_origins = [
        "http://localhost:3000",  # React development server
        "http://localhost:8000",  # FastAPI development server
        "https://yourdomain.com",  # Production domain
    ]

    # In development, allow localhost origins
    if settings.ENVIRONMENT == "development":
        allowed_origins.extend([
            "http://127.0.0.1:3000",
            "http://127.0.0.1:8000",
            "http://localhost:5000",
        ])

    app.add_middleware(
        CORSMiddleware,
        allow_origins=allowed_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        allow_headers=[
            "Accept",
            "Accept-Language",
            "Content-Language",
            "Content-Type",
            "Authorization",
            "X-Requested-With",
            "X-Request-ID",
            "X-Correlation-ID",
        ],
        expose_headers=["X-Request-ID", "X-Correlation-ID"],
        max_age=600,  # Cache preflight requests for 10 minutes
    )

    # Add a direct health endpoint at /api/v1/health for tests - KEPT
    api_health_router = APIRouter()
    @api_health_router.get("/", tags=["health"])
    async def api_health_check():
        return {"status": "ok"}

    app.include_router(api_health_router, prefix=f"{settings.API_V1_STR}/health")

    # Create main v1 API router
    v1_router = APIRouter()

    # Add a root endpoint for the v1 API that redirects to the Swagger UI documentation
    @v1_router.get("/", tags=["v1-root"])
    async def v1_root():
        """Root endpoint for the v1 API - redirects to API documentation."""
        return RedirectResponse(url=f"{settings.API_V1_STR}/docs/")

    # Include all v1 API routes
    v1_router.include_router(auth.router, prefix="/auth", tags=["auth"])
    v1_router.include_router(users.router, tags=["users"])
    # v1_router.include_router(endpoint_monitoring.router, prefix="/monitoring", tags=["monitoring"])
    v1_router.include_router(items.router, prefix="/items", tags=["items"])
    # New consolidated VM endpoints - this is the primary API now
    v1_router.include_router(consolidated_vagrant.router, tags=["virtual-machines"])

    # Include the VM injection router
    v1_router.include_router(vm_injection.router, prefix="/vm_injection", tags=["vm_injection"])
    v1_router.include_router(mfa.router, tags=["mfa"])
    v1_router.include_router(test.router, prefix="/test", tags=["test"])
    # Commented out missing routers
    # v1_router.include_router(storage.router, prefix="/storage", tags=["storage"])
    # v1_router.include_router(minio_status.router, prefix="/minio_status", tags=["minio_status"])
    # v1_router.include_router(minio_health.router, prefix="/minio_health", tags=["minio_health"])
    # v1_router.include_router(docker.router, prefix="/docker", tags=["docker"])
    v1_router.include_router(file_upload.router, prefix="/file_upload", tags=["file_upload"])
    # v1_router.include_router(docs.router, prefix="/docs", tags=["docs"])
    v1_router.include_router(file_selection.router, prefix="/file_selection", tags=["file_selection"])
    # v1_router.include_router(swagger_ui.router, prefix="/docs/all", tags=["docs"])
    # v1_router.include_router(local_swagger.router, tags=["docs"])
    # v1_router.include_router(logs.router, tags=["logs"])
    # v1_router.include_router(system.router, tags=["system"])
    # v1_router.include_router(file_to_vm.router, tags=["file_to_vm"])
    # v1_router.include_router(success_criteria.router, prefix="/success_criteria", tags=["success_criteria"])
    # v1_router.include_router(virustotal.router, tags=["virustotal"])
    # v1_router.include_router(static_analysis.static_analysis_router, prefix="/static_analysis", tags=["static_analysis"])
    # v1_router.include_router(static_files.router, tags=["static"])

    # Include the async routes
    v1_router.include_router(async_routes.router, prefix="/async", tags=["async"])

    # Mount the v1 API router
    app.include_router(v1_router, prefix=settings.API_V1_STR)

    # Mount static files
    app.mount("/static", StaticFiles(directory="static"), name="static")

    # # Add JWT auth middleware
    # app.add_middleware(AuthMiddleware, auth_service_getter=lambda: None)

    # # Add MFA middleware
    # app.add_middleware(MFAMiddleware)

    logger.info("All routes registered successfully")
    logger.info(f"API routes available at {settings.API_V1_STR}")
    logger.info("Flask UI server mounted at /ui")

    @app.get("/openapi.json", include_in_schema=False)
    async def get_root_openapi_json():
        if not hasattr(app, "openapi_schema"):
            # Generate OpenAPI schema if not already generated
            openapi_schema = get_openapi(
                title=app.title,
                version=app.version,
                openapi_version="3.0.3",
                description=app.description,
                routes=app.routes,
            )
            # Ensure the OpenAPI version is set
            openapi_schema["openapi"] = "3.0.3"
            app.openapi_schema = openapi_schema


    # Custom HTML templates for Swagger UI and ReDoc
    SWAGGER_UI_HTML = '<!DOCTYPE html>\n<html>\n<head>\n  <meta charset="UTF-8">\n  <title>TurdParty API - Swagger UI</title>\n  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css">\n  <link rel="icon" type="image/png" href="https://fastapi.tiangolo.com/img/favicon.png">\n  <style>\n    html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }\n    *, *:before, *:after { box-sizing: inherit; }\n    body { margin: 0; background: #fafafa; }\n  </style>\n</head>\n<body>\n  <div id="swagger-ui"></div>\n\n  <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>\n  <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-standalone-preset.js"></script>\n  <script>\n    window.onload = function() {\n      const ui = SwaggerUIBundle({\n        url: \'/api/v1/openapi.json\',\n        dom_id: \'#swagger-ui\',\n        deepLinking: true,\n        presets: [\n          SwaggerUIBundle.presets.apis,\n          SwaggerUIStandalonePreset\n        ],\n        plugins: [\n          SwaggerUIBundle.plugins.DownloadUrl\n        ],\n        layout: "BaseLayout",\n        defaultModelsExpandDepth: -1,\n        displayRequestDuration: true,\n        filter: true,\n        syntaxHighlight: {\n          theme: "monokai"\n        },\n        operationsSorter: "alpha"\n      });\n      window.ui = ui;\n    };\n  </script>\n</body>\n</html>\n'

    REDOC_HTML = '<!DOCTYPE html>\n<html>\n<head>\n  <meta charset="UTF-8">\n  <title>TurdParty API - ReDoc</title>\n  <meta name="viewport" content="width=device-width, initial-scale=1.0">\n  <link rel="shortcut icon" href="https://fastapi.tiangolo.com/img/favicon.png">\n  <style>\n    body { margin: 0; padding: 0; }\n  </style>\n</head>\n<body>\n  <div id="redoc-container"></div>\n  <script src="https://cdn.jsdelivr.net/npm/redoc@2.0.0/bundles/redoc.standalone.js"></script>\n  <script>\n    Redoc.init(\'/api/v1/openapi.json\', {\n      scrollYOffset: 50\n    }, document.getElementById(\'redoc-container\'))\n  </script>\n</body>\n</html>\n'

    # Add custom routes for Swagger UI and ReDoc
    @app.get("/api/v1/custom-docs", include_in_schema=False)
    async def serve_custom_swagger_ui():
        # Serve the custom Swagger UI HTML
        return HTMLResponse(content=SWAGGER_UI_HTML)

    @app.get("/api/v1/custom-redoc", include_in_schema=False)
    async def serve_custom_redoc():
        # Serve the custom ReDoc HTML
        return HTMLResponse(content=REDOC_HTML)

    # Override the default Swagger UI and ReDoc routes
    @app.get("/api/v1/docs", include_in_schema=False)
    async def override_swagger_ui():
        # Override the default Swagger UI route
        return HTMLResponse(content=SWAGGER_UI_HTML)

    @app.get("/api/v1/redoc", include_in_schema=False)
    async def override_redoc():
        # Override the default ReDoc route
        return HTMLResponse(content=REDOC_HTML)
    return app
