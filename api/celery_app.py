"""
Celery application configuration for TurdParty.

This module sets up the Celery application with the appropriate configuration
for task queuing and processing.
"""
import os
from celery import Celery
from kombu import Queue, Exchange

# Set the default Django settings module
os.environ.setdefault('CELERY_CONFIG_MODULE', 'api.core.config')

# Create the Celery app
celery_app = Celery('turdparty')

# Configure Celery using the config module
celery_app.config_from_object('api.core.celery_config', namespace='CELERY')

# Define task queues
default_exchange = Exchange('default', type='direct')
file_exchange = Exchange('file_ops', type='direct')
vm_exchange = Exchange('vm_ops', type='direct')
analysis_exchange = Exchange('analysis', type='direct')
monitoring_exchange = Exchange('monitoring', type='direct')

# Define the task queues with their exchanges and routing keys
task_queues = (
    Queue('default', default_exchange, routing_key='default'),
    Queue('file_ops', file_exchange, routing_key='file_ops'),
    Queue('vm_ops', vm_exchange, routing_key='vm_ops'),
    Queue('analysis', analysis_exchange, routing_key='analysis'),
    Queue('monitoring', monitoring_exchange, routing_key='monitoring'),
)

# Configure Celery with the task queues
celery_app.conf.task_queues = task_queues

# Set the default queue and exchange
celery_app.conf.task_default_queue = 'default'
celery_app.conf.task_default_exchange = 'default'
celery_app.conf.task_default_routing_key = 'default'

# Auto-discover tasks in all installed apps
celery_app.autodiscover_tasks(['api.tasks'])

# Define task routing
task_routes = {
    'api.tasks.file_ops.*': {'queue': 'file_ops'},
    'api.tasks.vm_ops.*': {'queue': 'vm_ops'},
    'api.tasks.analysis.*': {'queue': 'analysis'},
    'api.tasks.monitoring.*': {'queue': 'monitoring'},
}

celery_app.conf.task_routes = task_routes

# Export the app for workers to use
app = celery_app
