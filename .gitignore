# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
.venv/
ENV/
env/
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Temporary binary files for testing
temp_binaries/

# Environment variables and secrets
.env
*.env
*.env*

# Coverage
.coverage
coverage.xml
htmlcov/
.pytest_cache/
.tox/
nosetests.xml
coverage/

# Logs
*.log
logs/

# keygen
.dockerwrapper/container_key.pub
.dockerwrapper/error_log.txt

# start
start/*

# Screenshots
docs/screenshots/*
!docs/screenshots/*.html
!docs/screenshots/ui-links/*.html
!docs/screenshots/README.md
turdparty-app/docs/screenshots/
node_modules
node_modules

# Docker
.dockerwrapper/docker-compose.dynamic.yml
.dockerwrapper/port_mappings.json
.dockerwrapper/config/*.json
.dockerwrapper/.dockerwrapper/

# Patches
*.patch
*.diff

# Test results and temporary files
test-results/
test_screenshots/
*.png
*.jpg
*.jpeg
test_*.py
test_*.txt
test_*.js

# Frontend build artifacts
frontend/build/
frontend/node_modules/

# Playwright reports
playwright-report/
