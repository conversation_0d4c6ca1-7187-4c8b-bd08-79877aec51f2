from playwright.sync_api import sync_playwright
import time
import os

def take_screenshot(url, output_path):
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        page = browser.new_page()
        
        try:
            # Navigate to the URL
            print(f"Navigating to {url}")
            page.goto(url, wait_until='networkidle')
            
            # Wait a moment to ensure everything loads
            time.sleep(5)
            
            # Take a screenshot
            page.screenshot(path=output_path)
            print(f"Screenshot saved to {output_path}")
            
        except Exception as e:
            print(f"ERROR: {str(e)}")
        finally:
            browser.close()

if __name__ == "__main__":
    # Take screenshots of both endpoints
    take_screenshot("http://localhost:3050/api/v1/", "api_root_screenshot.png")
    take_screenshot("http://localhost:3050/api/v1/docs/all/docs", "swagger_ui_screenshot.png")
    
    # Print the current directory for reference
    print(f"Current directory: {os.getcwd()}")
