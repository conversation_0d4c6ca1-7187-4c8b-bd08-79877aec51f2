# TurdParty Full Test Suite Report

**Generated:** 2025-06-04 18:08:23

## Summary

- **Total Tests:** 22
- **Passed:** 3
- **Failed:** 19
- **Pass Rate:** 13.6%

## Test Results by Category


### Health Checks


### Api Tests

- **scripts/check_health.py:** ❌ FAIL
  - Error: `Traceback (most recent call last):
  File "/home/<USER>/dev/10Baht/turdparty/scripts/check_health.py", ...`
- **api/tests/test_models.py:** ❌ FAIL
  - Error: `ERROR: file or directory not found: api/tests/test_models.py

...`
- **api/tests/test_repositories.py:** ❌ FAIL
  - Error: `ImportError while loading conftest '/home/<USER>/dev/10Baht/turdparty/api/tests/conftest.py'.
api/tests...`
- **api/tests/test_services.py:** ❌ FAIL
  - Error: `ImportError while loading conftest '/home/<USER>/dev/10Baht/turdparty/api/tests/conftest.py'.
api/tests...`
- **api/tests/test_api_endpoints.py:** ❌ FAIL
  - Error: `ERROR: file or directory not found: api/tests/test_api_endpoints.py

...`
- **api/tests/test_file_upload_api.py:** ❌ FAIL
  - Error: `ImportError while loading conftest '/home/<USER>/dev/10Baht/turdparty/api/tests/conftest.py'.
api/tests...`
- **api/tests/test_minio_integration.py:** ❌ FAIL
  - Error: `ERROR: file or directory not found: api/tests/test_minio_integration.py

...`

### Ui Tests

- **playwright_setup:** ❌ FAIL
  - Error: `time="2025-06-04T18:08:21+02:00" level=warning msg="The \"i\" variable is not set. Defaulting to a b...`

### Integration Tests

- **api/tests/test_minio_e2e.py:** ❌ FAIL
  - Error: `ImportError while loading conftest '/home/<USER>/dev/10Baht/turdparty/api/tests/conftest.py'.
api/tests...`
- **api/tests/test_file_upload_service.py:** ❌ FAIL
  - Error: `ImportError while loading conftest '/home/<USER>/dev/10Baht/turdparty/api/tests/conftest.py'.
api/tests...`
- **api/tests/test_vagrant_vm_api.py:** ❌ FAIL
  - Error: `ImportError while loading conftest '/home/<USER>/dev/10Baht/turdparty/api/tests/conftest.py'.
api/tests...`
- **api/tests/test_database_integration.py:** ❌ FAIL
  - Error: `ERROR: file or directory not found: api/tests/test_database_integration.py

...`

### Cachet Tests

- **cachet_api:** ✅ PASS
- **dark_theme:** ✅ PASS
- **service_icons:** ✅ PASS
- **status_monitoring:** ❌ FAIL


## Recommendations

- ⚠️ Pass rate is below 80%. Review failed tests and fix issues.


---
*Report generated by TurdParty Test Suite Runner*
