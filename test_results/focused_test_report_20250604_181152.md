# TurdParty Focused Test Report

**Generated:** 2025-06-04 18:11:52

## Summary

- **Total Tests:** 25
- **Passed:** 20
- **Failed:** 5
- **Pass Rate:** 80.0%

## Test Results


### Connectivity Tests

- **Frontend:** ✅ PASS
- **Cachet Status Page:** ✅ PASS
- **Cachet API:** ✅ PASS
- **Cachet Components:** ✅ PASS
- **MinIO Console:** ✅ PASS
- **Celery Flower:** ✅ PASS
- **Redis:** ❌ FAIL
  - Error: `[Errno 2] No such file or directory: 'redis-cli'...`

### Cachet Customizations

- **Dark Theme CSS:** ✅ PASS
- **Service Icons JS:** ✅ PASS
- **Component Groups:** ✅ PASS
- **Components with Emojis:** ✅ PASS
  - Emoji components: 9/9

### Container Health

- **containers:** ❌ FAIL

### File System

- **Directory: api:** ✅ PASS
- **Directory: turdparty-app:** ✅ PASS
- **Directory: .dockerwrapper:** ✅ PASS
- **Directory: scripts:** ✅ PASS
- **Directory: docs:** ✅ PASS
- **File: docker-compose.yml:** ✅ PASS
- **File: .dockerwrapper/docker-compose.yml:** ✅ PASS
- **File: scripts/run_full_test_suite.py:** ✅ PASS
- **File: scripts/cachet_service_manager.py:** ✅ PASS
- **File: scripts/update_cachet_status.py:** ✅ PASS

### Status Monitoring



## Working Services

Frontend, Cachet Status Page, Cachet API, Cachet Components, MinIO Console, Celery Flower

## Recommendations

- 🎉 Good pass rate! Most services are working well.
- ✅ Frontend is accessible at http://localhost:3100
- ✅ Cachet status page is accessible at http://localhost:3501


---
*Report generated by TurdParty Focused Test Suite*
