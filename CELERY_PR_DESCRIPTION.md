# Add Celery Workers for Asynchronous Task Processing

## Description

This pull request adds Celery workers for asynchronous task processing in the TurdParty application. It implements a robust framework for handling long-running operations in the background, such as file uploads, VM provisioning, and file injections.

## Features

- **Task Status Tracking**: Implemented a TaskStatus model to track the status of asynchronous tasks
- **Specialized Task Queues**: Created separate queues for different types of tasks (file operations, VM lifecycle, VM injection, monitoring)
- **Asynchronous API Endpoints**: Added API endpoints for asynchronous operations
- **Task Monitoring**: Implemented task monitoring and status reporting
- **Comprehensive Documentation**: Added detailed documentation for the Celery implementation

## Implementation Details

### Task Queues

- **file_ops**: For file upload and selection operations
- **vm_lifecycle**: For VM creation and management
- **vm_injection**: For file injection into VMs
- **monitoring**: For monitoring and health check tasks

### API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/async/file_upload` | POST | Upload a file asynchronously |
| `/api/v1/async/file_selection` | POST | Create a file selection asynchronously |
| `/api/v1/async/vms` | POST | Provision a VM asynchronously |
| `/api/v1/async/vm_injection` | POST | Create a VM injection asynchronously |
| `/api/v1/async/vm_injection/{injection_id}/retry` | POST | Retry a failed VM injection |
| `/api/v1/async/task/{task_id}` | GET | Get the status of an asynchronous task |
| `/api/v1/async/tasks` | GET | Get all tasks for the current user |

### Documentation

- Added comprehensive documentation for the Celery implementation
- Created architecture diagrams showing the Celery components and their interactions
- Documented the task model, API endpoints, and configuration
- Updated the architecture overview to include Celery workers
- Enhanced the README with information about the Celery implementation

## Testing

- Added unit tests for the TaskStatus model
- Implemented integration tests for the asynchronous API endpoints
- Tested task queuing and processing
- Verified task status tracking and reporting

## Benefits

- **Improved User Experience**: Long-running operations no longer block the UI
- **Scalability**: Each worker type can be scaled independently based on workload
- **Resilience**: Failed tasks can be retried automatically
- **Monitoring**: Task status is tracked in the database for visibility
- **Separation of Concerns**: Different types of tasks are handled by specialized workers

## Checklist

- [x] Implemented Celery workers for asynchronous task processing
- [x] Added API endpoints for asynchronous operations
- [x] Created comprehensive documentation
- [x] Added unit and integration tests
- [x] Updated README with information about the Celery implementation
- [x] Ensured compatibility with existing code
