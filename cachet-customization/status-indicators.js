// Status Indicators Enhancement for TurdParty Cachet
(function() {
    'use strict';

    // Status color mappings
    const statusColors = {
        1: 'operational',    // Green
        2: 'performance',    // Amber/Yellow
        3: 'partial',        // Orange
        4: 'major'          // Red
    };

    // Create and inject components if they don't exist
    function createComponentsDisplay() {
        console.log('Creating components display...');

        // Check if components are already displayed
        if (document.querySelector('.component')) {
            console.log('Components already exist, applying status indicators');
            applyStatusIndicators();
            return;
        }

        // Find a good place to inject components
        let targetContainer = document.querySelector('.container');
        if (!targetContainer) {
            targetContainer = document.body;
        }

        // Create components section
        const componentsSection = document.createElement('div');
        componentsSection.className = 'components-section';
        componentsSection.innerHTML = `
            <div class="components-container">
                <h2 class="components-title">
                    <span class="components-icon">🔧</span>
                    Service Status
                </h2>
                <div id="components-list" class="components-list">
                    <div class="loading-components">Loading services...</div>
                </div>
            </div>
        `;

        // Insert after the architecture section or at the end
        const architectureSection = document.querySelector('.architecture-section');
        if (architectureSection) {
            architectureSection.parentNode.insertBefore(componentsSection, architectureSection.nextSibling);
        } else {
            targetContainer.appendChild(componentsSection);
        }

        // Load and display components
        loadAndDisplayComponents();
    }

    // Load components from API and display them
    function loadAndDisplayComponents() {
        console.log('Loading components from API...');

        fetch('/api/v1/components')
            .then(response => response.json())
            .then(data => {
                const components = data.data || [];
                console.log('Found components:', components.length);

                if (components.length === 0) {
                    document.getElementById('components-list').innerHTML = '<div class="no-components">No services configured</div>';
                    return;
                }

                // Group components by group_id
                const groups = {};
                components.forEach(component => {
                    if (!groups[component.group_id]) {
                        groups[component.group_id] = [];
                    }
                    groups[component.group_id].push(component);
                });

                // Render component groups
                const componentsList = document.getElementById('components-list');
                componentsList.innerHTML = '';

                Object.keys(groups).sort().forEach(groupId => {
                    const groupComponents = groups[groupId];
                    const groupDiv = document.createElement('div');
                    groupDiv.className = 'component-group';

                    const groupName = getGroupName(parseInt(groupId));
                    groupDiv.innerHTML = `
                        <h4 class="component-group-title">${groupName}</h4>
                        ${groupComponents.map(component => `
                            <div class="component" data-status="${component.status}" data-component-id="${component.id}">
                                <div class="component-name">${component.name}</div>
                                <div class="component-description">
                                    ${component.description}
                                    <span class="status-badge status-${component.status}">${getStatusText(component.status)}</span>
                                </div>
                            </div>
                        `).join('')}
                    `;

                    componentsList.appendChild(groupDiv);
                });

                console.log('Components rendered successfully');

                // Apply status indicators
                setTimeout(() => {
                    applyStatusIndicators();
                }, 100);

            })
            .catch(error => {
                console.error('Failed to load components:', error);
                document.getElementById('components-list').innerHTML = '<div class="error-components">Failed to load services</div>';
            });
    }

    // Apply status indicators to components
    function applyStatusIndicators() {
        console.log('Applying status indicators...');

        const components = document.querySelectorAll('.component[data-status]');
        console.log(`Found ${components.length} components to apply status indicators to`);

        components.forEach((element, index) => {
            const status = element.getAttribute('data-status');
            if (status) {
                element.classList.add(`status-${status}`);
                console.log(`Applied status ${status} to component ${index + 1}`);
            }
        });

        console.log('Status indicators applied successfully');
    }
    
    // Get group name by ID
    function getGroupName(groupId) {
        const groupNames = {
            1: '🌐 Frontend Services',
            2: '🚀 API Services',
            3: '🗄️ Storage Services'
        };
        return groupNames[groupId] || `Service Group ${groupId}`;
    }

    // Get human-readable status text
    function getStatusText(status) {
        const statusTexts = {
            1: 'Operational',
            2: 'Performance Issues',
            3: 'Partial Outage',
            4: 'Major Outage'
        };
        return statusTexts[status] || 'Unknown';
    }

    // Test status indicators functionality
    function testStatusIndicators() {
        console.log('🧪 Testing status indicators...');

        const components = document.querySelectorAll('.component[data-status]');
        let passedTests = 0;
        let totalTests = 0;

        components.forEach((component, index) => {
            const status = component.getAttribute('data-status');
            const statusBadge = component.querySelector('.status-badge');

            totalTests += 2;

            // Test 1: Component has status attribute
            if (status) {
                console.log(`✅ Component ${index + 1}: Has status attribute (${status})`);
                passedTests++;
            } else {
                console.error(`❌ Component ${index + 1}: Missing status attribute`);
            }

            // Test 2: Status badge exists
            if (statusBadge) {
                console.log(`✅ Component ${index + 1}: Has status badge`);
                passedTests++;
            } else {
                console.error(`❌ Component ${index + 1}: Missing status badge`);
            }
        });

        const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
        console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed (${successRate}%)`);

        if (successRate >= 90) {
            console.log('🎉 Status indicators are working correctly!');
        } else if (successRate >= 70) {
            console.warn('⚠️ Status indicators partially working');
        } else {
            console.error('❌ Status indicators need fixes');
        }

        return { passedTests, totalTests, successRate };
    }

    // Initialize when DOM is ready
    function init() {
        console.log('Status indicators script loaded');

        // Create components display after a short delay
        setTimeout(() => {
            createComponentsDisplay();

            // Run tests after components are loaded
            setTimeout(() => {
                testStatusIndicators();
            }, 2000);
        }, 1000);

        // Set up mutation observer for dynamic updates
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    const hasNewComponents = Array.from(mutation.addedNodes).some(node =>
                        node.nodeType === 1 && (
                            node.classList.contains('component') ||
                            node.querySelector('.component')
                        )
                    );

                    if (hasNewComponents) {
                        console.log('New components detected, reapplying status indicators');
                        setTimeout(applyStatusIndicators, 500);
                    }
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('Mutation observer set up for component changes');
    }

    // Expose test function globally for manual testing
    if (typeof window !== 'undefined') {
        window.testStatusIndicators = testStatusIndicators;
        window.TurdPartyStatusIndicators = {
            test: testStatusIndicators,
            applyStatusIndicators: applyStatusIndicators,
            createComponentsDisplay: createComponentsDisplay
        };
    }

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();
