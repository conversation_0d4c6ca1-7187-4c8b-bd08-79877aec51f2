{"title": "[NEEDS_TRANSLATION] File Upload", "description": "[NEEDS_TRANSLATION] Upload files for malware analysis", "drag_drop_area": "[NEEDS_TRANSLATION] Drag and drop files here, or click to select", "select_files": "[NEEDS_TRANSLATION] Select Files", "upload_progress": "[NEEDS_TRANSLATION] Upload Progress", "upload_complete": "[NEEDS_TRANSLATION] Upload Complete", "upload_failed": "[NEEDS_TRANSLATION] Upload Failed", "file_too_large": "[NEEDS_TRANSLATION] File is too large. Maximum size is {maxSize}.", "file_type_not_allowed": "[NEEDS_TRANSLATION] File type not allowed. Allowed types: {allowedTypes}", "max_files_exceeded": "[NEEDS_TRANSLATION] Maximum number of files exceeded. Limit: {maxFiles}", "upload_in_progress": "[NEEDS_TRANSLATION] Upload in progress...", "cancel_upload": "[NEEDS_TRANSLATION] Cancel Upload", "retry_upload": "[NEEDS_TRANSLATION] Retry Upload", "remove_file": "[NEEDS_TRANSLATION] Remove File", "file_details": "[NEEDS_TRANSLATION] File Details", "file_name": "[NEEDS_TRANSLATION] File Name", "file_size": "[NEEDS_TRANSLATION] File Size", "file_type": "[NEEDS_TRANSLATION] File Type", "upload_date": "[NEEDS_TRANSLATION] Upload Date", "analysis_status": "[NEEDS_TRANSLATION] Analysis Status", "download_results": "[NEEDS_TRANSLATION] Download Results", "view_report": "[NEEDS_TRANSLATION] View Report", "queue_position": "[NEEDS_TRANSLATION] Queue Position: {position}", "estimated_time": "[NEEDS_TRANSLATION] Estimated Time: {time}", "supported_formats": "[NEEDS_TRANSLATION] Supported Formats", "upload_guidelines": "[NEEDS_TRANSLATION] Upload Guidelines", "max_file_size": "[NEEDS_TRANSLATION] Maximum file size: {size}", "max_files_per_upload": "[NEEDS_TRANSLATION] Maximum files per upload: {count}", "analysis_types": "[NEEDS_TRANSLATION] Analysis Types", "static_analysis": "[NEEDS_TRANSLATION] Static Analysis", "dynamic_analysis": "[NEEDS_TRANSLATION] Dynamic Analysis", "sandbox_analysis": "[NEEDS_TRANSLATION] Sandbox Analysis", "select_analysis_type": "[NEEDS_TRANSLATION] Select Analysis Type", "priority_level": "[NEEDS_TRANSLATION] Priority Level", "priority_low": "[NEEDS_TRANSLATION] Low", "priority_normal": "[NEEDS_TRANSLATION] Normal", "priority_high": "[NEEDS_TRANSLATION] High", "priority_urgent": "[NEEDS_TRANSLATION] Urgent", "notification_preferences": "[NEEDS_TRANSLATION] Notification Preferences", "email_notifications": "[NEEDS_TRANSLATION] Email Notifications", "browser_notifications": "[NEEDS_TRANSLATION] Browser Notifications", "upload_history": "[NEEDS_TRANSLATION] Upload History", "recent_uploads": "[NEEDS_TRANSLATION] Recent Uploads", "all_uploads": "[NEEDS_TRANSLATION] All Uploads", "filter_by_status": "[NEEDS_TRANSLATION] Filter by Status", "filter_by_date": "[NEEDS_TRANSLATION] Filter by Date", "search_files": "[NEEDS_TRANSLATION] Search Files", "bulk_actions": "[NEEDS_TRANSLATION] Bulk Actions", "select_all": "[NEEDS_TRANSLATION] Select All", "deselect_all": "[NEEDS_TRANSLATION] Deselect All", "delete_selected": "[NEEDS_TRANSLATION] Delete Selected", "reanalyse_selected": "[NEEDS_TRANSLATION] Reanalyse Selected", "export_results": "[NEEDS_TRANSLATION] Export Results", "share_results": "[NEEDS_TRANSLATION] Share Results"}