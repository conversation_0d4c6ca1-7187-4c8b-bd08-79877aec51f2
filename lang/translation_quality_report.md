# Translation Quality Report

Generated: 1749470791.2302341

## Summary

- Total languages: 29
- Languages with UI issues: 8
- Languages with documentation issues: 29

## UI Translation Issues

### ZH
- Terminology inconsistency in components/common.json, key 'app.description': Expected '上传' for 'Upload'
- Terminology inconsistency in components/common.json, key 'app.description': Expected '注入' for 'Injection'
- Terminology inconsistency in components/common.json, key 'nav.vms': Expected '虚拟机' for 'Virtual Machine'
- Terminology inconsistency in components/common.json, key 'nav.injections': Expected '注入' for 'Injection'
- Terminology inconsistency in components/common.json, key 'button.upload': Expected '上传' for 'Upload'
- Terminology inconsistency in components/common.json, key 'button.download': Expected '下载' for 'Download'
- Terminology inconsistency in components/common.json, key 'error.server': Expected '服务器' for 'Server'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.vm_list': Expected '虚拟机' for 'Virtual Machine'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.injections': Expected '注入' for 'Injection'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.file_injections': Expected '注入' for 'Injection'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.no_injections': Expected '注入' for 'Injection'
- Terminology inconsistency in components/file_upload.json, key 'title': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected '恶意软件' for 'Malware'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_progress': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_complete': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_failed': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_in_progress': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'cancel_upload': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'retry_upload': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_date': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_status': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'download_results': Expected '下载' for 'Download'
- Terminology inconsistency in components/file_upload.json, key 'upload_guidelines': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'max_files_per_upload': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_types': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'static_analysis': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'dynamic_analysis': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'sandbox_analysis': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'select_analysis_type': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_history': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'recent_uploads': Expected '上传' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'all_uploads': Expected '上传' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'welcome_message': Expected '恶意软件' for 'Malware'
- Terminology inconsistency in pages/dashboard.json, key 'welcome_message': Expected '分析' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'total_files_uploaded': Expected '上传' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'database_status': Expected '数据库' for 'Database'
- Terminology inconsistency in pages/dashboard.json, key 'vm_status': Expected '虚拟机' for 'Virtual Machine'
- Terminology inconsistency in pages/dashboard.json, key 'recent_uploads': Expected '上传' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'upload_new_file': Expected '上传' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'analysis_throughput': Expected '分析' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'average_analysis_time': Expected '分析' for 'Analysis'

### DE
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'title': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Analyse' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_progress': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_complete': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_failed': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_in_progress': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'cancel_upload': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'retry_upload': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_date': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_status': Expected 'Analyse' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'download_results': Expected 'Herunterladen' for 'Download'
- Terminology inconsistency in components/file_upload.json, key 'upload_guidelines': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'max_files_per_upload': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_types': Expected 'Analyse' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'static_analysis': Expected 'Analyse' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'dynamic_analysis': Expected 'Analyse' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'sandbox_analysis': Expected 'Analyse' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'select_analysis_type': Expected 'Analyse' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_history': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'recent_uploads': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'all_uploads': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'welcome_message': Expected 'Analyse' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'total_files_uploaded': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'database_status': Expected 'Datenbank' for 'Database'
- Terminology inconsistency in pages/dashboard.json, key 'vm_status': Expected 'Virtuelle Maschine' for 'Virtual Machine'
- Terminology inconsistency in pages/dashboard.json, key 'recent_uploads': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'upload_new_file': Expected 'Hochladen' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'analysis_throughput': Expected 'Analyse' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'average_analysis_time': Expected 'Analyse' for 'Analysis'

### FR
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'Téléverser' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Logiciel malveillant' for 'Malware'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Analyse' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'max_files_per_upload': Expected 'Téléverser' for 'Upload'

### ES
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'Subir' for 'Upload'
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'Inyección' for 'Injection'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.no_injections': Expected 'Inyección' for 'Injection'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Análisis' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'max_files_per_upload': Expected 'Subir' for 'Upload'

### RU
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'Инъекция' for 'Injection'
- Terminology inconsistency in components/common.json, key 'nav.vms': Expected 'Виртуальная машина' for 'Virtual Machine'
- Terminology inconsistency in components/common.json, key 'nav.injections': Expected 'Инъекция' for 'Injection'
- Terminology inconsistency in components/common.json, key 'button.upload': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/common.json, key 'button.download': Expected 'Скачать' for 'Download'
- Terminology inconsistency in components/common.json, key 'error.server': Expected 'Сервер' for 'Server'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.vm_list': Expected 'Виртуальная машина' for 'Virtual Machine'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.injections': Expected 'Инъекция' for 'Injection'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.file_injections': Expected 'Инъекция' for 'Injection'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.no_injections': Expected 'Инъекция' for 'Injection'
- Terminology inconsistency in components/file_upload.json, key 'title': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Вредоносное ПО' for 'Malware'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Анализ' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_progress': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_complete': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_failed': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_in_progress': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'cancel_upload': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'retry_upload': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_date': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_status': Expected 'Анализ' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'download_results': Expected 'Скачать' for 'Download'
- Terminology inconsistency in components/file_upload.json, key 'upload_guidelines': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'max_files_per_upload': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_types': Expected 'Анализ' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'static_analysis': Expected 'Анализ' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'dynamic_analysis': Expected 'Анализ' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'sandbox_analysis': Expected 'Анализ' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'select_analysis_type': Expected 'Анализ' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_history': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'recent_uploads': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'all_uploads': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'welcome_message': Expected 'Вредоносное ПО' for 'Malware'
- Terminology inconsistency in pages/dashboard.json, key 'welcome_message': Expected 'Анализ' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'total_files_uploaded': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'database_status': Expected 'База данных' for 'Database'
- Terminology inconsistency in pages/dashboard.json, key 'vm_status': Expected 'Виртуальная машина' for 'Virtual Machine'
- Terminology inconsistency in pages/dashboard.json, key 'recent_uploads': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'upload_new_file': Expected 'Загрузить' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'analysis_throughput': Expected 'Анализ' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'average_analysis_time': Expected 'Анализ' for 'Analysis'

### JA
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'インジェクション' for 'Injection'
- Terminology inconsistency in components/common.json, key 'nav.vms': Expected '仮想マシン' for 'Virtual Machine'
- Terminology inconsistency in components/common.json, key 'nav.injections': Expected 'インジェクション' for 'Injection'
- Terminology inconsistency in components/common.json, key 'button.upload': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/common.json, key 'button.download': Expected 'ダウンロード' for 'Download'
- Terminology inconsistency in components/common.json, key 'error.server': Expected 'サーバー' for 'Server'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.vm_list': Expected '仮想マシン' for 'Virtual Machine'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.injections': Expected 'インジェクション' for 'Injection'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.file_injections': Expected 'インジェクション' for 'Injection'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.no_injections': Expected 'インジェクション' for 'Injection'
- Terminology inconsistency in components/file_upload.json, key 'title': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'マルウェア' for 'Malware'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_progress': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_complete': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_failed': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_in_progress': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'cancel_upload': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'retry_upload': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_date': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_status': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'download_results': Expected 'ダウンロード' for 'Download'
- Terminology inconsistency in components/file_upload.json, key 'upload_guidelines': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'max_files_per_upload': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_types': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'static_analysis': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'dynamic_analysis': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'sandbox_analysis': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'select_analysis_type': Expected '分析' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_history': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'recent_uploads': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'all_uploads': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'welcome_message': Expected 'マルウェア' for 'Malware'
- Terminology inconsistency in pages/dashboard.json, key 'welcome_message': Expected '分析' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'total_files_uploaded': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'database_status': Expected 'データベース' for 'Database'
- Terminology inconsistency in pages/dashboard.json, key 'vm_status': Expected '仮想マシン' for 'Virtual Machine'
- Terminology inconsistency in pages/dashboard.json, key 'recent_uploads': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'upload_new_file': Expected 'アップロード' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'analysis_throughput': Expected '分析' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'average_analysis_time': Expected '分析' for 'Analysis'

### PT
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'Injeção' for 'Injection'
- Terminology inconsistency in components/common.json, key 'nav.vms': Expected 'Máquina Virtual' for 'Virtual Machine'
- Terminology inconsistency in components/common.json, key 'nav.injections': Expected 'Injeção' for 'Injection'
- Terminology inconsistency in components/common.json, key 'button.upload': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/common.json, key 'button.download': Expected 'Baixar' for 'Download'
- Terminology inconsistency in components/common.json, key 'error.server': Expected 'Servidor' for 'Server'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.vm_list': Expected 'Máquina Virtual' for 'Virtual Machine'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.injections': Expected 'Injeção' for 'Injection'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.file_injections': Expected 'Injeção' for 'Injection'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.no_injections': Expected 'Injeção' for 'Injection'
- Terminology inconsistency in components/file_upload.json, key 'title': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Análise' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_progress': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_complete': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_failed': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_in_progress': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'cancel_upload': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'retry_upload': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_date': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_status': Expected 'Análise' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'download_results': Expected 'Baixar' for 'Download'
- Terminology inconsistency in components/file_upload.json, key 'upload_guidelines': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'max_files_per_upload': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_types': Expected 'Análise' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'static_analysis': Expected 'Análise' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'dynamic_analysis': Expected 'Análise' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'sandbox_analysis': Expected 'Análise' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'select_analysis_type': Expected 'Análise' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_history': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'recent_uploads': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'all_uploads': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'welcome_message': Expected 'Análise' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'total_files_uploaded': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'database_status': Expected 'Base de dados' for 'Database'
- Terminology inconsistency in pages/dashboard.json, key 'vm_status': Expected 'Máquina Virtual' for 'Virtual Machine'
- Terminology inconsistency in pages/dashboard.json, key 'recent_uploads': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'upload_new_file': Expected 'Carregar' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'analysis_throughput': Expected 'Análise' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'average_analysis_time': Expected 'Análise' for 'Analysis'

### IT
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/common.json, key 'app.description': Expected 'Iniezione' for 'Injection'
- Terminology inconsistency in components/common.json, key 'nav.vms': Expected 'Macchina Virtuale' for 'Virtual Machine'
- Terminology inconsistency in components/common.json, key 'nav.injections': Expected 'Iniezione' for 'Injection'
- Terminology inconsistency in components/common.json, key 'button.upload': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/common.json, key 'button.download': Expected 'Scaricare' for 'Download'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.vm_list': Expected 'Macchina Virtuale' for 'Virtual Machine'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.injections': Expected 'Iniezione' for 'Injection'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.file_injections': Expected 'Iniezione' for 'Injection'
- Terminology inconsistency in components/vm_status.json, key 'vm_status.no_injections': Expected 'Iniezione' for 'Injection'
- Terminology inconsistency in components/file_upload.json, key 'title': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'description': Expected 'Analisi' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_progress': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_complete': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_failed': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_in_progress': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'cancel_upload': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'retry_upload': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'upload_date': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_status': Expected 'Analisi' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'download_results': Expected 'Scaricare' for 'Download'
- Terminology inconsistency in components/file_upload.json, key 'upload_guidelines': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'max_files_per_upload': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'analysis_types': Expected 'Analisi' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'static_analysis': Expected 'Analisi' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'dynamic_analysis': Expected 'Analisi' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'sandbox_analysis': Expected 'Analisi' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'select_analysis_type': Expected 'Analisi' for 'Analysis'
- Terminology inconsistency in components/file_upload.json, key 'upload_history': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'recent_uploads': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in components/file_upload.json, key 'all_uploads': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'welcome_message': Expected 'Analisi' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'total_files_uploaded': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'vm_status': Expected 'Macchina Virtuale' for 'Virtual Machine'
- Terminology inconsistency in pages/dashboard.json, key 'recent_uploads': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'upload_new_file': Expected 'Caricare' for 'Upload'
- Terminology inconsistency in pages/dashboard.json, key 'analysis_throughput': Expected 'Analisi' for 'Analysis'
- Terminology inconsistency in pages/dashboard.json, key 'average_analysis_time': Expected 'Analisi' for 'Analysis'

## Documentation Translation Issues

### UK
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### NL
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### ZH
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### SV
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### DE
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### BG
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### LT
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### HU
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### DA
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### PL
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### FI
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### FR
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### LV
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### EL
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### TR
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### SL
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### CS
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### GSW
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### RO
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### ZU
- Missing translation: test_mode.md
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: static_analysis_providers.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: vagrant_operations.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: vagrant_architecture.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: server.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: vagrant_ssh.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: vagrant_setup_guide.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: minio_storage.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### TEMP
- Missing translation: test_mode.md
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: static_analysis_providers.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: vagrant_operations.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: vagrant_architecture.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: server.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: vagrant_ssh.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: vagrant_setup_guide.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: minio_storage.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### AF
- Missing translation: test_mode.md
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: static_analysis_providers.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: vagrant_operations.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: vagrant_architecture.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: server.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: vagrant_ssh.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: vagrant_setup_guide.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: minio_storage.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### ET
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### ES
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### RU
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### JA
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### PT
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### SK
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md

### IT
- Missing translation: TurdParty-Testing-PRD.md
- Missing translation: git_branch_diagram.md
- Missing translation: target_model_state.md
- Missing translation: SECURITY_IMPROVEMENTS.md
- Missing translation: CONTRIBUTING.md
- Missing translation: MIGRATION.md
- Missing translation: fastAPI_request_flow_and_error_handling.md
- Missing translation: test-fix-pattern.md
- Missing translation: test_results.md
- Missing translation: updated_git_branch.md
- Missing translation: file_upload_merged_git_branch.md
- Missing translation: final_git_branch.md
- Missing translation: file-upload-test-status.md
- Missing translation: gitgraph.md
- Missing translation: file-upload-fixes.md
- Missing translation: complete_git_branch.md
- Missing translation: file-upload-testing.md
- Missing translation: DASHBOARD.md
- Missing translation: model_analysis.md
- Missing translation: IMPLEMENTATION_PLAN.md
- Missing translation: api-frontend-alignment.md
- Missing translation: test-results-summary.md
- Missing translation: vagrant-vm-management-system.md
- Missing translation: merged_git_branch.md
- Missing translation: upload-status-diagram.md
- Missing translation: prd.md
- Missing translation: MinIO-Test-Fix-Report.md
- Missing translation: ROADMAP.md
- Missing translation: cleaned_git_branch.md
- Missing translation: MODULE_MAP.md
- Missing translation: celery.md
- Missing translation: downloaded_readme.md
- Missing translation: SUMMARY.md
- Missing translation: vagrant_integration.md
- Missing translation: ui_improvements_merged_git_branch.md
- Missing translation: sphinx_merged_git_branch.md
- Missing translation: docker-wrapper-changes.md
- Missing translation: component_test_analysis.md
- Missing translation: DOCKER.md
- Missing translation: BRANCH_CONSOLIDATION_PRD.md
- Missing translation: upload-test-summary.md
- Missing translation: CACHET_CUSTOMIZATION.md
- Missing translation: playwright-test-plan.md
- Missing translation: end_to_end_integration_testing_prd.md
- Missing translation: file-upload-e2e-status.md
- Missing translation: remediation-plan.md
- Missing translation: test_plan.md
- Missing translation: minio-tests-summary.md
- Missing translation: mermaid_test.md
- Missing translation: TESTING.md
- Missing translation: test_suite_analysis.md
- Missing translation: prd_celery_integration.md
- Missing translation: simple_git_branch.md
- Missing translation: database_model_fixes_prd.md
- Missing translation: SECURITY_TESTING.md
- Missing translation: RESTRUCTURING_README.md
- Missing translation: upload-testing-findings.md
- Missing translation: migration_strategy.md
- Missing translation: fastAPI_application_infrastructure.md
- Missing translation: refactor_friday.md
- Missing translation: fastAPI_app_startup_flow.md
- Missing translation: file-upload-troubleshooting.md
- Missing translation: end_to_end_workflow_analysis.md
- Missing translation: RESTRUCTURING_TEST_PLAN.md
- Missing translation: PROJECT_RESTRUCTURING_PRD.md
- Missing translation: INTEGRATION-TEST-SUMMARY.md
- Missing translation: upload-implementation-todos.md
- Missing translation: sphinx_docs_merged_git_branch.md
- Missing translation: testing_suite_PRD.md
- Missing translation: TEST_TROUBLESHOOTING.md
- Missing translation: CHANGELOG.md
- Missing translation: minio_vagrant_integration.md
- Missing translation: inspektor_gadget_ml_integration_prd.md
- Missing translation: combined_server.md
- Missing translation: turdparty_analysis_summary.md
- Missing translation: final_merged_git_branch.md
- Missing translation: FEATURE_BRANCH_MERGE_PLAN.md
- Missing translation: COMPREHENSIVE_TEST_RESULTS.md
- Missing translation: TRANSLATION_IMPROVEMENTS_SUMMARY.md
- Missing translation: CONSOLIDATION_SUMMARY.md
- Missing translation: FEATURE_MERGE_COMPLETION_REPORT.md
- Missing translation: SECURITY.md
- Missing translation: CELERY_PR_DESCRIPTION.md
- Missing translation: PR_DESCRIPTION.md
- Missing translation: CHANGELOG.md
- Missing translation: PRODUCTION_READY_COMPLETE.md
- Missing translation: SECURITY_REFACTOR_SUMMARY.md
