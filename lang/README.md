# TurdParty Translation System

This directory contains the comprehensive translation system for the TurdParty project, supporting both documentation and user interface translations across multiple languages.

## 🌍 Supported Languages

### Complete Language Support (28 languages)
- **Afrikaans (af)** - UI ✅ Documentation ✅
- **Bulgarian (bg)** - UI ✅ Documentation ✅
- **Czech (cs)** - UI ✅ Documentation ✅
- **Danish (da)** - UI ✅ Documentation ✅
- **German (de)** - UI ✅ Documentation ✅
- **Greek (el)** - UI ✅ Documentation ✅
- **English UK (en_GB)** - Base language
- **Spanish (es)** - UI ✅ Documentation ✅
- **Estonian (et)** - UI ✅ Documentation ✅
- **Finnish (fi)** - UI ✅ Documentation ✅
- **French (fr)** - UI ✅ Documentation ✅
- **Swiss German (gsw)** - UI ✅ Documentation ✅
- **Hungarian (hu)** - UI ✅ Documentation ✅
- **Italian (it)** - UI ✅ Documentation ✅
- **Japanese (ja)** - UI ✅ Documentation ✅
- **Lithuanian (lt)** - UI ✅ Documentation ✅
- **Latvian (lv)** - UI ✅ Documentation ✅
- **Dutch (nl)** - UI ✅ Documentation ✅
- **Polish (pl)** - UI ✅ Documentation ✅
- **Portuguese (pt)** - UI ✅ Documentation ✅
- **Romanian (ro)** - UI ✅ Documentation ✅
- **Russian (ru)** - UI ✅ Documentation ✅
- **Slovak (sk)** - UI ✅ Documentation ✅
- **Slovenian (sl)** - UI ✅ Documentation ✅
- **Swedish (sv)** - UI ✅ Documentation ✅
- **Turkish (tr)** - UI ✅ Documentation ✅
- **Ukrainian (uk)** - UI ✅ Documentation ✅
- **Chinese (zh)** - UI ✅ Documentation ✅
- **Zulu (zu)** - UI ✅ Documentation ✅

## 🚀 Quick Start

### Using the Translation Manager (Recommended)

```bash
# Check translation status
make translation-status

# Run comprehensive translation update
make translate

# Translate UI components only
make translate-ui

# Translate documentation only
make translate-docs

# Check translation quality
make check-translations

# Fix missing translations
make fix-translations
```

### Manual Commands

```bash
# Generate status report
python scripts/translation_manager.py --report lang/status_report.md

# Translate specific language
python scripts/translation_manager.py --translate --language de

# Run quality checks
python scripts/translation_manager.py --check-quality

# Fix missing translations
python scripts/translation_manager.py --fix-missing
```

## 📁 Directory Structure

```
lang/
├── README.md                          # This file
├── terminology.json                   # Translation terminology dictionary
├── translation_quality_report.md     # Quality assessment report
├── improved_translation_status.md    # Current status report
├── .translation_cache.json          # Translation cache (auto-generated)
├── en_GB/                            # Base language (British English)
│   ├── ui/                           # UI translations
│   │   ├── components/               # Component translations
│   │   │   ├── common.json          # Common UI elements
│   │   │   ├── file_upload.json     # File upload component
│   │   │   └── vm_status.json       # VM status component
│   │   └── pages/                    # Page translations
│   │       └── dashboard.json        # Dashboard page
│   └── docs/                         # Documentation translations
├── de/                               # German translations
│   ├── ui/                           # German UI translations
│   └── *.md                          # German documentation
├── fr/                               # French translations
│   ├── ui/                           # French UI translations
│   └── *.md                          # French documentation
└── [other languages]/               # Other language translations
```

## 🛠️ Translation Tools

### 1. Translation Manager (`scripts/translation_manager.py`)
**Main orchestration tool** - Coordinates all translation activities

**Features:**
- Unified interface for all translation operations
- Status reporting and quality assessment
- Automatic missing translation detection
- Integration with multiple translation services

### 2. Translation Quality Checker (`scripts/translation_quality_checker.py`)
**Quality assurance tool** - Validates translation completeness and consistency

**Features:**
- Terminology consistency checking
- Missing translation detection
- Translation completeness assessment
- Automatic template generation for missing files

### 3. Enhanced Translator (`scripts/enhanced_translate.py`)
**Advanced translation engine** - Provides high-quality translations with context awareness

**Features:**
- Multiple translation service support (Google, DeepL, Claude)
- Context-aware translations
- Terminology consistency enforcement
- Quality validation and retry logic
- Translation caching

### 4. Simple UI Translator (`scripts/simple_translate_ui.py`)
**Lightweight UI translator** - Quick UI translations using terminology dictionary

**Features:**
- Fast UI component translation
- Terminology-based translation
- No external API dependencies
- Manual translation flagging

## 🎯 Translation Quality Features

### Terminology Management
- **Consistent terminology** across all translations
- **Technical term preservation** (API, Docker, FastAPI, etc.)
- **Context-aware translations** for UI vs documentation
- **Language-specific terminology** dictionary

### Quality Assurance
- **Automated quality checks** for translation completeness
- **Length ratio validation** to detect poor translations
- **Forbidden pattern detection** to catch errors
- **Terminology consistency validation**

### Translation Services Integration
- **Google Translate API** - Primary service for most languages
- **DeepL API** - Premium quality for supported languages
- **Claude AI** - Fallback for complex technical content
- **Terminology-based** - Local dictionary for consistent terms

## 📊 Translation Status

### Current Status (Latest Update)
- **Total Languages:** 28
- **UI Translation Coverage:** 100% (28/28 languages)
- **Documentation Coverage:** 100% (28/28 languages)
- **Quality Score:** High (with terminology consistency)

### Priority Languages
Focus languages for enhanced quality:
- **German (de)** - Enhanced with native-quality translations
- **French (fr)** - Comprehensive terminology coverage
- **Spanish (es)** - Complete UI and documentation
- **Italian (it)** - Full translation coverage
- **Portuguese (pt)** - Complete translation set
- **Russian (ru)** - Comprehensive coverage
- **Chinese (zh)** - Full translation support
- **Japanese (ja)** - Complete language pack
5. **Publication**: Final translations are committed to the respective language directories.

## Translation Scripts

The following scripts are used in the translation process:

- `scripts/translate_docs.py`: Main script for translating documents
- `scripts/setup_translation_api.py`: Script for configuring translation API credentials

## Adding a New Language

To add support for a new language:

1. Create a new directory under `lang/` with the appropriate language code
2. Run the translation script with the new language code:
   ```bash
   python scripts/translate_docs.py --language <language_code>
   ```
3. Review and correct the translated files
4. Update this README.md to include the new language

## Contributing Translations

Contributions to improve existing translations or add new languages are welcome. Please follow these guidelines:

1. Only modify files for languages you are fluent in
2. Maintain consistent formatting with the original documents
3. Keep technical terms consistent across translations
4. Submit a pull request with your changes

## Translation Status

| Document | af | de | en_GB | ro | zu |
|----------|----|----|-------|----|----|
| README.md | ✅ | ✅ | ✅ | ✅ | ✅ |
| vagrant_ssh.md | ✅ | ✅ | ✅ | ✅ | ❌ |
| minio_storage.md | ❌ | ✅ | ✅ | ❌ | ❌ |
| test_mode.md | ✅ | ✅ | ✅ | ✅ | ✅ |

## Future Plans

- Implement automated translation verification
- Add more languages (es, fr, pt, zh, ja)
- Create a web interface for browsing translated documentation
- Implement DeepL API integration for improved translation quality

# Language Support Documentation

## Status Update (March 10, 2025)

### UI Testing and Babel Translation Support
- All language components now running in stable UI environment
- Internationalization (i18n) framework operational with Babel support
- Language switching functionality verified
- Translation strings loading correctly
- Added support for 25+ languages

### Supported Languages
- English (en_GB) - Primary development language
- German (de)
- French (fr)
- Spanish (es)
- Italian (it)
- Dutch (nl)
- Polish (pl)
- Portuguese (pt)
- Russian (ru)
- Japanese (ja)
- Chinese (zh)
- Czech (cs)
- Swedish (sv)
- Danish (da)
- Finnish (fi)
- Greek (el)
- Hungarian (hu)
- Romanian (ro)
- Turkish (tr)
- Ukrainian (uk)
- Bulgarian (bg)
- Estonian (et)
- Latvian (lv)
- Lithuanian (lt)
- Slovenian (sl)
- Slovak (sk)
- Afrikaans (af)
- Zulu (zu)

### Development Notes
- UI components now stable for language integration
- Hot reloading working for translation updates
- TypeScript warnings present but not affecting translations
- Babel translation system implemented for React components

## Babel Translation System

### Overview
The application now uses Babel for internationalization (i18n) of React components. This allows for:
- Dynamic language switching
- Component-level translations
- Pluralization support
- Date and number formatting

### Directory Structure
```
lang/
├── [language_code]/
│   ├── ui/
│   │   ├── components/
│   │   │   ├── [component_name].json
│   │   ├── pages/
│   │   │   ├── [page_name].json
│   │   ├── common.json
│   ├── api/
│   │   ├── messages.json
│   │   ├── errors.json
│   ├── docs/
│   │   ├── [doc_name].md
```

### Translation Files
Translation files are JSON files with the following structure:
```json
{
  "key": "translation",
  "nested.key": "nested translation",
  "parameterized": "Hello, {name}!",
  "plural": "{count, plural, one {# item} other {# items}}"
}
```

### Usage in React Components
```jsx
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation('components/myComponent');
  
  return (
    <div>
      <h1>{t('title')}</h1>
      <p>{t('description')}</p>
      <p>{t('welcome', { name: 'User' })}</p>
      <p>{t('items', { count: 5 })}</p>
    </div>
  );
}
```

### Language Switching
```jsx
import { useTranslation } from 'react-i18next';

function LanguageSwitcher() {
  const { i18n } = useTranslation();
  
  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };
  
  return (
    <div>
      <button onClick={() => changeLanguage('en_GB')}>English</button>
      <button onClick={() => changeLanguage('de')}>German</button>
      <button onClick={() => changeLanguage('fr')}>French</button>
    </div>
  );
}
```

## Translation Guidelines

The translation process involves the following steps:

1. **Source Document Selection**: Important documentation files are selected for translation.
2. **Initial Translation**: Documents are translated using the `scripts/translate_docs.py` script, which leverages translation APIs.
3. **Manual Review**: Translations are reviewed for accuracy and consistency.
4. **Formatting Check**: Markdown formatting is verified to ensure consistency across languages.
5. **Publication**: Final translations are committed to the respective language directories.

## Adding New Translations

To add translations for a new component:

1. Create a new JSON file in the `lang/en_GB/ui/components/` directory
2. Add translation keys and values
3. Run the translation script to generate translations for other languages:
   ```bash
   npm run translate -- --component=myComponent
   ```

## Adding a New Language

To add support for a new language:

1. Create a new directory under `lang/` with the appropriate language code
2. Run the translation script with the new language code:
   ```bash
   npm run translate -- --language=<language_code>
   ```
3. Review and correct the translated files
4. Update this README.md to include the new language