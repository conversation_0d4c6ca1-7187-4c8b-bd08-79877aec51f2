{"app.title": "[NEEDS_TRANSLATION] TurdParty Application", "app.description": "[NEEDS_TRANSLATION] A comprehensive application for file upload, VM management, and injection operations", "app.welcome": "[NEEDS_TRANSLATION] Welcome to TurdParty, {name}!", "app.items": "[NEEDS_TRANSLATION] {count, plural, one {# item} other {# items}}", "app.version": "[NEEDS_TRANSLATION] Version {version}", "app.copyright": "[NEEDS_TRANSLATION] © 2025 TurdParty Project", "nav.home": "Accueil", "nav.files": "Fichiers", "nav.vms": "Machine Virtuelles", "nav.injections": "[NEEDS_TRANSLATION] Injections", "nav.status": "VM Statut", "nav.docs": "Documentation", "nav.settings": "Paramètres", "nav.help": "[NEEDS_TRANSLATION] Help", "nav.about": "[NEEDS_TRANSLATION] About", "button.create": "<PERSON><PERSON><PERSON>", "button.edit": "Modifier", "button.delete": "<PERSON><PERSON><PERSON><PERSON>", "button.cancel": "Annuler", "button.save": "Enregistrer", "button.refresh": "[NEEDS_TRANSLATION] Refresh", "button.upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "button.download": "Télécharger", "button.select": "[NEEDS_TRANSLATION] Select", "button.start": "[NEEDS_TRANSLATION] Start", "button.stop": "[NEEDS_TRANSLATION] Stop", "button.restart": "[NEEDS_TRANSLATION] Restart", "button.pause": "[NEEDS_TRANSLATION] Pause", "button.resume": "[NEEDS_TRANSLATION] Resume", "button.configure": "[NEEDS_TRANSLATION] Configure", "button.view": "[NEEDS_TRANSLATION] View", "button.close": "[NEEDS_TRANSLATION] Close", "button.submit": "[NEEDS_TRANSLATION] Submit", "button.reset": "[NEEDS_TRANSLATION] Reset", "button.apply": "[NEEDS_TRANSLATION] Apply", "button.confirm": "[NEEDS_TRANSLATION] Confirm", "button.retry": "[NEEDS_TRANSLATION] Retry", "status.loading": "Chargement...", "status.success": "Su<PERSON>ès", "status.error": "<PERSON><PERSON><PERSON>", "status.warning": "Avertissement", "status.info": "[NEEDS_TRANSLATION] Information", "status.pending": "[NEEDS_TRANSLATION] Pending", "status.completed": "[NEEDS_TRANSLATION] Completed", "status.failed": "<PERSON><PERSON><PERSON>", "status.cancelled": "<PERSON><PERSON><PERSON><PERSON>", "status.running": "En cours", "status.stopped": "<PERSON><PERSON><PERSON><PERSON>", "status.healthy": "<PERSON><PERSON>", "status.unhealthy": "Malsain", "status.unknown": "[NEEDS_TRANSLATION] Unknown", "form.required": "[NEEDS_TRANSLATION] Required", "form.optional": "[NEEDS_TRANSLATION] Optional", "form.invalid": "[NEEDS_TRANSLATION] Invalid input", "form.save_success": "Enregistrerd successfully", "form.save_error": "<PERSON><PERSON><PERSON> to save", "form.validation_error": "[NEEDS_TRANSLATION] Please check your input", "table.no_data": "[NEEDS_TRANSLATION] No data available", "table.loading": "Chargement data...", "table.rows_per_page": "[NEEDS_TRANSLATION] Rows per page", "table.page_of": "[NEEDS_TRANSLATION] Page {current} of {total}", "table.sort_by": "[NEEDS_TRANSLATION] Sort by {column}", "dialog.confirm_delete": "[NEEDS_TRANSLATION] Are you sure you want to delete this item?", "dialog.unsaved_changes": "[NEEDS_TRANSLATION] You have unsaved changes. Do you want to continue?", "dialog.operation_success": "[NEEDS_TRANSLATION] Operation completed successfully", "dialog.operation_failed": "[NEEDS_TRANSLATION] Operation failed: {error}", "time.just_now": "[NEEDS_TRANSLATION] Just now", "time.minutes_ago": "[NEEDS_TRANSLATION] {count, plural, one {# minute ago} other {# minutes ago}}", "time.hours_ago": "[NEEDS_TRANSLATION] {count, plural, one {# hour ago} other {# hours ago}}", "time.days_ago": "[NEEDS_TRANSLATION] {count, plural, one {# day ago} other {# days ago}}", "file.size_bytes": "[NEEDS_TRANSLATION] {size} bytes", "file.size_kb": "[NEEDS_TRANSLATION] {size} KB", "file.size_mb": "[NEEDS_TRANSLATION] {size} MB", "file.size_gb": "[NEEDS_TRANSLATION] {size} GB", "error.network": "[NEEDS_TRANSLATION] Network error. Please check your connection.", "error.server": "<PERSON><PERSON><PERSON> error. Please try again later.", "error.unauthorized": "[NEEDS_TRANSLATION] You are not authorised to perform this action.", "error.not_found": "[NEEDS_TRANSLATION] The requested resource was not found.", "error.validation": "[NEEDS_TRANSLATION] Please check your input and try again.", "error.unknown": "[NEEDS_TRANSLATION] An unexpected error occurred."}