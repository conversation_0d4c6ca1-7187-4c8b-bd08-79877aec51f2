{"vm_status.title": "[NEEDS_TRANSLATION] VM Status Dashboard", "vm_status.loading": "[NEEDS_TRANSLATION] Loading VM data...", "vm_status.no_vms": "[NEEDS_TRANSLATION] No VMs found", "vm_status.select_vm": "[NEEDS_TRANSLATION] Select a VM from the list to view details", "vm_status.vm_list": "[NEEDS_TRANSLATION] Virtual Machines", "vm_status.overview": "[NEEDS_TRANSLATION] Overview", "vm_status.injections": "[NEEDS_TRANSLATION] Injections", "vm_status.logs": "[NEEDS_TRANSLATION] Logs", "vm_status.vm_info": "[NEEDS_TRANSLATION] VM Information", "vm_status.resource_usage": "[NEEDS_TRANSLATION] Resource Usage", "vm_status.cpu_usage": "[NEEDS_TRANSLATION] CPU Usage", "vm_status.memory_usage": "[NEEDS_TRANSLATION] Memory Usage", "vm_status.disk_usage": "[NEEDS_TRANSLATION] Disk Usage", "vm_status.no_resource_data": "[NEEDS_TRANSLATION] Resource usage data not available", "vm_status.file_injections": "[NEEDS_TRANSLATION] File Injections", "vm_status.no_injections": "[NEEDS_TRANSLATION] No injections found for this VM", "vm_status.vm_logs": "[NEEDS_TRANSLATION] VM Logs", "vm_status.no_logs": "[NEEDS_TRANSLATION] No logs available for this VM", "vm_status.description": "[NEEDS_TRANSLATION] Description", "vm_status.no_description": "[NEEDS_TRANSLATION] No description", "vm_status.additional_command": "[NEEDS_TRANSLATION] Additional Command", "vm_status.status.running": "[NEEDS_TRANSLATION] RUNNING", "vm_status.status.stopped": "[NEEDS_TRANSLATION] STOPPED", "vm_status.status.starting": "[NEEDS_TRANSLATION] STARTING", "vm_status.status.stopping": "[NEEDS_TRANSLATION] STOPPING", "vm_status.status.provisioning": "[NEEDS_TRANSLATION] PROVISIONING", "vm_status.status.error": "[NEEDS_TRANSLATION] ERROR", "vm_status.injection.completed": "[NEEDS_TRANSLATION] COMPLETED", "vm_status.injection.failed": "[NEEDS_TRANSLATION] FAILED", "vm_status.injection.pending": "[NEEDS_TRANSLATION] PENDING", "vm_status.injection.in_progress": "[NEEDS_TRANSLATION] IN PROGRESS", "vm_status.refresh": "[NEEDS_TRANSLATION] Refresh"}