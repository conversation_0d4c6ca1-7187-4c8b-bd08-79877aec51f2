# Afrikaans translations for Documentation Translation.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the Documentation
# Translation project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Documentation Translation 1.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-03 07:15+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: af\n"
"Language-Team: af <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#, python-brace-format
msgctxt "./lang/ro/docs/static_analysis_providers.md"
msgid ""
"[RO] \n"
"# Static Analysis Providers\n"
"\n"
"This document outlines the third-party services integrated into our "
"static analysis system, including their rate limits and our caching "
"strategy.\n"
"\n"
"## VirusTotal\n"
"\n"
"VirusTotal is a service that analyzes files and URLs for malicious "
"content. It aggregates multiple antivirus engines and website scanners to"
" check for viruses, worms, trojans, and other kinds of malicious content."
"\n"
"\n"
"### API Endpoints Used\n"
"\n"
"- **File Hash Lookup**: `GET "
"https://www.virustotal.com/api/v3/files/{hash}`\n"
"  - This endpoint retrieves analysis results for a specific file hash "
"(MD5, SHA-1, or SHA-256).\n"
"\n"
"### Rate Limits\n"
"\n"
"VirusTotal's public API has the following rate limits:\n"
"\n"
"- **Free API Key**: 4 requests per minute, 500 requests per day\n"
"- **Premium API Key**: Varies by subscription level, typically 1000+ "
"requests per day\n"
"\n"
"### Caching Strategy\n"
"\n"
"Due to the strict rate limits, we implement the following caching "
"strategy:\n"
"\n"
"1. **Database Caching**: All VirusTotal lookup results are stored in the "
"database using the `HashReport` model\n"
"2. **Cache Duration**: Reports are considered valid for 7 days by default"
"\n"
"3. **Cache Invalidation**: Users can force a refresh of a cached report "
"if needed, but this should be rate-limited\n"
"4. **Failed Lookups**: Even failed lookups are cached with the error "
"message to prevent repeated attempts for invalid hashes\n"
"\n"
"### Implementation Notes\n"
"\n"
"- Always check the database for an existing report before making an API "
"call\n"
"- Implement exponential backoff when rate limits are encountered\n"
"- Monitor API usage to avoid hitting daily limits\n"
"- Consider batch processing for multiple hash lookups to optimize API "
"usage\n"
"\n"
"## Future Providers\n"
"\n"
"### MetaDefender (Planned)\n"
"\n"
"[MetaDefender](https://www.opswat.com/products/metadefender) is another "
"multi-scanning engine that will be integrated in the future.\n"
"\n"
"### Potential Rate Limits\n"
"\n"
"- Free tier: 10 lookups per day\n"
"- Professional tiers: Various levels up to unlimited \n"
"\n"
"### Caching Strategy\n"
"\n"
"The same caching strategy as VirusTotal will be applied.\n"
"\n"
"### Implementation Notes\n"
"\n"
"- Will use the same database model as VirusTotal with a different "
"provider field\n"
"- Results will be normalized to a common format for consistent UI display"
"\n"
"\n"
"### HybridAnalysis (Planned)\n"
"\n"
"[Hybrid Analysis](https://www.hybrid-analysis.com/) provides advanced "
"malware analysis and can be used to complement VirusTotal results.\n"
"\n"
"## Best Practices for Static Analysis Services\n"
"\n"
"1. **Always Cache Results**: Due to rate limits on most static analysis "
"APIs, cache results aggressively\n"
"2. **Normalize Data**: Convert the different API responses to a common "
"format in our application\n"
"3. **Handle Errors Gracefully**: Implement comprehensive error handling "
"for API failures\n"
"4. **Respect Rate Limits**: Implement proper backoff mechanisms to avoid "
"being blocked\n"
"5. **Provide Alternatives**: When one service is unavailable, try to fall"
" back to another service\n"
"\n"
"## Implementation Guidelines\n"
"\n"
"When adding new static analysis providers:\n"
"\n"
"1. Create a dedicated service class that handles API communication\n"
"2. Update the database models to store results with provider information\n"
"3. Implement proper caching and error handling\n"
"4. Add the provider to the `StaticAnalysisService` main class\n"
"5. Update UI components to display the new provider's results\n"
"6. Write comprehensive tests for the new integration\n"
msgstr ""
"[AF] [RO] \n"
"# Static Analysis Providers\n"
"\n"
"This document outlines the third-party services integrated into our "
"static analysis system, including their rate limits and our caching "
"strategy.\n"
"\n"
"## VirusTotal\n"
"\n"
"VirusTotal is a service that analyzes files and URLs for malicious "
"content. It aggregates multiple antivirus engines and website scanners to"
" check for viruses, worms, trojans, and other kinds of malicious content."
"\n"
"\n"
"### API Endpoints Used\n"
"\n"
"- **File Hash Lookup**: `GET "
"https://www.virustotal.com/api/v3/files/{hash}`\n"
"  - This endpoint retrieves analysis results for a specific file hash "
"(MD5, SHA-1, or SHA-256).\n"
"\n"
"### Rate Limits\n"
"\n"
"VirusTotal's public API has the following rate limits:\n"
"\n"
"- **Free API Key**: 4 requests per minute, 500 requests per day\n"
"- **Premium API Key**: Varies by subscription level, typically 1000+ "
"requests per day\n"
"\n"
"### Caching Strategy\n"
"\n"
"Due to the strict rate limits, we implement the following caching "
"strategy:\n"
"\n"
"1. **Database Caching**: All VirusTotal lookup results are stored in the "
"database using the `HashReport` model\n"
"2. **Cache Duration**: Reports are considered valid for 7 days by default"
"\n"
"3. **Cache Invalidation**: Users can force a refresh of a cached report "
"if needed, but this should be rate-limited\n"
"4. **Failed Lookups**: Even failed lookups are cached with the error "
"message to prevent repeated attempts for invalid hashes\n"
"\n"
"### Implementation Notes\n"
"\n"
"- Always check the database for an existing report before making an API "
"call\n"
"- Implement exponential backoff when rate limits are encountered\n"
"- Monitor API usage to avoid hitting daily limits\n"
"- Consider batch processing for multiple hash lookups to optimize API "
"usage\n"
"\n"
"## Future Providers\n"
"\n"
"### MetaDefender (Planned)\n"
"\n"
"[MetaDefender](https://www.opswat.com/products/metadefender) is another "
"multi-scanning engine that will be integrated in the future.\n"
"\n"
"### Potential Rate Limits\n"
"\n"
"- Free tier: 10 lookups per day\n"
"- Professional tiers: Various levels up to unlimited \n"
"\n"
"### Caching Strategy\n"
"\n"
"The same caching strategy as VirusTotal will be applied.\n"
"\n"
"### Implementation Notes\n"
"\n"
"- Will use the same database model as VirusTotal with a different "
"provider field\n"
"- Results will be normalized to a common format for consistent UI display"
"\n"
"\n"
"### HybridAnalysis (Planned)\n"
"\n"
"[Hybrid Analysis](https://www.hybrid-analysis.com/) provides advanced "
"malware analysis and can be used to complement VirusTotal results.\n"
"\n"
"## Best Practices for Static Analysis Services\n"
"\n"
"1. **Always Cache Results**: Due to rate limits on most static analysis "
"APIs, cache results aggressively\n"
"2. **Normalize Data**: Convert the different API responses to a common "
"format in our application\n"
"3. **Handle Errors Gracefully**: Implement comprehensive error handling "
"for API failures\n"
"4. **Respect Rate Limits**: Implement proper backoff mechanisms to avoid "
"being blocked\n"
"5. **Provide Alternatives**: When one service is unavailable, try to fall"
" back to another service\n"
"\n"
"## Implementation Guidelines\n"
"\n"
"When adding new static analysis providers:\n"
"\n"
"1. Create a dedicated service class that handles API communication\n"
"2. Update the database models to store results with provider information\n"
"3. Implement proper caching and error handling\n"
"4. Add the provider to the `StaticAnalysisService` main class\n"
"5. Update UI components to display the new provider's results\n"
"6. Write comprehensive tests for the new integration\n"

