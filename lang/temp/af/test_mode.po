# Afrikaans translations for Documentation Translation.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the Documentation
# Translation project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Documentation Translation 1.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-03 07:16+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: af\n"
"Language-Team: af <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgctxt "./lang/en_GB/docs/test_mode.md"
msgid ""
"\n"
"# Test Mode Documentation\n"
"\n"
"## Authentication Bypass for Testing\n"
"\n"
"This project includes a test mode feature that allows you to bypass "
"authentication requirements during development and testing. This makes it"
" easier to test API endpoints without having to include authentication "
"tokens with every request.\n"
"\n"
"## How It Works\n"
"\n"
"Test mode is controlled by the `TestSettings` class in the "
"`api/core/test_config.py` module. When enabled, authentication middleware"
" will detect the test mode and skip authentication verification.\n"
"\n"
"## Using Test Mode\n"
"\n"
"### Via CLI Script\n"
"\n"
"The `scripts/toggle_test_mode.py` script provides a command-line "
"interface to enable, disable, or check the status of test mode:\n"
"\n"
"```bash\n"
"# Enable test mode (bypass authentication)\n"
"python scripts/toggle_test_mode.py --enable\n"
"\n"
"# Disable test mode (enforce authentication)\n"
"python scripts/toggle_test_mode.py --disable\n"
"\n"
"# Check current test mode status\n"
"python scripts/toggle_test_mode.py --status\n"
"```\n"
"\n"
"### Programmatically\n"
"\n"
"You can also control test mode programmatically in your application code:"
"\n"
"\n"
"```python\n"
"from api.core.test_config import test_settings\n"
"\n"
"# Enable test mode\n"
"test_settings.enable_test_mode()\n"
"\n"
"# Disable test mode\n"
"test_settings.disable_test_mode()\n"
"\n"
"# Check test mode status\n"
"is_in_test_mode = test_settings.is_testing()\n"
"```\n"
"\n"
"## Security Warning\n"
"\n"
"**Important**: Test mode should never be enabled in production "
"environments as it completely bypasses authentication checks, which would"
" allow unauthorized access to protected endpoints.\n"
"\n"
"## Test Authentication Tokens\n"
"\n"
"For scenarios where you need to test with actual authentication, you can "
"use the token generator script:\n"
"\n"
"```bash\n"
"# Generate a user token\n"
"python scripts/generate_test_token.py\n"
"\n"
"# Generate an admin token\n"
"python scripts/generate_test_token.py --admin\n"
"\n"
"# Generate a token for a specific user\n"
"python scripts/generate_test_token.py --username john_doe\n"
"```\n"
"\n"
"This will generate a JWT token you can use with the Authorization header:"
"\n"
"```\n"
"Authorization: Bearer your_token_here\n"
"```\n"
"\n"
"## Recommended Testing Workflow\n"
"\n"
"1. For initial API development, enable test mode to work without "
"authentication\n"
"2. For integration testing, disable test mode and use generated test "
"tokens\n"
"3. For production deployment, ensure test mode is disabled\n"
"\n"
"By following this workflow, you can efficiently develop and test your API"
" while ensuring proper security in production.\n"
msgstr ""
"[AF] \n"
"# Test Mode Documentation\n"
"\n"
"## Authentication Bypass for Testing\n"
"\n"
"This project includes a test mode feature that allows you to bypass "
"authentication requirements during development and testing. This makes it"
" easier to test API endpoints without having to include authentication "
"tokens with every request.\n"
"\n"
"## How It Works\n"
"\n"
"Test mode is controlled by the `TestSettings` class in the "
"`api/core/test_config.py` module. When enabled, authentication middleware"
" will detect the test mode and skip authentication verification.\n"
"\n"
"## Using Test Mode\n"
"\n"
"### Via CLI Script\n"
"\n"
"The `scripts/toggle_test_mode.py` script provides a command-line "
"interface to enable, disable, or check the status of test mode:\n"
"\n"
"```bash\n"
"# Enable test mode (bypass authentication)\n"
"python scripts/toggle_test_mode.py --enable\n"
"\n"
"# Disable test mode (enforce authentication)\n"
"python scripts/toggle_test_mode.py --disable\n"
"\n"
"# Check current test mode status\n"
"python scripts/toggle_test_mode.py --status\n"
"```\n"
"\n"
"### Programmatically\n"
"\n"
"You can also control test mode programmatically in your application code:"
"\n"
"\n"
"```python\n"
"from api.core.test_config import test_settings\n"
"\n"
"# Enable test mode\n"
"test_settings.enable_test_mode()\n"
"\n"
"# Disable test mode\n"
"test_settings.disable_test_mode()\n"
"\n"
"# Check test mode status\n"
"is_in_test_mode = test_settings.is_testing()\n"
"```\n"
"\n"
"## Security Warning\n"
"\n"
"**Important**: Test mode should never be enabled in production "
"environments as it completely bypasses authentication checks, which would"
" allow unauthorized access to protected endpoints.\n"
"\n"
"## Test Authentication Tokens\n"
"\n"
"For scenarios where you need to test with actual authentication, you can "
"use the token generator script:\n"
"\n"
"```bash\n"
"# Generate a user token\n"
"python scripts/generate_test_token.py\n"
"\n"
"# Generate an admin token\n"
"python scripts/generate_test_token.py --admin\n"
"\n"
"# Generate a token for a specific user\n"
"python scripts/generate_test_token.py --username john_doe\n"
"```\n"
"\n"
"This will generate a JWT token you can use with the Authorization header:"
"\n"
"```\n"
"Authorization: Bearer your_token_here\n"
"```\n"
"\n"
"## Recommended Testing Workflow\n"
"\n"
"1. For initial API development, enable test mode to work without "
"authentication\n"
"2. For integration testing, disable test mode and use generated test "
"tokens\n"
"3. For production deployment, ensure test mode is disabled\n"
"\n"
"By following this workflow, you can efficiently develop and test your API"
" while ensuring proper security in production.\n"

