# Translations template for Documentation Translation.
# Copyright (C) 2025 Replit Project
# This file is distributed under the same license as the Documentation
# Translation project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Documentation Translation 1.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-03 07:17+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#, python-brace-format
msgctxt "./lang/ro/docs/vagrant_architecture.md"
msgid ""
"[RO] \n"
"# Vagrant Service Architecture\n"
"\n"
"## Overview\n"
"\n"
"The Vagrant service provides a comprehensive API for managing Vagrant "
"virtual machines. It supports two communication methods:\n"
"\n"
"1. **SSH-based communication** (primary) - Direct communication with "
"Vagrant hosts via SSH\n"
"2. **gRPC-based communication** (fallback) - Communication through a "
"custom gRPC server\n"
"\n"
"## Component Diagram\n"
"\n"
"```mermaid\n"
"graph TD\n"
"    Client[Client/UI] --> API[FastAPI Application]\n"
"    API --> VR[Vagrant Routes]\n"
"    VR --> VC[VagrantClient]\n"
"    \n"
"    subgraph \"Communication Methods\"\n"
"        VC --> |Primary| SSH[SSH Client]\n"
"        VC --> |Fallback| GRPC[gRPC Client]\n"
"    end\n"
"    \n"
"    SSH --> |Secure Shell| VH[Vagrant Hosts]\n"
"    GRPC --> GS[gRPC Server]\n"
"    GS --> VS[Vagrant Service]\n"
"    VS --> VH\n"
"    \n"
"    VH --> VMS[Vagrant VMs]\n"
"```\n"
"\n"
"## Setup Process\n"
"\n"
"```mermaid\n"
"sequenceDiagram\n"
"    participant U as User\n"
"    participant S as Setup Script\n"
"    participant L as Local System\n"
"    participant V as Vagrant Hosts\n"
"    \n"
"    U->>S: Run setup_ssh.py\n"
"    S->>L: Generate SSH keys\n"
"    S->>L: Configure SSH config\n"
"    U->>S: Run setup_vagrant_ssh.py\n"
"    S->>V: Copy public key to hosts\n"
"    S->>V: Add to authorized_keys\n"
"    S->>U: Confirm setup complete\n"
"```\n"
"\n"
"## VM Operation Flow\n"
"\n"
"```mermaid\n"
"sequenceDiagram\n"
"    participant C as Client\n"
"    participant A as API\n"
"    participant VC as VagrantClient\n"
"    participant SSH as SSH Client\n"
"    participant VH as Vagrant Host\n"
"    participant VM as Virtual Machine\n"
"    \n"
"    C->>A: Request VM operation (e.g., /vagrant/up/{vm_id})\n"
"    A->>VC: Forward request\n"
"    VC->>SSH: Connect and send command\n"
"    SSH->>VH: Execute vagrant command\n"
"    VH->>VM: Perform operation\n"
"    VM->>VH: Return status\n"
"    VH->>SSH: Return result\n"
"    SSH->>VC: Process result\n"
"    VC->>A: Return formatted response\n"
"    A->>C: Return API response\n"
"```\n"
"\n"
"## Component Descriptions\n"
"\n"
"### VagrantClient\n"
"\n"
"The `VagrantClient` class handles communication with Vagrant hosts "
"through either SSH or gRPC. It determines which communication method to "
"use based on configuration.\n"
"\n"
"Key methods:\n"
"- `connect()`: Establishes connection to Vagrant hosts\n"
"- `status()`: Retrieves VM status\n"
"- `up()`: Starts a VM\n"
"- `halt()`: Stops a VM\n"
"- `destroy()`: Destroys a VM\n"
"- `execute_command()`: Runs commands on a VM\n"
"\n"
"### Vagrant Routes\n"
"\n"
"The routes module defines API endpoints that clients can use to interact "
"with Vagrant VMs.\n"
"\n"
"Key endpoints:\n"
"- GET `/vagrant/vms`: List all VMs\n"
"- GET `/vagrant/vms/{vm_id}`: Get VM status\n"
"- POST `/vagrant/vms/{vm_id}/up`: Start a VM\n"
"- POST `/vagrant/vms/{vm_id}/halt`: Stop a VM\n"
"- POST `/vagrant/vms/{vm_id}/destroy`: Destroy a VM\n"
"- POST `/vagrant/vms/{vm_id}/execute`: Execute command on a VM\n"
"\n"
"### gRPC Server\n"
"\n"
"For scenarios where direct SSH access is not possible, the application "
"includes a gRPC server implementation that can be deployed on Vagrant "
"hosts. This server provides a standardized interface for VM operations.\n"
"\n"
"## Configuration\n"
"\n"
"### Environment Variables\n"
"\n"
"- `USE_VAGRANT_SSH`: Set to \"true\" to use SSH communication (default)\n"
"- `DEFAULT_VAGRANT_SERVER`: Default hostname for Vagrant hosts\n"
"- `VAGRANT_GRPC_SERVER`: gRPC server address (for gRPC mode)\n"
"\n"
"### SSH Configuration\n"
"\n"
"SSH keys are stored in:\n"
"- Private key: `~/.ssh/replit`\n"
"- Public key: `~/.ssh/replit.pub`\n"
"\n"
"Vagrant host configurations can be stored in `config/vagrant_hosts.json`."
"\n"
msgstr ""

