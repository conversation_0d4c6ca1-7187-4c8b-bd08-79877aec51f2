# Translations template for Documentation Translation.
# Copyright (C) 2025 Replit Project
# This file is distributed under the same license as the Documentation
# Translation project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Documentation Translation 1.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-03 07:16+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#, python-brace-format
msgctxt "./lang/de/docs/vagrant_operations.md"
msgid ""
"[DE] # Vagrant Operations Guide\n"
"\n"
"## Overview\n"
"\n"
"This guide explains how to use the Vagrant service API to manage virtual "
"machines.\n"
"\n"
"## API Endpoints\n"
"\n"
"### VM Management\n"
"\n"
"| Endpoint | Method | Description |\n"
"|----------|--------|-------------|\n"
"| `/vagrant/vms` | GET | List all available VMs |\n"
"| `/vagrant/vms/{vm_id}` | GET | Get status of specific VM |\n"
"| `/vagrant/vms/{vm_id}/up` | POST | Start a VM |\n"
"| `/vagrant/vms/{vm_id}/halt` | POST | Stop a VM |\n"
"| `/vagrant/vms/{vm_id}/destroy` | POST | Destroy a VM |\n"
"| `/vagrant/vms/{vm_id}/execute` | POST | Execute a command on the VM |\n"
"| `/vagrant/vms/{vm_id}/status` | GET | Get detailed VM status |\n"
"| `/vagrant/vms/{vm_id}/suspend` | POST | Suspend a VM |\n"
"| `/vagrant/boxes` | GET | List available Vagrant boxes |\n"
"\n"
"## Usage Examples\n"
"\n"
"### Starting a VM\n"
"\n"
"```bash\n"
"curl -X POST \"http://localhost:8080/vagrant/vms/my-vm/up\" \\\n"
"  -H \"Content-Type: application/json\" \\\n"
"  -d '{\"provision\": true}'\n"
"```\n"
"\n"
"### Executing a Command\n"
"\n"
"```bash\n"
"curl -X POST \"http://localhost:8080/vagrant/vms/my-vm/execute\" \\\n"
"  -H \"Content-Type: application/json\" \\\n"
"  -d '{\"command\": \"ls -la\", \"sudo\": false}'\n"
"```\n"
"\n"
"### Getting VM Status\n"
"\n"
"```bash\n"
"curl -X GET \"http://localhost:8080/vagrant/vms/my-vm/status\"\n"
"```\n"
"\n"
"## Data Models\n"
"\n"
"### VagrantVM\n"
"\n"
"```json\n"
"{\n"
"  \"vm_id\": \"my-vm\"\n"
"}\n"
"```\n"
"\n"
"### VagrantVMInfo\n"
"\n"
"```json\n"
"{\n"
"  \"vm_id\": \"my-vm\",\n"
"  \"state\": \"running\",\n"
"  \"name\": \"my-ubuntu-vm\",\n"
"  \"provider\": \"virtualbox\",\n"
"  \"directory\": \"/home/<USER>/vagrant/my-vm\",\n"
"  \"network\": {\n"
"    \"private_network\": \"*************\"\n"
"  }\n"
"}\n"
"```\n"
"\n"
"### VagrantCommandResult\n"
"\n"
"```json\n"
"{\n"
"  \"success\": true,\n"
"  \"message\": \"VM started successfully\",\n"
"  \"error\": null\n"
"}\n"
"```\n"
"\n"
"### VagrantExecuteCommandResult\n"
"\n"
"```json\n"
"{\n"
"  \"success\": true,\n"
"  \"stdout\": \"file1.txt\\nfile2.txt\",\n"
"  \"stderr\": \"\",\n"
"  \"exit_code\": 0\n"
"}\n"
"```\n"
"\n"
"## Error Handling\n"
"\n"
"The API returns appropriate HTTP status codes for errors:\n"
"\n"
"- `400 Bad Request`: Invalid input parameters\n"
"- `404 Not Found`: VM or resource not found\n"
"- `500 Internal Server Error`: Server-side error\n"
"\n"
"Response body for errors:\n"
"\n"
"```json\n"
"{\n"
"  \"detail\": \"Error message describing the issue\"\n"
"}\n"
"```\n"
"\n"
"## Authentication\n"
"\n"
"All API endpoints require authentication. Use the Authorization header "
"with a valid token:\n"
"\n"
"```bash\n"
"curl -X GET \"http://localhost:8080/vagrant/vms\" \\\n"
"  -H \"Authorization: Bearer your_token_here\"\n"
"```\n"
"\n"
"## Available Operations\n"
"\n"
"### Getting VM Status\n"
"\n"
"To retrieve the status of a VM:\n"
"\n"
"```http\n"
"GET /vagrant/vms/{vm_id}\n"
"```\n"
"\n"
"### Starting VMs\n"
"\n"
"To start a VM:\n"
"\n"
"```http\n"
"POST /vagrant/vms/{vm_id}/up\n"
"```\n"
"\n"
"Optional query parameters:\n"
"- `provision`: Boolean to control whether provisioners run (default: "
"true)\n"
"\n"
"### Stopping VMs\n"
"\n"
"To stop a VM:\n"
"\n"
"```http\n"
"POST /vagrant/vms/{vm_id}/halt\n"
"```\n"
"\n"
"### Suspending VMs\n"
"\n"
"To suspend a VM (save its state and stop it):\n"
"\n"
"```http\n"
"POST /vagrant/vms/{vm_id}/suspend\n"
"```\n"
"\n"
"### Destroying VMs\n"
"\n"
"To destroy a VM:\n"
"\n"
"```http\n"
"POST /vagrant/vms/{vm_id}/destroy\n"
"```\n"
"\n"
"Optional query parameters:\n"
"- `force`: Boolean to force destroy without confirmation (default: false)"
msgstr ""

