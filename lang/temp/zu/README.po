# Zulu translations for Documentation Translation.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the Documentation
# Translation project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Documentation Translation 1.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-03 07:16+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zu\n"
"Language-Team: zu <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

msgctxt "./lang/zu/README.md"
msgid ""
"[ZU] \n"
"# Project Milestones\n"
"\n"
"## Milestone 1: Foundation Setup\n"
"- ✅ Project structure established with FastAPI backend\n"
"- ✅ Database configurations and connection handling implemented\n"
"- ✅ Dependency checking system implemented to ensure all required "
"packages are installed\n"
"- ✅ Base soft-delete model implemented to support logical deletion\n"
"- ✅ Logging system configured with file rotation and console output\n"
"\n"
"## Milestone 2: UI Framework Integration\n"
"- ✅ Service connector pattern for API communication\n"
"- ✅ Item service implementation with full CRUD operations\n"
"- ✅ Component architecture for UI development\n"
"- ✅ Test suite for UI services\n"
"\n"
"## Next Steps\n"
"- User management\n"
"- UI enhancements\n"
"\n"
"## Fathom SSH Integration\n"
"\n"
"The project has been refactored to use SSH for Vagrant communication via "
"Fathom:\n"
"\n"
"- **Direct SSH Communication**: Connect to Vagrant VMs using standard SSH"
" protocol\n"
"- **Environment Variable Configuration**: Configure SSH settings using "
"environment variables\n"
"- **Automatic Key Setup**: Scripts to set up and validate SSH keys "
"automatically\n"
"- **Secure Key Handling**: Support for both key path and direct key "
"content via environment variables\n"
"- **Validation**: Key validation before connection attempts to ensure "
"proper formatting\n"
"\n"
"### SSH Configuration\n"
"\n"
"- **VAGRANT_SSH_KEY**: Direct SSH key content as a string (ED25519 "
"format, no newlines)\n"
"- **SSH_KEY_PATH**: Path to SSH key file (default: \"~/.ssh/replit\")\n"
"- **DEFAULT_VAGRANT_SSH_USER**: Username for SSH connections (default: "
"\"vagrant\")\n"
"- **DEFAULT_VAGRANT_SERVER**: Default Vagrant server hostname/IP\n"
"\n"
"See [Vagrant SSH Documentation](docs/vagrant_ssh.md) for more details.\n"
"\n"
"## Authentication System\n"
"\n"
"The project implements a JWT (JSON Web Token) based authentication "
"system:\n"
"\n"
"- **Token-Based Authentication**: Users authenticate using JWT tokens\n"
"- **Test Token Generation**: For development purposes, a test token "
"generator script is available\n"
"- **Role-Based Authorization**: Support for regular users and superusers\n"
"- **Token Expiration**: Configurable token expiration times\n"
"- **Test Mode**: Authentication bypass option for development and testing"
"\n"
"\n"
"### Using the Test Token Generator\n"
"\n"
"For development and testing purposes, you can generate test "
"authentication tokens:\n"
"\n"
"```bash\n"
"# Generate a regular user token\n"
"python scripts/generate_test_token.py\n"
"\n"
"# Generate an admin/superuser token\n"
"python scripts/generate_test_token.py --admin\n"
"\n"
"# Generate a token for a specific username\n"
"python scripts/generate_test_token.py --username john\n"
"```\n"
"\n"
"The token can be used to authenticate API requests by including it in the"
" Authorization header:\n"
"```\n"
"Authorization: Bearer your_token_here\n"
"```\n"
"\n"
"### Test Mode for Authentication Bypass\n"
"\n"
"During development and testing, you can enable test mode to bypass "
"authentication requirements:\n"
"\n"
"```bash\n"
"# Enable test mode (bypass authentication)\n"
"python scripts/toggle_test_mode.py --enable\n"
"\n"
"# Disable test mode (enforce authentication)\n"
"python scripts/toggle_test_mode.py --disable\n"
"\n"
"# Check current test mode status\n"
"python scripts/toggle_test_mode.py --status\n"
"```\n"
"\n"
"See [Test Mode Documentation](docs/test_mode.md) for more details.\n"
"\n"
"## Error Handling\n"
"\n"
"This project implements a comprehensive error handling system:\n"
"\n"
"- **Centralized Error Classes**: API errors are handled with custom "
"exception classes\n"
"- **Structured Error Responses**: All API errors return consistent JSON "
"structures\n"
"- **Validation Errors**: Pydantic validation errors are formatted "
"consistently\n"
"- **Database Errors**: SQLAlchemy errors are properly caught and "
"translated to user-friendly messages\n"
"- **Request IDs**: Each request has a unique ID for tracing errors "
"through logs\n"
"\n"
"## Logging\n"
"\n"
"The project implements a robust logging system:\n"
"\n"
"- **Structured JSON Logs**: All logs are formatted as JSON for easier "
"parsing and analysis\n"
"- **Log Rotation**: Log files are automatically rotated to prevent them "
"from growing too large\n"
"- **Request Logging**: All HTTP requests are logged with timing "
"information\n"
"- **Error Logging**: Errors are logged with full stack traces and context"
" information\n"
"- **Database Logging**: Database operations can be logged for debugging\n"
"- **Correlation IDs**: Requests can be traced through the system using "
"correlation IDs\n"
"\n"
"## Documentation Translations\n"
"\n"
"Documentation for this project is available in multiple languages:\n"
"\n"
"- [Afrikaans (af)](./lang/af/)\n"
"- [English (UK) (en_GB)](./lang/en_GB/)\n"
"- [German (de)](./lang/de/)\n"
"- [Romanian (ro)](./lang/ro/)\n"
"- [isiZulu (zu)](./lang/zu/)\n"
"\n"
"For more information about the translation process, see [Translation "
"Documentation](./lang/README.md).\n"
"\n"
"## Architecture Patterns\n"
"\n"
"### Service Connector Pattern\n"
"\n"
"This project implements a service connector pattern for API "
"communication:\n"
"\n"
"- **Generic Type Support**: Type-safe API communication with generic type"
" parameters\n"
"- **Consistent Error Handling**: Standardized error handling for all API "
"requests\n"
"- **Request Logging**: Comprehensive logging of all API interactions\n"
"- **Response Deserialization**: Automatic conversion of JSON responses to"
" typed objects\n"
"\n"
"The implementation consists of:\n"
"\n"
"- **ServiceConnector[T]**: Generic base connector class supporting GET, "
"POST, PUT, DELETE\n"
"- **ItemService**: Concrete implementation for item-related API endpoints"
"\n"
"- **Test Suite**: Comprehensive unit tests with mocked responses\n"
"\n"
"### Dependency Injection\n"
"\n"
"This project implements a dependency injection container to manage "
"service dependencies:\n"
"\n"
"- **Container**: Centralized registry for application services and "
"repositories\n"
"- **Service Resolution**: Automatic resolution of dependencies when "
"needed\n"
"- **Testing Support**: Easy mocking of dependencies for unit testing\n"
"\n"
"### Repository Pattern\n"
"\n"
"This project uses a repository pattern for data access:\n"
"\n"
"### BaseRepository\n"
"The `BaseRepository` provides a generic implementation for all CRUD "
"operations:\n"
"- `get()`: Retrieve a record by ID\n"
"- `get_all()`: Get all records with pagination\n"
"- `create()`: Create a new record\n"
"- `update()`: Update an existing record\n"
"- `delete()`: Hard delete a record\n"
"- `soft_delete()`: Soft delete a record (setting deleted_on timestamp)\n"
"- `restore()`: Restore a soft-deleted record\n"
"- `count()`: Count total non-deleted records\n"
"- `exists()`: Check if a record exists\n"
"\n"
"### Specific Repositories\n"
"Each entity has its own repository that extends `BaseRepository` and "
"implements entity-specific methods:\n"
"- `ItemRepository`: Handles CRUD operations for items with methods like "
"`get_by_title()` and `get_active_items()`\n"
"\n"
"### Services Layer\n"
"Services use repositories to implement business logic and handle data "
"operations, providing a clean separation of concerns.\n"
"\n"
"## UI Component Architecture\n"
"\n"
"The UI is built with a reusable component architecture:\n"
"\n"
"- **BaseComponent**: Abstract base class for UI components with lifecycle"
" methods\n"
"- **CardComponent**: Reusable card UI element\n"
"- **FormComponent**: Form handling with validation support\n"
"- **StatusComponent**: Status indicators and notifications\n"
"\n"
"## Development Guidelines\n"
"- Follow PEP-8, PEP-257, and PEP-484 for Python code\n"
"- Implement test suite with high coverage \n"
"- API-first development approach\n"
"- Maintain comprehensive logging\n"
"\n"
"## Dependencies\n"
"\n"
"### Python Dependencies\n"
"\n"
"The project relies on the following key Python packages:\n"
"\n"
"#### Core Dependencies\n"
"- FastAPI (>=0.95.0): Web framework for building APIs\n"
"- Pydantic (>=2.0.0): Data validation and settings management\n"
"- SQLAlchemy (>=2.0.0): ORM for database operations\n"
"- Uvicorn (>=0.17.0): ASGI server for running the application\n"
"- Alembic (>=1.8.0): Database migration tool\n"
"\n"
"#### Database Dependencies\n"
"- psycopg2-binary (>=2.9.0): PostgreSQL adapter\n"
"- asyncpg (>=0.27.0): Asynchronous PostgreSQL driver\n"
"\n"
"#### Authentication & Security\n"
"- python-jose (>=3.3.0): JWT token handling\n"
"- passlib (>=1.7.4): Password hashing utilities\n"
"- bcrypt (>=4.0.1): Password hashing algorithm\n"
"- pyjwt (>=2.10.1): JWT implementation\n"
"\n"
"#### Testing Dependencies\n"
"- pytest (>=7.4.3): Testing framework\n"
"- pytest-cov (>=4.1.0): Coverage plugin for pytest\n"
"- coverage (>=7.3.2): Code coverage measurement\n"
"\n"
"### System Dependencies\n"
"\n"
"The following system packages are required:\n"
"\n"
"- openssh: For SSH connections to remote servers\n"
"- postgresql: PostgreSQL database client and server\n"
"- libxcrypt: Extended crypt library\n"
"\n"
"### Environment Variables and Secrets\n"
"\n"
"The following environment variables/secrets are used:\n"
"\n"
"- `DEFAULT_VAGRANT_SSH_USER`: The username for SSH connections to Vagrant"
" hosts (secret)\n"
"- `DEFAULT_VAGRANT_SERVER`: The hostname/IP address of the Vagrant server"
" (secret)\n"
"- `DEFAULT_VAGRANT_SSH_KEY`: The ed25519 SSH key with no linebreaks for "
"authentication (secret)\n"
"- `SSH_KEY_PATH`: Path to SSH key file (default: \"~/.ssh/replit\")\n"
"\n"
"### Checking Dependencies\n"
"\n"
"You can check your installed dependencies using the provided script:\n"
"\n"
"```bash\n"
"python scripts/check_dependencies.py\n"
"```\n"
"\n"
"To install missing Python dependencies:\n"
"\n"
"```bash\n"
"python -m poetry install --with dev\n"
"```\n"
"\n"
"The system dependencies are already defined in `replit.nix` for Replit "
"environments.\n"
msgstr ""
"[ZU] [ZU] \n"
"# Project Milestones\n"
"\n"
"## Milestone 1: Foundation Setup\n"
"- ✅ Project structure established with FastAPI backend\n"
"- ✅ Database configurations and connection handling implemented\n"
"- ✅ Dependency checking system implemented to ensure all required "
"packages are installed\n"
"- ✅ Base soft-delete model implemented to support logical deletion\n"
"- ✅ Logging system configured with file rotation and console output\n"
"\n"
"## Milestone 2: UI Framework Integration\n"
"- ✅ Service connector pattern for API communication\n"
"- ✅ Item service implementation with full CRUD operations\n"
"- ✅ Component architecture for UI development\n"
"- ✅ Test suite for UI services\n"
"\n"
"## Next Steps\n"
"- User management\n"
"- UI enhancements\n"
"\n"
"## Fathom SSH Integration\n"
"\n"
"The project has been refactored to use SSH for Vagrant communication via "
"Fathom:\n"
"\n"
"- **Direct SSH Communication**: Connect to Vagrant VMs using standard SSH"
" protocol\n"
"- **Environment Variable Configuration**: Configure SSH settings using "
"environment variables\n"
"- **Automatic Key Setup**: Scripts to set up and validate SSH keys "
"automatically\n"
"- **Secure Key Handling**: Support for both key path and direct key "
"content via environment variables\n"
"- **Validation**: Key validation before connection attempts to ensure "
"proper formatting\n"
"\n"
"### SSH Configuration\n"
"\n"
"- **VAGRANT_SSH_KEY**: Direct SSH key content as a string (ED25519 "
"format, no newlines)\n"
"- **SSH_KEY_PATH**: Path to SSH key file (default: \"~/.ssh/replit\")\n"
"- **DEFAULT_VAGRANT_SSH_USER**: Username for SSH connections (default: "
"\"vagrant\")\n"
"- **DEFAULT_VAGRANT_SERVER**: Default Vagrant server hostname/IP\n"
"\n"
"See [Vagrant SSH Documentation](docs/vagrant_ssh.md) for more details.\n"
"\n"
"## Authentication System\n"
"\n"
"The project implements a JWT (JSON Web Token) based authentication "
"system:\n"
"\n"
"- **Token-Based Authentication**: Users authenticate using JWT tokens\n"
"- **Test Token Generation**: For development purposes, a test token "
"generator script is available\n"
"- **Role-Based Authorization**: Support for regular users and superusers\n"
"- **Token Expiration**: Configurable token expiration times\n"
"- **Test Mode**: Authentication bypass option for development and testing"
"\n"
"\n"
"### Using the Test Token Generator\n"
"\n"
"For development and testing purposes, you can generate test "
"authentication tokens:\n"
"\n"
"```bash\n"
"# Generate a regular user token\n"
"python scripts/generate_test_token.py\n"
"\n"
"# Generate an admin/superuser token\n"
"python scripts/generate_test_token.py --admin\n"
"\n"
"# Generate a token for a specific username\n"
"python scripts/generate_test_token.py --username john\n"
"```\n"
"\n"
"The token can be used to authenticate API requests by including it in the"
" Authorization header:\n"
"```\n"
"Authorization: Bearer your_token_here\n"
"```\n"
"\n"
"### Test Mode for Authentication Bypass\n"
"\n"
"During development and testing, you can enable test mode to bypass "
"authentication requirements:\n"
"\n"
"```bash\n"
"# Enable test mode (bypass authentication)\n"
"python scripts/toggle_test_mode.py --enable\n"
"\n"
"# Disable test mode (enforce authentication)\n"
"python scripts/toggle_test_mode.py --disable\n"
"\n"
"# Check current test mode status\n"
"python scripts/toggle_test_mode.py --status\n"
"```\n"
"\n"
"See [Test Mode Documentation](docs/test_mode.md) for more details.\n"
"\n"
"## Error Handling\n"
"\n"
"This project implements a comprehensive error handling system:\n"
"\n"
"- **Centralized Error Classes**: API errors are handled with custom "
"exception classes\n"
"- **Structured Error Responses**: All API errors return consistent JSON "
"structures\n"
"- **Validation Errors**: Pydantic validation errors are formatted "
"consistently\n"
"- **Database Errors**: SQLAlchemy errors are properly caught and "
"translated to user-friendly messages\n"
"- **Request IDs**: Each request has a unique ID for tracing errors "
"through logs\n"
"\n"
"## Logging\n"
"\n"
"The project implements a robust logging system:\n"
"\n"
"- **Structured JSON Logs**: All logs are formatted as JSON for easier "
"parsing and analysis\n"
"- **Log Rotation**: Log files are automatically rotated to prevent them "
"from growing too large\n"
"- **Request Logging**: All HTTP requests are logged with timing "
"information\n"
"- **Error Logging**: Errors are logged with full stack traces and context"
" information\n"
"- **Database Logging**: Database operations can be logged for debugging\n"
"- **Correlation IDs**: Requests can be traced through the system using "
"correlation IDs\n"
"\n"
"## Documentation Translations\n"
"\n"
"Documentation for this project is available in multiple languages:\n"
"\n"
"- [Afrikaans (af)](./lang/af/)\n"
"- [English (UK) (en_GB)](./lang/en_GB/)\n"
"- [German (de)](./lang/de/)\n"
"- [Romanian (ro)](./lang/ro/)\n"
"- [isiZulu (zu)](./lang/zu/)\n"
"\n"
"For more information about the translation process, see [Translation "
"Documentation](./lang/README.md).\n"
"\n"
"## Architecture Patterns\n"
"\n"
"### Service Connector Pattern\n"
"\n"
"This project implements a service connector pattern for API "
"communication:\n"
"\n"
"- **Generic Type Support**: Type-safe API communication with generic type"
" parameters\n"
"- **Consistent Error Handling**: Standardized error handling for all API "
"requests\n"
"- **Request Logging**: Comprehensive logging of all API interactions\n"
"- **Response Deserialization**: Automatic conversion of JSON responses to"
" typed objects\n"
"\n"
"The implementation consists of:\n"
"\n"
"- **ServiceConnector[T]**: Generic base connector class supporting GET, "
"POST, PUT, DELETE\n"
"- **ItemService**: Concrete implementation for item-related API endpoints"
"\n"
"- **Test Suite**: Comprehensive unit tests with mocked responses\n"
"\n"
"### Dependency Injection\n"
"\n"
"This project implements a dependency injection container to manage "
"service dependencies:\n"
"\n"
"- **Container**: Centralized registry for application services and "
"repositories\n"
"- **Service Resolution**: Automatic resolution of dependencies when "
"needed\n"
"- **Testing Support**: Easy mocking of dependencies for unit testing\n"
"\n"
"### Repository Pattern\n"
"\n"
"This project uses a repository pattern for data access:\n"
"\n"
"### BaseRepository\n"
"The `BaseRepository` provides a generic implementation for all CRUD "
"operations:\n"
"- `get()`: Retrieve a record by ID\n"
"- `get_all()`: Get all records with pagination\n"
"- `create()`: Create a new record\n"
"- `update()`: Update an existing record\n"
"- `delete()`: Hard delete a record\n"
"- `soft_delete()`: Soft delete a record (setting deleted_on timestamp)\n"
"- `restore()`: Restore a soft-deleted record\n"
"- `count()`: Count total non-deleted records\n"
"- `exists()`: Check if a record exists\n"
"\n"
"### Specific Repositories\n"
"Each entity has its own repository that extends `BaseRepository` and "
"implements entity-specific methods:\n"
"- `ItemRepository`: Handles CRUD operations for items with methods like "
"`get_by_title()` and `get_active_items()`\n"
"\n"
"### Services Layer\n"
"Services use repositories to implement business logic and handle data "
"operations, providing a clean separation of concerns.\n"
"\n"
"## UI Component Architecture\n"
"\n"
"The UI is built with a reusable component architecture:\n"
"\n"
"- **BaseComponent**: Abstract base class for UI components with lifecycle"
" methods\n"
"- **CardComponent**: Reusable card UI element\n"
"- **FormComponent**: Form handling with validation support\n"
"- **StatusComponent**: Status indicators and notifications\n"
"\n"
"## Development Guidelines\n"
"- Follow PEP-8, PEP-257, and PEP-484 for Python code\n"
"- Implement test suite with high coverage \n"
"- API-first development approach\n"
"- Maintain comprehensive logging\n"
"\n"
"## Dependencies\n"
"\n"
"### Python Dependencies\n"
"\n"
"The project relies on the following key Python packages:\n"
"\n"
"#### Core Dependencies\n"
"- FastAPI (>=0.95.0): Web framework for building APIs\n"
"- Pydantic (>=2.0.0): Data validation and settings management\n"
"- SQLAlchemy (>=2.0.0): ORM for database operations\n"
"- Uvicorn (>=0.17.0): ASGI server for running the application\n"
"- Alembic (>=1.8.0): Database migration tool\n"
"\n"
"#### Database Dependencies\n"
"- psycopg2-binary (>=2.9.0): PostgreSQL adapter\n"
"- asyncpg (>=0.27.0): Asynchronous PostgreSQL driver\n"
"\n"
"#### Authentication & Security\n"
"- python-jose (>=3.3.0): JWT token handling\n"
"- passlib (>=1.7.4): Password hashing utilities\n"
"- bcrypt (>=4.0.1): Password hashing algorithm\n"
"- pyjwt (>=2.10.1): JWT implementation\n"
"\n"
"#### Testing Dependencies\n"
"- pytest (>=7.4.3): Testing framework\n"
"- pytest-cov (>=4.1.0): Coverage plugin for pytest\n"
"- coverage (>=7.3.2): Code coverage measurement\n"
"\n"
"### System Dependencies\n"
"\n"
"The following system packages are required:\n"
"\n"
"- openssh: For SSH connections to remote servers\n"
"- postgresql: PostgreSQL database client and server\n"
"- libxcrypt: Extended crypt library\n"
"\n"
"### Environment Variables and Secrets\n"
"\n"
"The following environment variables/secrets are used:\n"
"\n"
"- `DEFAULT_VAGRANT_SSH_USER`: The username for SSH connections to Vagrant"
" hosts (secret)\n"
"- `DEFAULT_VAGRANT_SERVER`: The hostname/IP address of the Vagrant server"
" (secret)\n"
"- `DEFAULT_VAGRANT_SSH_KEY`: The ed25519 SSH key with no linebreaks for "
"authentication (secret)\n"
"- `SSH_KEY_PATH`: Path to SSH key file (default: \"~/.ssh/replit\")\n"
"\n"
"### Checking Dependencies\n"
"\n"
"You can check your installed dependencies using the provided script:\n"
"\n"
"```bash\n"
"python scripts/check_dependencies.py\n"
"```\n"
"\n"
"To install missing Python dependencies:\n"
"\n"
"```bash\n"
"python -m poetry install --with dev\n"
"```\n"
"\n"
"The system dependencies are already defined in `replit.nix` for Replit "
"environments.\n"

