# Translations template for Documentation Translation.
# Copyright (C) 2025 Replit Project
# This file is distributed under the same license as the Documentation
# Translation project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Documentation Translation 1.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-03 07:16+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#, python-brace-format
msgctxt "./lang/de/docs/server.md"
msgid ""
"[DE] \n"
"# Server Documentation\n"
"\n"
"This document outlines the server architecture, startup process, and "
"dependency checking flow.\n"
"\n"
"## Server Startup Process\n"
"\n"
"The application uses FastAPI with a structured startup process that "
"includes dependency verification, application initialization, and "
"middleware configuration.\n"
"\n"
"```mermaid\n"
"flowchart TD\n"
"    A[Start main.py] --> B[Setup Logging]\n"
"    B --> C{Verify Critical Dependencies}\n"
"    C -->|Success| D[Initialize Application]\n"
"    C -->|Failure| E[Log Error and Exit]\n"
"    D --> F[Register Middleware]\n"
"    F --> G[Register Exception Handlers]\n"
"    G --> H[Include Routers]\n"
"    H --> I[Register Startup/Shutdown Events]\n"
"    I --> J[Start Uvicorn Server]\n"
"    J --> K[Application Running]\n"
"    \n"
"    subgraph \"Startup Event\"\n"
"    L[Initialize Dependency Injection Container]\n"
"    end\n"
"    \n"
"    I -.-> L\n"
"```\n"
"\n"
"## Dependency Checking Process\n"
"\n"
"The application utilizes a comprehensive dependency checking system to "
"ensure all required packages are installed with compatible versions "
"before starting.\n"
"\n"
"```mermaid\n"
"flowchart TD\n"
"    A[verify_critical_dependencies] --> B[check_dependencies]\n"
"    B --> C[Iterate through required packages]\n"
"    C --> D{Package installed?}\n"
"    D -->|No| E[Add to missing packages]\n"
"    D -->|Yes| F{Check version?}\n"
"    F -->|No| G[Add to success list]\n"
"    F -->|Yes| H{Version compatible?}\n"
"    H -->|No| I[Add to failure list]\n"
"    H -->|Yes| G\n"
"    \n"
"    B --> J{Any failures?}\n"
"    J -->|Yes| K[Log error and return false]\n"
"    J -->|No| L[Return true]\n"
"    \n"
"    subgraph \"check_package_version function\"\n"
"    M[Import packaging]\n"
"    M --> N[Parse version strings]\n"
"    N --> O[Compare versions]\n"
"    O --> P[Return compatibility result]\n"
"    end\n"
"    \n"
"    F -.-> M\n"
"```\n"
"\n"
"## Application Architecture\n"
"\n"
"The application follows a layered architecture with clear separation of "
"concerns:\n"
"\n"
"```mermaid\n"
"flowchart TD\n"
"    A[Client] --> B[FastAPI Application]\n"
"    B --> C[Middleware Layer]\n"
"    C --> D[API Routes]\n"
"    D --> E[Services]\n"
"    E --> F[Repositories]\n"
"    F --> G[Database]\n"
"    \n"
"    subgraph \"Middleware\"\n"
"    C1[Request Logging]\n"
"    C2[Correlation ID]\n"
"    C3[CORS]\n"
"    end\n"
"    \n"
"    subgraph \"Exception Handlers\"\n"
"    B1[Validation Errors]\n"
"    B2[HTTP Exceptions]\n"
"    B3[API Errors]\n"
"    B4[General Exceptions]\n"
"    end\n"
"    \n"
"    C --- C1 & C2 & C3\n"
"    B --- B1 & B2 & B3 & B4\n"
"```\n"
"\n"
"## Database Initialization\n"
"\n"
"The database initialization process is handled by a separate script:\n"
"\n"
"```mermaid\n"
"flowchart TD\n"
"    A[init_db.py] --> B[Setup Logging]\n"
"    B --> C[Check Database Dependencies]\n"
"    C --> D[Create Database Engine]\n"
"    D --> E[Create Tables with SQLAlchemy]\n"
"    E --> F[Verify Tables Created]\n"
"    F --> G{Tables Created?}\n"
"    G -->|Yes| H[Log Success]\n"
"    G -->|No| I[Log Error and Exit]\n"
"    \n"
"    subgraph \"Error Handling\"\n"
"    J[Catch Exceptions]\n"
"    J --> K[Log Error]\n"
"    J --> L[Exit with Error Code]\n"
"    end\n"
"    \n"
"    E -.-> J\n"
"```\n"
msgstr ""

