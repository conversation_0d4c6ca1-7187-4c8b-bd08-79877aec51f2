# Translations template for Documentation Translation.
# Copyright (C) 2025 Replit Project
# This file is distributed under the same license as the Documentation
# Translation project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Documentation Translation 1.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-03 07:16+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#, python-brace-format
msgctxt "./lang/de/docs/minio_storage.md"
msgid ""
"[DE] \n"
"# MinIO Storage Documentation\n"
"\n"
"## Overview\n"
"\n"
"This document provides detailed information about the MinIO storage "
"implementation, which allows secure access to a MinIO object storage "
"server through an SSH tunnel. The implementation includes a service "
"client, REST API endpoints, status monitoring, and comprehensive testing "
"utilities.\n"
"\n"
"## Architecture\n"
"\n"
"### SSH Tunneling Approach\n"
"\n"
"Our implementation uses SSH tunneling to securely connect to a remote "
"MinIO server. This approach offers several advantages:\n"
"\n"
"1. **Security**: All data is encrypted through the SSH tunnel\n"
"2. **Simplicity**: No need to expose MinIO publicly\n"
"3. **Authentication**: Leverages existing SSH infrastructure\n"
"4. **Flexibility**: Works across network boundaries and firewalls\n"
"\n"
"The process works as follows:\n"
"\n"
"1. Establish an SSH tunnel to the remote server using SSH keys\n"
"2. Map the remote MinIO port (typically 9000) to a local port\n"
"3. Connect to MinIO via the local port using the S3 API\n"
"4. Perform operations using the boto3 client\n"
"\n"
"### Component Overview\n"
"\n"
"- **MinIOSSHClient**: Core service that manages SSH tunnels and MinIO "
"operations\n"
"- **Storage API**: REST endpoints for bucket and object operations\n"
"- **Status API**: Endpoints for monitoring MinIO server status\n"
"- **Testing Utilities**: Scripts and test suites for verification\n"
"\n"
"## MinIOSSHClient Service\n"
"\n"
"The `MinIOSSHClient` class in `api/services/minio_ssh_client.py` provides"
" the following functionality:\n"
"\n"
"- Establishing and managing SSH tunnels\n"
"- Connecting to MinIO using boto3 S3 client\n"
"- Performing bucket operations (create, delete, list)\n"
"- Managing objects (upload, download, delete, list)\n"
"- Retrieving object metadata\n"
"\n"
"### Key Features\n"
"\n"
"- **Environment-based Configuration**: Uses environment variables for SSH"
" and MinIO credentials\n"
"- **Tunnel Management**: Automatic establishment and cleanup of SSH "
"tunnels\n"
"- **Error Handling**: Comprehensive error management for both SSH and "
"MinIO operations\n"
"- **Asynchronous Operations**: All methods are async-compatible\n"
"\n"
"### Configuration Parameters\n"
"\n"
"| Parameter | Environment Variable | Default | Description |\n"
"|-----------|---------------------|---------|-------------|\n"
"| SSH Key Path | `SSH_KEY_PATH` | `~/.ssh/replit` | Path to SSH private "
"key |\n"
"| SSH User | `VAGRANT_SSH_USER` | `vagrant` | SSH username |\n"
"| MinIO Access Key | `MINIO_ACCESS_KEY` | `minioadmin` | MinIO access key"
" |\n"
"| MinIO Secret Key | `MINIO_SECRET_KEY` | `minioadmin` | MinIO secret key"
" |\n"
"| Local Port | N/A | `9001` | Local port for SSH tunnel |\n"
"\n"
"## API Endpoints\n"
"\n"
"### Storage API\n"
"\n"
"**Base Path**: `/api/storage`\n"
"\n"
"| Endpoint | Method | Parameters | Description |\n"
"|----------|--------|------------|-------------|\n"
"| `/buckets` | GET | `hostname` (query) | List all buckets |\n"
"| `/buckets` | POST | `hostname` (query), `bucket_name` (body) | Create a"
" new bucket |\n"
"| `/buckets/{bucket_name}` | DELETE | `hostname` (query) | Delete a "
"bucket |\n"
"| `/objects/{bucket_name}` | GET | `hostname` (query), `prefix` (query, "
"optional) | List objects in a bucket |\n"
"| `/objects/{bucket_name}/{object_key}` | GET | `hostname` (query), "
"`download_path` (query, optional) | Download an object |\n"
"| `/objects/{bucket_name}/{object_key}` | PUT | `hostname` (query), "
"`file` (form) | Upload an object |\n"
"| `/objects/{bucket_name}/{object_key}` | DELETE | `hostname` (query) | "
"Delete an object |\n"
"| `/metadata/{bucket_name}/{object_key}` | GET | `hostname` (query) | Get"
" object metadata |\n"
"\n"
"### Status API\n"
"\n"
"**Base Path**: `/api/minio-status`\n"
"\n"
"| Endpoint | Method | Parameters | Description |\n"
"|----------|--------|------------|-------------|\n"
"| `/health` | GET | `hostname` (query) | Check MinIO server health |\n"
"| `/stats` | GET | `hostname` (query) | Get MinIO storage statistics |\n"
"| `/buckets/count` | GET | `hostname` (query) | Get bucket count |\n"
"| `/objects/count` | GET | `hostname` (query), `bucket_name` (query, "
"optional) | Get object count |\n"
"\n"
"### Health API\n"
"\n"
"**Base Path**: `/api/minio-health`\n"
"\n"
"| Endpoint | Method | Parameters | Description |\n"
"|----------|--------|------------|-------------|\n"
"| `/health` | GET | `hostname` (query) | Basic health check of MinIO "
"server |\n"
"| `/detailed-health` | GET | `hostname` (query) | Detailed health "
"information including bucket status |\n"
"\n"
"Additionally, the main API health endpoint at `/api/health` can include "
"MinIO status by providing the `hostname` query parameter.\n"
"\n"
"## Testing Infrastructure\n"
"\n"
"### Test Scripts\n"
"\n"
"The following scripts are provided for testing MinIO functionality:\n"
"\n"
"- **`scripts/test_minio_ssh.py`**: Command-line utility for testing SSH "
"connection and basic MinIO operations\n"
"- **`scripts/test_minio.py`**: Comprehensive test script for MinIO "
"operations\n"
"- **`scripts/run_minio_tests.py`**: Script to run all MinIO-related tests"
" with coverage reporting\n"
"- **`scripts/minio_coverage_report.py`**: Script to generate MinIO-"
"specific test coverage reports\n"
"\n"
"### Test Suites\n"
"\n"
"The following test suites are included:\n"
"\n"
"1. **Unit Tests**:\n"
"   - `api/tests/services/test_minio_ssh_client.py`: Basic unit tests\n"
"   - `api/tests/services/test_minio_ssh_client_advanced.py`: Advanced "
"unit tests\n"
"   - `api/tests/services/test_minio_ssh_client_mock.py`: Mocked tests "
"without SSH\n"
"\n"
"2. **API Route Tests**:\n"
"   - `api/tests/routes/test_storage_routes.py`: Storage API tests\n"
"   - `api/tests/routes/test_minio_status_routes.py`: Status API tests\n"
"   - `api/tests/routes/test_minio_operations.py`: Advanced operation "
"tests\n"
"   - `api/tests/routes/test_minio_status_advanced.py`: Advanced status "
"tests\n"
"\n"
"3. **Integration Tests**:\n"
"   - `api/tests/integration/test_minio_integration.py`: Basic integration"
" tests\n"
"   - `api/tests/integration/test_minio_ssh_integration.py`: SSH "
"integration tests\n"
"\n"
"### Running Tests\n"
"\n"
"```bash\n"
"# Run all MinIO tests with coverage\n"
"python scripts/run_minio_tests.py\n"
"\n"
"# Run tests without coverage\n"
"python scripts/run_minio_tests.py --no-coverage\n"
"\n"
"# Generate MinIO-specific coverage report\n"
"python scripts/minio_coverage_report.py\n"
"```\n"
"\n"
"## Best Practices\n"
"\n"
"1. **SSH Key Management**:\n"
"   - Store SSH keys securely\n"
"   - Use environment variables for key paths or content\n"
"   - Rotate keys regularly\n"
"\n"
"2. **Error Handling**:\n"
"   - Always check the `success` field in API responses\n"
"   - Log detailed error information\n"
"   - Implement proper retries for transient errors\n"
"\n"
"3. **Resource Cleanup**:\n"
"   - Always call `stop_ssh_tunnel()` when done\n"
"   - Use the `cleanup()` method to remove temporary files\n"
"   - Implement proper connection pooling for production use\n"
"\n"
"4. **Testing**:\n"
"   - Run tests regularly against a test MinIO instance\n"
"   - Verify both positive and negative test cases\n"
"   - Check error handling and edge cases\n"
"\n"
"## Troubleshooting\n"
"\n"
"### Common Issues\n"
"\n"
"1. **SSH Connection Failures**:\n"
"   - Verify SSH key exists and has correct permissions\n"
"   - Check that the remote server is reachable\n"
"   - Ensure the SSH user has appropriate access\n"
"\n"
"2. **MinIO Operation Errors**:\n"
"   - Verify MinIO is running on the remote server\n"
"   - Check access and secret keys\n"
"   - Ensure bucket and object names are valid\n"
"\n"
"3. **Performance Issues**:\n"
"   - SSH tunneling adds overhead, not suitable for high-throughput "
"scenarios\n"
"   - Consider direct MinIO access for production workloads\n"
"   - Use async operations for better concurrency\n"
"\n"
"### Debugging Tools\n"
"\n"
"- Use `scripts/test_minio_ssh.py --verbose` for detailed logging\n"
"- Check application logs for SSH and MinIO errors\n"
"- Run integration tests to verify end-to-end functionality\n"
"\n"
"## Additional Resources\n"
"\n"
"- [MinIO Documentation](https://docs.min.io/)\n"
"- [Boto3 S3 "
"Documentation](https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/s3.html)"
"\n"
"- [SSH Tunneling Guide](https://www.ssh.com/academy/ssh/tunneling)\n"
msgstr ""

