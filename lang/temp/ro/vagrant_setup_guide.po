# Romanian translations for Documentation Translation.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the Documentation
# Translation project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Documentation Translation 1.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-03 07:15+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: ro\n"
"Language-Team: ro <<EMAIL>>\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : (n==0 || (n%100 > 0 && n%100"
" < 20)) ? 1 : 2);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#, python-brace-format
msgctxt "./lang/af/docs/vagrant_setup_guide.md"
msgid ""
"[AF] \n"
"# Vagrant Service Setup Guide\n"
"\n"
"## Prerequisites\n"
"\n"
"- Python 3.8+\n"
"- SSH access to Vagrant hosts\n"
"- Vagrant installed on remote hosts\n"
"\n"
"## Initial Setup\n"
"\n"
"### 1. Install Dependencies\n"
"\n"
"Ensure all required dependencies are installed:\n"
"\n"
"```bash\n"
"pip install paramiko cryptography grpcio grpcio-tools\n"
"```\n"
"\n"
"### 2. Generate SSH Keys\n"
"\n"
"Run the SSH setup script to generate keys:\n"
"\n"
"```bash\n"
"python scripts/setup_ssh.py\n"
"```\n"
"\n"
"This script:\n"
"- Creates SSH key pair in `~/.ssh/replit` and `~/.ssh/replit.pub`\n"
"- Sets proper file permissions\n"
"- Updates SSH configuration\n"
"\n"
"### 3. Configure Vagrant Hosts\n"
"\n"
"#### Option 1: Manual Configuration\n"
"\n"
"Copy the public key to each Vagrant host:\n"
"\n"
"```bash\n"
"# View your public key\n"
"cat ~/.ssh/replit.pub\n"
"\n"
"# Manually add this key to your Vagrant host's authorized_keys file\n"
"```\n"
"\n"
"#### Option 2: Automated Configuration\n"
"\n"
"Use the provided script to configure hosts:\n"
"\n"
"```bash\n"
"# For a single host\n"
"python scripts/setup_vagrant_ssh.py --host vagrant.example.com --user "
"vagrant --password vagrant\n"
"\n"
"# For multiple hosts using a configuration file\n"
"python scripts/setup_vagrant_ssh.py --config config/vagrant_hosts.json\n"
"```\n"
"\n"
"Create a host configuration file at `config/vagrant_hosts.json`:\n"
"\n"
"```json\n"
"[\n"
"  {\n"
"    \"hostname\": \"vagrant1.example.com\",\n"
"    \"username\": \"vagrant\",\n"
"    \"password\": \"vagrant\"\n"
"  },\n"
"  {\n"
"    \"hostname\": \"vagrant2.example.com\",\n"
"    \"username\": \"vagrant\",\n"
"    \"password\": \"vagrant\"\n"
"  }\n"
"]\n"
"```\n"
"\n"
"### 4. Test the SSH Connection\n"
"\n"
"Verify the SSH connection:\n"
"\n"
"```bash\n"
"python scripts/test_fathom_connection.py --host vagrant.example.com\n"
"```\n"
"\n"
"### 5. Configure Environment Variables\n"
"\n"
"Update your environment with:\n"
"\n"
"```\n"
"USE_VAGRANT_SSH=true\n"
"DEFAULT_VAGRANT_SERVER=vagrant.example.com\n"
"```\n"
"\n"
"## Alternative: gRPC Setup\n"
"\n"
"If SSH is not an option, you can use the gRPC server:\n"
"\n"
"### 1. Generate gRPC Code\n"
"\n"
"```bash\n"
"python scripts/generate_grpc.py\n"
"```\n"
"\n"
"### 2. Start the gRPC Server\n"
"\n"
"On your Vagrant host machine:\n"
"\n"
"```bash\n"
"python scripts/run_grpc_server.py\n"
"```\n"
"\n"
"### 3. Configure Environment Variables\n"
"\n"
"```\n"
"USE_VAGRANT_SSH=false\n"
"VAGRANT_GRPC_SERVER=vagrant.example.com:50051\n"
"```\n"
"\n"
"## Troubleshooting\n"
"\n"
"### SSH Connection Issues\n"
"\n"
"If you're having SSH connection problems:\n"
"\n"
"1. Verify SSH keys are correctly set up:\n"
"   ```bash\n"
"   ls -la ~/.ssh/replit*\n"
"   ```\n"
"\n"
"2. Check SSH configuration:\n"
"   ```bash\n"
"   cat ~/.ssh/config\n"
"   ```\n"
"\n"
"3. Test direct SSH connection:\n"
"   ```bash\n"
"   ssh -i ~/.ssh/replit <EMAIL>\n"
"   ```\n"
"\n"
"### gRPC Connection Issues\n"
"\n"
"If gRPC connection fails:\n"
"\n"
"1. Verify the gRPC server is running:\n"
"   ```bash\n"
"   curl -v telnet://vagrant.example.com:50051\n"
"   ```\n"
"\n"
"2. Check for firewall issues:\n"
"   ```bash\n"
"   sudo iptables -L | grep 50051\n"
"   ```\n"
"\n"
"3. Verify generated gRPC code exists:\n"
"   ```bash\n"
"   ls -la api/grpc/vagrant_pb2*.py\n"
"   ```\n"
msgstr ""
"[RO] [AF] \n"
"# Vagrant Service Setup Guide\n"
"\n"
"## Prerequisites\n"
"\n"
"- Python 3.8+\n"
"- SSH access to Vagrant hosts\n"
"- Vagrant installed on remote hosts\n"
"\n"
"## Initial Setup\n"
"\n"
"### 1. Install Dependencies\n"
"\n"
"Ensure all required dependencies are installed:\n"
"\n"
"```bash\n"
"pip install paramiko cryptography grpcio grpcio-tools\n"
"```\n"
"\n"
"### 2. Generate SSH Keys\n"
"\n"
"Run the SSH setup script to generate keys:\n"
"\n"
"```bash\n"
"python scripts/setup_ssh.py\n"
"```\n"
"\n"
"This script:\n"
"- Creates SSH key pair in `~/.ssh/replit` and `~/.ssh/replit.pub`\n"
"- Sets proper file permissions\n"
"- Updates SSH configuration\n"
"\n"
"### 3. Configure Vagrant Hosts\n"
"\n"
"#### Option 1: Manual Configuration\n"
"\n"
"Copy the public key to each Vagrant host:\n"
"\n"
"```bash\n"
"# View your public key\n"
"cat ~/.ssh/replit.pub\n"
"\n"
"# Manually add this key to your Vagrant host's authorized_keys file\n"
"```\n"
"\n"
"#### Option 2: Automated Configuration\n"
"\n"
"Use the provided script to configure hosts:\n"
"\n"
"```bash\n"
"# For a single host\n"
"python scripts/setup_vagrant_ssh.py --host vagrant.example.com --user "
"vagrant --password vagrant\n"
"\n"
"# For multiple hosts using a configuration file\n"
"python scripts/setup_vagrant_ssh.py --config config/vagrant_hosts.json\n"
"```\n"
"\n"
"Create a host configuration file at `config/vagrant_hosts.json`:\n"
"\n"
"```json\n"
"[\n"
"  {\n"
"    \"hostname\": \"vagrant1.example.com\",\n"
"    \"username\": \"vagrant\",\n"
"    \"password\": \"vagrant\"\n"
"  },\n"
"  {\n"
"    \"hostname\": \"vagrant2.example.com\",\n"
"    \"username\": \"vagrant\",\n"
"    \"password\": \"vagrant\"\n"
"  }\n"
"]\n"
"```\n"
"\n"
"### 4. Test the SSH Connection\n"
"\n"
"Verify the SSH connection:\n"
"\n"
"```bash\n"
"python scripts/test_fathom_connection.py --host vagrant.example.com\n"
"```\n"
"\n"
"### 5. Configure Environment Variables\n"
"\n"
"Update your environment with:\n"
"\n"
"```\n"
"USE_VAGRANT_SSH=true\n"
"DEFAULT_VAGRANT_SERVER=vagrant.example.com\n"
"```\n"
"\n"
"## Alternative: gRPC Setup\n"
"\n"
"If SSH is not an option, you can use the gRPC server:\n"
"\n"
"### 1. Generate gRPC Code\n"
"\n"
"```bash\n"
"python scripts/generate_grpc.py\n"
"```\n"
"\n"
"### 2. Start the gRPC Server\n"
"\n"
"On your Vagrant host machine:\n"
"\n"
"```bash\n"
"python scripts/run_grpc_server.py\n"
"```\n"
"\n"
"### 3. Configure Environment Variables\n"
"\n"
"```\n"
"USE_VAGRANT_SSH=false\n"
"VAGRANT_GRPC_SERVER=vagrant.example.com:50051\n"
"```\n"
"\n"
"## Troubleshooting\n"
"\n"
"### SSH Connection Issues\n"
"\n"
"If you're having SSH connection problems:\n"
"\n"
"1. Verify SSH keys are correctly set up:\n"
"   ```bash\n"
"   ls -la ~/.ssh/replit*\n"
"   ```\n"
"\n"
"2. Check SSH configuration:\n"
"   ```bash\n"
"   cat ~/.ssh/config\n"
"   ```\n"
"\n"
"3. Test direct SSH connection:\n"
"   ```bash\n"
"   ssh -i ~/.ssh/replit <EMAIL>\n"
"   ```\n"
"\n"
"### gRPC Connection Issues\n"
"\n"
"If gRPC connection fails:\n"
"\n"
"1. Verify the gRPC server is running:\n"
"   ```bash\n"
"   curl -v telnet://vagrant.example.com:50051\n"
"   ```\n"
"\n"
"2. Check for firewall issues:\n"
"   ```bash\n"
"   sudo iptables -L | grep 50051\n"
"   ```\n"
"\n"
"3. Verify generated gRPC code exists:\n"
"   ```bash\n"
"   ls -la api/grpc/vagrant_pb2*.py\n"
"   ```\n"

