# Romanian translations for Documentation Translation.
# Copyright (C) 2025 ORGANIZATION
# This file is distributed under the same license as the Documentation
# Translation project.
# <AUTHOR> <EMAIL>, 2025.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Documentation Translation 1.0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-03-03 07:16+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: ro\n"
"Language-Team: ro <<EMAIL>>\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : (n==0 || (n%100 > 0 && n%100"
" < 20)) ? 1 : 2);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#, python-brace-format
msgctxt "./lang/en_GB/docs/vagrant_ssh.md"
msgid ""
"\n"
"# Vagrant SSH Integration\n"
"\n"
"This document explains how to set up SSH-based communication with Vagrant"
" virtual machines using Fathom.\n"
"\n"
"## Overview\n"
"\n"
"The application can connect to Vagrant VMs using two methods:\n"
"- gRPC service (original implementation)\n"
"- SSH via Fathom (new implementation)\n"
"\n"
"The SSH approach allows for direct communication with Vagrant hosts "
"without the need for a custom gRPC server.\n"
"\n"
"## Setup\n"
"\n"
"### 1. Generate SSH Keys\n"
"\n"
"Run the following script to generate SSH keys:\n"
"\n"
"```bash\n"
"python scripts/setup_ssh.py\n"
"```\n"
"\n"
"This will:\n"
"- Create an SSH key pair in `~/.ssh/replit` and `~/.ssh/replit.pub`\n"
"- Set up proper permissions\n"
"- Create a configuration in `~/.ssh/config` for Vagrant hosts\n"
"\n"
"### 2. Configure Vagrant Hosts\n"
"\n"
"You have two options to configure your Vagrant hosts:\n"
"\n"
"#### Option 1: Manual Setup\n"
"\n"
"Copy the public key to your Vagrant hosts' `authorized_keys` file:\n"
"\n"
"```bash\n"
"# Get the public key\n"
"cat ~/.ssh/replit.pub\n"
"\n"
"# Then manually add it to your Vagrant host's authorized_keys file\n"
"```\n"
"\n"
"#### Option 2: Automated Setup\n"
"\n"
"Use the provided script:\n"
"\n"
"```bash\n"
"python scripts/setup_vagrant_ssh.py --host vagrant.example.com --user "
"vagrant --password vagrant\n"
"```\n"
"\n"
"Or with a configuration file:\n"
"\n"
"```bash\n"
"python scripts/setup_vagrant_ssh.py --config config/vagrant_hosts.json\n"
"```\n"
"\n"
"### 3. Configure Environment Variables\n"
"\n"
"Set the following environment variables:\n"
"\n"
"- `USE_VAGRANT_SSH`: Set to \"true\" to use SSH (default is \"true\")\n"
"- `DEFAULT_VAGRANT_SERVER`: The default Vagrant server to connect to\n"
"- `SSH_KEY_PATH`: Path to the SSH private key (default: "
"\"~/.ssh/replit\")\n"
"- `VAGRANT_SSH_USER`: Username for SSH connection (default: \"vagrant\")\n"
"- `VAGRANT_SSH_KEY`: Direct SSH key content as a string (takes precedence"
" over `SSH_KEY_PATH`)\n"
"\n"
"## Usage\n"
"\n"
"The `VagrantClient` class will automatically use SSH when "
"`USE_VAGRANT_SSH` is set to \"true\".\n"
"\n"
"### Examples\n"
"\n"
"```python\n"
"from api.services.vagrant_client import VagrantClient\n"
"\n"
"async def example():\n"
"    client = VagrantClient()\n"
"    await client.connect()\n"
"    \n"
"    # Get VM status\n"
"    status = await client.status(\"default\")\n"
"    print(f\"VM status: {status}\")\n"
"    \n"
"    # Execute command on VM\n"
"    result = await client.execute_command(\"default\", \"ls -la\")\n"
"    print(f\"Command output: {result['stdout']}\")\n"
"```\n"
"\n"
"## Testing\n"
"\n"
"Run the tests for the SSH implementation:\n"
"\n"
"```bash\n"
"python scripts/run_tests.py --file api/tests/test_fathom_ssh_client.py\n"
"```\n"
"\n"
"## Troubleshooting\n"
"\n"
"### SSH Connection Issues\n"
"\n"
"1. Verify SSH key setup:\n"
"   ```bash\n"
"   ls -la ~/.ssh/replit ~/.ssh/replit.pub\n"
"   ```\n"
"\n"
"2. Test SSH connection manually:\n"
"   ```bash\n"
"   ssh -i ~/.ssh/replit <EMAIL> \"echo Connection "
"successful\"\n"
"   ```\n"
"\n"
"3. Check SSH debug logs:\n"
"   ```bash\n"
"   ssh -i ~/.ssh/replit -vvv <EMAIL>\n"
"   ```\n"
"\n"
"### Permission Issues\n"
"\n"
"1. Check key permissions:\n"
"   ```bash\n"
"   chmod 600 ~/.ssh/replit\n"
"   chmod 644 ~/.ssh/replit.pub\n"
"   ```\n"
"\n"
"2. Verify authorized_keys on the Vagrant host:\n"
"   ```bash\n"
"   ssh <EMAIL> \"cat ~/.ssh/authorized_keys\"\n"
"   ```\n"
msgstr ""
"[RO] \n"
"# Vagrant SSH Integration\n"
"\n"
"This document explains how to set up SSH-based communication with Vagrant"
" virtual machines using Fathom.\n"
"\n"
"## Overview\n"
"\n"
"The application can connect to Vagrant VMs using two methods:\n"
"- gRPC service (original implementation)\n"
"- SSH via Fathom (new implementation)\n"
"\n"
"The SSH approach allows for direct communication with Vagrant hosts "
"without the need for a custom gRPC server.\n"
"\n"
"## Setup\n"
"\n"
"### 1. Generate SSH Keys\n"
"\n"
"Run the following script to generate SSH keys:\n"
"\n"
"```bash\n"
"python scripts/setup_ssh.py\n"
"```\n"
"\n"
"This will:\n"
"- Create an SSH key pair in `~/.ssh/replit` and `~/.ssh/replit.pub`\n"
"- Set up proper permissions\n"
"- Create a configuration in `~/.ssh/config` for Vagrant hosts\n"
"\n"
"### 2. Configure Vagrant Hosts\n"
"\n"
"You have two options to configure your Vagrant hosts:\n"
"\n"
"#### Option 1: Manual Setup\n"
"\n"
"Copy the public key to your Vagrant hosts' `authorized_keys` file:\n"
"\n"
"```bash\n"
"# Get the public key\n"
"cat ~/.ssh/replit.pub\n"
"\n"
"# Then manually add it to your Vagrant host's authorized_keys file\n"
"```\n"
"\n"
"#### Option 2: Automated Setup\n"
"\n"
"Use the provided script:\n"
"\n"
"```bash\n"
"python scripts/setup_vagrant_ssh.py --host vagrant.example.com --user "
"vagrant --password vagrant\n"
"```\n"
"\n"
"Or with a configuration file:\n"
"\n"
"```bash\n"
"python scripts/setup_vagrant_ssh.py --config config/vagrant_hosts.json\n"
"```\n"
"\n"
"### 3. Configure Environment Variables\n"
"\n"
"Set the following environment variables:\n"
"\n"
"- `USE_VAGRANT_SSH`: Set to \"true\" to use SSH (default is \"true\")\n"
"- `DEFAULT_VAGRANT_SERVER`: The default Vagrant server to connect to\n"
"- `SSH_KEY_PATH`: Path to the SSH private key (default: "
"\"~/.ssh/replit\")\n"
"- `VAGRANT_SSH_USER`: Username for SSH connection (default: \"vagrant\")\n"
"- `VAGRANT_SSH_KEY`: Direct SSH key content as a string (takes precedence"
" over `SSH_KEY_PATH`)\n"
"\n"
"## Usage\n"
"\n"
"The `VagrantClient` class will automatically use SSH when "
"`USE_VAGRANT_SSH` is set to \"true\".\n"
"\n"
"### Examples\n"
"\n"
"```python\n"
"from api.services.vagrant_client import VagrantClient\n"
"\n"
"async def example():\n"
"    client = VagrantClient()\n"
"    await client.connect()\n"
"    \n"
"    # Get VM status\n"
"    status = await client.status(\"default\")\n"
"    print(f\"VM status: {status}\")\n"
"    \n"
"    # Execute command on VM\n"
"    result = await client.execute_command(\"default\", \"ls -la\")\n"
"    print(f\"Command output: {result['stdout']}\")\n"
"```\n"
"\n"
"## Testing\n"
"\n"
"Run the tests for the SSH implementation:\n"
"\n"
"```bash\n"
"python scripts/run_tests.py --file api/tests/test_fathom_ssh_client.py\n"
"```\n"
"\n"
"## Troubleshooting\n"
"\n"
"### SSH Connection Issues\n"
"\n"
"1. Verify SSH key setup:\n"
"   ```bash\n"
"   ls -la ~/.ssh/replit ~/.ssh/replit.pub\n"
"   ```\n"
"\n"
"2. Test SSH connection manually:\n"
"   ```bash\n"
"   ssh -i ~/.ssh/replit <EMAIL> \"echo Connection "
"successful\"\n"
"   ```\n"
"\n"
"3. Check SSH debug logs:\n"
"   ```bash\n"
"   ssh -i ~/.ssh/replit -vvv <EMAIL>\n"
"   ```\n"
"\n"
"### Permission Issues\n"
"\n"
"1. Check key permissions:\n"
"   ```bash\n"
"   chmod 600 ~/.ssh/replit\n"
"   chmod 644 ~/.ssh/replit.pub\n"
"   ```\n"
"\n"
"2. Verify authorized_keys on the Vagrant host:\n"
"   ```bash\n"
"   ssh <EMAIL> \"cat ~/.ssh/authorized_keys\"\n"
"   ```\n"

