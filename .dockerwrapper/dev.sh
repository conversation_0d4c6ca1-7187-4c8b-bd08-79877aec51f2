#!/bin/bash

# Script to manage the development environment

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# Function to display usage
function show_usage {
    echo -e "${YELLOW}Usage:${NC} $0 [start|stop|restart|logs|status]"
    echo ""
    echo "Commands:"
    echo "  start   - Start the development environment with live editing"
    echo "  stop    - Stop the development environment"
    echo "  restart - Restart the development environment"
    echo "  logs    - Show logs from all containers"
    echo "  ps      - Show running containers"
}

# Check for command
if [ $# -eq 0 ]; then
    usage
    exit 1
fi

# Function to stop the development environment
function stop_dev {
    echo -e "${YELLOW}Stopping development environment...${NC}"
    docker compose -f dev-compose.yml down
    echo -e "${GREEN}Development environment stopped.${NC}"
}

# Function to show logs
function show_logs {
    echo -e "${GREEN}Showing logs from all containers...${NC}"
    docker compose -f dev-compose.yml logs -f
}

# Function to show status
function show_status {
    echo -e "${GREEN}Status of development containers:${NC}"
    docker compose -f dev-compose.yml ps
}

# Main script logic
case "$1" in
    up)
        echo -e "${GREEN}Starting containers...${NC}"
        docker compose -f dev-compose.yml up -d
        ;;
    down)
        echo -e "${GREEN}Stopping containers...${NC}"
        docker compose -f dev-compose.yml down
        ;;
    logs)
        echo -e "${GREEN}Showing logs...${NC}"
        docker compose -f dev-compose.yml logs -f
        ;;
    ps)
        echo -e "${GREEN}Listing containers...${NC}"
        docker compose -f dev-compose.yml ps
        ;;
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        usage
        exit 1
        ;;
esac