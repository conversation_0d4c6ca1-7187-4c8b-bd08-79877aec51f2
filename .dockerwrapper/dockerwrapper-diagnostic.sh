#!/bin/bash
# Progressive Diagnostic .dockerwrapper
# Provides layered diagnostics for FastAPI container deployment

# Configuration
APP_NAME="fastapi-app"
CONTAINER_NAME="${APP_NAME}-container"
IMAGE_NAME="${APP_NAME}-image"
NETWORK_NAME="${APP_NAME}-network"
DB_CONTAINER_NAME="${APP_NAME}-db"
LOG_DIR="./logs"
DIAGNOSTIC_LOG="${LOG_DIR}/docker-diagnostics.log"

# Set defaults
DIAGNOSTIC_MODE=0
VERBOSE_LEVEL=1
AUTO_REPAIR=0
TIMEOUT_SECONDS=120

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Prepare log directory
mkdir -p "${LOG_DIR}"
touch "${DIAGNOSTIC_LOG}"

# Function to display timestamp
timestamp() {
    date "+%Y-%m-%d %H:%M:%S"
}

# Logging functions with different levels
log_info() {
    echo -e "${BLUE}[$(timestamp) INFO]${NC} $1"
    echo "[$(timestamp) INFO] $1" >> "${DIAGNOSTIC_LOG}"
}

log_success() {
    echo -e "${GREEN}[$(timestamp) SUCCESS]${NC} $1"
    echo "[$(timestamp) SUCCESS] $1" >> "${DIAGNOSTIC_LOG}"
}

log_warning() {
    echo -e "${YELLOW}[$(timestamp) WARNING]${NC} $1"
    echo "[$(timestamp) WARNING] $1" >> "${DIAGNOSTIC_LOG}"
}

log_error() {
    echo -e "${RED}[$(timestamp) ERROR]${NC} $1"
    echo "[$(timestamp) ERROR] $1" >> "${DIAGNOSTIC_LOG}"
}

log_diagnostic() {
    if [ $DIAGNOSTIC_MODE -ge 1 ]; then
        echo -e "${MAGENTA}[$(timestamp) DIAGNOSTIC]${NC} $1"
        echo "[$(timestamp) DIAGNOSTIC] $1" >> "${DIAGNOSTIC_LOG}"
    fi
}

# Function to display step info
display_step() {
    echo -e "\n${CYAN}=== STEP $1: $2 ===${NC}"
    echo -e "=== STEP $1: $2 ===\n" >> "${DIAGNOSTIC_LOG}"
}

# Function to check if docker is available
check_docker() {
    display_step "1" "Checking Docker availability"

    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        log_info "Please install Docker: https://docs.docker.com/get-docker/"
        return 1
    fi

    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        log_info "Start Docker daemon and try again"
        return 1
    fi

    log_success "Docker is installed and running"

    # Show Docker version in diagnostic mode
    if [ $DIAGNOSTIC_MODE -ge 1 ]; then
        docker_version=$(docker --version)
        log_diagnostic "Docker version: ${docker_version}"

        # Check if docker compose is installed
        if command -v docker &> /dev/null; then
            compose_version=$(docker compose version 2>/dev/null || echo "Docker Compose plugin not installed")
            log_diagnostic "Docker Compose version: ${compose_version}"
        else
            log_diagnostic "Docker Compose not found (not required but recommended)"
        fi
    fi

    return 0
}

# Check for ports in use
check_ports() {
    display_step "2" "Checking for port conflicts"

    local port_conflicts=0
    local required_ports=(8000 5432)

    for port in "${required_ports[@]}"; do
        if [ $DIAGNOSTIC_MODE -ge 1 ]; then
            log_diagnostic "Checking if port $port is available..."
        fi

        # Check if port is in use
        if lsof -i:${port} &> /dev/null; then
            log_warning "Port ${port} is already in use"

            # In verbose mode, show what's using the port
            if [ $DIAGNOSTIC_MODE -ge 2 ]; then
                local port_info=$(lsof -i:${port})
                log_diagnostic "Port ${port} is used by:\n${port_info}"
            fi

            port_conflicts=$((port_conflicts + 1))
        else
            log_info "Port ${port} is available"
        fi
    done

    if [ $port_conflicts -gt 0 ]; then
        log_error "${port_conflicts} port conflict(s) detected"
        log_info "Please free up the required ports and try again"
        return 1
    fi

    log_success "All required ports are available"
    return 0
}

# Clean up existing containers if they exist
cleanup_existing() {
    display_step "3" "Cleaning up existing containers"

    # Check if container exists
    if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_info "Found existing container: ${CONTAINER_NAME}"

        # Check if container is running
        if docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
            log_info "Stopping container: ${CONTAINER_NAME}"
            docker stop "${CONTAINER_NAME}" > /dev/null

            if [ $? -ne 0 ]; then
                log_error "Failed to stop container: ${CONTAINER_NAME}"
                return 1
            fi
        fi

        log_info "Removing container: ${CONTAINER_NAME}"
        docker rm "${CONTAINER_NAME}" > /dev/null

        if [ $? -ne 0 ]; then
            log_error "Failed to remove container: ${CONTAINER_NAME}"
            return 1
        fi
    else
        log_info "No existing container found: ${CONTAINER_NAME}"
    fi

    # Same for DB container if it exists
    if docker ps -a --format '{{.Names}}' | grep -q "^${DB_CONTAINER_NAME}$"; then
        log_info "Found existing DB container: ${DB_CONTAINER_NAME}"

        if docker ps --format '{{.Names}}' | grep -q "^${DB_CONTAINER_NAME}$"; then
            log_info "Stopping DB container: ${DB_CONTAINER_NAME}"
            docker stop "${DB_CONTAINER_NAME}" > /dev/null

            if [ $? -ne 0 ]; then
                log_error "Failed to stop DB container: ${DB_CONTAINER_NAME}"
                return 1
            fi
        fi

        log_info "Removing DB container: ${DB_CONTAINER_NAME}"
        docker rm "${DB_CONTAINER_NAME}" > /dev/null

        if [ $? -ne 0 ]; then
            log_error "Failed to remove DB container: ${DB_CONTAINER_NAME}"
            return 1
        fi
    else
        log_info "No existing DB container found: ${DB_CONTAINER_NAME}"
    fi

    log_success "Cleanup completed successfully"
    return 0
}

# Set up Docker network
setup_network() {
    display_step "4" "Setting up Docker network"

    # Check if network already exists
    if docker network ls --format '{{.Name}}' | grep -q "^${NETWORK_NAME}$"; then
        log_info "Network already exists: ${NETWORK_NAME}"
    else
        log_info "Creating network: ${NETWORK_NAME}"
        docker network create "${NETWORK_NAME}" > /dev/null

        if [ $? -ne 0 ]; then
            log_error "Failed to create network: ${NETWORK_NAME}"
            return 1
        fi
    fi

    log_success "Network setup completed"
    return 0
}

# Build container image
build_image() {
    display_step "5" "Building container image"

    local build_log="${LOG_DIR}/docker-build.log"
    log_info "Building image: ${IMAGE_NAME}"
    log_info "This may take a few minutes..."

    # Execute docker build, writing to both the build log and diagnostic log
    if [ $DIAGNOSTIC_MODE -ge 2 ]; then
        # In high verbosity mode, show the build output in real-time
        docker build -t "${IMAGE_NAME}" . | tee "${build_log}"
    else
        # In normal mode, redirect output to log file
        docker build -t "${IMAGE_NAME}" . > "${build_log}" 2>&1
    fi

    if [ $? -ne 0 ]; then
        log_error "Build failed. See build log for details: ${build_log}"

        # Extract the last 10 lines of the build log for context
        local build_error=$(tail -n 10 "${build_log}")
        log_error "Last lines of build log:\n${build_error}"

        # Provide specific guidance based on common errors
        if grep -q "connection refused" "${build_log}"; then
            log_error "Connection issues detected. Check your network connectivity."
        elif grep -q "apt-get" "${build_log}"; then
            log_error "Package installation issues. You may need to update your Dockerfile."
        elif grep -q "pip install" "${build_log}"; then
            log_error "Python dependency issues. Check your requirements.txt file."
        fi

        return 1
    fi

    log_success "Image built successfully: ${IMAGE_NAME}"
    return 0
}

# Start PostgreSQL container
start_database() {
    display_step "6" "Starting PostgreSQL database"

    local pg_password="postgres"

    log_info "Starting PostgreSQL container: ${DB_CONTAINER_NAME}"
    docker run -d \
        --name "${DB_CONTAINER_NAME}" \
        --network "${NETWORK_NAME}" \
        -e POSTGRES_PASSWORD="${pg_password}" \
        -e POSTGRES_USER="postgres" \
        -e POSTGRES_DB="app" \
        -p 5432:5432 \
        postgres:13 > /dev/null

    if [ $? -ne 0 ]; then
        log_error "Failed to start PostgreSQL container"
        return 1
    fi

    # Wait for PostgreSQL to start up
    log_info "Waiting for PostgreSQL to initialize..."

    local pg_ready=0
    local max_attempts=30
    local attempts=0

    while [ $pg_ready -eq 0 ] && [ $attempts -lt $max_attempts ]; do
        attempts=$((attempts + 1))

        # Check if PostgreSQL is accepting connections
        docker exec "${DB_CONTAINER_NAME}" pg_isready -U postgres &> /dev/null

        if [ $? -eq 0 ]; then
            pg_ready=1
        else
            sleep 1
            if [ $DIAGNOSTIC_MODE -ge 2 ]; then
                log_diagnostic "PostgreSQL not ready, waiting... (Attempt ${attempts}/${max_attempts})"
            fi
        fi
    done

    if [ $pg_ready -eq 0 ]; then
        log_error "PostgreSQL failed to initialize in time"
        log_info "Check PostgreSQL logs: docker logs ${DB_CONTAINER_NAME}"
        return 1
    fi

    log_success "PostgreSQL started successfully"

    # In diagnostic mode, show the connection details
    if [ $DIAGNOSTIC_MODE -ge 1 ]; then
        log_diagnostic "PostgreSQL connection details:"
        log_diagnostic "  - Host: localhost"
        log_diagnostic "  - Port: 5432"
        log_diagnostic "  - User: postgres"
        log_diagnostic "  - Password: ${pg_password}"
        log_diagnostic "  - Database: app"
        log_diagnostic "  - Connection string: postgresql://postgres:${pg_password}@localhost:5432/app"
    fi

    return 0
}

# Start the application container
start_application() {
    display_step "7" "Starting application container"

    local db_host="${DB_CONTAINER_NAME}"
    local db_user="postgres"
    local db_password="postgres"
    local db_name="app"

    # Prepare database URLs for both sync and async connections
    local db_url_async="postgresql+asyncpg://${db_user}:${db_password}@${db_host}:5432/${db_name}"
    local db_url_sync="postgresql+psycopg2://${db_user}:${db_password}@${db_host}:5432/${db_name}"

    log_info "Starting application container: ${CONTAINER_NAME}"
    docker run -d \
        --name "${CONTAINER_NAME}" \
        --network "${NETWORK_NAME}" \
        -e DATABASE_URL_ASYNC="${db_url_async}" \
        -e DATABASE_URL_SYNC="${db_url_sync}" \
        -e ENVIRONMENT="production" \
        -p 8000:8000 \
        "${IMAGE_NAME}" > /dev/null

    if [ $? -ne 0 ]; then
        log_error "Failed to start application container"
        return 1
    fi

    log_info "Application container started"

    # In diagnostic mode, show more details
    if [ $DIAGNOSTIC_MODE -ge 1 ]; then
        log_diagnostic "Application connection details:"
        log_diagnostic "  - Application URL: http://localhost:8000"
        log_diagnostic "  - API Docs: http://localhost:8000/docs"
        log_diagnostic "  - Health Check: http://localhost:8000/status"
    fi

    return 0
}

# Verify application health
verify_application() {
    display_step "8" "Verifying application health"

    log_info "Waiting for application to initialize..."

    local app_ready=0
    local max_attempts=30
    local attempts=0

    while [ $app_ready -eq 0 ] && [ $attempts -lt $max_attempts ]; do
        attempts=$((attempts + 1))

        # Use curl to check if the application is responding
        local http_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/status)

        if [ "$http_code" = "200" ]; then
            app_ready=1
        else
            sleep 1
            if [ $DIAGNOSTIC_MODE -ge 2 ]; then
                log_diagnostic "Application not ready, waiting... (Attempt ${attempts}/${max_attempts}, HTTP code: ${http_code})"
            fi
        fi
    done

    if [ $app_ready -eq 0 ]; then
        log_error "Application failed to initialize in time"
        log_info "Check application logs: docker logs ${CONTAINER_NAME}"

        # In diagnostic mode, show more detailed troubleshooting steps
        if [ $DIAGNOSTIC_MODE -ge 1 ]; then
            log_diagnostic "Troubleshooting steps:"
            log_diagnostic "1. Check if database is accessible from application container:"
            log_diagnostic "   docker exec ${CONTAINER_NAME} python -c \"import psycopg2; conn=psycopg2.connect('postgresql://postgres:postgres@${DB_CONTAINER_NAME}:5432/app'); print('Connected successfully!');\""
            log_diagnostic "2. Check if application is listening on correct port:"
            log_diagnostic "   docker exec ${CONTAINER_NAME} netstat -tulpn | grep 8000"
            log_diagnostic "3. Check for errors in application log:"
            log_diagnostic "   docker logs ${CONTAINER_NAME}"
        fi

        return 1
    fi

    log_success "Application is up and running!"

    # Perform additional health checks if in diagnostic mode
    if [ $DIAGNOSTIC_MODE -ge 1 ]; then
        # Fetch health check response
        log_diagnostic "Fetching health check details..."
        local health_response=$(curl -s http://localhost:8000/status)
        log_diagnostic "Health check response: ${health_response}"

        # Check API docs accessibility
        log_diagnostic "Verifying API documentation..."
        local docs_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs)

        if [ "$docs_code" = "200" ]; then
            log_diagnostic "API documentation is accessible at http://localhost:8000/docs"
        else
            log_warning "API documentation is not accessible (HTTP code: ${docs_code})"
        fi
    fi

    return 0
}

# Show application information
show_info() {
    display_step "9" "Application Information"

    echo -e "\n${GREEN}=== Application Successfully Deployed ===${NC}"
    echo "Application URL: http://localhost:8000"
    echo "API Documentation: http://localhost:8000/docs"
    echo "Health Status: http://localhost:8000/status"
    echo ""
    echo "Container Information:"
    echo "  - Application: ${CONTAINER_NAME}"
    echo "  - Database: ${DB_CONTAINER_NAME}"
    echo ""
    echo "Useful Commands:"
    echo "  - View application logs: docker logs ${CONTAINER_NAME}"
    echo "  - View database logs: docker logs ${DB_CONTAINER_NAME}"
    echo "  - Stop application: docker stop ${CONTAINER_NAME} ${DB_CONTAINER_NAME}"
    echo "  - Remove containers: docker rm ${CONTAINER_NAME} ${DB_CONTAINER_NAME}"
    echo ""
    echo "Diagnostic log: ${DIAGNOSTIC_LOG}"
    echo -e "${GREEN}============================================${NC}\n"
}

# Display usage information
usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -d, --diagnostic   Enable diagnostic mode"
    echo "  -v, --verbose      Increase verbosity level"
    echo "  -r, --repair       Enable auto-repair mode"
    echo "  -c, --cleanup      Clean up existing containers and exit"
    echo "  -h, --help         Display this help message"
    echo ""
    echo "Examples:"
    echo "  $0                 Start containers normally"
    echo "  $0 --diagnostic    Start with basic diagnostics"
    echo "  $0 -d -v           Start with detailed diagnostics"
    echo "  $0 --cleanup       Only cleanup existing containers"
    echo ""
}

# Process command line arguments
process_args() {
    while [ "$1" != "" ]; do
        case $1 in
            -d | --diagnostic )
                DIAGNOSTIC_MODE=1
                ;;
            -v | --verbose )
                VERBOSE_LEVEL=$((VERBOSE_LEVEL + 1))
                # If verbosity is increased, also increase diagnostic level
                if [ $VERBOSE_LEVEL -gt 1 ] && [ $DIAGNOSTIC_MODE -ge 1 ]; then
                    DIAGNOSTIC_MODE=2
                fi
                ;;
            -r | --repair )
                AUTO_REPAIR=1
                ;;
            -c | --cleanup )
                cleanup_existing
                exit $?
                ;;
            -h | --help )
                usage
                exit 0
                ;;
            * )
                usage
                exit 1
                ;;
        esac
        shift
    done
}

# Main function to coordinate all steps
main() {
    echo -e "${CYAN}=== FastAPI Application Deployment ($(timestamp)) ===${NC}"
    echo "Logging to: ${DIAGNOSTIC_LOG}"

    if [ $DIAGNOSTIC_MODE -ge 1 ]; then
        log_diagnostic "Diagnostic mode enabled (level ${DIAGNOSTIC_MODE})"
        log_diagnostic "Auto-repair mode: $([[ $AUTO_REPAIR -eq 1 ]] && echo "Enabled" || echo "Disabled")"
    fi

    # Step 1: Check Docker
    check_docker
    if [ $? -ne 0 ]; then
        log_error "Docker check failed. Cannot continue."
        exit 1
    fi

    # Step 2: Check ports
    check_ports
    if [ $? -ne 0 ]; then
        log_error "Port check failed. Cannot continue."
        exit 1
    fi

    # Step 3: Clean up existing containers
    cleanup_existing
    if [ $? -ne 0 ]; then
        log_error "Cleanup failed. Cannot continue."
        exit 1
    fi

    # Step 4: Set up network
    setup_network
    if [ $? -ne 0 ]; then
        log_error "Network setup failed. Cannot continue."
        exit 1
    fi

    # Step 5: Build image
    build_image
    if [ $? -ne 0 ]; then
        log_error "Image build failed. Cannot continue."
        exit 1
    fi

    # Step 6: Start database
    start_database
    if [ $? -ne 0 ]; then
        log_error "Database startup failed. Cannot continue."
        exit 1
    fi

    # Step 7: Start application
    start_application
    if [ $? -ne 0 ]; then
        log_error "Application startup failed. Cannot continue."
        exit 1
    fi

    # Step 8: Verify application health
    verify_application
    if [ $? -ne 0 ]; then
        log_error "Application verification failed."

        if [ $AUTO_REPAIR -eq 1 ]; then
            log_warning "Attempting automatic repair..."

            # Get container logs for analysis
            app_logs=$(docker logs ${CONTAINER_NAME} 2>&1)

            # Check for common issues in the logs
            if echo "$app_logs" | grep -q "asyncpg.exceptions"; then
                log_warning "Database connection issue detected."
                log_info "Restarting PostgreSQL container..."

                docker restart ${DB_CONTAINER_NAME} > /dev/null
                sleep 5

                log_info "Restarting application container..."
                docker restart ${CONTAINER_NAME} > /dev/null

                # Verify again
                log_info "Verifying application again..."
                verify_application
                if [ $? -eq 0 ]; then
                    log_success "Auto-repair successful!"
                else
                    log_error "Auto-repair failed."
                    exit 1
                fi
            else
                log_error "Could not automatically repair. Manual intervention required."
                exit 1
            fi
        else
            log_info "Use --repair option for automatic repair attempts."
            exit 1
        fi
    fi

    # Step 9: Show application information
    show_info

    log_success "Deployment completed successfully"
    exit 0
}

# Process command line arguments
process_args "$@"

# Run main function
main
