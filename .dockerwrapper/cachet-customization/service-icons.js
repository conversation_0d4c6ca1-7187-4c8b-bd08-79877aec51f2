// TurdParty Service Icons for Cachet
(function() {
    'use strict';
    
    // Service icon mapping
    const serviceIcons = {
        '🚀 API': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2L2 7L12 12L22 7L12 2Z"/><path d="M2 17L12 22L22 17"/><path d="M2 12L12 17L22 12"/></svg>',
        '🌐 Frontend': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/></svg>',
        '⚡ Redis': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M4 6H20V18H4V6Z"/><path d="M4 12H20"/><path d="M8 8V16"/><path d="M16 8V16"/></svg>',
        '⚙️ Celery Default': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M12 1V3"/><path d="M12 21V23"/><path d="M4.22 4.22L5.64 5.64"/><path d="M18.36 18.36L19.78 19.78"/><path d="M1 12H3"/><path d="M21 12H23"/><path d="M4.22 19.78L5.64 18.36"/><path d="M18.36 5.64L19.78 4.22"/></svg>',
        '📁 Celery File Ops': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M12 1V3"/><path d="M12 21V23"/><path d="M4.22 4.22L5.64 5.64"/><path d="M18.36 18.36L19.78 19.78"/><path d="M1 12H3"/><path d="M21 12H23"/><path d="M4.22 19.78L5.64 18.36"/><path d="M18.36 5.64L19.78 4.22"/></svg>',
        '🖥️ Celery VM Ops': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M12 1V3"/><path d="M12 21V23"/><path d="M4.22 4.22L5.64 5.64"/><path d="M18.36 18.36L19.78 19.78"/><path d="M1 12H3"/><path d="M21 12H23"/><path d="M4.22 19.78L5.64 18.36"/><path d="M18.36 5.64L19.78 4.22"/></svg>',
        '🌸 Celery Flower': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z"/><circle cx="12" cy="12" r="3"/></svg>',
        '🗄️ PostgreSQL': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M21 12C21 13.66 16.97 15 12 15S3 13.66 3 12"/><path d="M3 5V19C3 20.66 7.03 22 12 22S21 20.66 21 19V5"/></svg>',
        '📦 MinIO': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/></svg>',
        // Fallback for services without emojis
        'API': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2L2 7L12 12L22 7L12 2Z"/><path d="M2 17L12 22L22 17"/><path d="M2 12L12 17L22 12"/></svg>',
        'Frontend': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/></svg>',
        'Redis': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M4 6H20V18H4V6Z"/><path d="M4 12H20"/><path d="M8 8V16"/><path d="M16 8V16"/></svg>',
        'Celery Default': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M12 1V3"/><path d="M12 21V23"/><path d="M4.22 4.22L5.64 5.64"/><path d="M18.36 18.36L19.78 19.78"/><path d="M1 12H3"/><path d="M21 12H23"/><path d="M4.22 19.78L5.64 18.36"/><path d="M18.36 5.64L19.78 4.22"/></svg>',
        'Celery File Ops': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M12 1V3"/><path d="M12 21V23"/><path d="M4.22 4.22L5.64 5.64"/><path d="M18.36 18.36L19.78 19.78"/><path d="M1 12H3"/><path d="M21 12H23"/><path d="M4.22 19.78L5.64 18.36"/><path d="M18.36 5.64L19.78 4.22"/></svg>',
        'Celery VM Ops': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M12 1V3"/><path d="M12 21V23"/><path d="M4.22 4.22L5.64 5.64"/><path d="M18.36 18.36L19.78 19.78"/><path d="M1 12H3"/><path d="M21 12H23"/><path d="M4.22 19.78L5.64 18.36"/><path d="M18.36 5.64L19.78 4.22"/></svg>',
        'Celery Flower': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z"/><circle cx="12" cy="12" r="3"/></svg>',
        'PostgreSQL': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M21 12C21 13.66 16.97 15 12 15S3 13.66 3 12"/><path d="M3 5V19C3 20.66 7.03 22 12 22S21 20.66 21 19V5"/></svg>',
        'MinIO': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/></svg>'
    };
    
    function addServiceIcons() {
        // Find all component names and add icons
        const components = document.querySelectorAll('.component-name, .component h4, [class*="component"] h4, .component .name');
        
        components.forEach(component => {
            const text = component.textContent.trim();
            const icon = serviceIcons[text];
            
            if (icon && !component.querySelector('.service-icon')) {
                const iconSpan = document.createElement('span');
                iconSpan.className = 'service-icon';
                iconSpan.innerHTML = icon;
                iconSpan.style.marginRight = '8px';
                iconSpan.style.color = '#4a9eff';
                iconSpan.style.verticalAlign = 'middle';
                
                component.insertBefore(iconSpan, component.firstChild);
            }
        });
        
        // Also check for component links and names in different structures
        const componentLinks = document.querySelectorAll('a[href*="component"], .component-link, .service-name');
        componentLinks.forEach(link => {
            const text = link.textContent.trim();
            const icon = serviceIcons[text];
            
            if (icon && !link.querySelector('.service-icon')) {
                const iconSpan = document.createElement('span');
                iconSpan.className = 'service-icon';
                iconSpan.innerHTML = icon;
                iconSpan.style.marginRight = '8px';
                iconSpan.style.color = '#4a9eff';
                iconSpan.style.verticalAlign = 'middle';
                
                link.insertBefore(iconSpan, link.firstChild);
            }
        });
    }
    
    // Add icons when page loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addServiceIcons);
    } else {
        addServiceIcons();
    }
    
    // Re-add icons if content changes (for dynamic updates)
    const observer = new MutationObserver(function(mutations) {
        let shouldUpdate = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                shouldUpdate = true;
            }
        });
        if (shouldUpdate) {
            setTimeout(addServiceIcons, 100);
        }
    });
    
    observer.observe(document.body, { 
        childList: true, 
        subtree: true 
    });
    
    // Also run periodically to catch any missed updates
    setInterval(addServiceIcons, 5000);
    
})();
