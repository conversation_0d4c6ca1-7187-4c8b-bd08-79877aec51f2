#!/bin/bash

# Customize Cachet layout to include dark theme and service icons
set -e

echo "🎨 Customizing Cachet layout for dark theme..."

# Backup original layout if not already backed up
if [ ! -f "/var/www/html/resources/views/layout/master.blade.php.original" ]; then
    cp /var/www/html/resources/views/layout/master.blade.php /var/www/html/resources/views/layout/master.blade.php.original
    echo "✅ Backed up original master layout"
fi

# Check if customization is already applied
if grep -q "dark-theme.css" /var/www/html/resources/views/layout/master.blade.php; then
    echo "✅ Dark theme already applied to layout"
else
    echo "🔧 Applying dark theme to layout..."
    
    # Insert dark theme CSS and service icons JS before </head>
    sed -i '/<\/head>/i\    <!-- TurdParty Custom Dark Theme -->' /var/www/html/resources/views/layout/master.blade.php
    sed -i '/<\/head>/i\    <link rel="stylesheet" href="{{ asset('"'"'css/dark-theme.css'"'"') }}">' /var/www/html/resources/views/layout/master.blade.php
    sed -i '/<\/head>/i\    <script src="{{ asset('"'"'js/service-icons.js'"'"') }}" defer></script>' /var/www/html/resources/views/layout/master.blade.php
    sed -i '/<\/head>/i\    <style>' /var/www/html/resources/views/layout/master.blade.php
    sed -i '/<\/head>/i\        /* Ensure dark theme is applied immediately */' /var/www/html/resources/views/layout/master.blade.php
    sed -i '/<\/head>/i\        body.status-page {' /var/www/html/resources/views/layout/master.blade.php
    sed -i '/<\/head>/i\            background-color: #1a1a1a !important;' /var/www/html/resources/views/layout/master.blade.php
    sed -i '/<\/head>/i\            color: #ffffff !important;' /var/www/html/resources/views/layout/master.blade.php
    sed -i '/<\/head>/i\        }' /var/www/html/resources/views/layout/master.blade.php
    sed -i '/<\/head>/i\    </style>' /var/www/html/resources/views/layout/master.blade.php
    
    echo "✅ Dark theme applied to layout"
fi

# Ensure CSS and JS directories exist
mkdir -p /var/www/html/public/css
mkdir -p /var/www/html/public/js

# Copy theme files if they don't exist
if [ ! -f "/var/www/html/public/css/dark-theme.css" ]; then
    cp /var/www/html/public/css/dark-theme.css.template /var/www/html/public/css/dark-theme.css 2>/dev/null || echo "⚠️  Dark theme CSS template not found"
fi

if [ ! -f "/var/www/html/public/js/service-icons.js" ]; then
    cp /var/www/html/public/js/service-icons.js.template /var/www/html/public/js/service-icons.js 2>/dev/null || echo "⚠️  Service icons JS template not found"
fi

echo "🎨 Layout customization complete!"
