#!/bin/bash

# Initialize Cachet with TurdParty customizations
set -e

echo "🚀 Initializing Cachet with TurdParty customizations..."

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    if php /var/www/html/artisan migrate:status >/dev/null 2>&1; then
        echo "✅ Database is ready"
        break
    fi
    
    attempt=$((attempt + 1))
    echo "⏳ Waiting for database... (attempt $attempt/$max_attempts)"
    sleep 2
done

if [ $attempt -eq $max_attempts ]; then
    echo "❌ Database not ready after $max_attempts attempts"
    exit 1
fi

# Run migrations if needed
echo "🔧 Running database migrations..."
php /var/www/html/artisan migrate --force

# Check if component groups exist
echo "🔍 Checking component groups..."
GROUP_COUNT=$(php /var/www/html/artisan tinker --execute="echo \App\Models\ComponentGroup::count();" 2>/dev/null | tail -1 || echo "0")

if [ "$GROUP_COUNT" = "0" ]; then
    echo "📁 Creating component groups..."
    
    # Create component groups using Artisan tinker
    php /var/www/html/artisan tinker --execute="
    \App\Models\ComponentGroup::create(['name' => '🌐 Frontend Services', 'order' => 1, 'visible' => true]);
    \App\Models\ComponentGroup::create(['name' => '⚙️ Worker Services', 'order' => 2, 'visible' => true]);
    \App\Models\ComponentGroup::create(['name' => '🗄️ Backend Services', 'order' => 3, 'visible' => true]);
    echo 'Component groups created';
    " 2>/dev/null || echo "⚠️  Could not create groups via Artisan, will try SQL"
    
    # Fallback to direct SQL if Artisan fails
    if [ "$?" != "0" ]; then
        echo "🔧 Creating groups via SQL..."
        mysql -h"$DB_HOST" -u"$DB_USERNAME" -p"$DB_PASSWORD" "$DB_DATABASE" <<EOF
INSERT IGNORE INTO chq_component_groups (id, name, \`order\`, visible, created_at, updated_at) VALUES 
(1, '🌐 Frontend Services', 1, 1, NOW(), NOW()),
(2, '⚙️ Worker Services', 2, 1, NOW(), NOW()),
(3, '🗄️ Backend Services', 3, 1, NOW(), NOW());
EOF
    fi
    
    echo "✅ Component groups created"
else
    echo "✅ Component groups already exist"
fi

# Check if components exist
echo "🔍 Checking components..."
COMPONENT_COUNT=$(php /var/www/html/artisan tinker --execute="echo \App\Models\Component::count();" 2>/dev/null | tail -1 || echo "0")

if [ "$COMPONENT_COUNT" = "0" ]; then
    echo "🎯 Creating default components..."
    
    # Create default components
    php /var/www/html/artisan tinker --execute="
    \App\Models\Component::create(['name' => '🚀 API', 'description' => 'Main API service for TurdParty', 'status' => 1, 'group_id' => 1, 'order' => 1]);
    \App\Models\Component::create(['name' => '🌐 Frontend', 'description' => 'Frontend web interface', 'status' => 1, 'group_id' => 1, 'order' => 2]);
    \App\Models\Component::create(['name' => '⚡ Redis', 'description' => 'Cache and message broker', 'status' => 1, 'group_id' => 2, 'order' => 1]);
    \App\Models\Component::create(['name' => '⚙️ Celery Default', 'description' => 'Default task worker', 'status' => 1, 'group_id' => 2, 'order' => 2]);
    \App\Models\Component::create(['name' => '📁 Celery File Ops', 'description' => 'File operations task worker', 'status' => 1, 'group_id' => 2, 'order' => 3]);
    \App\Models\Component::create(['name' => '🖥️ Celery VM Ops', 'description' => 'VM operations task worker', 'status' => 1, 'group_id' => 2, 'order' => 4]);
    \App\Models\Component::create(['name' => '🌸 Celery Flower', 'description' => 'Celery monitoring tool', 'status' => 1, 'group_id' => 2, 'order' => 5]);
    \App\Models\Component::create(['name' => '🗄️ PostgreSQL', 'description' => 'Database', 'status' => 1, 'group_id' => 3, 'order' => 1]);
    \App\Models\Component::create(['name' => '📦 MinIO', 'description' => 'Object storage', 'status' => 1, 'group_id' => 3, 'order' => 2]);
    echo 'Components created';
    " 2>/dev/null || echo "⚠️  Could not create components via Artisan"
    
    echo "✅ Default components created"
else
    echo "✅ Components already exist"
fi

# Apply layout customizations
echo "🎨 Applying layout customizations..."
/usr/local/bin/customize-layout.sh

# Clear caches
echo "🧹 Clearing caches..."
php /var/www/html/artisan cache:clear
php /var/www/html/artisan view:clear
php /var/www/html/artisan config:clear

echo "🎉 Cachet initialization complete!"
