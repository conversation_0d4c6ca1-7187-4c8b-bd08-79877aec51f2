#!/bin/bash

# Custom entrypoint for Cachet with TurdParty customizations
set -e

echo "🚀 Starting Cachet with TurdParty customizations..."

# Run the original entrypoint preparations
if [ -f "/entrypoint.sh" ]; then
    echo "🔧 Running original entrypoint preparations..."
    # Source the original entrypoint but don't execute the final command
    source /entrypoint.sh || true
fi

# Wait a moment for any background processes
sleep 2

# Apply our customizations
echo "🎨 Applying TurdParty customizations..."
/usr/local/bin/init-cachet-customization.sh || echo "⚠️  Some customizations may have failed"

# Start the main process
echo "🌐 Starting Apache..."
exec "$@"
