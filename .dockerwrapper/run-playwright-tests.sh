#!/bin/bash

# Script to build and run Playwright tests in a dedicated container
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Default options
RESTART_CONTAINERS=true
FRESH_ENV=false
RUN_ALL_TESTS=false
HEADED_MODE=false
SPECIFIC_TEST=""

# Process command-line arguments
for arg in "$@"; do
  case $arg in
    --no-restart)
      RESTART_CONTAINERS=false
      shift
      ;;
    --fresh)
      FRESH_ENV=true
      RESTART_CONTAINERS=true
      shift
      ;;
    --all)
      RUN_ALL_TESTS=true
      shift
      ;;
    --headed)
      HEADED_MODE=true
      shift
      ;;
    *)
      if [[ "$arg" != --* && -n "$arg" ]]; then
        SPECIFIC_TEST="$arg"
      fi
      ;;
  esac
done

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Running Playwright Tests in Docker Container       ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Navigate to the root directory
cd "$(dirname "$0")/.."

# Create necessary directories
mkdir -p test_screenshots test-results playwright-report

# Function to check if a container is running and healthy
check_container_health() {
  local container_name=$1
  
  # Check if container exists and is running
  if docker ps | grep -q "$container_name"; then
    # Check if container has a health check
    if docker inspect "$container_name" | grep -q "Health"; then
      # Check if container is healthy
      health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name")
      if [ "$health_status" = "healthy" ]; then
        return 0  # Container is running and healthy
      else
        echo -e "${YELLOW}Container $container_name is running but not healthy (status: $health_status)${NC}"
        return 1  # Container is running but not healthy
      fi
    else
      # Container doesn't have health check, consider it healthy if running
      return 0
    fi
  else
    echo -e "${YELLOW}Container $container_name is not running${NC}"
    return 1  # Container is not running
  fi
}

# Check if we need to restart containers
all_containers_healthy=true
if [ "$FRESH_ENV" = true ]; then
  echo -e "${YELLOW}Fresh environment requested, stopping existing containers...${NC}"
  docker compose -f .dockerwrapper/docker-compose.playwright.yml down
  all_containers_healthy=false
elif [ "$RESTART_CONTAINERS" = true ]; then
  # Check if all required containers are running and healthy
  for container in "turdparty_playwright" "turdparty_api" "turdparty_ui" "turdparty_db" "turdparty_minio"; do
    if ! check_container_health "$container"; then
      all_containers_healthy=false
    fi
  done
  
  if [ "$all_containers_healthy" = false ]; then
    echo -e "${YELLOW}Some containers are not running or not healthy, stopping and restarting...${NC}"
    docker compose -f .dockerwrapper/docker-compose.playwright.yml down
  else
    echo -e "${GREEN}All containers are running and healthy${NC}"
  fi
fi

# Start containers if needed
if [ "$all_containers_healthy" = false ] || [ "$RESTART_CONTAINERS" = false ] && ! docker ps | grep -q "turdparty_playwright"; then
  echo -e "${YELLOW}Starting Playwright test environment...${NC}"
  docker compose -f .dockerwrapper/docker-compose.playwright.yml up -d --build
  
  # Give services time to initialize
  echo -e "${YELLOW}Waiting for services to be ready...${NC}"
  sleep 10
  
  # Check if the services are running properly
  echo -e "${YELLOW}Checking container health status...${NC}"
  for container in "turdparty_api" "turdparty_ui" "turdparty_playwright"; do
    if ! docker ps | grep -q "$container"; then
      echo -e "${RED}Error: $container is not running${NC}"
      docker compose -f .dockerwrapper/docker-compose.playwright.yml logs "$container"
      exit 1
    fi
  done
else
  echo -e "${GREEN}Using existing containers${NC}"
fi

# Determine which tests to run
TEST_ARGS=""
if [ "$RUN_ALL_TESTS" = true ]; then
  echo -e "${YELLOW}Running all tests${NC}"
  TEST_ARGS=""
elif [ -n "$SPECIFIC_TEST" ]; then
  echo -e "${YELLOW}Running specific test: $SPECIFIC_TEST${NC}"
  TEST_ARGS="$SPECIFIC_TEST"
else
  echo -e "${YELLOW}Running verification test${NC}"
  TEST_ARGS="tests/playwright/vagrant-integration.spec.js"
fi

# Set headed mode if requested
HEADED_ARGS=""
if [ "$HEADED_MODE" = true ]; then
  echo -e "${YELLOW}Running in headed mode${NC}"
  HEADED_ARGS="--headed"
fi

# Run the tests in the Playwright container
echo -e "${YELLOW}Running Playwright tests...${NC}"
docker exec -it turdparty_playwright bash -c "cd /app && npx playwright test $TEST_ARGS $HEADED_ARGS"
TEST_RESULT=$?

# Copy reports and screenshots
echo -e "${YELLOW}Copying test reports and screenshots...${NC}"
docker cp turdparty_playwright:/app/playwright-report/. ./playwright-report/ || true
docker cp turdparty_playwright:/app/test-results/. ./test-results/ || true
docker cp turdparty_playwright:/app/test_screenshots/. ./test_screenshots/ || true

# Check test result
if [ $TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}Tests completed successfully!${NC}"
  echo -e "${YELLOW}Test reports available at:${NC}"
  echo -e "  - test_screenshots/"
  echo -e "  - test-results/"
  echo -e "  - playwright-report/index.html"
else
  echo -e "${RED}Tests failed. Check the output above for details.${NC}"
  echo -e "${YELLOW}Logs available with: docker compose -f .dockerwrapper/docker-compose.playwright.yml logs${NC}"
fi

# Ask if we should clean up the containers
read -p "Do you want to stop the test containers? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
  echo -e "${YELLOW}Stopping test containers...${NC}"
  docker compose -f .dockerwrapper/docker-compose.playwright.yml down
else
  echo -e "${YELLOW}Leaving test containers running...${NC}"
  echo -e "${YELLOW}You can stop them with: docker compose -f .dockerwrapper/docker-compose.playwright.yml down${NC}"
fi

exit $TEST_RESULT 