input {
  # HTTP input for real-time Inspektor Gadget data
  http {
    port => 8080
    codec => json
    tags => ["inspektor-gadget"]
  }
  
  # File input for log files
  file {
    path => "/app/logs/inspektor-gadget/*.json"
    start_position => "beginning"
    codec => json
    tags => ["inspektor-gadget", "file"]
  }
  
  # Beats input for Filebeat
  beats {
    port => 5044
  }
}

filter {
  if "inspektor-gadget" in [tags] {
    # Parse timestamp
    if [timestamp] {
      date {
        match => [ "timestamp", "ISO8601" ]
      }
    }
    
    # Add file UUID correlation
    if [file_uuid] {
      mutate {
        add_field => { "correlation_id" => "%{file_uuid}" }
      }
    }
    
    # Process system call data
    if [gadget_type] == "trace_exec" {
      mutate {
        add_field => { "event_category" => "process" }
        add_field => { "event_type" => "execution" }
      }
    }
    
    # Process network data
    if [gadget_type] == "trace_tcp" or [gadget_type] == "trace_dns" {
      mutate {
        add_field => { "event_category" => "network" }
      }
      
      if [gadget_type] == "trace_tcp" {
        mutate {
          add_field => { "event_type" => "tcp_connection" }
        }
      }
      
      if [gadget_type] == "trace_dns" {
        mutate {
          add_field => { "event_type" => "dns_query" }
        }
      }
    }
    
    # Process file system data
    if [gadget_type] == "trace_open" or [gadget_type] == "trace_fsslower" {
      mutate {
        add_field => { "event_category" => "filesystem" }
      }
      
      if [gadget_type] == "trace_open" {
        mutate {
          add_field => { "event_type" => "file_open" }
        }
      }
      
      if [gadget_type] == "trace_fsslower" {
        mutate {
          add_field => { "event_type" => "file_operation" }
        }
      }
    }
    
    # Process security events
    if [gadget_type] == "trace_capabilities" or [gadget_type] == "audit_seccomp" {
      mutate {
        add_field => { "event_category" => "security" }
      }
      
      if [gadget_type] == "trace_capabilities" {
        mutate {
          add_field => { "event_type" => "capability_check" }
        }
      }
      
      if [gadget_type] == "audit_seccomp" {
        mutate {
          add_field => { "event_type" => "seccomp_audit" }
        }
      }
    }
    
    # Add enrichment data
    mutate {
      add_field => { "processed_at" => "%{@timestamp}" }
      add_field => { "source_system" => "turdparty" }
    }
    
    # Convert numeric fields
    if [pid] {
      mutate {
        convert => { "pid" => "integer" }
      }
    }
    
    if [uid] {
      mutate {
        convert => { "uid" => "integer" }
      }
    }
    
    if [gid] {
      mutate {
        convert => { "gid" => "integer" }
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "inspektor-gadget-%{+YYYY.MM.dd}"
    template_name => "inspektor-gadget"
    template_pattern => "inspektor-gadget-*"
    template => {
      "index_patterns" => ["inspektor-gadget-*"]
      "settings" => {
        "number_of_shards" => 1
        "number_of_replicas" => 0
      }
      "mappings" => {
        "properties" => {
          "@timestamp" => { "type" => "date" }
          "timestamp" => { "type" => "date" }
          "file_uuid" => { "type" => "keyword" }
          "correlation_id" => { "type" => "keyword" }
          "gadget_type" => { "type" => "keyword" }
          "event_category" => { "type" => "keyword" }
          "event_type" => { "type" => "keyword" }
          "pid" => { "type" => "integer" }
          "uid" => { "type" => "integer" }
          "gid" => { "type" => "integer" }
          "comm" => { "type" => "keyword" }
          "filename" => { "type" => "text" }
          "src_ip" => { "type" => "ip" }
          "dst_ip" => { "type" => "ip" }
          "src_port" => { "type" => "integer" }
          "dst_port" => { "type" => "integer" }
        }
      }
    }
  }
  
  # Debug output
  stdout {
    codec => rubydebug
  }
}
