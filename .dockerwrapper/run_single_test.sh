#!/bin/bash

# Set the working directory to the script's directory
cd "$(dirname "$0")"

# Create test results directory if it doesn't exist
mkdir -p test-results

# Start the test environment if it's not already running
if ! docker compose -f docker-compose.test.yml ps | grep -q "turdparty_test_runner"; then
    echo "Starting test environment..."
    docker compose -f docker-compose.test.yml up -d

    # Wait for services to be ready
    echo "Waiting for services to be ready..."
    sleep 30  # Increased wait time to ensure all services are ready

    # Run database migrations
    echo "Running database migrations..."
    docker compose -f docker-compose.test.yml exec -T api alembic upgrade head

    # Initialize MinIO buckets
    echo "Initializing MinIO buckets..."
    docker compose -f docker-compose.test.yml exec -T minio-setup /bin/sh -c "
      /usr/bin/mc config host add myminio http://minio:9000 minioadmin minioadmin;
      /usr/bin/mc mb myminio/uploads --ignore-existing;
      /usr/bin/mc policy set public myminio/uploads;
    "

    # Start the Vagrant gRPC service on the host if needed
    echo "Checking if Vagrant gRPC service is running..."
    if ! nc -z localhost 40000; then
      echo "Starting Vagrant gRPC service on the host..."
      echo "Please run the Vagrant gRPC service with: vagrant grpc --bind-addr 0.0.0.0:40000"
      echo "Press Enter to continue once the service is running, or Ctrl+C to cancel..."
      read -r
    fi
fi

# Run the specified test
echo "Running test: $1"
docker compose -f docker-compose.test.yml exec -e IN_DOCKER=true test-runner pytest "$1" -v

# Don't clean up automatically to allow for debugging
echo "Test environment is still running. Run 'docker compose -f docker-compose.test.yml down' to clean up when done."
