#!/bin/bash

# Fix Cachet Dark Mode - Quick Fix Script
set -e

echo "🎨 Fixing Cachet Dark Mode"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Find the running Cachet container
CACHET_CONTAINER=""

# Check for various possible container names
for container in "turdparty_cachet" "turdparty_cachet_custom" "cachet" "certrats-cachet"; do
    if docker ps --format "{{.Names}}" | grep -q "^${container}$"; then
        CACHET_CONTAINER=$container
        break
    fi
done

if [ -z "$CACHET_CONTAINER" ]; then
    print_error "No running Cachet container found!"
    print_status "Available containers:"
    docker ps --format "table {{.Names}}\t{{.Ports}}" | grep -E "(cachet|Cachet)"
    exit 1
fi

print_success "Found Cachet container: $CACHET_CONTAINER"

# Get the port mapping
PORT_INFO=$(docker port $CACHET_CONTAINER 2>/dev/null || echo "")
if [ -n "$PORT_INFO" ]; then
    print_status "Port mapping: $PORT_INFO"
else
    print_warning "No port mapping found for container"
fi

# Copy the updated CSS file to the container
print_status "Copying updated dark theme CSS..."
if [ -f "cachet-customization/dark-theme.css" ]; then
    docker cp cachet-customization/dark-theme.css $CACHET_CONTAINER:/var/www/html/public/css/dark-theme.css
    print_success "Dark theme CSS copied"
else
    print_error "Dark theme CSS file not found!"
    exit 1
fi

# Copy the service icons JS file
print_status "Copying service icons JS..."
if [ -f "cachet-customization/service-icons.js" ]; then
    docker cp cachet-customization/service-icons.js $CACHET_CONTAINER:/var/www/html/public/js/service-icons.js
    print_success "Service icons JS copied"
else
    print_warning "Service icons JS file not found, skipping..."
fi

# Inject CSS into the layout if not already present
print_status "Injecting dark theme into layout..."
docker exec $CACHET_CONTAINER sh -c "
# Create backup if it doesn't exist
if [ ! -f /var/www/html/resources/views/layout/master.blade.php.backup ]; then
    cp /var/www/html/resources/views/layout/master.blade.php /var/www/html/resources/views/layout/master.blade.php.backup
fi

# Check if dark theme is already injected
if ! grep -q 'dark-theme.css' /var/www/html/resources/views/layout/master.blade.php; then
    # Inject dark theme CSS before </head>
    sed -i '/<\/head>/i\    <!-- TurdParty Custom Dark Theme -->' /var/www/html/resources/views/layout/master.blade.php
    sed -i '/<\/head>/i\    <link rel=\"stylesheet\" href=\"{{ asset(\"css/dark-theme.css\") }}\">' /var/www/html/resources/views/layout/master.blade.php
    sed -i '/<\/head>/i\    <script src=\"{{ asset(\"js/service-icons.js\") }}\" defer></script>' /var/www/html/resources/views/layout/master.blade.php
    echo 'Dark theme injected into layout'
else
    echo 'Dark theme already present in layout'
fi
"

# Clear caches
print_status "Clearing Cachet caches..."
docker exec $CACHET_CONTAINER sh -c "
php /var/www/html/artisan view:clear 2>/dev/null || echo 'View cache cleared (or not available)'
php /var/www/html/artisan config:clear 2>/dev/null || echo 'Config cache cleared (or not available)'
echo 'Cache operations completed'
" 2>/dev/null

# Test if CSS is accessible
print_status "Testing CSS accessibility..."
CONTAINER_IP=$(docker inspect $CACHET_CONTAINER --format='{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}')
if [ -n "$CONTAINER_IP" ]; then
    if docker exec $CACHET_CONTAINER curl -s http://localhost/css/dark-theme.css | head -1 | grep -q "TurdParty"; then
        print_success "Dark theme CSS is accessible inside container"
    else
        print_warning "Dark theme CSS may not be accessible"
    fi
fi

# Get external access URL
EXTERNAL_PORT=""
if echo "$PORT_INFO" | grep -q "80/tcp"; then
    EXTERNAL_PORT=$(echo "$PORT_INFO" | grep "80/tcp" | sed 's/.*://' | sed 's/->.*$//')
elif echo "$PORT_INFO" | grep -q "8000/tcp"; then
    EXTERNAL_PORT=$(echo "$PORT_INFO" | grep "8000/tcp" | sed 's/.*://' | sed 's/->.*$//')
fi

if [ -n "$EXTERNAL_PORT" ]; then
    print_success "🎉 Dark mode fix applied!"
    echo ""
    print_status "Access your Cachet status page at:"
    echo "   🌐 http://localhost:$EXTERNAL_PORT"
    echo ""
    print_status "Features applied:"
    echo "   🌙 Enhanced dark mode theme"
    echo "   🎯 Service icons support"
    echo "   📱 Mobile responsive design"
    echo "   🔧 Fixed component group backgrounds"
else
    print_success "🎉 Dark mode fix applied!"
    print_warning "Could not determine external port. Check 'docker ps' for port mapping."
fi

print_success "Dark mode fix complete! 🚀"
