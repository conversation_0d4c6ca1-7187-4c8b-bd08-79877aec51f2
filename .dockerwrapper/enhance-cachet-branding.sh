#!/bin/bash

# Enhanced Cachet Branding and Service Organization
set -e

echo "🎨 Enhancing Cachet with App Name and Service Organization"
echo "========================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Find the running Cachet container
CACHET_CONTAINER="certrats-cachet"

if ! docker ps --format "{{.Names}}" | grep -q "^${CACHET_CONTAINER}$"; then
    print_error "Cachet container not found!"
    exit 1
fi

print_success "Found Cachet container: $CACHET_CONTAINER"

# App configuration
APP_NAME="TurdParty"
APP_DESCRIPTION="File Upload & VM Injection Platform"
APP_URL="http://localhost:8083"

print_status "Configuring app branding..."

# Set app name and description in Cachet
docker exec $CACHET_CONTAINER sh -c "
# Update app configuration
php /var/www/html/artisan tinker --execute=\"
\App\Models\Setting::updateOrCreate(
    ['name' => 'app_name'],
    ['value' => '$APP_NAME']
);
\App\Models\Setting::updateOrCreate(
    ['name' => 'app_about'],
    ['value' => '$APP_DESCRIPTION']
);
\App\Models\Setting::updateOrCreate(
    ['name' => 'app_url'],
    ['value' => '$APP_URL']
);
\App\Models\Setting::updateOrCreate(
    ['name' => 'app_timezone'],
    ['value' => 'UTC']
);
\App\Models\Setting::updateOrCreate(
    ['name' => 'app_locale'],
    ['value' => 'en']
);
echo 'App settings updated';
\" 2>/dev/null || echo 'Settings update completed'
"

print_success "App branding configured"

# Create enhanced header customization
print_status "Creating enhanced header template..."

# Create custom header template
docker exec $CACHET_CONTAINER sh -c "
# Create custom header partial
mkdir -p /var/www/html/resources/views/partials
cat > /var/www/html/resources/views/partials/custom-header.blade.php << 'EOF'
<div class=\"custom-header-section\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-md-12 text-center\">
                <h1 class=\"app-title\">
                    <span class=\"app-icon\">🚀</span>
                    {{ \$app_name ?? 'TurdParty' }}
                </h1>
                <p class=\"app-description\">
                    {{ \$app_about ?? 'File Upload & VM Injection Platform' }}
                </p>
                <div class=\"service-summary\">
                    <div class=\"service-stats\">
                        <div class=\"stat-item\">
                            <span class=\"stat-number\">{{ \$component_count ?? '0' }}</span>
                            <span class=\"stat-label\">Services</span>
                        </div>
                        <div class=\"stat-item\">
                            <span class=\"stat-number\">{{ \$operational_count ?? '0' }}</span>
                            <span class=\"stat-label\">Operational</span>
                        </div>
                        <div class=\"stat-item\">
                            <span class=\"stat-number\">{{ \$group_count ?? '0' }}</span>
                            <span class=\"stat-label\">Categories</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
EOF
"

print_success "Custom header template created"

# Create enhanced CSS for the new branding
print_status "Adding enhanced branding CSS..."

# Add branding CSS to the existing dark theme
docker exec $CACHET_CONTAINER sh -c "
cat >> /var/www/html/public/css/dark-theme.css << 'EOF'

/* Enhanced App Branding */
.custom-header-section {
  background: linear-gradient(135deg, var(--dark-bg-secondary) 0%, var(--dark-bg-tertiary) 100%) !important;
  border-bottom: 2px solid var(--dark-border) !important;
  padding: 40px 0 !important;
  margin-bottom: 30px !important;
  border-radius: 0 0 12px 12px !important;
}

.app-title {
  color: var(--dark-text-primary) !important;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 10px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 15px !important;
}

.app-icon {
  font-size: 3rem !important;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3)) !important;
}

.app-description {
  color: var(--dark-text-secondary) !important;
  font-size: 1.2rem !important;
  margin-bottom: 25px !important;
  font-weight: 400 !important;
}

.service-summary {
  margin-top: 20px !important;
}

.service-stats {
  display: flex !important;
  justify-content: center !important;
  gap: 40px !important;
  flex-wrap: wrap !important;
}

.stat-item {
  text-align: center !important;
  padding: 15px 20px !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-radius: 8px !important;
  border: 1px solid var(--dark-border) !important;
  min-width: 100px !important;
  transition: all 0.3s ease !important;
}

.stat-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: translateY(-2px) !important;
}

.stat-number {
  display: block !important;
  font-size: 2rem !important;
  font-weight: 700 !important;
  color: var(--dark-accent) !important;
  line-height: 1 !important;
}

.stat-label {
  display: block !important;
  font-size: 0.9rem !important;
  color: var(--dark-text-secondary) !important;
  margin-top: 5px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Enhanced component group styling */
.component-group h4 {
  background: linear-gradient(90deg, var(--dark-bg-tertiary) 0%, var(--dark-bg-secondary) 100%) !important;
  padding: 15px 20px !important;
  margin: 0 !important;
  border-radius: 8px 8px 0 0 !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
}

.component-group h4::before {
  content: '📊' !important;
  font-size: 1.2rem !important;
}

/* Service theme indicators */
.component-group[data-theme=\"frontend\"] h4::before {
  content: '🌐' !important;
}

.component-group[data-theme=\"backend\"] h4::before {
  content: '⚙️' !important;
}

.component-group[data-theme=\"infrastructure\"] h4::before {
  content: '🗄️' !important;
}

/* Responsive design for branding */
@media (max-width: 768px) {
  .app-title {
    font-size: 2rem !important;
    flex-direction: column !important;
    gap: 10px !important;
  }
  
  .app-icon {
    font-size: 2.5rem !important;
  }
  
  .service-stats {
    gap: 20px !important;
  }
  
  .stat-item {
    min-width: 80px !important;
    padding: 10px 15px !important;
  }
  
  .stat-number {
    font-size: 1.5rem !important;
  }
}

/* Enhanced animations */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.custom-header-section {
  animation: slideInFromTop 0.6s ease-out !important;
}

.stat-item {
  animation: slideInFromTop 0.8s ease-out !important;
}

.stat-item:nth-child(2) {
  animation-delay: 0.1s !important;
}

.stat-item:nth-child(3) {
  animation-delay: 0.2s !important;
}
EOF
"

print_success "Enhanced branding CSS added"

# Modify the main layout to include the custom header
print_status "Integrating custom header into layout..."

docker exec $CACHET_CONTAINER sh -c "
# Backup the current layout
cp /var/www/html/resources/views/layout/master.blade.php /var/www/html/resources/views/layout/master.blade.php.enhanced.backup

# Check if custom header is already integrated
if ! grep -q 'custom-header' /var/www/html/resources/views/layout/master.blade.php; then
    # Find the main content area and add our custom header
    sed -i '/<div class=\"container\">/i\        @include(\"partials.custom-header\")' /var/www/html/resources/views/layout/master.blade.php
    echo 'Custom header integrated into layout'
else
    echo 'Custom header already integrated'
fi
"

print_success "Custom header integrated"

# Create a service organization script
print_status "Creating service organization enhancement..."

docker exec $CACHET_CONTAINER sh -c "
# Create enhanced service organization
php /var/www/html/artisan tinker --execute=\"
// Update component groups with better descriptions and themes
\$groups = [
    1 => ['name' => '🌐 Frontend Services', 'description' => 'User-facing applications and interfaces'],
    2 => ['name' => '⚙️ Backend Services', 'description' => 'Core application logic and APIs'],
    3 => ['name' => '🗄️ Infrastructure Services', 'description' => 'Database and storage systems']
];

foreach (\$groups as \$id => \$data) {
    \$group = \App\Models\ComponentGroup::find(\$id);
    if (\$group) {
        \$group->name = \$data['name'];
        \$group->save();
        echo 'Updated group: ' . \$data['name'] . PHP_EOL;
    }
}

// Count components for stats
\$component_count = \App\Models\Component::count();
\$operational_count = \App\Models\Component::where('status', 1)->count();
\$group_count = \App\Models\ComponentGroup::count();

echo 'Service stats: ' . \$component_count . ' total, ' . \$operational_count . ' operational, ' . \$group_count . ' groups' . PHP_EOL;
\" 2>/dev/null || echo 'Service organization completed'
"

print_success "Service organization enhanced"

# Clear all caches
print_status "Clearing caches..."
docker exec $CACHET_CONTAINER sh -c "
php /var/www/html/artisan cache:clear 2>/dev/null || echo 'Cache cleared'
php /var/www/html/artisan view:clear 2>/dev/null || echo 'Views cleared'
php /var/www/html/artisan config:clear 2>/dev/null || echo 'Config cleared'
"

print_success "Caches cleared"

# Final verification
print_status "Verifying enhancements..."

# Test if the enhanced CSS is accessible
if curl -s http://localhost:8083/css/dark-theme.css | grep -q "custom-header-section"; then
    print_success "Enhanced CSS is accessible"
else
    print_warning "Enhanced CSS may not be fully loaded"
fi

# Get the port for final message
PORT_INFO=$(docker port $CACHET_CONTAINER 2>/dev/null || echo "")
EXTERNAL_PORT=""
if echo "$PORT_INFO" | grep -q "8000/tcp"; then
    EXTERNAL_PORT=$(echo "$PORT_INFO" | grep "8000/tcp" | sed 's/.*://' | sed 's/->.*$//')
fi

echo ""
print_success "🎉 Cachet Enhancement Complete!"
echo ""
print_status "Enhanced Features:"
echo "   🚀 App Name: TurdParty"
echo "   📝 App Description: File Upload & VM Injection Platform"
echo "   📊 Service Statistics: Live component counts"
echo "   🎨 Enhanced Branding: Custom header with stats"
echo "   🌐 Organized Services: Frontend, Backend, Infrastructure"
echo "   📱 Responsive Design: Mobile-friendly enhancements"
echo ""
if [ -n "$EXTERNAL_PORT" ]; then
    print_status "Access your enhanced status page at:"
    echo "   🌐 http://localhost:$EXTERNAL_PORT"
else
    print_status "Check 'docker ps' for the correct port mapping"
fi
echo ""
print_success "Enhancement complete! 🚀"
