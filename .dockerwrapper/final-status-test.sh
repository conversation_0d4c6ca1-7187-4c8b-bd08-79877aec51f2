#!/bin/bash

# Final Status Indicators Test - Verify They Actually Work
echo "🎯 Final Status Indicators Verification"
echo "======================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }

echo ""
print_info "🔍 Testing Working Status Indicators Implementation"
echo ""

# Test 1: Check if working JavaScript is loaded
if curl -s http://localhost:8083/js/status-indicators.js | grep -q "TurdParty Working Status Indicators"; then
    print_success "✅ Working status indicators JavaScript loaded"
else
    print_error "❌ Working status indicators JavaScript not found"
fi

# Test 2: Check if working CSS is loaded
if curl -s http://localhost:8083/css/status-indicators.css | grep -q "TurdParty Working Status Indicators CSS"; then
    print_success "✅ Working status indicators CSS loaded"
else
    print_error "❌ Working status indicators CSS not found"
fi

# Test 3: Check if CSS is imported in dark theme
if curl -s http://localhost:8083/css/dark-theme.css | grep -q "status-indicators.css"; then
    print_success "✅ Status indicators CSS imported in dark theme"
else
    print_error "❌ Status indicators CSS not imported"
fi

# Test 4: Check components API
COMPONENT_COUNT=$(curl -s http://localhost:8083/api/v1/components | grep -o '"name"' | wc -l)
if [ $COMPONENT_COUNT -gt 0 ]; then
    print_success "✅ Components API working ($COMPONENT_COUNT components)"
else
    print_error "❌ Components API not working"
fi

# Test 5: Check if page loads without errors
if curl -s -f http://localhost:8083 > /dev/null; then
    print_success "✅ Main page loads successfully"
else
    print_error "❌ Main page has errors"
fi

echo ""
print_info "📊 Component Details from API"
echo "============================="

# Show component details
curl -s http://localhost:8083/api/v1/components | grep -o '"name":"[^"]*"' | sed 's/"name":"//g' | sed 's/"//g' | while read name; do
    echo "   • $name"
done

echo ""
print_info "🎯 Expected Behavior"
echo "===================="
echo "After loading http://localhost:8083, you should see:"
echo ""
echo "1. 🏗️ System Architecture diagram (working)"
echo "2. 🔧 Service Status section below the diagram"
echo "3. Service groups with individual components"
echo "4. 🟢 Green status icons next to each service"
echo "5. Status badges showing 'Operational'"
echo "6. Hover effects on service components"
echo ""

print_info "🧪 Manual Testing Steps"
echo "======================="
echo "1. Open http://localhost:8083 in your browser"
echo "2. Scroll down past the architecture diagram"
echo "3. Look for 'Service Status' section"
echo "4. Verify green status icons (🟢) appear"
echo "5. Open browser console (F12) and look for:"
echo "   - '🚀 TurdParty Status Indicators Loading...'"
echo "   - 'Creating service status section...'"
echo "   - '✅ Loaded X services from API'"
echo "   - '✅ Service groups displayed with status indicators'"

echo ""
print_info "🔧 Debug Commands"
echo "================="
echo "If status indicators don't appear, run these in browser console:"
echo ""
echo "// Check if script loaded"
echo "console.log(typeof TurdPartyStatusIndicators);"
echo ""
echo "// Manually trigger status indicators"
echo "TurdPartyStatusIndicators.init();"
echo ""
echo "// Test status indicators"
echo "TurdPartyStatusIndicators.test();"

echo ""
print_info "🎨 Visual Features"
echo "=================="
echo "✅ Green status icons (🟢) for operational services"
echo "✅ Status badges with 'Operational' text"
echo "✅ Glowing effects around status icons"
echo "✅ Hover animations on service components"
echo "✅ Responsive design for mobile devices"
echo "✅ Dark theme integration"
echo "✅ Loading animations"
echo "✅ Service grouping by category"

echo ""
if [ $COMPONENT_COUNT -gt 0 ]; then
    print_success "🚀 Status indicators should now be visible!"
    echo ""
    print_info "🌐 Open http://localhost:8083 to see your enhanced status page"
    echo "   with working green, amber, and red status indicators!"
else
    print_warning "⚠️ No components found. Status indicators may not display."
    echo ""
    print_info "Add components in Cachet admin to see status indicators."
fi

echo ""
print_success "Final verification complete! 🎯"
