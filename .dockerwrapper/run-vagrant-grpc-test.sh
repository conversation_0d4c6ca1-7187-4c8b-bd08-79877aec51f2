#!/bin/bash

# Script to test the Vagrant gRPC service

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing Vagrant gRPC Service...${NC}"

# Check if the Docker network exists
NETWORK_NAME="turdparty-network"
if ! docker network inspect $NETWORK_NAME &>/dev/null; then
    echo -e "${YELLOW}Creating Docker network: $NETWORK_NAME${NC}"
    docker network create $NETWORK_NAME
fi

# Check if the Node container is running
CONTAINER_NAME="turdparty-node"
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${YELLOW}Starting Node container...${NC}"

    # Run the Node container
    docker run -d \
        --name $CONTAINER_NAME \
        --network $NETWORK_NAME \
        --network-alias node \
        --add-host=host.docker.internal:host-gateway \
        -v $(pwd):/app \
        -w /app \
        node:16 \
        tail -f /dev/null

    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to start Node container${NC}"
        exit 1
    fi

    echo -e "${GREEN}Node container started${NC}"
else
    echo -e "${GREEN}Node container is already running${NC}"
fi

# Install dependencies in the container
echo -e "${YELLOW}Installing dependencies...${NC}"
docker exec $CONTAINER_NAME npm install @grpc/grpc-js @grpc/proto-loader

# Run the test
echo -e "${YELLOW}Running Vagrant gRPC test...${NC}"
docker exec $CONTAINER_NAME node .dockerwrapper/test-vagrant-grpc.js

# Check the test result
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Test completed successfully!${NC}"
else
    echo -e "${RED}Test failed!${NC}"
    echo -e "${YELLOW}This might be expected if the Vagrant gRPC service is not running on the host.${NC}"
    echo -e "${YELLOW}Make sure the Vagrant gRPC service is running on port 40000.${NC}"
fi

# Optionally, stop the container
read -p "Do you want to stop the Node container? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Stopping Node container...${NC}"
    docker stop $CONTAINER_NAME
    docker rm $CONTAINER_NAME
    echo -e "${GREEN}Node container stopped and removed${NC}"
fi

echo -e "${GREEN}Done!${NC}"
