version: '3.8'

services:
  # Database service
  db:
    container_name: turdparty_db
    image: postgres:14-alpine
    environment:
      POSTGRES_USER: turdparty
      POSTGRES_PASSWORD: turdparty
      POSTGRES_DB: turdparty
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5433:5432"  # Changed to avoid port conflict
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U turdparty"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # Redis service for Celery
  redis:
    container_name: turdparty_redis
    image: redis:alpine
    ports:
      - "6380:6379"  # Changed to avoid port conflict
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  # MinIO service for file storage
  minio:
    container_name: turdparty_minio
    image: minio/minio
    ports:
      - "9010:9000"  # Changed to avoid port conflict
      - "9011:9001"  # Changed to avoid port conflict
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 5s
      timeout: 5s
      retries: 5

  # MinIO setup service
  minio-setup:
    container_name: turdparty_minio_setup
    image: minio/mc
    depends_on:
      minio:
        condition: service_healthy
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc config host add myminio http://minio:9000 minioadmin minioadmin;
      /usr/bin/mc mb myminio/uploads --ignore-existing;
      /usr/bin/mc policy set public myminio/uploads;
      exit 0;
      "

  # Celery worker for file operations
  celery-file-ops:
    container_name: turdparty_celery_file_ops
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      minio-setup:
        condition: service_completed_successfully
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/1
      DATABASE_URL: ****************************************/turdparty
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
      MINIO_SECURE: "False"
    command: celery -A tasks worker -Q file_ops -l info

  # Celery worker for VM operations
  celery-vm-ops:
    container_name: turdparty_celery_vm_ops
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/1
      DATABASE_URL: ****************************************/turdparty
      VAGRANT_API_URL: http://host.docker.internal:40000
    command: celery -A tasks worker -Q vm_lifecycle,vm_injection -l info
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Celery worker for monitoring operations
  celery-monitoring:
    container_name: turdparty_celery_monitoring
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/1
      DATABASE_URL: ****************************************/turdparty
    command: celery -A tasks worker -Q monitoring -l info

  # API service
  api:
    container_name: turdparty_api
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.api
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      celery-file-ops:
        condition: service_started
      celery-vm-ops:
        condition: service_started
      celery-monitoring:
        condition: service_started
    environment:
      DATABASE_URL: ****************************************/turdparty
      POSTGRES_USER: turdparty
      POSTGRES_PASSWORD: turdparty
      POSTGRES_DB: turdparty
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/1
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
      MINIO_SECURE: "False"
      VAGRANT_API_URL: http://host.docker.internal:40000
    ports:
      - "8010:8000"  # Changed to avoid port conflict
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Test runner service
  test-runner:
    container_name: turdparty_test_runner
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.test
    depends_on:
      api:
        condition: service_started
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    environment:
      DATABASE_URL: ****************************************/turdparty
      POSTGRES_USER: turdparty
      POSTGRES_PASSWORD: turdparty
      POSTGRES_DB: turdparty
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/1
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
      MINIO_SECURE: "False"
      API_URL: http://api:8000  # This is the internal Docker network URL, no need to change
    volumes:
      - ../tests:/app/tests
      - ./test-results:/app/test-results
    command: tail -f /dev/null

volumes:
  postgres_data:
  minio_data:

networks:
  default:
    name: turdparty_test_network
    external: false
