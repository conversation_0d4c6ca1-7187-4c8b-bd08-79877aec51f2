services:
  api:
    container_name: turdparty_api_1
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile
      args:
        - IMAGE_PREFIX=turdparty
    image: turdparty-api
    ports:
      - "3050:8000"
    depends_on:
      - postgres
      - minio
    environment:
      - DATABASE_URL=********************************************/app
      - DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@postgres:5432/app
      - PORT=8000
      - TEST_MODE=true
      - PYTHONPATH=/app
      - DEBUG=true
      - FILE_UPLOAD_DIR=/app/uploads
      - API_PREFIX=/api/v1
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_DIRECT=true
      - VAGRANT_TEST_MODE=1
    volumes:
      - ..:/app
      - ../logs:/app/logs
      - ../uploads:/app/uploads
      - turdparty_ssh_keys:/home/<USER>/.ssh
      - ./scripts/container-init.sh:/usr/local/bin/container-init.sh
      - ../vagrant_exec.sh:/app/vagrant_exec.sh
    entrypoint: ["/usr/local/bin/container-init.sh"]
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
    extra_hosts:
      - "host.docker.internal:host-gateway"
    cap_add:
      - NET_ADMIN
    networks:
      - turdparty_network
    restart: unless-stopped

  react-app:
    container_name: turdparty_frontend_1
    image: nginx:alpine
    ports:
      - "3100:80"
    depends_on:
      - api
    volumes:
      - ../turdparty-app/build:/usr/share/nginx/html
    networks:
      - turdparty_network
    restart: unless-stopped

  dashboard:
    container_name: turdparty_dashboard
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.dashboard
      args:
        - IMAGE_PREFIX=turdparty
    image: turdparty-dashboard
    ports:
      - "3150:8080"
    volumes:
      - ..:/app
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - PYTHONPATH=/app
      - CONTAINER_PREFIX=turdparty
    command: ["python", "/app/dashboard.py", "start"]
    user: root
    networks:
      - turdparty_network
    restart: "no"

  postgres:
    container_name: turdparty_postgres_1
    image: postgres:14-alpine
    ports:
      - "3200:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=app
    volumes:
      - turdparty_postgres_data:/var/lib/postgresql/data
    networks:
      - turdparty_network
    restart: unless-stopped

  minio:
    container_name: turdparty_minio_1
    image: minio/minio:latest
    ports:
      - "3300:9000"
      - "3301:9001"
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - turdparty_minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - turdparty_network
    restart: unless-stopped

  minio-ssh:
    container_name: turdparty_minio_ssh_1
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.minio
    ports:
      - "2223:22"
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - turdparty_minio_data:/data
    networks:
      - turdparty_network
    restart: unless-stopped

volumes:
  turdparty_postgres_data:
  turdparty_ssh_keys:
  turdparty_frontend_node_modules:
  turdparty_minio_data:

networks:
  turdparty_network:
    driver: bridge