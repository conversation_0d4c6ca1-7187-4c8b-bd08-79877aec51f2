#!/bin/bash

# Update Cachet with Specific TurdParty Services and Real Status
set -e

echo "🎯 Updating Cachet with Specific TurdParty Services"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Find the running Cachet container
CACHET_CONTAINER="certrats-cachet"

if ! docker ps --format "{{.Names}}" | grep -q "^${CACHET_CONTAINER}$"; then
    print_error "Cachet container not found!"
    exit 1
fi

print_success "Found Cachet container: $CACHET_CONTAINER"

# Function to check service status
check_service_status() {
    local service_name=$1
    local status=$(docker ps --format "{{.Names}}\t{{.Status}}" | grep "$service_name" | awk '{print $2}')
    
    if [[ "$status" == "Up" ]]; then
        echo "1"  # Operational
    elif [[ "$status" == *"Restarting"* ]]; then
        echo "3"  # Major Outage
    elif [[ "$status" == *"unhealthy"* ]]; then
        echo "2"  # Performance Issues
    elif [[ "$status" == *"healthy"* ]]; then
        echo "1"  # Operational
    else
        echo "4"  # Major Outage
    fi
}

# Get real service statuses
print_status "Checking real service statuses..."

FRONTEND_STATUS=$(check_service_status "turdparty_frontend")
API_STATUS=$(check_service_status "turdparty_api")
POSTGRES_STATUS=$(check_service_status "turdparty_postgres_1")
MINIO_STATUS=$(check_service_status "turdparty_minio_1")
MINIO_SSH_STATUS=$(check_service_status "turdparty_minio_ssh")
REDIS_STATUS=$(check_service_status "turdparty_redis")
CELERY_DEFAULT_STATUS=$(check_service_status "turdparty_celery_default")
CELERY_FILE_STATUS=$(check_service_status "turdparty_celery_file_ops")
CELERY_VM_STATUS=$(check_service_status "turdparty_celery_vm_ops")
CELERY_FLOWER_STATUS=$(check_service_status "turdparty_celery_flower")

print_status "Service statuses detected:"
echo "  Frontend: $FRONTEND_STATUS, API: $API_STATUS, PostgreSQL: $POSTGRES_STATUS"
echo "  MinIO: $MINIO_STATUS, Redis: $REDIS_STATUS, Celery Flower: $CELERY_FLOWER_STATUS"

# Update Cachet with specific TurdParty services
print_status "Updating Cachet with specific TurdParty services..."

docker exec $CACHET_CONTAINER sh -c "
# Clear existing components and groups
mysql -h \$DB_HOST -u \$DB_USERNAME -p\$DB_PASSWORD \$DB_DATABASE << 'EOF'
DELETE FROM chq_components;
DELETE FROM chq_component_groups;
ALTER TABLE chq_components AUTO_INCREMENT = 1;
ALTER TABLE chq_component_groups AUTO_INCREMENT = 1;

-- Create specific TurdParty component groups
INSERT INTO chq_component_groups (id, name, \`order\`, visible, created_at, updated_at) VALUES 
(1, '🌐 Frontend & User Interface', 1, 1, NOW(), NOW()),
(2, '🚀 API & Application Services', 2, 1, NOW(), NOW()),
(3, '⚙️ Task Processing & Workers', 3, 1, NOW(), NOW()),
(4, '🗄️ Data Storage & Infrastructure', 4, 1, NOW(), NOW()),
(5, '🔧 Monitoring & Management', 5, 1, NOW(), NOW());

-- Insert specific TurdParty services with real descriptions
INSERT INTO chq_components (name, description, status, \`order\`, group_id, created_at, updated_at) VALUES 

-- Frontend & User Interface
('🌐 React Frontend', 'TurdParty web application built with React for file upload and VM management', $FRONTEND_STATUS, 1, 1, NOW(), NOW()),

-- API & Application Services  
('🚀 FastAPI Backend', 'Main FastAPI application handling file uploads, VM operations, and API endpoints', $API_STATUS, 1, 2, NOW(), NOW()),
('🔐 Authentication Service', 'User authentication and authorization system for secure access', 1, 2, 2, NOW(), NOW()),
('📁 File Upload Service', 'Handles file uploads, validation, and processing for VM injection', $API_STATUS, 3, 2, NOW(), NOW()),
('🖥️ VM Management API', 'Vagrant VM lifecycle management, creation, and configuration', $API_STATUS, 4, 2, NOW(), NOW()),

-- Task Processing & Workers
('⚙️ Celery Default Worker', 'Primary task queue worker for general background processing', $CELERY_DEFAULT_STATUS, 1, 3, NOW(), NOW()),
('📂 Celery File Operations', 'Specialized worker for file processing, validation, and transfer operations', $CELERY_FILE_STATUS, 2, 3, NOW(), NOW()),
('🖥️ Celery VM Operations', 'Dedicated worker for VM provisioning, injection, and management tasks', $CELERY_VM_STATUS, 3, 3, NOW(), NOW()),

-- Data Storage & Infrastructure
('🗄️ PostgreSQL Database', 'Primary database storing user data, file metadata, and VM configurations', $POSTGRES_STATUS, 1, 4, NOW(), NOW()),
('📦 MinIO Object Storage', 'S3-compatible object storage for uploaded files and VM templates', $MINIO_STATUS, 2, 4, NOW(), NOW()),
('🔗 MinIO SSH Gateway', 'SSH access gateway for secure file transfer to MinIO storage', $MINIO_SSH_STATUS, 3, 4, NOW(), NOW()),
('⚡ Redis Cache & Queue', 'In-memory cache and message broker for Celery task queues', $REDIS_STATUS, 4, 4, NOW(), NOW()),

-- Monitoring & Management
('🌸 Celery Flower', 'Real-time monitoring dashboard for Celery workers and task queues', $CELERY_FLOWER_STATUS, 1, 5, NOW(), NOW()),
('📊 Cachet Status Page', 'This status page monitoring all TurdParty services and infrastructure', 1, 2, 5, NOW(), NOW());

EOF

echo 'TurdParty services updated in Cachet'
"

print_success "Specific TurdParty services configured"

# Update the custom header with more specific information
print_status "Updating header with TurdParty-specific information..."

docker exec $CACHET_CONTAINER sh -c "
cat > /var/www/html/resources/views/partials/custom-header.blade.php << 'EOF'
<div class=\"custom-header-section\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-md-12 text-center\">
                <h1 class=\"app-title\">
                    <span class=\"app-icon\">🚀</span>
                    TurdParty
                </h1>
                <p class=\"app-description\">
                    File Upload & VM Injection Platform - Real-time Service Status
                </p>
                <div class=\"service-summary\">
                    <div class=\"service-stats\">
                        <div class=\"stat-item\">
                            <span class=\"stat-number\" id=\"total-services\">13</span>
                            <span class=\"stat-label\">Services</span>
                        </div>
                        <div class=\"stat-item\">
                            <span class=\"stat-number\" id=\"operational-services\">-</span>
                            <span class=\"stat-label\">Operational</span>
                        </div>
                        <div class=\"stat-item\">
                            <span class=\"stat-number\" id=\"degraded-services\">-</span>
                            <span class=\"stat-label\">Degraded</span>
                        </div>
                        <div class=\"stat-item\">
                            <span class=\"stat-number\" id=\"service-groups\">5</span>
                            <span class=\"stat-label\">Categories</span>
                        </div>
                    </div>
                    <div class=\"service-categories\">
                        <div class=\"category-item\">
                            <span class=\"category-icon\">🌐</span>
                            <span class=\"category-name\">Frontend</span>
                        </div>
                        <div class=\"category-item\">
                            <span class=\"category-icon\">🚀</span>
                            <span class=\"category-name\">API Services</span>
                        </div>
                        <div class=\"category-item\">
                            <span class=\"category-icon\">⚙️</span>
                            <span class=\"category-name\">Workers</span>
                        </div>
                        <div class=\"category-item\">
                            <span class=\"category-icon\">🗄️</span>
                            <span class=\"category-name\">Storage</span>
                        </div>
                        <div class=\"category-item\">
                            <span class=\"category-icon\">🔧</span>
                            <span class=\"category-name\">Monitoring</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced service statistics for TurdParty
document.addEventListener('DOMContentLoaded', function() {
    // Fetch component data from Cachet API
    fetch('/api/v1/components')
        .then(response => response.json())
        .then(data => {
            const components = data.data || [];
            const totalServices = components.length;
            const operationalServices = components.filter(c => c.status === 1).length;
            const degradedServices = components.filter(c => c.status === 2 || c.status === 3 || c.status === 4).length;
            
            // Animate the statistics
            animateCounter('total-services', totalServices);
            animateCounter('operational-services', operationalServices);
            animateCounter('degraded-services', degradedServices);
            
            // Update service groups count
            fetch('/api/v1/components/groups')
                .then(response => response.json())
                .then(groupData => {
                    const groups = groupData.data || [];
                    animateCounter('service-groups', groups.length);
                })
                .catch(error => {
                    console.log('Could not fetch groups data');
                });
        })
        .catch(error => {
            console.log('Could not fetch components data');
        });
});

function animateCounter(elementId, finalValue) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    let currentValue = 0;
    const increment = Math.max(1, Math.ceil(finalValue / 20));
    
    const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= finalValue) {
            currentValue = finalValue;
            clearInterval(timer);
        }
        element.textContent = currentValue;
    }, 50);
}
</script>
EOF
"

print_success "Header updated with TurdParty-specific information"

# Add enhanced CSS for the new categories
print_status "Adding enhanced CSS for service categories..."

docker exec $CACHET_CONTAINER sh -c "
cat >> /var/www/html/public/css/dark-theme.css << 'EOF'

/* Service Categories Display */
.service-categories {
  margin-top: 25px !important;
  display: flex !important;
  justify-content: center !important;
  gap: 20px !important;
  flex-wrap: wrap !important;
}

.category-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  padding: 10px 15px !important;
  background-color: rgba(255, 255, 255, 0.03) !important;
  border-radius: 6px !important;
  border: 1px solid var(--dark-border) !important;
  transition: all 0.3s ease !important;
  min-width: 80px !important;
}

.category-item:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
  transform: translateY(-1px) !important;
}

.category-icon {
  font-size: 1.5rem !important;
  margin-bottom: 5px !important;
}

.category-name {
  font-size: 0.8rem !important;
  color: var(--dark-text-secondary) !important;
  text-align: center !important;
  font-weight: 500 !important;
}

/* Enhanced stat items for degraded services */
.stat-item:has(#degraded-services) {
  border-color: rgba(255, 193, 7, 0.3) !important;
}

.stat-item:has(#degraded-services) .stat-number {
  color: #ffc107 !important;
}

/* Responsive design for categories */
@media (max-width: 768px) {
  .service-categories {
    gap: 15px !important;
  }
  
  .category-item {
    min-width: 70px !important;
    padding: 8px 12px !important;
  }
  
  .category-icon {
    font-size: 1.2rem !important;
  }
  
  .category-name {
    font-size: 0.7rem !important;
  }
}
EOF
"

print_success "Enhanced CSS for service categories added"

# Clear caches
print_status "Clearing caches..."
docker exec $CACHET_CONTAINER sh -c "
php /var/www/html/artisan view:clear 2>/dev/null || echo 'Views cleared'
php /var/www/html/artisan config:clear 2>/dev/null || echo 'Config cleared'
"

print_success "Caches cleared"

# Verify the updates
print_status "Verifying TurdParty service updates..."

# Count the new services
SERVICE_COUNT=$(curl -s http://localhost:8083/api/v1/components | grep -o '"name"' | wc -l)
GROUP_COUNT=$(curl -s http://localhost:8083/api/v1/components/groups | grep -o '"name"' | wc -l)

print_success "Updated Cachet with $SERVICE_COUNT specific TurdParty services in $GROUP_COUNT categories"

echo ""
print_success "🎯 TurdParty Services Updated!"
echo ""
print_status "Specific Services Configured:"
echo "   🌐 Frontend: React Frontend"
echo "   🚀 API: FastAPI Backend, Auth, File Upload, VM Management"
echo "   ⚙️ Workers: Celery Default, File Ops, VM Ops"
echo "   🗄️ Storage: PostgreSQL, MinIO, MinIO SSH, Redis"
echo "   🔧 Monitoring: Celery Flower, Cachet Status Page"
echo ""
print_status "Real-time Status Monitoring:"
echo "   ✅ Live service health detection"
echo "   📊 Operational vs Degraded service counts"
echo "   🎯 Specific service descriptions and purposes"
echo "   📱 Enhanced mobile-friendly category display"
echo ""
print_status "Access your detailed TurdParty status page at:"
echo "   🌐 http://localhost:8083"
echo ""
print_success "Specific TurdParty service configuration complete! 🚀"
