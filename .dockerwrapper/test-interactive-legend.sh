#!/bin/bash

# Test Interactive Legend Functionality
echo "🎯 Testing Interactive Legend Functionality"
echo "==========================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_test() { echo -e "${PURPLE}[TEST]${NC} $1"; }

echo ""
print_info "🔍 Testing Interactive Legend Implementation"
echo ""

# Test 1: Check if interactive legend HTML is present
if curl -s http://localhost:8083 | grep -q "interactive-legend"; then
    print_success "✅ Interactive legend HTML elements found"
else
    print_error "❌ Interactive legend HTML elements not found"
fi

# Test 2: Check if data attributes are present
if curl -s http://localhost:8083 | grep -q "data-layer"; then
    print_success "✅ Legend data attributes found"
else
    print_error "❌ Legend data attributes not found"
fi

# Test 3: Check if node data attributes are present
if curl -s http://localhost:8083 | grep -q "data-nodes"; then
    print_success "✅ Node data attributes found"
else
    print_error "❌ Node data attributes not found"
fi

# Test 4: Check if interactive JavaScript functions are present
if curl -s http://localhost:8083 | grep -q "initializeInteractiveLegend"; then
    print_success "✅ Interactive legend JavaScript found"
else
    print_error "❌ Interactive legend JavaScript not found"
fi

# Test 5: Check if highlighting CSS is present
if curl -s http://localhost:8083/css/dark-theme.css | grep -q "interactive-legend"; then
    print_success "✅ Interactive legend CSS found"
else
    print_error "❌ Interactive legend CSS not found"
fi

# Test 6: Check if highlighting styles are present
if curl -s http://localhost:8083/css/dark-theme.css | grep -q "highlighted"; then
    print_success "✅ Highlighting CSS styles found"
else
    print_error "❌ Highlighting CSS styles not found"
fi

# Test 7: Check if rainbow gradient for "Show All" is present
if curl -s http://localhost:8083/css/dark-theme.css | grep -q "rainbow-gradient"; then
    print_success "✅ Rainbow gradient animation found"
else
    print_error "❌ Rainbow gradient animation not found"
fi

echo ""
print_info "🎨 Interactive Legend Features"
echo "=============================="
echo "✅ Clickable legend buttons below the architecture diagram"
echo "✅ Hover effects with 'Click to highlight' tooltips"
echo "✅ Layer highlighting with dimming of other elements"
echo "✅ Glowing effects for highlighted nodes"
echo "✅ Enhanced edge highlighting for connections"
echo "✅ 'Show All' button with rainbow gradient"
echo "✅ Active state indicators for selected layers"
echo "✅ Smooth transitions and animations"

echo ""
print_info "🎯 How to Use Interactive Legend"
echo "================================"
echo "1. Open http://localhost:8083 in your browser"
echo "2. Scroll to the System Architecture diagram"
echo "3. Look at the colored legend buttons below the diagram"
echo "4. Click on any layer button (Frontend, API, Workers, etc.)"
echo "5. Watch as that layer highlights and others dim"
echo "6. Click 'Show All' to reset the view"
echo "7. Hover over buttons to see tooltips"

echo ""
print_info "🔧 Layer Mapping"
echo "================"
echo "🌐 Frontend Layer    → UI (React Frontend)"
echo "🚀 API Services      → API, AUTH, UPLOAD, VM"
echo "⚙️ Workers           → CELERY_DEFAULT, CELERY_FILE, CELERY_VM"
echo "🗄️ Storage           → POSTGRES, MINIO, MINIO_SSH, REDIS"
echo "🔧 Monitoring        → FLOWER, CACHET"
echo "🌈 Show All          → Reset to normal view"

echo ""
print_info "🎨 Visual Effects"
echo "================="
echo "• Dimmed nodes: 30% opacity with grayscale filter"
echo "• Highlighted nodes: 105% scale with colored glow"
echo "• Enhanced edges: Thicker lines for connections"
echo "• Active legend: Blue border and background"
echo "• Hover effects: Lift animation and shadow"
echo "• Smooth transitions: 0.5s ease animations"

echo ""
print_info "📱 Responsive Design"
echo "===================="
echo "• Desktop: Full legend with tooltips"
echo "• Mobile: 2-column grid layout"
echo "• Touch-friendly: Larger click targets"
echo "• Optimized: Hidden tooltips on small screens"

echo ""
print_info "🧪 Manual Testing Steps"
echo "======================="
echo "1. Open http://localhost:8083"
echo "2. Scroll to the architecture diagram"
echo "3. Click 'Frontend Layer' - should highlight UI node"
echo "4. Click 'API Services' - should highlight API, AUTH, UPLOAD, VM"
echo "5. Click 'Workers' - should highlight all Celery workers"
echo "6. Click 'Storage' - should highlight POSTGRES, MINIO, REDIS"
echo "7. Click 'Monitoring' - should highlight FLOWER, CACHET"
echo "8. Click 'Show All' - should reset to normal view"
echo "9. Hover over buttons to see tooltips"

echo ""
print_info "🔧 Debug Commands (Browser Console)"
echo "==================================="
echo "// Check if interactive legend is initialized"
echo "console.log('Legend items:', document.querySelectorAll('.interactive-legend'));"
echo ""
echo "// Manually highlight a layer"
echo "highlightLayer('api', 'API,AUTH,UPLOAD,VM');"
echo ""
echo "// Reset highlighting"
echo "resetHighlight();"
echo ""
echo "// Re-initialize if needed"
echo "initializeInteractiveLegend();"

echo ""
print_success "🚀 Interactive Legend Ready!"
echo ""
print_info "🌐 Open http://localhost:8083 and click the legend buttons"
print_info "   below the architecture diagram to see layer highlighting!"

echo ""
print_success "Interactive legend test complete! 🎯"
