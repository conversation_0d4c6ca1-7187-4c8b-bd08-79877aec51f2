#!/bin/bash
# Service Testing Script for Containerized FastAPI
# This script performs comprehensive testing of all services within the container

# Configuration
APP_NAME="TurdParty"
CONTAINER_NAME="${APP_NAME}-container"
DB_CONTAINER_NAME="${APP_NAME}-db"
API_BASE_URL="http://localhost:8000"
TIMEOUT_SECONDS=2
LOG_DIR="./logs"
TEST_LOG="${LOG_DIR}/service-tests.log"
RUNTIME_FILE=".lastruntime"

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test results counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_SKIPPED=0

# Timing variables
SCRIPT_START_TIME=$(date +%s)
LAST_RUNTIME_SECONDS=0
CURRENT_TEST_START_TIME=0
TEST_TIMES=()
TEST_NAMES=()

# Function to format seconds into a readable time
format_time() {
    local seconds=$1
    local minutes=$((seconds / 60))
    local remaining_seconds=$((seconds % 60))

    if [ ${minutes} -gt 0 ]; then
        echo "${minutes}m ${remaining_seconds}s"
    else
        echo "${seconds}s"
    fi
}

# Check if we have a previous runtime to compare against
if [ -f "${RUNTIME_FILE}" ]; then
    LAST_RUNTIME_SECONDS=$(cat "${RUNTIME_FILE}")
    log "INFO" "Previous run took $(format_time ${LAST_RUNTIME_SECONDS})"
fi

# Prepare log directory
mkdir -p "${LOG_DIR}"
touch "${TEST_LOG}"

# Display header with runtime comparison if available
echo -e "${CYAN}===============================================${NC}"
echo -e "${CYAN}=== FastAPI Container Service Test Suite ===${NC}"
echo -e "${CYAN}===============================================${NC}"

# Check if we have a previous runtime to compare against
if [ -f "${RUNTIME_FILE}" ]; then
    LAST_RUNTIME_SECONDS=$(cat "${RUNTIME_FILE}")
    echo -e "${BLUE}Previous run took:${NC} $(format_time ${LAST_RUNTIME_SECONDS})"
    echo -e "${BLUE}Started at:${NC} $(date)"
else
    echo -e "${YELLOW}First run - no previous runtime data available${NC}"
fi
echo -e "${CYAN}===============================================${NC}\n"

# Function to log with timestamp
log() {
    local level=$1
    local message=$2
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")

    case $level in
        "INFO")
            echo -e "${BLUE}[${timestamp} INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[${timestamp} SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[${timestamp} WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[${timestamp} ERROR]${NC} $message"
            ;;
        "TEST")
            echo -e "${MAGENTA}[${timestamp} TEST]${NC} $message"
            ;;
    esac

    # Also log to file
    echo "[${timestamp} ${level}] $message" >> "${TEST_LOG}"
}

# Function to display a test section header
section() {
    echo -e "\n${CYAN}=== $1 ===${NC}"
    echo "=== $1 ===" >> "${TEST_LOG}"
}

# Test function - runs a test and tracks results
test_case() {
    local name=$1
    local command=$2
    local expected_result=$3
    local error_message=${4:-"Test failed"}

    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    CURRENT_TEST_START_TIME=$(date +%s)

    echo -e "\n${MAGENTA}[TEST ${TESTS_TOTAL}]${NC} ${name}"
    echo "[TEST ${TESTS_TOTAL}] ${name}" >> "${TEST_LOG}"

    # Execute the test command
    echo "+ ${command}" >> "${TEST_LOG}"

    # Use eval to execute the command and capture output
    result=$(eval ${command} 2>&1)
    exit_code=$?

    # Calculate and store test execution time
    local test_end_time=$(date +%s)
    local test_duration=$((test_end_time - CURRENT_TEST_START_TIME))
    TEST_TIMES+=("${test_duration}")
    TEST_NAMES+=("${name}")

    # Log the command result
    echo "Result: ${result}" >> "${TEST_LOG}"
    echo "Exit code: ${exit_code}" >> "${TEST_LOG}"
    echo "Test duration: $(format_time ${test_duration})" >> "${TEST_LOG}"

    # Check if the result matches expected
    if [[ $result == *"$expected_result"* ]] && [ $exit_code -eq 0 ]; then
        echo -e "  ${GREEN}✓ PASSED${NC} (took $(format_time ${test_duration}))"
        echo "  ✓ PASSED" >> "${TEST_LOG}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "  ${RED}✗ FAILED${NC}: ${error_message} (took $(format_time ${test_duration}))"
        echo "  ✗ FAILED: ${error_message}" >> "${TEST_LOG}"
        echo "  Expected to contain: '${expected_result}'" >> "${TEST_LOG}"
        echo "  Actual: '${result}'" >> "${TEST_LOG}"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
}

# Skip a test with a reason
skip_test() {
    local name=$1
    local reason=$2

    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    TESTS_SKIPPED=$((TESTS_SKIPPED + 1))

    # Add dummy timing for skipped tests
    TEST_TIMES+=("0")
    TEST_NAMES+=("${name} (SKIPPED)")

    echo -e "\n${MAGENTA}[TEST ${TESTS_TOTAL}]${NC} ${name}"
    echo -e "  ${YELLOW}⚠ SKIPPED${NC}: ${reason}"

    echo "[TEST ${TESTS_TOTAL}] ${name}" >> "${TEST_LOG}"
    echo "  ⚠ SKIPPED: ${reason}" >> "${TEST_LOG}"
}

# Test if a container is running
test_container_running() {
    local container_name=$1

    test_case "Container ${container_name} is running" \
              "docker ps --format '{{.Names}}' | grep '^${container_name}$'" \
              "${container_name}" \
              "Container ${container_name} is not running"
}

# Test if a port is open
test_port_open() {
    local host=$1
    local port=$2
    local max_attempts=3
    local attempt=1
    local wait_time=2

    while [ ${attempt} -le ${max_attempts} ]; do
        log "INFO" "Checking if port ${port} is open on ${host} (attempt ${attempt}/${max_attempts})"

        # Try with netcat first
        local nc_result=$(nc -z -v -w ${TIMEOUT_SECONDS} ${host} ${port} 2>&1)
        local nc_exit=$?

        if [ ${nc_exit} -eq 0 ]; then
            test_case "Port ${port} is open on ${host}" \
                      "echo 'Connection successful'" \
                      "Connection successful" \
                      "Port ${port} is accessible"
            return 0
        else
            # If first attempt fails, log more details
            if [ ${attempt} -eq 1 ]; then
                echo -e "${BLUE}[Diagnostics]${NC} nc output: ${nc_result}"
            fi

            # On second attempt, try with curl
            if [ ${attempt} -eq 2 ]; then
                echo -e "${BLUE}[Diagnostics]${NC} Trying curl instead of nc..."
                curl -s -o /dev/null -m ${TIMEOUT_SECONDS} ${host}:${port} 2>/dev/null
                local curl_exit=$?

                if [ ${curl_exit} -eq 0 ]; then
                    test_case "Port ${port} is open on ${host} (curl)" \
                              "echo 'Connection successful'" \
                              "Connection successful" \
                              "Port is accessible via curl"
                    return 0
                else
                    echo -e "${BLUE}[Diagnostics]${NC} curl exit code: ${curl_exit}"
                fi
            fi

            # On last attempt, add detailed diagnostics
            if [ ${attempt} -eq ${max_attempts} ]; then
                echo -e "${BLUE}[Diagnostics]${NC} Running detailed port scan..."

                # Check what's listening on the port
                echo -e "${BLUE}[Diagnostics]${NC} Processes listening on port ${port}:"
                ss -tulpn | grep ":${port}" || true

                # Check Docker port mappings
                echo -e "${BLUE}[Diagnostics]${NC} Docker port mappings:"
                docker ps --format "{{.Names}}: {{.Ports}}" || true

                test_case "Port ${port} is open on ${host}" \
                          "nc -z -w ${TIMEOUT_SECONDS} ${host} ${port} && echo 'Connection successful'" \
                          "Connection successful" \
                          "Port ${port} is not accessible on ${host} after ${max_attempts} attempts"
                return 1
            fi
        fi

        attempt=$((attempt + 1))
        sleep ${wait_time}
        wait_time=$((wait_time + 2))
    done
}

# Test HTTP endpoint
test_endpoint() {
    local name=$1
    local url=$2
    local expected_status=${3:-200}
    local expected_content=$4

    # Test for status code
    test_case "${name} returns status ${expected_status}" \
              "curl -s -o /dev/null -w '%{http_code}' ${url}" \
              "${expected_status}" \
              "Endpoint did not return expected status code"

    # If content check is requested
    if [ ! -z "${expected_content}" ]; then
        test_case "${name} contains expected content" \
                  "curl -s ${url}" \
                  "${expected_content}" \
                  "Endpoint response did not contain expected content"
    fi
}

# Test database connection from container
test_database_connection() {
    local container=$1
    local db_host=$2
    local db_user=$3
    local db_pass=$4
    local db_name=$5

    test_case "Database connection from ${container}" \
              "docker exec ${container} python -c \"import psycopg2; conn=psycopg2.connect('postgresql://${db_user}:${db_pass}@${db_host}:5432/${db_name}'); print('Connected successfully!')\"" \
              "Connected successfully!" \
              "Database connection failed from container"
}

# Test container resource usage
test_resource_usage() {
    local container=$1
    local resource=$2
    local threshold=$3

    case $resource in
        "cpu")
            test_case "${container} CPU usage is below ${threshold}%" \
                      "docker stats ${container} --no-stream --format '{{.CPUPerc}}' | sed 's/%//'" \
                      "??" \
                      "CPU usage exceeds threshold" \
                      "value < ${threshold}"
            ;;
        "memory")
            test_case "${container} memory usage is below ${threshold}MB" \
                      "docker stats ${container} --no-stream --format '{{.MemUsage}}' | awk '{print \$1}' | sed 's/MiB//' | sed 's/GiB/*1024/' | bc" \
                      "??" \
                      "Memory usage exceeds threshold" \
                      "value < ${threshold}"
            ;;
    esac
}

# Test application logs for errors
test_logs_for_errors() {
    local container=$1
    local error_patterns=("Error" "Exception" "Traceback" "CRITICAL" "FATAL")

    # Get the logs
    local logs=$(docker logs ${container} 2>&1)

    # Check for each error pattern
    for pattern in "${error_patterns[@]}"; do
        # Count occurrences
        local count=$(echo "${logs}" | grep -c "${pattern}")

        if [ ${count} -gt 0 ]; then
            # Extract sample errors (max 3)
            local samples=$(echo "${logs}" | grep -m3 "${pattern}")

            test_case "Container ${container} logs free of '${pattern}'" \
                      "echo '${count} occurrences found'" \
                      "0 occurrences found" \
                      "Found ${count} occurrences. Examples: ${samples}"
        else
            test_case "Container ${container} logs free of '${pattern}'" \
                      "echo '0 occurrences found'" \
                      "0 occurrences found"
        fi
    done
}

# Test microservice communication
test_service_communication() {
    local service1=$1
    local service2=$2
    local max_attempts=3
    local attempt=1
    local timeout=5

    log "INFO" "Testing communication from ${service1} to ${service2}..."

    # First check if both containers are running
    local service1_running=$(docker ps --format '{{.Names}}' | grep -c "^${service1}$")
    local service2_running=$(docker ps --format '{{.Names}}' | grep -c "^${service2}$")

    if [ ${service1_running} -eq 0 ]; then
        TESTS_TOTAL=$((TESTS_TOTAL + 1))
        TESTS_FAILED=$((TESTS_FAILED + 1))
        echo -e "\n${MAGENTA}[TEST ${TESTS_TOTAL}]${NC} Communication from ${service1} to ${service2}"
        echo -e "  ${RED}✗ FAILED${NC}: Source container ${service1} is not running"
        echo "[TEST ${TESTS_TOTAL}] Communication from ${service1} to ${service2}" >> "${TEST_LOG}"
        echo "  ✗ FAILED: Source container ${service1} is not running" >> "${TEST_LOG}"
        return 1
    fi

    if [ ${service2_running} -eq 0 ]; then
        TESTS_TOTAL=$((TESTS_TOTAL + 1))
        TESTS_FAILED=$((TESTS_FAILED + 1))
        echo -e "\n${MAGENTA}[TEST ${TESTS_TOTAL}]${NC} Communication from ${service1} to ${service2}"
        echo -e "  ${RED}✗ FAILED${NC}: Target container ${service2} is not running"
        echo "[TEST ${TESTS_TOTAL}] Communication from ${service1} to ${service2}" >> "${TEST_LOG}"
        echo "  ✗ FAILED: Target container ${service2} is not running" >> "${TEST_LOG}"
        return 1
    fi

    # Get container IPs for diagnostics
    local service1_ip=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${service1})
    local service2_ip=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${service2})

    log "INFO" "${service1} IP: ${service1_ip}"
    log "INFO" "${service2} IP: ${service2_ip}"

    # Check network connection with increasing verbosity on retries
    while [ ${attempt} -le ${max_attempts} ]; do
        log "INFO" "Attempt ${attempt}/${max_attempts}: Testing connectivity with timeout ${timeout}s"

        if [ ${attempt} -eq 1 ]; then
            # First attempt - standard ping test with timeout
            test_case "Communication from ${service1} to ${service2}" \
                    "timeout ${timeout} docker exec ${service1} ping -c 1 ${service2}" \
                    "1 received" \
                    "Network communication failed"
        elif [ ${attempt} -eq 2 ]; then
            # Second attempt - check DNS resolution
            echo -e "\n${BLUE}[Diagnostics]${NC} Checking DNS resolution from ${service1}..."
            docker exec ${service1} nslookup ${service2} || true

            # Try ping with IP directly instead of hostname
            test_case "Direct IP communication from ${service1} to ${service2}" \
                    "timeout ${timeout} docker exec ${service1} ping -c 1 ${service2_ip}" \
                    "1 received" \
                    "Direct IP communication failed"
        else
            # Third attempt - detailed network diagnostics
            echo -e "\n${BLUE}[Diagnostics]${NC} Checking network configuration in ${service1}..."
            docker exec ${service1} ip addr show || true
            docker exec ${service1} ip route || true

            echo -e "\n${BLUE}[Diagnostics]${NC} Checking network configuration in ${service2}..."
            docker exec ${service2} ip addr show || true

            echo -e "\n${BLUE}[Diagnostics]${NC} Checking Docker network..."
            docker network inspect bridge || true

            # Final attempt with traceroute if available
            test_case "Traceroute from ${service1} to ${service2}" \
                    "docker exec ${service1} sh -c 'type traceroute >/dev/null 2>&1 && traceroute ${service2} || echo No traceroute available'" \
                    "" \
                    "Traceroute diagnostics failed"

            # Collect Docker logs for both containers
            echo -e "\n${BLUE}[Diagnostics]${NC} Last 10 lines of ${service1} logs:"
            docker logs --tail 10 ${service1} || true

            echo -e "\n${BLUE}[Diagnostics]${NC} Last 10 lines of ${service2} logs:"
            docker logs --tail 10 ${service2} || true
        fi

        # Check if the last test was successful
        if [ ${?} -eq 0 ]; then
            return 0
        fi

        # Increment counters but adjust the total since we'll have multiple tests
        if [ ${attempt} -lt ${max_attempts} ]; then
            TESTS_TOTAL=$((TESTS_TOTAL - 1))
        fi

        # Increment attempt counter and increase timeout
        attempt=$((attempt + 1))
        timeout=$((timeout + 5))
    done

    # If we get here, all attempts failed
    log "ERROR" "All connectivity tests between ${service1} and ${service2} failed after ${max_attempts} attempts"
    return 1
}

# Test environment variables
test_environment_variable() {
    local container=$1
    local variable=$2
    local expected_value=$3

    test_case "Environment variable ${variable} in ${container}" \
              "docker exec ${container} printenv ${variable}" \
              "${expected_value}" \
              "Environment variable not set correctly"
}

# Test API performance
test_api_performance() {
    local endpoint=$1
    local max_latency=$2

    test_case "API endpoint ${endpoint} responds within ${max_latency}ms" \
              "curl -s -w '%{time_total}\\n' -o /dev/null ${endpoint} | awk '{print \$1*1000}'" \
              "??" \
              "API response time exceeds threshold" \
              "value < ${max_latency}"
}

# Test file permissions
test_file_permissions() {
    local container=$1
    local file_path=$2
    local expected_perms=$3

    test_case "File permissions for ${file_path} in ${container}" \
              "docker exec ${container} stat -c '%a' ${file_path}" \
              "${expected_perms}" \
              "File has incorrect permissions"
}

# Test file system usage
test_filesystem_usage() {
    local container=$1
    local max_percent=$2

    test_case "Filesystem usage in ${container} below ${max_percent}%" \
              "docker exec ${container} df -h / | awk 'NR==2 {print \$5}' | sed 's/%//'" \
              "??" \
              "Filesystem usage exceeds threshold" \
              "value < ${max_percent}"
}

# Main function to run all tests
run_tests() {
    section "Container Status Tests"
    test_container_running "${CONTAINER_NAME}"
    test_container_running "${DB_CONTAINER_NAME}"

    section "Network Connectivity Tests"
    test_port_open "localhost" "8000"
    test_port_open "localhost" "5432"

    # Check Docker network configuration
    echo -e "\n${CYAN}=== Docker Network Diagnostics ===${NC}"
    echo "=== Docker Network Diagnostics ===" >> "${TEST_LOG}"

    echo -e "${BLUE}[Docker Networks]${NC}"
    docker network ls
    echo "[Docker Networks]" >> "${TEST_LOG}"
    docker network ls >> "${TEST_LOG}"

    # Get the Docker Compose network name (usually <project>_default)
    DOCKER_NETWORK=$(docker network ls | grep "${APP_NAME}" | awk '{print $2}')
    if [ -z "${DOCKER_NETWORK}" ]; then
        DOCKER_NETWORK="bridge"
        log "WARNING" "Could not find Docker Compose network for ${APP_NAME}, using default bridge network"
    else
        log "INFO" "Found Docker Compose network: ${DOCKER_NETWORK}"
    fi

    # Inspect network with retry
    echo -e "${BLUE}[Network Inspection]${NC} ${DOCKER_NETWORK}"
    docker network inspect "${DOCKER_NETWORK}" || log "ERROR" "Failed to inspect network ${DOCKER_NETWORK}"
    echo "[Network Inspection] ${DOCKER_NETWORK}" >> "${TEST_LOG}"
    docker network inspect "${DOCKER_NETWORK}" >> "${TEST_LOG}" 2>&1

    # Test service communication
    test_service_communication "${CONTAINER_NAME}" "${DB_CONTAINER_NAME}"

    section "Database Tests"
    test_database_connection "${CONTAINER_NAME}" "${DB_CONTAINER_NAME}" "postgres" "postgres" "app"

    section "API Endpoint Tests"
    test_endpoint "Health check endpoint" "${API_BASE_URL}/status" "200" "healthy"
    test_endpoint "API documentation" "${API_BASE_URL}/docs" "200" "Swagger"
    test_endpoint "OpenAPI spec" "${API_BASE_URL}/openapi.json" "200" "paths"

    section "Resource Usage Tests"
    test_resource_usage "${CONTAINER_NAME}" "cpu" "50"
    test_resource_usage "${CONTAINER_NAME}" "memory" "300"

    section "Log Analysis Tests"
    test_logs_for_errors "${CONTAINER_NAME}"

    section "Configuration Tests"
    test_environment_variable "${CONTAINER_NAME}" "DATABASE_URL_ASYNC" "postgresql+asyncpg://postgres:postgres@${DB_CONTAINER_NAME}:5432/app"

    section "Performance Tests"
    test_api_performance "${API_BASE_URL}/status" "100"

    # Calculate total runtime
    local SCRIPT_END_TIME=$(date +%s)
    local TOTAL_RUNTIME=$((SCRIPT_END_TIME - SCRIPT_START_TIME))

    # Save runtime for future comparison
    echo "${TOTAL_RUNTIME}" > "${RUNTIME_FILE}"

    # Display test summary with timing information
    echo -e "\n${CYAN}=== Test Summary ===${NC}"
    echo -e "Total tests:  ${TESTS_TOTAL}"
    echo -e "Passed:      ${GREEN}${TESTS_PASSED}${NC}"

    if [ ${TESTS_FAILED} -gt 0 ]; then
        echo -e "Failed:      ${RED}${TESTS_FAILED}${NC}"
    else
        echo -e "Failed:      ${TESTS_FAILED}"
    fi

    if [ ${TESTS_SKIPPED} -gt 0 ]; then
        echo -e "Skipped:     ${YELLOW}${TESTS_SKIPPED}${NC}"
    else
        echo -e "Skipped:     ${TESTS_SKIPPED}"
    fi

    # Display runtime information
    echo -e "\n${CYAN}=== Runtime Information ===${NC}"
    echo -e "Total runtime: $(format_time ${TOTAL_RUNTIME})"

    # Compare with previous run if available
    if [ ${LAST_RUNTIME_SECONDS} -gt 0 ]; then
        local runtime_diff=$((TOTAL_RUNTIME - LAST_RUNTIME_SECONDS))
        local runtime_percent=$(( (runtime_diff * 100) / LAST_RUNTIME_SECONDS ))

        if [ ${runtime_diff} -lt 0 ]; then
            echo -e "Improvement:   ${GREEN}$((runtime_diff * -1))s faster (${runtime_percent}% improvement)${NC}"
        elif [ ${runtime_diff} -gt 0 ]; then
            echo -e "Regression:    ${RED}${runtime_diff}s slower (${runtime_percent}% slower)${NC}"
        else
            echo -e "No change in runtime"
        fi
    fi

    # Display slow tests (if any took more than 2 seconds)
    echo -e "\n${CYAN}=== Test Execution Times ===${NC}"
    local slow_tests_found=0

    for i in "${!TEST_TIMES[@]}"; do
        local test_time=${TEST_TIMES[$i]}
        local test_name=${TEST_NAMES[$i]}

        # Only report tests taking more than 2 seconds
        if [ ${test_time} -gt 2 ]; then
            if [ ${slow_tests_found} -eq 0 ]; then
                echo -e "${YELLOW}Slow tests:${NC}"
                slow_tests_found=1
            fi
            echo -e "  ${test_name}: ${YELLOW}$(format_time ${test_time})${NC}"
        fi
    done

    if [ ${slow_tests_found} -eq 0 ]; then
        echo -e "${GREEN}No slow tests detected${NC}"
    fi

    # Add timing info to log file
    echo -e "\n=== Runtime Information ===" >> "${TEST_LOG}"
    echo "Total runtime: $(format_time ${TOTAL_RUNTIME})" >> "${TEST_LOG}"

    for i in "${!TEST_TIMES[@]}"; do
        echo "  ${TEST_NAMES[$i]}: $(format_time ${TEST_TIMES[$i]})" >> "${TEST_LOG}"
    done

    # Return appropriate exit code
    if [ ${TESTS_FAILED} -eq 0 ]; then
        log "SUCCESS" "All tests passed successfully!"
        return 0
    else
        # Print additional diagnostics for common issues
        log "ERROR" "${TESTS_FAILED} tests failed. Running additional diagnostics..."

        # Collect Docker container status
        echo -e "\n${CYAN}=== Docker Container Status ===${NC}"
        docker ps -a

        # Check Docker Compose config
        echo -e "\n${CYAN}=== Docker Compose Configuration ===${NC}"
        cd $(dirname "$0") && docker compose config || true

        # Check for Docker networking issues
        echo -e "\n${CYAN}=== Container DNS Configuration ===${NC}"
        for container in ${CONTAINER_NAME} ${DB_CONTAINER_NAME}; do
            if docker ps -q -f name=${container} >/dev/null 2>&1; then
                echo -e "${BLUE}[${container} DNS Configuration]${NC}"
                docker exec ${container} cat /etc/resolv.conf || echo "Could not read resolv.conf"
                echo -e "${BLUE}[${container} Hosts File]${NC}"
                docker exec ${container} cat /etc/hosts || echo "Could not read hosts file"
            fi
        done

        # Check for database-specific issues if database tests failed
        if grep -q "database.*failed" "${TEST_LOG}"; then
            echo -e "\n${CYAN}=== Database Connection Diagnostics ===${NC}"

            # Check if PostgreSQL is accepting connections
            if docker ps -q -f name=${DB_CONTAINER_NAME} >/dev/null 2>&1; then
                echo -e "${BLUE}[PostgreSQL Logs]${NC}"
                docker logs --tail 20 ${DB_CONTAINER_NAME}

                echo -e "${BLUE}[PostgreSQL Configuration]${NC}"
                docker exec ${DB_CONTAINER_NAME} cat /var/lib/postgresql/data/postgresql.conf | grep "listen_addresses\|port\|password" || echo "Could not read PostgreSQL config"

                echo -e "${BLUE}[PostgreSQL Connection Test]${NC}"
                docker exec ${DB_CONTAINER_NAME} pg_isready -h localhost || echo "PostgreSQL is not ready"
            fi
        fi

        log "ERROR" "${TESTS_FAILED} tests failed. Check the logs for details: ${TEST_LOG}"
        return 1
    fi
}

# Execute tests
run_tests
