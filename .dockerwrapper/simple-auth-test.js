const { test, expect } = require('@playwright/test');

test.describe('Authentication Test', () => {
  test('should load main page without authentication errors', async ({ page }) => {
    console.log('Testing main page authentication...');
    
    // Navigate to the main page
    await page.goto('http://localhost:3100');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check if the page loaded successfully
    const title = await page.title();
    console.log('Page title:', title);
    
    // Look for authentication error messages
    const authErrorSelectors = [
      'text=authentication error',
      'text=Authentication Error',
      'text=get authentication token',
      'text=Get Authentication Token',
      'text=Failed to authenticate',
      'text=Login required',
      '[data-testid="auth-error"]',
      '.auth-error',
      '.error-message'
    ];
    
    let authErrorFound = false;
    for (const selector of authErrorSelectors) {
      try {
        const element = await page.locator(selector).first();
        if (await element.isVisible()) {
          console.log(`Authentication error found: ${selector}`);
          authErrorFound = true;
          break;
        }
      } catch (e) {
        // Selector not found, continue
      }
    }
    
    // Take a screenshot for debugging
    await page.screenshot({ path: '/app/test_screenshots/main-page-auth-test.png' });
    
    // Check if authentication was successful
    if (!authErrorFound) {
      console.log('✅ No authentication errors found on main page');
    } else {
      console.log('❌ Authentication error detected on main page');
    }
    
    // The test should pass if no authentication errors are found
    expect(authErrorFound).toBe(false);
  });

  test('should be able to get test token from API', async ({ request }) => {
    console.log('Testing API authentication endpoint...');
    
    // Test the test-token endpoint
    const response = await request.post('http://localhost:3050/api/v1/auth/test-token');
    
    console.log('API response status:', response.status());
    
    expect(response.status()).toBe(200);
    
    const responseBody = await response.json();
    console.log('API response:', responseBody);
    
    expect(responseBody).toHaveProperty('access_token');
    expect(responseBody).toHaveProperty('token_type');
    expect(responseBody.token_type).toBe('bearer');
    
    console.log('✅ API authentication endpoint working correctly');
  });
});
