#!/bin/bash

# Script to run the file upload and VM injection test

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting File Upload and VM Injection Test...${NC}"

# Check if the Docker network exists
NETWORK_NAME="turdparty-network"
if ! docker network inspect $NETWORK_NAME &>/dev/null; then
    echo -e "${YELLOW}Creating Docker network: $NETWORK_NAME${NC}"
    docker network create $NETWORK_NAME
fi

# Check if the Playwright container is running
CONTAINER_NAME="turdparty-playwright"
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${YELLOW}Starting Playwright container...${NC}"

    # Run the Playwright container
    docker run -d \
        --name $CONTAINER_NAME \
        --network $NETWORK_NAME \
        --add-host=host.docker.internal:host-gateway \
        -v $(pwd):/app \
        -w /app \
        mcr.microsoft.com/playwright:v1.40.0-jammy \
        tail -f /dev/null

    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to start Playwright container${NC}"
        exit 1
    fi

    echo -e "${GREEN}Playwright container started${NC}"
else
    echo -e "${GREEN}Playwright container is already running${NC}"
fi

# Install dependencies in the container
echo -e "${YELLOW}Installing dependencies...${NC}"
docker exec $CONTAINER_NAME npm install @playwright/test axios

# Run the tests
echo -e "${YELLOW}Running simple file upload test...${NC}"
docker exec $CONTAINER_NAME npx playwright test tests/simple-file-upload.spec.js --config=.dockerwrapper/playwright.config.js

echo -e "${YELLOW}Running simple VM injection test...${NC}"
docker exec $CONTAINER_NAME npx playwright test tests/simple-vm-injection.spec.js --config=.dockerwrapper/playwright.config.js

echo -e "${YELLOW}Running VM injection SSH test...${NC}"
docker exec $CONTAINER_NAME npx playwright test tests/vm-injection-ssh.spec.js --config=.dockerwrapper/playwright.config.js

# Check the test result
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Test completed successfully!${NC}"
else
    echo -e "${RED}Test failed!${NC}"
fi

# Show the test results location
echo -e "${YELLOW}Test screenshots are available in the test_screenshots directory${NC}"

# Optionally, stop the container
read -p "Do you want to stop the Playwright container? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Stopping Playwright container...${NC}"
    docker stop $CONTAINER_NAME
    docker rm $CONTAINER_NAME
    echo -e "${GREEN}Playwright container stopped and removed${NC}"
fi

echo -e "${GREEN}Done!${NC}"
