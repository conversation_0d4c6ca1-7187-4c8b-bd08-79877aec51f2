#!/bin/bash

echo "🔍 TurdParty Services Health Check"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check HTTP endpoint
check_http() {
    local url=$1
    local service_name=$2
    local expected_status=${3:-200}
    
    echo -n "Checking $service_name ($url)... "
    
    if response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 --max-time 10 "$url" 2>/dev/null); then
        if [ "$response" -eq "$expected_status" ]; then
            echo -e "${GREEN}✓ OK${NC} (HTTP $response)"
            return 0
        else
            echo -e "${YELLOW}⚠ WARNING${NC} (HTTP $response, expected $expected_status)"
            return 1
        fi
    else
        echo -e "${RED}✗ FAILED${NC} (Connection failed)"
        return 1
    fi
}

# Function to check container status
check_container() {
    local container_name=$1
    local service_name=$2
    
    echo -n "Checking $service_name container... "
    
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "^$container_name.*Up"; then
        echo -e "${GREEN}✓ RUNNING${NC}"
        return 0
    elif docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep -q "^$container_name"; then
        status=$(docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep "^$container_name" | awk '{print $2}')
        echo -e "${RED}✗ NOT RUNNING${NC} ($status)"
        return 1
    else
        echo -e "${RED}✗ NOT FOUND${NC}"
        return 1
    fi
}

echo ""
echo "📦 Container Status Check"
echo "------------------------"

# Core Services
check_container "turdparty_api" "FastAPI Backend"
check_container "turdparty_postgres" "PostgreSQL Database"
check_container "turdparty_minio" "MinIO Storage"
check_container "turdparty_minio_ssh" "MinIO SSH"

# Frontend Services
check_container "turdparty_frontend" "React Frontend (Production)"
check_container "turdparty_react_dev" "React Frontend (Development)"
check_container "turdparty_dashboard" "Service Dashboard"

# Celery Services
check_container "turdparty_redis" "Redis"
check_container "turdparty_celery_file" "Celery File Worker"
check_container "turdparty_celery_vm" "Celery VM Worker"
check_container "turdparty_celery_injection" "Celery Injection Worker"
check_container "turdparty_celery_monitoring" "Celery Monitoring Worker"
check_container "turdparty_celery_beat" "Celery Beat Scheduler"
check_container "turdparty_flower" "Flower Monitoring"

# Status Services
check_container "turdparty_cachet" "Cachet Status Page"
check_container "turdparty_cachet_postgres" "Cachet PostgreSQL"

echo ""
echo "🌐 HTTP Endpoint Check"
echo "----------------------"

# Core API endpoints
check_http "http://localhost:3050/api/v1/health/" "API Health Check"
check_http "http://localhost:3050/api/v1/docs/" "API Documentation"

# Frontend endpoints
check_http "http://localhost:3100" "React Frontend (Production)"
check_http "http://localhost:3250" "React Frontend (Development)"
check_http "http://localhost:3150" "Service Dashboard"

# Storage endpoints
check_http "http://localhost:3300/minio/health/live" "MinIO Health"
check_http "http://localhost:3301" "MinIO Console"

# Monitoring endpoints
check_http "http://localhost:5556" "Flower Monitoring"

# Status page
check_http "http://localhost:3501" "Cachet Status Page"

echo ""
echo "📊 Service Summary"
echo "------------------"

# Count running containers
running_containers=$(docker ps --filter "name=turdparty" --format "{{.Names}}" | wc -l)
total_containers=$(docker ps -a --filter "name=turdparty" --format "{{.Names}}" | wc -l)

echo "Running containers: $running_containers/$total_containers"

# Show port mappings
echo ""
echo "🔌 Port Mappings"
echo "----------------"
docker ps --filter "name=turdparty" --format "table {{.Names}}\t{{.Ports}}" | grep -v "PORTS"

echo ""
echo "Health check completed!"
