[tool.poetry]
name = "turdparty-dashboard"
version = "1.0.0"
description = "A CLI dashboard for monitoring and managing Docker containers"
authors = ["TurdParty Team"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.8"
click = "^8.1.3"
docker = "^6.1.3"
urwid = "^2.1.2"
pyyaml = "^6.0"

[tool.poetry.dev-dependencies]
pytest = "^7.3.1"
black = "^23.3.0"
isort = "^5.12.0"

[tool.poetry.scripts]
turdparty-dashboard = "dashboard:cli"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api" 