#!/bin/bash

# Script to run an upload test in Docker
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Running Docker-based File Upload Test              ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Ensure directories exist
mkdir -p test_screenshots test-results

# Check if the frontend is running
echo -e "${YELLOW}Checking if the application is running...${NC}"
if ! curl -s http://localhost:3100 > /dev/null; then
  echo -e "${RED}Error: Frontend is not accessible at http://localhost:3100${NC}"
  echo -e "${YELLOW}Make sure all containers are running with: docker compose -f .dockerwrapper/dev-compose.yml up -d${NC}"
  exit 1
fi

# Create test file
TEST_FILE="test-upload.txt"
echo "Test file for upload testing" > "$TEST_FILE"
echo "Created at: $(date)" >> "$TEST_FILE"
echo "Random ID: $(date +%s)" >> "$TEST_FILE"
echo -e "${YELLOW}Created test file: ${TEST_FILE}${NC}"

# Get the host IP from the container perspective
HOST_IP=$(ip -4 addr show docker0 | grep -oP '(?<=inet\s)\d+(\.\d+){3}' || echo "**********")
echo -e "${YELLOW}Using host IP: ${HOST_IP}${NC}"

# Run using a temporary Docker container
echo -e "${YELLOW}Running test in a Docker container...${NC}"
docker run --rm -v "$(pwd):/app" -w /app mcr.microsoft.com/playwright:v1.41.0-focal /bin/bash -c "
cd /app
npm init -y > /dev/null 2>&1
npm install playwright@latest --no-save > /dev/null 2>&1
cat > upload-test.js << EOF
// Standalone upload test
const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function runTest() {
  console.log('Starting file upload test');

  const browser = await chromium.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Step 1: Navigate to upload page
    console.log('Navigating to upload page...');
    await page.goto('http://${HOST_IP}:3100/upload', { timeout: 30000 });
    await page.screenshot({ path: '/app/test_screenshots/01-upload-page.png' });

    // Step 2: Fill form
    console.log('Filling upload form...');
    await page.locator('textarea[placeholder*=\"description\"]').fill('Test upload from Docker');

    // Step 3: Select file
    console.log('Selecting file...');
    await page.locator('input[type=\"file\"]').setInputFiles('/app/test-upload.txt');
    await page.screenshot({ path: '/app/test_screenshots/02-file-selected.png' });

    // Step 4: Submit form
    console.log('Submitting form...');
    await page.locator('button:has-text(\"Upload\")').click();

    // Step 5: Wait for success message
    console.log('Waiting for upload to complete...');
    await page.waitForSelector('.ant-message-success, .upload-success', { timeout: 30000 });
    await page.screenshot({ path: '/app/test_screenshots/03-upload-success.png' });

    // Step 6: Navigate to files page
    console.log('Navigating to files page...');
    await page.goto('http://${HOST_IP}:3100/files', { timeout: 30000 });
    await page.screenshot({ path: '/app/test_screenshots/04-files-page.png' });

    console.log('Test completed successfully!');
    return true;
  } catch (error) {
    console.error('Test failed:', error);
    await page.screenshot({ path: '/app/test_screenshots/error-state.png' });
    return false;
  } finally {
    await browser.close();
  }
}

// Run the test
runTest()
  .then(success => {
    if (success) {
      console.log('✅ Test passed!');
      process.exit(0);
    } else {
      console.error('❌ Test failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
EOF

node upload-test.js
"

# Check if the test was successful
if [ $? -eq 0 ]; then
  echo -e "${GREEN}File upload test completed successfully!${NC}"
  echo -e "${YELLOW}Screenshots saved to test_screenshots/${NC}"

  # List the generated screenshots
  echo -e "${YELLOW}Generated screenshots:${NC}"
  ls -la test_screenshots/*.png

  echo -e "${GREEN}✅ Test passed!${NC}"
  exit 0
else
  echo -e "${RED}Test failed. Check the logs above for details.${NC}"
  echo -e "${YELLOW}Screenshots of the failure are available in test_screenshots/error-state.png${NC}"
  exit 1
fi