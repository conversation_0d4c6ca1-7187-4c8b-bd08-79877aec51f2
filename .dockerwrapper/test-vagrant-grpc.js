/**
 * Test script to verify the connection to the Vagrant gRPC service.
 *
 * This script attempts to connect to the Vagrant gRPC service running on the host
 * and perform a simple status check.
 */

const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
const fs = require('fs');
const path = require('path');

// Path to your proto file - we'll create a temporary one if it doesn't exist
const PROTO_PATH = path.join(__dirname, 'vagrant.proto');

// Create a simple proto file if it doesn't exist
if (!fs.existsSync(PROTO_PATH)) {
  console.log('Creating temporary proto file...');
  const protoContent = `
syntax = "proto3";

package vagrant;

service VagrantService {
  rpc Status(VagrantStatusRequest) returns (VagrantStatusResponse) {}
  rpc Up(VagrantUpRequest) returns (VagrantCommandResponse) {}
  rpc Halt(VagrantHaltRequest) returns (VagrantCommandResponse) {}
  rpc Destroy(VagrantDestroyRequest) returns (VagrantCommandResponse) {}
  rpc ExecuteCommand(VagrantExecuteCommandRequest) returns (VagrantExecuteCommandResponse) {}
  rpc GetMachineInfo(VagrantMachineInfoRequest) returns (VagrantMachineInfoResponse) {}
  rpc ListBoxes(VagrantBoxesRequest) returns (VagrantBoxesResponse) {}
}

message VagrantStatusRequest {
  string vm_id = 1;
}

message VagrantStatusResponse {
  string status = 1;
  string error_message = 2;
}

message VagrantUpRequest {
  string vm_id = 1;
  bool provision = 2;
}

message VagrantHaltRequest {
  string vm_id = 1;
  bool force = 2;
}

message VagrantDestroyRequest {
  string vm_id = 1;
  bool force = 2;
}

message VagrantCommandResponse {
  bool success = 1;
  string message = 2;
  string error_message = 3;
}

message VagrantExecuteCommandRequest {
  string vm_id = 1;
  string command = 2;
  bool sudo = 3;
}

message VagrantExecuteCommandResponse {
  bool success = 1;
  string stdout = 2;
  string stderr = 3;
  int32 exit_code = 4;
}

message VagrantMachineInfoRequest {
  string vm_id = 1;
}

message VagrantMachineInfoResponse {
  string name = 1;
  string provider = 2;
  string state = 3;
  string directory = 4;
  map<string, string> network = 5;
  string error_message = 6;
}

message VagrantBoxInfo {
  string name = 1;
  string provider = 2;
  string version = 3;
}

message VagrantBoxesRequest {
}

message VagrantBoxesResponse {
  repeated VagrantBoxInfo boxes = 1;
  string error_message = 2;
}
  `;

  fs.writeFileSync(PROTO_PATH, protoContent);
}

// Load the proto file
console.log('Loading proto file...');
const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
  keepCase: true,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true
});

const protoDescriptor = grpc.loadPackageDefinition(packageDefinition);
const vagrant = protoDescriptor.vagrant;

// Create a client
console.log('Creating gRPC client...');
// Connect directly to the Vagrant gRPC service on the host
const client = new vagrant.VagrantService('host.docker.internal:40000', grpc.credentials.createInsecure());

// Log the connection details
console.log('Connecting to Vagrant gRPC service at host.docker.internal:40000');

// Test the connection
console.log('Testing connection to Vagrant gRPC service...');
client.Status({ vm_id: 'test' }, (err, response) => {
  if (err) {
    console.error('Error connecting to Vagrant gRPC service:');
    console.error(err);
    process.exit(1);
  } else {
    console.log('Successfully connected to Vagrant gRPC service!');
    console.log('Response:', response);
    process.exit(0);
  }
});

// Set a timeout to exit if the connection hangs
setTimeout(() => {
  console.error('Connection timed out after 5 seconds');
  process.exit(1);
}, 5000);
