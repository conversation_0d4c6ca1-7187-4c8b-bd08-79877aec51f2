#!/bin/bash

# Script to test the VM injection API endpoint

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# API configuration
API_BASE_URL="http://turdparty_api:8000"
API_UPLOAD_ENDPOINT="/api/v1/files/upload"
API_VM_CREATE_ENDPOINT="/api/v1/vagrant/create"
API_VM_INJECT_ENDPOINT="/api/v1/vagrant/inject"
API_VM_STATUS_ENDPOINT="/api/v1/vagrant/status"
API_VM_DESTROY_ENDPOINT="/api/v1/vagrant/destroy"

# Test file configuration
TEST_FILE="/tmp/test_file.txt"
TEST_FILE_CONTENT="This is a test file for VM injection via API."
TARGET_PATH="/tmp/injection_test/test_file.txt"

# Create a test file
echo -e "${YELLOW}Creating test file at ${TEST_FILE}${NC}"
echo "${TEST_FILE_CONTENT}" > "${TEST_FILE}"

# Function to make API requests
function api_request() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local file="$4"

    if [ -n "$file" ]; then
        # File upload request
        curl -s -X "${method}" \
            "${API_BASE_URL}${endpoint}" \
            -F "file=@${file}"
    elif [ -n "$data" ]; then
        # JSON request
        curl -s -X "${method}" \
            "${API_BASE_URL}${endpoint}" \
            -H "Content-Type: application/json" \
            -d "${data}"
    else
        # Simple request
        curl -s -X "${method}" \
            "${API_BASE_URL}${endpoint}"
    fi
}

# Upload the test file
echo -e "${YELLOW}Uploading test file to API${NC}"
UPLOAD_RESPONSE=$(api_request "POST" "${API_UPLOAD_ENDPOINT}" "" "${TEST_FILE}")
echo "Upload response: ${UPLOAD_RESPONSE}"

# Extract the file ID from the response
FILE_ID=$(echo "${UPLOAD_RESPONSE}" | grep -o '"file_id":"[^"]*"' | cut -d'"' -f4)

if [ -z "${FILE_ID}" ]; then
    echo -e "${RED}Failed to upload file${NC}"
    exit 1
fi

echo -e "${GREEN}File uploaded successfully with ID ${FILE_ID}${NC}"

# Create a VM
echo -e "${YELLOW}Creating VM${NC}"
CREATE_VM_DATA="{\"name\":\"test_vm\"}"
CREATE_VM_RESPONSE=$(api_request "POST" "${API_VM_CREATE_ENDPOINT}" "${CREATE_VM_DATA}")
echo "Create VM response: ${CREATE_VM_RESPONSE}"

# Extract the VM ID from the response
VM_ID=$(echo "${CREATE_VM_RESPONSE}" | grep -o '"vm_id":"[^"]*"' | cut -d'"' -f4)

if [ -z "${VM_ID}" ]; then
    echo -e "${RED}Failed to create VM${NC}"
    exit 1
fi

echo -e "${GREEN}VM created successfully with ID ${VM_ID}${NC}"

# Wait for the VM to be ready
echo -e "${YELLOW}Waiting for VM to be ready${NC}"
MAX_WAIT_TIME=300 # 5 minutes
START_TIME=$(date +%s)

while true; do
    CURRENT_TIME=$(date +%s)
    ELAPSED_TIME=$((CURRENT_TIME - START_TIME))

    if [ ${ELAPSED_TIME} -gt ${MAX_WAIT_TIME} ]; then
        echo -e "${RED}Timeout waiting for VM to be ready${NC}"
        exit 1
    fi

    VM_STATUS_RESPONSE=$(api_request "GET" "${API_VM_STATUS_ENDPOINT}/${VM_ID}")
    VM_STATUS=$(echo "${VM_STATUS_RESPONSE}" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

    echo "VM status: ${VM_STATUS}"

    if [ "${VM_STATUS}" == "running" ]; then
        echo -e "${GREEN}VM is ready${NC}"
        break
    fi

    echo "Waiting for VM to be ready... (${ELAPSED_TIME}s elapsed)"
    sleep 10
done

# Inject the file into the VM
echo -e "${YELLOW}Injecting file into VM${NC}"
INJECT_DATA="{\"vm_id\":\"${VM_ID}\",\"file_id\":\"${FILE_ID}\",\"target_path\":\"${TARGET_PATH}\"}"
INJECT_RESPONSE=$(api_request "POST" "${API_VM_INJECT_ENDPOINT}" "${INJECT_DATA}")
echo "Inject response: ${INJECT_RESPONSE}"

# Check if the injection was successful
INJECT_SUCCESS=$(echo "${INJECT_RESPONSE}" | grep -o '"success":[^,}]*' | cut -d':' -f2)

if [ "${INJECT_SUCCESS}" != "true" ]; then
    echo -e "${RED}Failed to inject file${NC}"
    # Clean up
    api_request "DELETE" "${API_VM_DESTROY_ENDPOINT}/${VM_ID}"
    exit 1
fi

echo -e "${GREEN}File injected successfully${NC}"

# Verify the file injection
echo -e "${YELLOW}Verifying file injection${NC}"
VERIFY_DATA="{\"command\":\"cat ${TARGET_PATH}\"}"
VERIFY_RESPONSE=$(api_request "POST" "${API_VM_STATUS_ENDPOINT}/${VM_ID}/execute" "${VERIFY_DATA}")
echo "Verify response: ${VERIFY_RESPONSE}"

# Extract the command output
COMMAND_OUTPUT=$(echo "${VERIFY_RESPONSE}" | grep -o '"stdout":"[^"]*"' | cut -d'"' -f4)

if [[ "${COMMAND_OUTPUT}" != *"${TEST_FILE_CONTENT}"* ]]; then
    echo -e "${RED}File content verification failed${NC}"
    # Clean up
    api_request "DELETE" "${API_VM_DESTROY_ENDPOINT}/${VM_ID}"
    exit 1
fi

echo -e "${GREEN}File content verified successfully${NC}"

# Destroy the VM
echo -e "${YELLOW}Destroying VM${NC}"
DESTROY_RESPONSE=$(api_request "DELETE" "${API_VM_DESTROY_ENDPOINT}/${VM_ID}")
echo "Destroy response: ${DESTROY_RESPONSE}"

# Check if the VM was destroyed successfully
DESTROY_SUCCESS=$(echo "${DESTROY_RESPONSE}" | grep -o '"success":[^,}]*' | cut -d':' -f2)

if [ "${DESTROY_SUCCESS}" != "true" ]; then
    echo -e "${RED}Failed to destroy VM${NC}"
    exit 1
fi

echo -e "${GREEN}VM destroyed successfully${NC}"

# Clean up the test file
rm -f "${TEST_FILE}"

echo -e "${GREEN}End-to-end test completed successfully!${NC}"
exit 0
