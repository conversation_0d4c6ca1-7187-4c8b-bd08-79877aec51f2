#!/usr/bin/env python3

"""
Script to update docker-compose files to use port mappings from port_mappings.json.
"""

import json
import os
import re
import sys

# Path to the port mappings file
PORT_MAPPINGS_FILE = os.path.join(os.path.dirname(__file__), 'port_mappings.json')

# Paths to the docker-compose files
DOCKER_COMPOSE_FILES = [
    os.path.join(os.path.dirname(__file__), 'docker-compose.yml'),
    os.path.join(os.path.dirname(__file__), 'docker-compose.playwright.yml')
]

def load_port_mappings():
    """
    Load port mappings from the JSON file.

    Returns:
        dict: The port mappings.
    """
    with open(PORT_MAPPINGS_FILE, 'r') as f:
        return json.load(f)

def update_docker_compose_file(file_path, port_mappings):
    """
    Update a docker-compose file to use port mappings from the JSON file.

    Args:
        file_path: The path to the docker-compose file.
        port_mappings: The port mappings.
    """
    print(f"Updating {file_path}...")

    # Read the file as <PERSON>AM<PERSON>
    with open(file_path, 'r') as f:
        content = f.read()

    # Parse the YAML structure to find services
    services_section = False
    current_service = None
    service_indentation = 0
    lines = content.split('\n')
    new_lines = []

    for line in lines:
        # Check if we're in the services section
        if line.strip() == 'services:' or line.strip() == 'services:':
            services_section = True
            service_indentation = len(line) - len(line.lstrip())

        # Check if this line defines a service (must be in services section and have the right indentation)
        if services_section and line.strip() and not line.strip().startswith('#'):
            indentation = len(line) - len(line.lstrip())
            if indentation == service_indentation + 2 and ':' in line and not line.strip().startswith('-'):
                current_service = line.strip().split(':')[0].strip()
                print(f"Found service: {current_service}")

        # Check if this line defines a port mapping
        port_match = re.search(r'(\s+- )"?(\d+):(\d+)"?', line)
        if port_match and current_service:
            indent = port_match.group(1)
            host_port = port_match.group(2)
            container_port = port_match.group(3)

            # Check if we have a port mapping for this service
            if current_service in port_mappings:
                new_host_port = port_mappings[current_service]
                print(f"  Replacing port mapping for {current_service}: {host_port}:{container_port} -> {new_host_port}:{container_port}")
                line = line[:port_match.start()] + f'{indent}"{new_host_port}:{container_port}"' + line[port_match.end():]
            else:
                print(f"  No port mapping found for service {current_service}, keeping {host_port}:{container_port}")

        new_lines.append(line)

    # Write the updated content back to the file
    with open(file_path, 'w') as f:
        f.write('\n'.join(new_lines))

    print(f"Updated {file_path}")

def main():
    """Main function."""
    # Load port mappings
    port_mappings = load_port_mappings()
    print(f"Loaded port mappings: {port_mappings}")

    # Update docker-compose files
    for file_path in DOCKER_COMPOSE_FILES:
        if os.path.exists(file_path):
            update_docker_compose_file(file_path, port_mappings)
        else:
            print(f"File not found: {file_path}")

    print("Done!")

if __name__ == "__main__":
    main()
