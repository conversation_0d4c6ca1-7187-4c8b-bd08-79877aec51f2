#!/bin/bash

# Script to fix network connectivity issues between Docker containers
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=======================================================${NC}"
echo -e "${GREEN}    Fixing Container Network Configuration             ${NC}"
echo -e "${GREEN}=======================================================${NC}"

# Check if the Playwright container is running
PLAYWRIGHT_CONTAINER=$(docker ps -q --filter "name=turdparty_playwright")
if [ -z "$PLAYWRIGHT_CONTAINER" ]; then
    echo -e "${RED}Playwright container is not running.${NC}"
    echo -e "Start it with: ${YELLOW}.dockerwrapper/persistent-test-env.sh${NC}"
    exit 1
fi
echo -e "Found Playwright container: ${YELLOW}$PLAYWRIGHT_CONTAINER${NC}"

# Check if the API container is running
API_CONTAINER=$(docker ps -q --filter "name=turdparty_api")
if [ -z "$API_CONTAINER" ]; then
    echo -e "${RED}API container is not running.${NC}"
    echo -e "Start it with: ${YELLOW}docker compose up -d api${NC}"
    exit 1
fi
echo -e "Found API container: ${YELLOW}$API_CONTAINER${NC}"

# Get the networks of both containers
echo -e "\n${GREEN}Network Information:${NC}"
PLAYWRIGHT_NETWORKS=$(docker inspect --format='{{range $net,$v := .NetworkSettings.Networks}}{{$net}} {{end}}' $PLAYWRIGHT_CONTAINER)
API_NETWORKS=$(docker inspect --format='{{range $net,$v := .NetworkSettings.Networks}}{{$net}} {{end}}' $API_CONTAINER)

echo -e "Playwright container networks: ${YELLOW}$PLAYWRIGHT_NETWORKS${NC}"
echo -e "API container networks: ${YELLOW}$API_NETWORKS${NC}"

# Checking for shared networks
COMMON_NETWORK=""
for NETWORK in $PLAYWRIGHT_NETWORKS; do
    if [[ $API_NETWORKS == *"$NETWORK"* ]]; then
        COMMON_NETWORK=$NETWORK
        break
    fi
done

# Find a suitable network to connect both containers
if [ -z "$COMMON_NETWORK" ]; then
    echo -e "${YELLOW}The containers do not share a common network.${NC}"

    # Try to find a preferred network from API container
    if [[ $API_NETWORKS == *"dockerwrapper_turdparty_network"* ]]; then
        NETWORK_TO_USE="dockerwrapper_turdparty_network"
    elif [[ $API_NETWORKS == *"bridge"* ]]; then
        NETWORK_TO_USE="bridge"
    else
        # Use the first network of the API container
        NETWORK_TO_USE=$(echo $API_NETWORKS | awk '{print $1}')
    fi

    echo -e "Connecting Playwright container to ${GREEN}$NETWORK_TO_USE${NC} network..."
    docker network connect $NETWORK_TO_USE $PLAYWRIGHT_CONTAINER
    COMMON_NETWORK=$NETWORK_TO_USE
else
    echo -e "Containers already share common network: ${GREEN}$COMMON_NETWORK${NC}"
fi

# Get IP addresses
API_IP=$(docker inspect -f "{{range .NetworkSettings.Networks}}{{if eq \"$COMMON_NETWORK\" \"${NC}\"}}{{.IPAddress}}{{end}}{{end}}" $API_CONTAINER)
if [ -z "$API_IP" ]; then
    API_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $API_CONTAINER)
fi

echo -e "API container IP: ${YELLOW}$API_IP${NC}"

# Add the API hostname to the container's hosts file
echo -e "\n${GREEN}Configuring Playwright container hostname resolution...${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "grep -q 'api' /etc/hosts || echo '$API_IP api' | sudo tee -a /etc/hosts"
docker exec $PLAYWRIGHT_CONTAINER bash -c "cat /etc/hosts | grep api"

# Verify connectivity
echo -e "\n${GREEN}Verifying connectivity to API...${NC}"
docker exec $PLAYWRIGHT_CONTAINER ping -c 3 api || echo -e "${YELLOW}Ping to 'api' hostname failed, but this might be blocked by container security.${NC}"

# Test HTTP connection to API
echo -e "\n${GREEN}Testing HTTP connection to API...${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "curl -s -o /dev/null -w '%{http_code}' http://api:3050/api/health || echo Connection failed"

# Update API URL in test files if needed
echo -e "\n${GREEN}Checking test configurations...${NC}"
API_URL_CONFIG=$(docker exec $PLAYWRIGHT_CONTAINER bash -c "find /app/tests -type f -name '*.js' -o -name '*.ts' | xargs grep -l 'API_URL\|apiUrl\|baseURL' 2>/dev/null || echo 'No files found'")

if [[ "$API_URL_CONFIG" != "No files found" ]]; then
    echo -e "Found test configuration files with API URL references:"
    echo -e "$API_URL_CONFIG"
    echo -e "\n${YELLOW}You may need to update these files to use 'http://api:3050' as the API URL.${NC}"
fi

# Create a helper script in the container to test API connectivity
echo -e "\n${GREEN}Creating API connectivity test script in container...${NC}"
docker exec $PLAYWRIGHT_CONTAINER bash -c "cat > /app/test-api-connection.js << 'EOF'
const http = require('http');

const testEndpoints = [
  { url: 'http://api:3050/api/health', name: 'Health Endpoint' },
  { url: 'http://api:3050/api/version', name: 'Version Endpoint' },
  { url: 'http://api:3050/api/status', name: 'Status Endpoint' }
];

async function testConnection(endpoint) {
  return new Promise((resolve) => {
    console.log(`Testing ${endpoint.name}: ${endpoint.url}`);
    const req = http.get(endpoint.url, (res) => {
      console.log(`Status Code: ${res.statusCode}`);
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        console.log(`Response: ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`);
        resolve(true);
      });
    });

    req.on('error', (error) => {
      console.error(`Error: ${error.message}`);
      resolve(false);
    });

    req.setTimeout(5000, () => {
      console.error('Request timed out');
      req.abort();
      resolve(false);
    });
  });
}

async function runTests() {
  console.log('Starting API connection tests...');
  for (const endpoint of testEndpoints) {
    await testConnection(endpoint);
    console.log('----------------------------');
  }
  console.log('API connection tests completed.');
}

runTests();
EOF"

echo -e "\n${GREEN}Network configuration complete!${NC}"
echo -e "To test API connectivity from the container, run:"
echo -e "${YELLOW}docker exec $PLAYWRIGHT_CONTAINER node /app/test-api-connection.js${NC}"
echo -e "\nIf you're still having issues, try restarting the containers with:"
echo -e "${YELLOW}docker restart $PLAYWRIGHT_CONTAINER $API_CONTAINER${NC}"