
syntax = "proto3";

package vagrant;

service VagrantService {
  rpc Status(VagrantStatusRequest) returns (VagrantStatusResponse) {}
  rpc Up(VagrantUpRequest) returns (VagrantCommandResponse) {}
  rpc Halt(VagrantHaltRequest) returns (VagrantCommandResponse) {}
  rpc Destroy(VagrantDestroyRequest) returns (VagrantCommandResponse) {}
  rpc ExecuteCommand(VagrantExecuteCommandRequest) returns (VagrantExecuteCommandResponse) {}
  rpc GetMachineInfo(VagrantMachineInfoRequest) returns (VagrantMachineInfoResponse) {}
  rpc ListBoxes(VagrantBoxesRequest) returns (VagrantBoxesResponse) {}
}

message VagrantStatusRequest {
  string vm_id = 1;
}

message VagrantStatusResponse {
  string status = 1;
  string error_message = 2;
}

message VagrantUpRequest {
  string vm_id = 1;
  bool provision = 2;
}

message VagrantHaltRequest {
  string vm_id = 1;
  bool force = 2;
}

message VagrantDestroyRequest {
  string vm_id = 1;
  bool force = 2;
}

message VagrantCommandResponse {
  bool success = 1;
  string message = 2;
  string error_message = 3;
}

message VagrantExecuteCommandRequest {
  string vm_id = 1;
  string command = 2;
  bool sudo = 3;
}

message VagrantExecuteCommandResponse {
  bool success = 1;
  string stdout = 2;
  string stderr = 3;
  int32 exit_code = 4;
}

message VagrantMachineInfoRequest {
  string vm_id = 1;
}

message VagrantMachineInfoResponse {
  string name = 1;
  string provider = 2;
  string state = 3;
  string directory = 4;
  map<string, string> network = 5;
  string error_message = 6;
}

message VagrantBoxInfo {
  string name = 1;
  string provider = 2;
  string version = 3;
}

message VagrantBoxesRequest {
}

message VagrantBoxesResponse {
  repeated VagrantBoxInfo boxes = 1;
  string error_message = 2;
}
  