#!/bin/bash

# Script to test the VM injection API endpoint

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing VM Injection API Endpoint...${NC}"

# Get a test token
echo -e "${YELLOW}Getting test token...${NC}"
TOKEN_RESPONSE=$(curl -s http://localhost:3055/api/test-auth/token/admin)
TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo -e "${RED}Failed to get test token${NC}"
    echo "Response: $TOKEN_RESPONSE"
    exit 1
fi

echo -e "${GREEN}Successfully obtained test token${NC}"

# Create a test file upload
echo -e "${YELLOW}Creating test file upload...${NC}"
FILE_UPLOAD_RESPONSE=$(curl -s -X POST \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: multipart/form-data" \
    -F "file=@.dockerwrapper/test-vm-injection-api.sh" \
    http://localhost:3055/api/v1/file_upload/)

FILE_UPLOAD_ID=$(echo $FILE_UPLOAD_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -z "$FILE_UPLOAD_ID" ]; then
    echo -e "${RED}Failed to create test file upload${NC}"
    echo "Response: $FILE_UPLOAD_RESPONSE"
    exit 1
fi

echo -e "${GREEN}Successfully created test file upload with ID: $FILE_UPLOAD_ID${NC}"

# Create a test VM injection
echo -e "${YELLOW}Creating test VM injection...${NC}"
VM_INJECTION_DATA='{
    "file_upload_id": "'$FILE_UPLOAD_ID'",
    "template_id": "ubuntu-small",
    "target_path": "/app/test-file.sh",
    "permissions": "0755"
}'

echo "Request data: $VM_INJECTION_DATA"

VM_INJECTION_RESPONSE=$(curl -s -X POST \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d "$VM_INJECTION_DATA" \
    http://localhost:3055/api/v1/virtual-machines/injections/)

echo "Response: $VM_INJECTION_RESPONSE"

VM_INJECTION_ID=$(echo $VM_INJECTION_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)

if [ -z "$VM_INJECTION_ID" ]; then
    echo -e "${RED}Failed to create test VM injection${NC}"
    echo "Response: $VM_INJECTION_RESPONSE"
    exit 1
fi

echo -e "${GREEN}Successfully created test VM injection with ID: $VM_INJECTION_ID${NC}"

# Get the VM injection status
echo -e "${YELLOW}Getting VM injection status...${NC}"
VM_INJECTION_STATUS_RESPONSE=$(curl -s \
    -H "Authorization: Bearer $TOKEN" \
    http://localhost:3055/api/v1/virtual-machines/injections/$VM_INJECTION_ID/status)

echo "Status response: $VM_INJECTION_STATUS_RESPONSE"

VM_INJECTION_STATUS=$(echo $VM_INJECTION_STATUS_RESPONSE | grep -o '"status":"[^"]*' | cut -d'"' -f4)

echo -e "${GREEN}VM injection status: $VM_INJECTION_STATUS${NC}"

# Get debug information
echo -e "${YELLOW}Getting debug information...${NC}"
DEBUG_RESPONSE=$(curl -s \
    -H "Authorization: Bearer $TOKEN" \
    http://localhost:3055/api/v1/virtual-machines/injections/debug)

echo "Debug response: $DEBUG_RESPONSE"

echo -e "${GREEN}Test completed successfully!${NC}"
