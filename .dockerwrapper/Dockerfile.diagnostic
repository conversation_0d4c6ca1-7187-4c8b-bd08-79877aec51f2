FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    openssh-client \
    procps \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy diagnostic scripts
COPY check_test_env.py /app/
COPY diagnose_test.py /app/
COPY run_tests.py /app/
COPY requirements-test.txt /app/

# Install Python dependencies
RUN pip install -r requirements-test.txt

# Set default command
CMD ["python", "check_test_env.py"] 