// TurdParty Working Status Indicators
(function() {
    'use strict';
    
    console.log('🚀 TurdParty Status Indicators Loading...');
    
    // Create and inject service status section
    function createServiceStatusSection() {
        console.log('Creating service status section...');
        
        // Find the container
        const container = document.querySelector('.container#app') || document.querySelector('.container');
        if (!container) {
            console.error('Could not find container to inject status section');
            return;
        }
        
        // Check if already exists
        if (document.getElementById('turdparty-service-status')) {
            console.log('Service status section already exists');
            return;
        }
        
        // Create the service status section
        const statusSection = document.createElement('div');
        statusSection.id = 'turdparty-service-status';
        statusSection.className = 'section-components';
        statusSection.innerHTML = `
            <div class="components-header">
                <h2 class="components-title">
                    <span class="components-icon">🔧</span>
                    Service Status
                </h2>
                <p class="components-subtitle">Real-time status of all TurdParty services</p>
            </div>
            <div id="service-components" class="service-components">
                <div class="loading-services">
                    <div class="loading-spinner"></div>
                    <p>Loading services...</p>
                </div>
            </div>
        `;
        
        // Insert after the timeline section or at the end
        const timelineSection = document.querySelector('.section-timeline');
        if (timelineSection) {
            timelineSection.parentNode.insertBefore(statusSection, timelineSection.nextSibling);
        } else {
            container.appendChild(statusSection);
        }
        
        console.log('✅ Service status section created');
        
        // Load and display services
        setTimeout(() => {
            loadAndDisplayServices();
        }, 500);
    }
    
    // Load services from API and display them
    function loadAndDisplayServices() {
        console.log('Loading services from Cachet API...');
        
        fetch('/api/v1/components')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                const components = data.data || [];
                console.log(`✅ Loaded ${components.length} services from API`);
                
                if (components.length === 0) {
                    displayNoServices();
                    return;
                }
                
                // Group components by group_id
                const groups = {};
                components.forEach(component => {
                    if (!groups[component.group_id]) {
                        groups[component.group_id] = [];
                    }
                    groups[component.group_id].push(component);
                });
                
                displayServiceGroups(groups);
                
            })
            .catch(error => {
                console.error('❌ Failed to load services:', error);
                displayError(error.message);
            });
    }
    
    // Display service groups with status indicators
    function displayServiceGroups(groups) {
        const container = document.getElementById('service-components');
        if (!container) {
            console.error('Service components container not found');
            return;
        }
        
        container.innerHTML = '';
        
        // Sort groups by ID
        const sortedGroupIds = Object.keys(groups).sort((a, b) => parseInt(a) - parseInt(b));
        
        sortedGroupIds.forEach(groupId => {
            const groupComponents = groups[groupId];
            const groupDiv = document.createElement('div');
            groupDiv.className = 'service-group';
            
            const groupName = getGroupName(parseInt(groupId));
            
            groupDiv.innerHTML = `
                <div class="service-group-header">
                    <h3 class="service-group-title">${groupName}</h3>
                    <div class="service-group-status">
                        ${groupComponents.length} services
                    </div>
                </div>
                <div class="service-group-components">
                    ${groupComponents.map(component => createServiceComponent(component)).join('')}
                </div>
            `;
            
            container.appendChild(groupDiv);
        });
        
        console.log('✅ Service groups displayed with status indicators');
    }
    
    // Create individual service component HTML
    function createServiceComponent(component) {
        const statusClass = `status-${component.status}`;
        const statusText = getStatusText(component.status);
        const statusIcon = getStatusIcon(component.status);
        
        return `
            <div class="service-component ${statusClass}" data-status="${component.status}" data-component-id="${component.id}">
                <div class="service-status-icon">${statusIcon}</div>
                <div class="service-info">
                    <div class="service-name">${component.name}</div>
                    <div class="service-description">${component.description}</div>
                    <div class="service-status-badge">
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Get group name by ID
    function getGroupName(groupId) {
        const groupNames = {
            1: '🌐 Frontend Services',
            2: '🚀 API Services', 
            3: '🗄️ Storage Services'
        };
        return groupNames[groupId] || `Service Group ${groupId}`;
    }
    
    // Get status text
    function getStatusText(status) {
        const statusTexts = {
            1: 'Operational',
            2: 'Performance Issues',
            3: 'Partial Outage', 
            4: 'Major Outage'
        };
        return statusTexts[status] || 'Unknown';
    }
    
    // Get status icon
    function getStatusIcon(status) {
        const statusIcons = {
            1: '🟢',  // Green
            2: '🟡',  // Amber
            3: '🟠',  // Orange
            4: '🔴'   // Red
        };
        return statusIcons[status] || '⚪';
    }
    
    // Display no services message
    function displayNoServices() {
        const container = document.getElementById('service-components');
        if (container) {
            container.innerHTML = `
                <div class="no-services">
                    <p>No services configured</p>
                </div>
            `;
        }
    }
    
    // Display error message
    function displayError(message) {
        const container = document.getElementById('service-components');
        if (container) {
            container.innerHTML = `
                <div class="error-services">
                    <p>❌ Error loading services: ${message}</p>
                </div>
            `;
        }
    }
    
    // Test function
    function testStatusIndicators() {
        console.log('🧪 Testing status indicators...');
        
        const components = document.querySelectorAll('.service-component[data-status]');
        console.log(`Found ${components.length} service components`);
        
        let passedTests = 0;
        let totalTests = 0;
        
        components.forEach((component, index) => {
            const status = component.getAttribute('data-status');
            const statusBadge = component.querySelector('.status-badge');
            const statusIcon = component.querySelector('.service-status-icon');
            
            totalTests += 3;
            
            // Test 1: Component has status attribute
            if (status) {
                console.log(`✅ Component ${index + 1}: Has status attribute (${status})`);
                passedTests++;
            } else {
                console.error(`❌ Component ${index + 1}: Missing status attribute`);
            }
            
            // Test 2: Status badge exists
            if (statusBadge) {
                console.log(`✅ Component ${index + 1}: Has status badge`);
                passedTests++;
            } else {
                console.error(`❌ Component ${index + 1}: Missing status badge`);
            }
            
            // Test 3: Status icon exists
            if (statusIcon) {
                console.log(`✅ Component ${index + 1}: Has status icon`);
                passedTests++;
            } else {
                console.error(`❌ Component ${index + 1}: Missing status icon`);
            }
        });
        
        const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
        console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed (${successRate}%)`);
        
        if (successRate >= 90) {
            console.log('🎉 Status indicators are working correctly!');
        } else if (successRate >= 70) {
            console.warn('⚠️ Status indicators partially working');
        } else {
            console.error('❌ Status indicators need fixes');
        }
        
        return { passedTests, totalTests, successRate };
    }
    
    // Initialize
    function init() {
        console.log('🚀 Initializing TurdParty Status Indicators...');
        
        // Wait for DOM to be ready
        setTimeout(() => {
            createServiceStatusSection();
            
            // Run tests after components are loaded
            setTimeout(() => {
                testStatusIndicators();
            }, 2000);
        }, 1000);
    }
    
    // Expose functions globally for testing
    if (typeof window !== 'undefined') {
        window.TurdPartyStatusIndicators = {
            test: testStatusIndicators,
            init: init,
            createServiceStatusSection: createServiceStatusSection
        };
    }
    
    // Auto-initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();
