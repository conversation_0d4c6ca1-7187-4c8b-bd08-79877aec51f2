/* TurdParty Working Status Indicators CSS */

/* Service Status Section */
.section-components {
    margin: 40px 0 !important;
    padding: 0 20px !important;
    background-color: #2d3748 !important;
    border-radius: 12px !important;
    border: 1px solid #4a5568 !important;
}

.components-header {
    text-align: center !important;
    padding: 30px 20px 20px !important;
    border-bottom: 1px solid #4a5568 !important;
    margin-bottom: 30px !important;
}

.components-title {
    color: #e2e8f0 !important;
    font-size: 2rem !important;
    font-weight: 600 !important;
    margin: 0 0 10px 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 15px !important;
}

.components-icon {
    font-size: 2.5rem !important;
}

.components-subtitle {
    color: #a0aec0 !important;
    font-size: 1.1rem !important;
    margin: 0 !important;
}

/* Service Components Container */
.service-components {
    padding: 0 20px 30px !important;
}

/* Service Groups */
.service-group {
    margin-bottom: 30px !important;
    background-color: #1a202c !important;
    border-radius: 8px !important;
    border: 1px solid #4a5568 !important;
    overflow: hidden !important;
}

.service-group-header {
    background: linear-gradient(90deg, #4a5568 0%, #2d3748 100%) !important;
    padding: 15px 20px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    border-bottom: 1px solid #4a5568 !important;
}

.service-group-title {
    color: #e2e8f0 !important;
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    margin: 0 !important;
}

.service-group-status {
    color: #a0aec0 !important;
    font-size: 0.9rem !important;
}

.service-group-components {
    padding: 0 !important;
}

/* Individual Service Components */
.service-component {
    display: flex !important;
    align-items: center !important;
    padding: 15px 20px !important;
    border-bottom: 1px solid #4a5568 !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
    background-color: #1a202c !important;
}

.service-component:hover {
    background-color: #2d3748 !important;
    transform: translateX(5px) !important;
}

.service-component:last-child {
    border-bottom: none !important;
}

/* Status Icons */
.service-status-icon {
    font-size: 1.5rem !important;
    margin-right: 15px !important;
    min-width: 30px !important;
    text-align: center !important;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3)) !important;
}

/* Status-specific icon effects */
.service-component.status-1 .service-status-icon {
    filter: drop-shadow(0 0 8px rgba(82, 196, 26, 0.6)) !important;
}

.service-component.status-2 .service-status-icon {
    filter: drop-shadow(0 0 8px rgba(250, 173, 20, 0.6)) !important;
}

.service-component.status-3 .service-status-icon {
    filter: drop-shadow(0 0 8px rgba(255, 122, 69, 0.6)) !important;
}

.service-component.status-4 .service-status-icon {
    filter: drop-shadow(0 0 8px rgba(255, 77, 79, 0.6)) !important;
    animation: pulse-critical 2s infinite !important;
}

@keyframes pulse-critical {
    0%, 100% { 
        filter: drop-shadow(0 0 8px rgba(255, 77, 79, 0.6)) !important;
        transform: scale(1) !important;
    }
    50% { 
        filter: drop-shadow(0 0 16px rgba(255, 77, 79, 0.9)) !important;
        transform: scale(1.1) !important;
    }
}

/* Service Info */
.service-info {
    flex: 1 !important;
}

.service-name {
    color: #e2e8f0 !important;
    font-size: 1.1rem !important;
    font-weight: 500 !important;
    margin-bottom: 5px !important;
}

.service-description {
    color: #a0aec0 !important;
    font-size: 0.9rem !important;
    margin-bottom: 8px !important;
    line-height: 1.4 !important;
}

.service-status-badge {
    margin-top: 5px !important;
}

/* Status Badges */
.status-badge {
    display: inline-block !important;
    padding: 3px 10px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    border: 1px solid transparent !important;
}

.status-badge.status-1 {
    background-color: rgba(82, 196, 26, 0.15) !important;
    color: #52c41a !important;
    border-color: rgba(82, 196, 26, 0.3) !important;
}

.status-badge.status-2 {
    background-color: rgba(250, 173, 20, 0.15) !important;
    color: #faad14 !important;
    border-color: rgba(250, 173, 20, 0.3) !important;
}

.status-badge.status-3 {
    background-color: rgba(255, 122, 69, 0.15) !important;
    color: #ff7a45 !important;
    border-color: rgba(255, 122, 69, 0.3) !important;
}

.status-badge.status-4 {
    background-color: rgba(255, 77, 79, 0.15) !important;
    color: #ff4d4f !important;
    border-color: rgba(255, 77, 79, 0.3) !important;
}

/* Loading States */
.loading-services {
    text-align: center !important;
    padding: 40px 20px !important;
    color: #a0aec0 !important;
}

.loading-spinner {
    width: 40px !important;
    height: 40px !important;
    border: 4px solid #4a5568 !important;
    border-top: 4px solid #4a90e2 !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
    margin: 0 auto 15px !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error and No Services States */
.no-services,
.error-services {
    text-align: center !important;
    padding: 40px 20px !important;
    color: #a0aec0 !important;
    font-style: italic !important;
}

.error-services {
    color: #ff4d4f !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .components-title {
        font-size: 1.5rem !important;
        flex-direction: column !important;
        gap: 10px !important;
    }
    
    .components-icon {
        font-size: 2rem !important;
    }
    
    .service-component {
        flex-direction: column !important;
        align-items: flex-start !important;
        text-align: left !important;
    }
    
    .service-status-icon {
        margin-right: 0 !important;
        margin-bottom: 10px !important;
        align-self: center !important;
    }
    
    .service-group-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 5px !important;
    }
}

/* Dark theme integration */
body.status-page .section-components {
    background-color: #2d3748 !important;
}

body.status-page .service-component {
    background-color: #1a202c !important;
}

body.status-page .service-component:hover {
    background-color: #2d3748 !important;
}
