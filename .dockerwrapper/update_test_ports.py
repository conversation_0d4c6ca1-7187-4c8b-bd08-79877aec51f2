#!/usr/bin/env python3

"""
Script to update test scripts to use port mappings from port_mappings.json.
"""

import json
import os
import re
import sys

# Path to the port mappings file
PORT_MAPPINGS_FILE = os.path.join(os.path.dirname(__file__), 'port_mappings.json')

# Paths to the test scripts
TEST_SCRIPTS = [
    os.path.join(os.path.dirname(os.path.dirname(__file__)), 'test-vm-injection-api.py'),
    os.path.join(os.path.dirname(os.path.dirname(__file__)), 'test-api-reachable.py')
]

def load_port_mappings():
    """
    Load port mappings from the JSON file.
    
    Returns:
        dict: The port mappings.
    """
    with open(PORT_MAPPINGS_FILE, 'r') as f:
        return json.load(f)

def update_test_script(file_path, port_mappings):
    """
    Update a test script to use port mappings from the JSON file.
    
    Args:
        file_path: The path to the test script.
        port_mappings: The port mappings.
    """
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return
    
    print(f"Updating {file_path}...")
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Define a regex pattern to match API_BASE_URL
    pattern = r'API_BASE_URL\s*=\s*"http://localhost:(\d+)"'
    
    # Find all matches
    matches = re.findall(pattern, content)
    
    if not matches:
        print(f"  No API_BASE_URL found in {file_path}")
        return
    
    # Get the current port
    current_port = matches[0]
    
    # Get the new port
    new_port = port_mappings.get('api')
    
    if not new_port:
        print(f"  No port mapping found for 'api'")
        return
    
    # Replace the port
    print(f"  Replacing port {current_port} with {new_port}")
    new_content = re.sub(pattern, f'API_BASE_URL = "http://localhost:{new_port}"', content)
    
    # Write the updated content back to the file
    with open(file_path, 'w') as f:
        f.write(new_content)
    
    print(f"Updated {file_path}")

def main():
    """Main function."""
    # Load port mappings
    port_mappings = load_port_mappings()
    print(f"Loaded port mappings: {port_mappings}")
    
    # Update test scripts
    for file_path in TEST_SCRIPTS:
        update_test_script(file_path, port_mappings)
    
    print("Done!")

if __name__ == "__main__":
    main()
