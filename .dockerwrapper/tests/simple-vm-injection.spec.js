const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

test('should create a VM injection', async ({ page }) => {
  console.log('Testing VM injection functionality...');
  
  // Navigate to the main page
  await page.goto('http://localhost:3100/');
  
  // Wait for the page to load
  await page.waitForSelector('.main-page', { timeout: 15000 })
    .catch(error => {
      console.error('Failed to find .main-page:', error);
    });
  
  // Take a screenshot of the initial page
  await page.screenshot({ path: 'test_screenshots/vm-injection-initial.png' });
  
  // Create a temporary test file
  const testFilePath = path.join(__dirname, 'test-file.txt');
  fs.writeFileSync(testFilePath, 'This is a test file for VM injection');
  
  // Set file input
  const fileInput = await page.locator('input[type="file"]');
  await fileInput.setInputFiles(testFilePath);
  
  // Wait for upload to complete
  await page.waitForSelector('.ant-upload-list-item-done', { timeout: 30000 })
    .catch(error => {
      console.error('Failed to wait for upload completion:', error);
      // Continue anyway to see what happens
    });
  
  // Take a screenshot after upload
  await page.screenshot({ path: 'test_screenshots/vm-injection-upload.png' });
  
  // Check for success message
  const uploadSuccessVisible = await page.locator('.ant-alert-success').isVisible()
    .catch(() => false);
  
  console.log('Upload success message visibility:', uploadSuccessVisible);
  
  if (uploadSuccessVisible) {
    // Select a template
    await page.locator('.template-card').first().click()
      .catch(error => {
        console.error('Failed to click template card:', error);
      });
    
    // Take a screenshot after template selection
    await page.screenshot({ path: 'test_screenshots/vm-injection-template.png' });
    
    // Click continue
    await page.locator('button:has-text("Continue")').click()
      .catch(error => {
        console.error('Failed to click Continue button:', error);
      });
    
    // Take a screenshot of target configuration
    await page.screenshot({ path: 'test_screenshots/vm-injection-target.png' });
    
    // Click create injection
    await page.locator('button:has-text("Create Injection")').click()
      .catch(error => {
        console.error('Failed to click Create Injection button:', error);
      });
    
    // Wait for completion or error
    await Promise.race([
      page.waitForSelector('.ant-alert-success', { timeout: 30000 }),
      page.waitForSelector('.ant-alert-error', { timeout: 30000 })
    ]).catch(error => {
      console.error('Failed to wait for success or error message:', error);
    });
    
    // Take a screenshot of the result
    await page.screenshot({ path: 'test_screenshots/vm-injection-result.png' });
    
    // Check for success or error message
    const injectionSuccessVisible = await page.locator('.ant-alert-success').isVisible()
      .catch(() => false);
    
    const errorVisible = await page.locator('.ant-alert-error').isVisible()
      .catch(() => false);
    
    console.log('Injection success message visibility:', injectionSuccessVisible);
    console.log('Error message visibility:', errorVisible);
  }
  
  // Clean up the test file
  try {
    fs.unlinkSync(testFilePath);
  } catch (error) {
    console.error('Failed to clean up test file:', error);
  }
  
  console.log('VM injection test completed');
});
