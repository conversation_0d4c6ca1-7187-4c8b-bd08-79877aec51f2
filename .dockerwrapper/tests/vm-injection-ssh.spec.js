const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Test variables
let authToken;
let fileUploadId;
let fileSelectionId;
let vmId;
let vmInjectionId;

test.beforeAll(async () => {
    console.log('Setting up VM Injection SSH test...');

    try {
      // Get test auth token
      console.log('Getting test auth token...');
      const tokenResponse = await axios.get('http://localhost:3055/api/test-auth/token/admin');
      authToken = tokenResponse.data.access_token;
      console.log('Successfully obtained test token');

      // Create a test file
      console.log('Creating test file...');
      const testFilePath = path.join(__dirname, 'test-file.txt');
      fs.writeFileSync(testFilePath, 'This is a test file for VM injection SSH test');

      // Upload the test file
      console.log('Uploading test file...');
      const formData = new FormData();
      formData.append('file', new Blob([fs.readFileSync(testFilePath)]), 'test-file.txt');

      const uploadResponse = await axios.post('http://localhost:3055/api/v1/file_upload/', formData, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'multipart/form-data'
        }
      });

      fileUploadId = uploadResponse.data.id;
      console.log(`Successfully uploaded test file with ID: ${fileUploadId}`);

      // Create a file selection
      console.log('Creating file selection...');
      const fileSelectionResponse = await axios.post('http://localhost:3055/api/v1/file_selection/', {
        file_upload_id: fileUploadId,
        name: 'Test Selection',
        description: 'Test file selection for VM injection SSH test'
      }, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      fileSelectionId = fileSelectionResponse.data.id;
      console.log(`Successfully created file selection with ID: ${fileSelectionId}`);

      // Get available VMs or create one
      console.log('Getting available VMs...');
      const vmsResponse = await axios.get('http://localhost:3055/api/v1/vagrant_vm/', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      const vms = vmsResponse.data;

      if (vms && vms.length > 0) {
        vmId = vms[0].id;
        console.log(`Using existing VM with ID: ${vmId}`);
      } else {
        console.log('No VMs found, creating a new VM...');

        // Get available templates
        const templatesResponse = await axios.get('http://localhost:3055/api/v1/virtual-machines', {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });

        const templates = templatesResponse.data;

        if (!templates || templates.length === 0) {
          throw new Error('No templates found');
        }

        const templateId = templates[0].id;
        console.log(`Using template ID: ${templateId}`);

        // Create a new VM
        const vmCreateResponse = await axios.post('http://localhost:3055/api/v1/vagrant_vm/', {
          template_id: templateId,
          name: `test-vm-${Date.now()}`
        }, {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        });

        vmId = vmCreateResponse.data.id;
        console.log(`Successfully created VM with ID: ${vmId}`);
      }

      // Clean up the test file
      fs.unlinkSync(testFilePath);
    } catch (error) {
      console.error('Error in test setup:', error);
      throw error;
    }
  });

test('should create a VM injection with SSH mode', async ({ page }) => {
    console.log('Testing VM injection with SSH mode...');

    try {
      // Create a VM injection
      console.log('Creating VM injection...');
      const vmInjectionResponse = await axios.post('http://localhost:3055/api/v1/vm_injection/', {
        vagrant_vm_id: vmId,
        file_selection_id: fileSelectionId,
        description: 'Test VM injection with SSH mode',
        target_path: '/app/test-file.txt',
        permissions: '0755',
        additional_command: 'echo "File injected successfully" > /app/injection-success.txt'
      }, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      vmInjectionId = vmInjectionResponse.data.id;
      console.log(`Successfully created VM injection with ID: ${vmInjectionId}`);

      // Navigate to the VM injection page to see the status
      await page.goto('http://localhost:3100/vm_injection');

      // Take a screenshot of the VM injection page
      await page.screenshot({ path: 'test_screenshots/vm-injection-ssh-page.png' });

      // Poll the VM injection status
      console.log('Polling VM injection status...');
      const maxAttempts = 20;
      let attempts = 0;
      let status = 'pending';

      while (status === 'pending' || status === 'in_progress') {
        attempts++;

        if (attempts > maxAttempts) {
          console.log('Maximum attempts reached, stopping polling');
          break;
        }

        console.log(`Polling attempt ${attempts} of ${maxAttempts}...`);

        const statusResponse = await axios.get(`http://localhost:3055/api/v1/vm_injection/${vmInjectionId}/status`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });

        status = statusResponse.data.status;
        console.log(`VM injection status: ${status}`);

        if (status === 'completed' || status === 'failed') {
          break;
        }

        // Wait before polling again
        await page.waitForTimeout(5000);

        // Refresh the page to see the updated status
        await page.reload();

        // Take a screenshot of the updated status
        await page.screenshot({ path: `test_screenshots/vm-injection-ssh-status-${attempts}.png` });
      }

      // Get VM injection details
      console.log('Getting VM injection details...');
      const detailsResponse = await axios.get(`http://localhost:3055/api/v1/vm_injection/${vmInjectionId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      const details = detailsResponse.data;
      console.log('VM injection details:', details);

      // Verify the status
      expect(status).toMatch(/completed|failed/);

      if (status === 'failed') {
        console.log('VM injection failed with error:', details.error_message);
      } else {
        console.log('VM injection completed successfully');
      }

      // Take a final screenshot
      await page.screenshot({ path: 'test_screenshots/vm-injection-ssh-final.png' });
    } catch (error) {
      console.error('Error in VM injection test:', error);

      // Take a screenshot of the error
      await page.screenshot({ path: 'test_screenshots/vm-injection-ssh-error.png' });

      throw error;
    }
  });
