const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

test('should upload a file successfully', async ({ page }) => {
  console.log('Testing file upload functionality...');
  
  // Navigate to the main page
  await page.goto('http://localhost:3100/');
  
  // Wait for the page to load
  await page.waitForSelector('.main-page', { timeout: 15000 })
    .catch(error => {
      console.error('Failed to find .main-page:', error);
    });
  
  // Take a screenshot of the initial page
  await page.screenshot({ path: 'test_screenshots/file-upload-initial.png' });
  
  // Create a temporary test file
  const testFilePath = path.join(__dirname, 'test-file.txt');
  fs.writeFileSync(testFilePath, 'This is a test file for upload');
  
  // Set file input
  const fileInput = await page.locator('input[type="file"]');
  await fileInput.setInputFiles(testFilePath);
  
  // Wait for upload to complete
  await page.waitForSelector('.ant-upload-list-item-done', { timeout: 30000 })
    .catch(error => {
      console.error('Failed to wait for upload completion:', error);
      // Continue anyway to see what happens
    });
  
  // Take a screenshot after upload
  await page.screenshot({ path: 'test_screenshots/file-upload-complete.png' });
  
  // Check for success message
  const successVisible = await page.locator('.ant-alert-success').isVisible()
    .catch(() => false);
  
  console.log('Success message visibility after upload:', successVisible);
  
  // Clean up the test file
  try {
    fs.unlinkSync(testFilePath);
  } catch (error) {
    console.error('Failed to clean up test file:', error);
  }
  
  console.log('File upload functionality test completed');
});
