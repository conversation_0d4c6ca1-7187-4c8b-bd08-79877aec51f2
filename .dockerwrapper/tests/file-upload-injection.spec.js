const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

/**
 * File Upload and VM Injection Tests
 * Tests the functionality of the file upload and VM injection workflow.
 */
// Test auth state
let authToken;

test.beforeAll(async ({ request }) => {
    console.log('Setting up File Upload and VM Injection tests...');

    // Get test auth token
    try {
      const response = await request.get('http://localhost:3050/api/test-auth/token/admin');
      const data = await response.json();
      authToken = data.access_token;
      console.log('Successfully obtained test token');
    } catch (error) {
      console.error('Failed to get test token:', error);
      // Continue without token - some tests may still work
    }
  });

  test.beforeEach(async ({ page }) => {
    console.log('Preparing for File Upload and VM Injection test...');

    // Set auth token in local storage
    await page.goto('http://localhost:3100/');
    if (authToken) {
      await page.evaluate((token) => {
        localStorage.setItem('authToken', token);
      }, authToken);
    } else {
      console.log('No auth token available, some tests may fail');
    }

    // Navigate to the main page
    console.log('Navigating to main page...');
    await page.goto('http://localhost:3100/');

    // Wait for the page to load
    console.log('Waiting for page to load...');
    await page.waitForSelector('.main-page', { timeout: 15000 })
      .catch(error => {
        console.error('Failed to find .main-page:', error);
      });

    // Take a screenshot of the initial page
    await page.screenshot({ path: 'test_screenshots/file-upload-initial.png' });
  });

  test('should complete the full file upload and VM injection workflow', async ({ page }) => {
    console.log('Testing file upload and VM injection workflow...');

    // Verify page title
    await expect(page.locator('.welcome-header h2'))
      .toHaveText('Welcome to TurdParty')
      .catch(error => {
        console.error('Failed to verify page title:', error);
        throw error;
      });

    // Step 1: File Upload
    console.log('Step 1: Testing file upload...');

    // Create a temporary test file
    const testFilePath = path.join(__dirname, 'test-file.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for upload');

    // Set file input
    const fileInput = await page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    // Wait for upload to complete
    await page.waitForSelector('.ant-upload-list-item-done', { timeout: 30000 })
      .catch(error => {
        console.error('Failed to wait for upload completion:', error);
        // Continue anyway to see what happens
      });

    // Take a screenshot after upload
    await page.screenshot({ path: 'test_screenshots/file-upload-complete.png' });

    // Check for success message
    const successVisible = await page.locator('.ant-alert-success').isVisible()
      .catch(() => false);

    console.log('Success message visibility after upload:', successVisible);

    // If success message is visible, proceed to next step
    if (successVisible) {
      // Step 2: Template Selection
      console.log('Step 2: Testing template selection...');

      // Wait for the template selection step to appear
      await page.waitForSelector('.template-grid', { timeout: 15000 })
        .catch(error => {
          console.error('Failed to find template grid:', error);
          // Continue anyway to see what happens
        });

      // Take a screenshot of template selection
      await page.screenshot({ path: 'test_screenshots/template-selection.png' });

      // Select the first template
      await page.locator('.template-card').first().click()
        .catch(error => {
          console.error('Failed to click template card:', error);
          throw error;
        });

      // Click continue
      await page.locator('button:has-text("Continue")').click()
        .catch(error => {
          console.error('Failed to click Continue button:', error);
          throw error;
        });

      // Step 3: Target Configuration
      console.log('Step 3: Testing target configuration...');

      // Wait for the target configuration step to appear
      await page.waitForSelector('form', { timeout: 15000 })
        .catch(error => {
          console.error('Failed to find target configuration form:', error);
          // Continue anyway to see what happens
        });

      // Take a screenshot of target configuration
      await page.screenshot({ path: 'test_screenshots/target-configuration.png' });

      // Set target path
      await page.locator('input[placeholder="/app"]').fill('/app/test-file.txt')
        .catch(error => {
          console.error('Failed to fill target path:', error);
          // Continue anyway
        });

      // Click create injection
      await page.locator('button:has-text("Create Injection")').click()
        .catch(error => {
          console.error('Failed to click Create Injection button:', error);
          throw error;
        });

      // Wait for completion or error
      await Promise.race([
        page.waitForSelector('.ant-alert-success', { timeout: 30000 }),
        page.waitForSelector('.ant-alert-error', { timeout: 30000 })
      ]).catch(error => {
        console.error('Failed to wait for success or error message:', error);
        // Continue anyway to see what happens
      });

      // Take a screenshot of the result
      await page.screenshot({ path: 'test_screenshots/injection-result.png' });

      // Check for success or error message
      const finalSuccessVisible = await page.locator('.ant-alert-success').isVisible()
        .catch(() => false);

      const errorVisible = await page.locator('.ant-alert-error').isVisible()
        .catch(() => false);

      console.log('Final success message visibility:', finalSuccessVisible);
      console.log('Error message visibility:', errorVisible);

      // If we have a success message, check for the Debug Injections button
      if (finalSuccessVisible) {
        // Click Debug Injections button if available
        const debugButtonVisible = await page.locator('button:has-text("Debug Injections")').isVisible()
          .catch(() => false);

        if (debugButtonVisible) {
          console.log('Clicking Debug Injections button...');
          await page.locator('button:has-text("Debug Injections")').click()
            .catch(error => {
              console.error('Failed to click Debug Injections button:', error);
              // Continue anyway
            });

          // Take a screenshot after clicking debug button
          await page.screenshot({ path: 'test_screenshots/debug-injections.png' });
        }
      }
    }

    // Clean up the test file
    try {
      fs.unlinkSync(testFilePath);
    } catch (error) {
      console.error('Failed to clean up test file:', error);
    }

    console.log('File upload and VM injection workflow test completed');
  });

  test('should handle API errors gracefully', async ({ page }) => {
    console.log('Testing error handling...');

    // Intercept API requests to simulate error for VM injection
    await page.route('**/api/v1/virtual-machines/injections', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            detail: 'Internal server error'
          })
        });
      } else {
        await route.continue();
      }
    });

    // Create a temporary test file
    const testFilePath = path.join(__dirname, 'test-file.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for upload');

    // Set file input
    const fileInput = await page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    // Wait for upload to complete
    await page.waitForSelector('.ant-upload-list-item-done', { timeout: 30000 })
      .catch(error => {
        console.error('Failed to wait for upload completion:', error);
        // Continue anyway to see what happens
      });

    // Take a screenshot after upload
    await page.screenshot({ path: 'test_screenshots/error-handling-upload.png' });

    // Check for success message
    const successVisible = await page.locator('.ant-alert-success').isVisible()
      .catch(() => false);

    console.log('Success message visibility after upload:', successVisible);

    // If success message is visible, proceed to next step
    if (successVisible) {
      // Step 2: Template Selection
      console.log('Step 2: Testing template selection...');

      // Wait for the template selection step to appear
      await page.waitForSelector('.template-grid', { timeout: 15000 })
        .catch(error => {
          console.error('Failed to find template grid:', error);
          // Continue anyway to see what happens
        });

      // Select the first template
      await page.locator('.template-card').first().click()
        .catch(error => {
          console.error('Failed to click template card:', error);
          throw error;
        });

      // Click continue
      await page.locator('button:has-text("Continue")').click()
        .catch(error => {
          console.error('Failed to click Continue button:', error);
          throw error;
        });

      // Step 3: Target Configuration
      console.log('Step 3: Testing target configuration...');

      // Wait for the target configuration step to appear
      await page.waitForSelector('form', { timeout: 15000 })
        .catch(error => {
          console.error('Failed to find target configuration form:', error);
          // Continue anyway to see what happens
        });

      // Click create injection
      await page.locator('button:has-text("Create Injection")').click()
        .catch(error => {
          console.error('Failed to click Create Injection button:', error);
          throw error;
        });

      // Wait for error message
      await page.waitForSelector('.ant-alert-error', { timeout: 30000 })
        .catch(error => {
          console.error('Failed to wait for error message:', error);
          // Continue anyway to see what happens
        });

      // Take a screenshot of the error
      await page.screenshot({ path: 'test_screenshots/injection-error.png' });

      // Verify error message is visible
      const errorVisible = await page.locator('.ant-alert-error').isVisible()
        .catch(() => false);

      console.log('Error message visibility:', errorVisible);
      expect(errorVisible).toBeTruthy();
    }

    // Clean up the test file
    try {
      fs.unlinkSync(testFilePath);
    } catch (error) {
      console.error('Failed to clean up test file:', error);
    }

    console.log('Error handling test completed');
  });
