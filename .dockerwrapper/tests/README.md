# File Upload and VM Injection Tests

This directory contains Playwright tests for the file upload and VM injection functionality.

## Test Files

- `file-upload-component.spec.js`: Tests the FileUpload component with retry logic
- `file-upload-injection.spec.js`: Tests the full workflow of uploading a file and creating a VM injection
- `vm-management.spec.js`: Tests the VM management functionality

## Running the Tests

You can run the tests using the provided script:

```bash
./.dockerwrapper/run-file-upload-test.sh
```

This script will:
1. Create a Docker network if it doesn't exist
2. Start a Playwright container if it's not already running
3. Install the necessary dependencies
4. Run the file upload component test
5. Run the file upload and VM injection workflow test
6. Show the test results
7. Optionally stop the Playwright container

## Test Screenshots

Test screenshots are saved in the `test_screenshots` directory. These screenshots are taken at various points during the test execution and can be useful for debugging.

## Requirements

- Docker must be installed and running
- The TurdParty application must be running on port 3100
- The API server must be running on port 3050
- The Vagrant gRPC service must be running on the host

## Troubleshooting

If the tests fail, check the following:

1. Make sure the TurdParty application is running and accessible at http://localhost:3100
2. Make sure the API server is running and accessible at http://localhost:3050
3. Make sure the Vagrant gRPC service is running on the host
4. Check the test screenshots in the `test_screenshots` directory for visual clues
5. Check the console output for error messages

## Adding New Tests

To add a new test:

1. Create a new test file in the `tests` directory
2. Follow the pattern of the existing tests
3. Update the `run-file-upload-test.sh` script to include your new test
