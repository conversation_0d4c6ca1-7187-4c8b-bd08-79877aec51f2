const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

/**
 * File Upload Component Tests
 * Tests the functionality of the FileUpload component with retry logic.
 */
// Test auth state
let authToken;

test.beforeAll(async ({ request }) => {
    console.log('Setting up FileUpload Component tests...');

    // Get test auth token
    try {
      const response = await request.get('http://localhost:3050/api/test-auth/token/admin');
      const data = await response.json();
      authToken = data.access_token;
      console.log('Successfully obtained test token');
    } catch (error) {
      console.error('Failed to get test token:', error);
      // Continue without token - some tests may still work
    }
  });

  test.beforeEach(async ({ page }) => {
    console.log('Preparing for FileUpload Component test...');

    // Set auth token in local storage
    await page.goto('http://localhost:3100/');
    if (authToken) {
      await page.evaluate((token) => {
        localStorage.setItem('authToken', token);
      }, authToken);
    } else {
      console.log('No auth token available, some tests may fail');
    }

    // Navigate to the file upload page
    console.log('Navigating to file upload page...');
    await page.goto('http://localhost:3100/file_upload');

    // Wait for the page to load
    console.log('Waiting for page to load...');
    await page.waitForSelector('.file-upload-container', { timeout: 15000 })
      .catch(error => {
        console.error('Failed to find .file-upload-container:', error);
      });

    // Take a screenshot of the initial page
    await page.screenshot({ path: 'test_screenshots/file-upload-component-initial.png' });
  });

  test('should upload a file successfully', async ({ page }) => {
    console.log('Testing file upload functionality...');

    // Create a temporary test file
    const testFilePath = path.join(__dirname, 'test-file.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for upload');

    // Set file input
    const fileInput = await page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    // Wait for upload to complete
    await page.waitForSelector('.ant-upload-list-item-done', { timeout: 30000 })
      .catch(error => {
        console.error('Failed to wait for upload completion:', error);
        // Continue anyway to see what happens
      });

    // Take a screenshot after upload
    await page.screenshot({ path: 'test_screenshots/file-upload-component-complete.png' });

    // Check for success message
    const successVisible = await page.locator('.ant-alert-success, .ant-message-success').isVisible()
      .catch(() => false);

    console.log('Success message visibility after upload:', successVisible);
    expect(successVisible).toBeTruthy();

    // Clean up the test file
    try {
      fs.unlinkSync(testFilePath);
    } catch (error) {
      console.error('Failed to clean up test file:', error);
    }

    console.log('File upload functionality test completed');
  });

  test('should handle network errors with retry logic', async ({ page }) => {
    console.log('Testing file upload retry logic...');

    // Intercept the first upload request and fail it
    let requestCount = 0;
    await page.route('**/api/v1/file_upload', async (route) => {
      if (route.request().method() === 'POST') {
        requestCount++;

        if (requestCount === 1) {
          // Fail the first request with a network error
          console.log('Simulating network error for first request');
          await route.abort('failed');
        } else {
          // Let subsequent requests succeed
          console.log(`Allowing request #${requestCount} to proceed`);
          await route.continue();
        }
      } else {
        await route.continue();
      }
    });

    // Create a temporary test file
    const testFilePath = path.join(__dirname, 'test-file.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for retry logic');

    // Set file input
    const fileInput = await page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    // Wait for upload to complete (with longer timeout for retry)
    await page.waitForSelector('.ant-upload-list-item-done', { timeout: 60000 })
      .catch(error => {
        console.error('Failed to wait for upload completion:', error);
        // Continue anyway to see what happens
      });

    // Take a screenshot after upload
    await page.screenshot({ path: 'test_screenshots/file-upload-component-retry.png' });

    // Check for success message
    const successVisible = await page.locator('.ant-alert-success, .ant-message-success').isVisible()
      .catch(() => false);

    console.log('Success message visibility after retry:', successVisible);
    console.log(`Total requests made: ${requestCount}`);

    // Verify that more than one request was made (indicating retry)
    expect(requestCount).toBeGreaterThan(1);
    expect(successVisible).toBeTruthy();

    // Clean up the test file
    try {
      fs.unlinkSync(testFilePath);
    } catch (error) {
      console.error('Failed to clean up test file:', error);
    }

    console.log('File upload retry logic test completed');
  });

  test('should handle server errors with retry logic', async ({ page }) => {
    console.log('Testing file upload server error handling...');

    // Intercept the first upload request and return a server error
    let requestCount = 0;
    await page.route('**/api/v1/file_upload', async (route) => {
      if (route.request().method() === 'POST') {
        requestCount++;

        if (requestCount === 1) {
          // Return a 500 error for the first request
          console.log('Simulating 500 server error for first request');
          await route.fulfill({
            status: 500,
            contentType: 'application/json',
            body: JSON.stringify({
              detail: 'Internal server error'
            })
          });
        } else {
          // Let subsequent requests succeed
          console.log(`Allowing request #${requestCount} to proceed`);
          await route.continue();
        }
      } else {
        await route.continue();
      }
    });

    // Create a temporary test file
    const testFilePath = path.join(__dirname, 'test-file.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for server error handling');

    // Set file input
    const fileInput = await page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    // Wait for upload to complete (with longer timeout for retry)
    await page.waitForSelector('.ant-upload-list-item-done', { timeout: 60000 })
      .catch(error => {
        console.error('Failed to wait for upload completion:', error);
        // Continue anyway to see what happens
      });

    // Take a screenshot after upload
    await page.screenshot({ path: 'test_screenshots/file-upload-component-server-error.png' });

    // Check for success message
    const successVisible = await page.locator('.ant-alert-success, .ant-message-success').isVisible()
      .catch(() => false);

    console.log('Success message visibility after server error retry:', successVisible);
    console.log(`Total requests made: ${requestCount}`);

    // Verify that more than one request was made (indicating retry)
    expect(requestCount).toBeGreaterThan(1);
    expect(successVisible).toBeTruthy();

    // Clean up the test file
    try {
      fs.unlinkSync(testFilePath);
    } catch (error) {
      console.error('Failed to clean up test file:', error);
    }

    console.log('File upload server error handling test completed');
  });
