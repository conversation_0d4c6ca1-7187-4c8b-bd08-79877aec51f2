
#!/bin/bash
# Container Network Fixer Script
# Automatically diagnoses and fixes container network issues

APP_CONTAINER="fastapi-app-container"
DB_CONTAINER="fastapi-app-db"
NETWORK_NAME="app-network"
COMPOSE_FILE="$(dirname "$0")/docker-compose.yml"

echo "=== Container Network Fixer ==="
echo "Checking container status..."

# Check if containers are running
app_running=$(docker ps --format '{{.Names}}' | grep -c "^${APP_CONTAINER}$")
db_running=$(docker ps --format '{{.Names}}' | grep -c "^${DB_CONTAINER}$")

echo "App container running: ${app_running}"
echo "DB container running: ${db_running}"

# Check if network exists
network_exists=$(docker network ls --format '{{.Name}}' | grep -c "^${NETWORK_NAME}$")
echo "Network ${NETWORK_NAME} exists: ${network_exists}"

# If containers aren't running, start them
if [ ${app_running} -eq 0 ] || [ ${db_running} -eq 0 ]; then
    echo "Starting containers with docker-compose..."
    cd "$(dirname "$0")" || exit 1
    docker compose down
    docker compose up -d

    # Wait for containers to start
    echo "Waiting for containers to start..."
    sleep 5

    # Check again if containers are running
    app_running=$(docker ps --format '{{.Names}}' | grep -c "^${APP_CONTAINER}$")
    db_running=$(docker ps --format '{{.Names}}' | grep -c "^${DB_CONTAINER}$")

    echo "App container running: ${app_running}"
    echo "DB container running: ${db_running}"

    if [ ${app_running} -eq 0 ] || [ ${db_running} -eq 0 ]; then
        echo "ERROR: Containers failed to start"
        exit 1
    fi
fi

# Check if containers are on the same network
app_networks=$(docker inspect -f '{{range $key, $val := .NetworkSettings.Networks}}{{$key}} {{end}}' ${APP_CONTAINER})
db_networks=$(docker inspect -f '{{range $key, $val := .NetworkSettings.Networks}}{{$key}} {{end}}' ${DB_CONTAINER})

echo "App container networks: ${app_networks}"
echo "DB container networks: ${db_networks}"

# Connect containers to network if needed
if [[ ! "${app_networks}" =~ "${NETWORK_NAME}" ]]; then
    echo "Connecting app container to network ${NETWORK_NAME}..."
    docker network connect ${NETWORK_NAME} ${APP_CONTAINER}
fi

if [[ ! "${db_networks}" =~ "${NETWORK_NAME}" ]]; then
    echo "Connecting DB container to network ${NETWORK_NAME}..."
    docker network connect ${NETWORK_NAME} ${DB_CONTAINER}
fi

# Test connectivity
echo "Testing connectivity from app to db..."
docker exec ${APP_CONTAINER} ping -c 1 ${DB_CONTAINER}
ping_status=$?

if [ ${ping_status} -eq 0 ]; then
    echo "SUCCESS: App container can reach DB container"
else
    echo "ERROR: App container cannot reach DB container"

    # Restart containers as a last resort
    echo "Restarting containers..."
    cd "$(dirname "$0")" || exit 1
    docker compose restart

    sleep 5

    # Test connectivity again
    echo "Testing connectivity after restart..."
    docker exec ${APP_CONTAINER} ping -c 1 ${DB_CONTAINER}
    ping_status=$?

    if [ ${ping_status} -eq 0 ]; then
        echo "SUCCESS: App container can now reach DB container after restart"
    else
        echo "ERROR: Network issues persist after restart"
    fi
fi

# Print container IPs for debugging
app_ip=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${APP_CONTAINER})
db_ip=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' ${DB_CONTAINER})

echo "App container IP: ${app_ip}"
echo "DB container IP: ${db_ip}"

# Test DB connection
echo "Testing database connection..."
docker exec ${APP_CONTAINER} python -c "import psycopg2; conn=psycopg2.connect('postgresql://postgres:postgres@${DB_CONTAINER}:5432/app'); print('Database connection successful!')" || echo "Database connection failed"

echo "Network diagnostics complete"
