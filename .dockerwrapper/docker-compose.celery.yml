services:
  redis:
    image: redis:alpine
    container_name: turdparty_redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  # File operations worker
  celery-file-worker:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    container_name: turdparty_celery_file
    command: celery -A tasks worker -Q file_ops -c 4 -n file@%h
    environment:
      - CELERY_WORKER_TYPE=file
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_URL=********************************************/app
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - ../uploads:/app/uploads
      - ../logs:/app/logs
    depends_on:
      - redis
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "inspect", "ping", "-d", "file@%h"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s

  # VM lifecycle worker
  celery-vm-worker:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    container_name: turdparty_celery_vm
    command: celery -A tasks worker -Q vm_lifecycle -c 1 -n vm@%h
    environment:
      - CELERY_WORKER_TYPE=vm
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_URL=********************************************/app
      - VAGRANT_HOST=host.docker.internal
      - VAGRANT_PORT=40000
    volumes:
      - ../logs:/app/logs
    depends_on:
      - redis
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "inspect", "ping", "-d", "vm@%h"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s

  # VM injection worker
  celery-injection-worker:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    container_name: turdparty_celery_injection
    command: celery -A tasks worker -Q vm_injection -c 2 -n injection@%h
    environment:
      - CELERY_WORKER_TYPE=injection
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_URL=********************************************/app
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - VAGRANT_HOST=host.docker.internal
      - VAGRANT_PORT=40000
    volumes:
      - ../logs:/app/logs
    depends_on:
      - redis
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "inspect", "ping", "-d", "injection@%h"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s

  # Monitoring worker
  celery-monitoring-worker:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    container_name: turdparty_celery_monitoring
    command: celery -A tasks worker -Q monitoring -c 4 -n monitor@%h
    environment:
      - CELERY_WORKER_TYPE=monitoring
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_URL=********************************************/app
      - VAGRANT_HOST=host.docker.internal
      - VAGRANT_PORT=40000
    volumes:
      - ../logs:/app/logs
    depends_on:
      - redis
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "inspect", "ping", "-d", "monitor@%h"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s

  # Beat scheduler
  celery-beat:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    container_name: turdparty_celery_beat
    command: celery -A tasks beat
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_URL=********************************************/app
    volumes:
      - ../logs:/app/logs
    depends_on:
      - redis
    networks:
      - turdparty_network
    restart: unless-stopped

  # Flower monitoring
  flower:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.celery
    container_name: turdparty_flower
    command: celery -A tasks flower --port=5555
    ports:
      - "5556:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
    volumes:
      - ../logs:/app/logs
    depends_on:
      - redis
      - celery-file-worker
      - celery-vm-worker
      - celery-injection-worker
      - celery-monitoring-worker
    networks:
      - turdparty_network
    restart: unless-stopped

networks:
  turdparty_network:
    external: true
    name: dockerwrapper_turdparty_network

volumes:
  redis_data:
