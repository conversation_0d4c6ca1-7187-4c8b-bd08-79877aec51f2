services:
  # Simple Cachet service with port mapping
  cachet:
    image: cachethq/docker:latest
    container_name: turdparty_cachet
    ports:
      - "3501:8000"
    environment:
      - DB_DRIVER=pgsql
      - DB_HOST=postgres_cachet
      - DB_PORT=5432
      - DB_DATABASE=cachet
      - DB_USERNAME=postgres
      - DB_PASSWORD=turdparty_secure_postgres_password_2024
      - DB_PREFIX=chq_
      - APP_KEY=base64:O4qJhxlEaUKHJWG8ZVf8jKzlrFqQNzKjhxlEaUKHJWG=
      - APP_DEBUG=false
      - APP_URL=http://localhost:3501
      - APP_TIMEZONE=UTC
      - APP_LOCALE=en
      - CACHE_DRIVER=array
      - SESSION_DRIVER=file
      - QUEUE_DRIVER=sync
      - MAIL_DRIVER=log
    depends_on:
      - postgres_cachet
    networks:
      - turdparty_network
      - net
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cachet.rule=Host(`status.turdparty.localhost`)"
      - "traefik.http.routers.cachet.entrypoints=web"
      - "traefik.http.services.cachet.loadbalancer.server.port=8000"
      - "traefik.docker.network=net"
    restart: unless-stopped

  # PostgreSQL database for Cachet
  postgres_cachet:
    image: postgres:15-alpine
    container_name: turdparty_cachet_postgres
    environment:
      - POSTGRES_DB=cachet
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=turdparty_secure_postgres_password_2024
    volumes:
      - postgres_cachet_data:/var/lib/postgresql/data
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d cachet"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_cachet_data:
    name: turdparty_postgres_cachet_data

networks:
  turdparty_network:
    external: true
    name: dockerwrapper_turdparty_network
  net:
    external: true
