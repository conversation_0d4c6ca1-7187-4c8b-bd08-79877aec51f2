FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    openssh-client \
    procps \
    curl \
    gcc \
    python3-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements files
COPY requirements.txt requirements-test.txt ./

# Install Python dependencies
RUN pip install -r requirements.txt -r requirements-test.txt \
    && pip install pydantic-settings aiosqlite flask fastapi[all] python-multipart python-jose[cryptography] passlib[bcrypt] psycopg2-binary asyncpg aiofiles python-dotenv pydantic email-validator cryptography minio paramiko python-magic aiohttp requests python-dateutil prometheus-client python-json-logger boto3 pyotp qrcode grpcio psutil

# Set up environment
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Default command
CMD ["pytest", "-v"]
