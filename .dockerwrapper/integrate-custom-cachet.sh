#!/bin/bash

# Integrate custom Cachet into the main TurdParty Docker Compose setup
set -e

echo "🔗 Integrating Custom Cachet into TurdParty Setup"
echo "==============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "Please run this script from the .dockerwrapper directory"
    exit 1
fi

# Backup the original docker-compose.yml
if [ ! -f "docker-compose.yml.backup" ]; then
    print_status "Backing up original docker-compose.yml..."
    cp docker-compose.yml docker-compose.yml.backup
    print_success "Backup created: docker-compose.yml.backup"
fi

# Check if Cachet is already integrated
if grep -q "turdparty/cachet-custom" docker-compose.yml; then
    print_success "Custom Cachet is already integrated"
else
    print_status "Integrating custom Cachet into main compose file..."
    
    # Add custom Cachet service to the main docker-compose.yml
    # This will replace any existing cachet service
    
    # First, remove any existing cachet service
    if grep -q "cachet:" docker-compose.yml; then
        print_status "Removing existing Cachet service from compose file..."
        # Create a temporary file without the cachet service
        awk '
        /^  cachet:/ { skip=1; next }
        /^  [a-zA-Z]/ && skip { skip=0 }
        !skip { print }
        ' docker-compose.yml > docker-compose.yml.tmp
        mv docker-compose.yml.tmp docker-compose.yml
    fi
    
    # Add the custom Cachet service
    cat >> docker-compose.yml << 'EOF'

  # Custom Cachet service with dark theme and service icons
  cachet:
    build:
      context: ./cachet-customization
      dockerfile: Dockerfile
    container_name: turdparty_cachet
    ports:
      - "3501:80"
    environment:
      - DB_DRIVER=pgsql
      - DB_HOST=postgres_cachet
      - DB_PORT=5432
      - DB_DATABASE=cachet
      - DB_USERNAME=postgres
      - DB_PASSWORD=turdparty_secure_postgres_password_2024
      - DB_PREFIX=chq_
      - APP_KEY=base64:O4qJhxlEaUKHJWG8ZVf8jKzlrFqQNzKjhxlEaUKHJWG=
      - APP_DEBUG=false
      - APP_URL=http://localhost:3501
      - APP_TIMEZONE=UTC
      - APP_LOCALE=en
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_DRIVER=redis
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DATABASE=1
      - MAIL_DRIVER=log
      - MAIL_HOST=
      - MAIL_PORT=587
      - MAIL_USERNAME=
      - MAIL_PASSWORD=
      - MAIL_ADDRESS=<EMAIL>
      - MAIL_NAME="TurdParty Status"
      - MAIL_ENCRYPTION=tls
    volumes:
      - cachet_data:/var/www/html/storage
    depends_on:
      - postgres_cachet
      - redis
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/v1/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL database for Cachet
  postgres_cachet:
    image: postgres:15-alpine
    container_name: turdparty_postgres_cachet
    environment:
      - POSTGRES_DB=cachet
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=turdparty_secure_postgres_password_2024
    volumes:
      - postgres_cachet_data:/var/lib/postgresql/data
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d cachet"]
      interval: 10s
      timeout: 5s
      retries: 5
EOF

    # Add volumes if they don't exist
    if ! grep -q "cachet_data:" docker-compose.yml; then
        print_status "Adding Cachet volumes..."
        
        # Find the volumes section and add our volumes
        if grep -q "^volumes:" docker-compose.yml; then
            # Add to existing volumes section
            sed -i '/^volumes:/a\  cachet_data:\n    name: turdparty_cachet_data\n  postgres_cachet_data:\n    name: turdparty_postgres_cachet_data' docker-compose.yml
        else
            # Create volumes section
            cat >> docker-compose.yml << 'EOF'

volumes:
  cachet_data:
    name: turdparty_cachet_data
  postgres_cachet_data:
    name: turdparty_postgres_cachet_data
EOF
        fi
    fi
    
    print_success "Custom Cachet integrated into main compose file"
fi

# Update port mappings
print_status "Updating port mappings..."
if [ -f "port_mappings.json" ]; then
    # Update the port mappings to include custom Cachet
    python3 -c "
import json
try:
    with open('port_mappings.json', 'r') as f:
        ports = json.load(f)
    
    ports['cachet'] = 3501
    ports['postgres_cachet'] = 5433  # Internal port mapping
    
    with open('port_mappings.json', 'w') as f:
        json.dump(ports, f, indent=2)
    
    print('✅ Port mappings updated')
except Exception as e:
    print(f'⚠️  Could not update port mappings: {e}')
"
fi

# Create a startup script that includes the custom Cachet
print_status "Creating integrated startup script..."
cat > start-turdparty-with-custom-cachet.sh << 'EOF'
#!/bin/bash

# Start TurdParty with Custom Cachet
set -e

echo "🚀 Starting TurdParty with Custom Cachet"
echo "======================================="

# Build custom Cachet if needed
if ! docker images | grep -q "turdparty/cachet-custom"; then
    echo "🏗️  Building custom Cachet image..."
    ./build-custom-cachet.sh
fi

# Start all services
echo "🌐 Starting all TurdParty services..."
docker compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🔍 Checking service health..."
services=(
    "turdparty_api:3050"
    "turdparty_cachet:3501"
    "turdparty_redis:3400"
    "turdparty_postgres:3200"
    "turdparty_minio:3300"
)

for service_port in "${services[@]}"; do
    service=$(echo $service_port | cut -d: -f1)
    port=$(echo $service_port | cut -d: -f2)
    
    if docker ps --format "{{.Names}}" | grep -q "$service"; then
        echo "✅ $service is running"
    else
        echo "❌ $service is not running"
    fi
done

echo ""
echo "🎉 TurdParty with Custom Cachet is ready!"
echo ""
echo "🌐 Access URLs:"
echo "   • 🎨 Cachet Status:  http://localhost:3501"
echo "   • 🚀 API:           http://localhost:3050"
echo "   • 🌐 Frontend:      http://localhost:3100"
echo "   • 📦 MinIO:         http://localhost:3301"
echo ""
EOF

chmod +x start-turdparty-with-custom-cachet.sh

print_success "Integration complete!"
echo ""
echo "📋 Next Steps:"
echo "   1. Build the custom Cachet: ./build-custom-cachet.sh"
echo "   2. Start with custom Cachet: ./start-turdparty-with-custom-cachet.sh"
echo "   3. Or use regular startup: docker compose up -d"
echo ""
echo "🔧 Files created/modified:"
echo "   • docker-compose.yml (updated with custom Cachet)"
echo "   • docker-compose.yml.backup (original backup)"
echo "   • start-turdparty-with-custom-cachet.sh (integrated startup)"
echo ""
print_success "Custom Cachet integration ready! 🎨"
