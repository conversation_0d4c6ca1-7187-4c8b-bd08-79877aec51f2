[{"Id": "sha256:628b12611503d11dbdc490a20a621ce7b128b2e1aa8528f7a209817a31e5f472", "RepoTags": ["dockerwrapper_api:latest", "dockerwrapper_api:v1.0.0"], "RepoDigests": [], "Parent": "sha256:3604f442921a8943b1b6acd9ccb8267aa0d9e7453721fc469a72f3d31337afd1", "Comment": "", "Created": "2025-04-04T16:17:31.200777705Z", "DockerVersion": "26.1.3", "Author": "", "Config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "ExposedPorts": {"8000/tcp": {}}, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": ["PATH=/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "LANG=C.UTF-8", "GPG_KEY=A035C8C19219BA821ECEA86B64E628F8D684696D", "PYTHON_VERSION=3.10.12", "PYTHON_PIP_VERSION=23.0.1", "PYTHON_SETUPTOOLS_VERSION=65.5.1", "PYTHON_GET_PIP_URL=https://github.com/pypa/get-pip/raw/0d8570dc44796f4369b652222cf176b3db6ac70e/public/get-pip.py", "PYTHON_GET_PIP_SHA256=96461deced5c2a487ddc65207ec5a9cffeca0d34e7af7ea1afc470ff0d746207", "PYTHONUNBUFFERED=1", "PYTHONDONTWRITEBYTECODE=1", "PIP_NO_CACHE_DIR=off", "PIP_DISABLE_PIP_VERSION_CHECK=on", "PIP_DEFAULT_TIMEOUT=100"], "Cmd": ["u<PERSON><PERSON>", "main:app", "--host", "0.0.0.0", "--port", "8000"], "Image": "sha256:3604f442921a8943b1b6acd9ccb8267aa0d9e7453721fc469a72f3d31337afd1", "Volumes": null, "WorkingDir": "/app", "Entrypoint": ["/usr/local/bin/container-init.sh"], "OnBuild": null, "Labels": null}, "Architecture": "amd64", "Os": "linux", "Size": 925518986, "GraphDriver": {"Data": {"LowerDir": "/var/lib/docker/overlay2/15fbad6d2e295dde066eb6fc48d9bac9b7e7544ff978c9b3d9c02d99ea21d722/diff:/var/lib/docker/overlay2/04acd68bbdc6384087ee129d523f80012ef79ab8cc9ad171ad25f7dd63134553/diff:/var/lib/docker/overlay2/f52d5afe57b0d61d3f53b1c99ed06c0a08c1741c42179ed8d32abf721554cb46/diff:/var/lib/docker/overlay2/82ebfc2d342d551b02912ca683b45fb5795f049c5169a9067a9e0b909e5d7488/diff:/var/lib/docker/overlay2/d31ca93f81278a72da6381109a743389a707324d078537f02cf77188d3c6c452/diff:/var/lib/docker/overlay2/124f6403fa0b9333c36189c74ba2fe06d8d1df5c545b32333c2702708d6c0985/diff:/var/lib/docker/overlay2/0dabb59c144aa95413ac69f391306645aade39ed40fdbb7f61517921410e42c9/diff:/var/lib/docker/overlay2/558c208e55703deb049702f5624d1cfedd6939fcc1f5d3cde6041baab47ab592/diff:/var/lib/docker/overlay2/2dd84d0c095335f8364183555406cfd143c47a967e014d21edebb571cc5eafc3/diff:/var/lib/docker/overlay2/af0273495f297222112b214d6c7eb9b0f5ef37be586137455f0d7fa7e88824b9/diff:/var/lib/docker/overlay2/605370174caefce2e09b862d05a21cb7e71d7e2f58b462c7a7436023f83d4303/diff", "MergedDir": "/var/lib/docker/overlay2/7204eabcc3970954ef5973d79d5371708709cf68f1b94105e7ff55ca3fe5ac46/merged", "UpperDir": "/var/lib/docker/overlay2/7204eabcc3970954ef5973d79d5371708709cf68f1b94105e7ff55ca3fe5ac46/diff", "WorkDir": "/var/lib/docker/overlay2/7204eabcc3970954ef5973d79d5371708709cf68f1b94105e7ff55ca3fe5ac46/work"}, "Name": "overlay2"}, "RootFS": {"Type": "layers", "Layers": ["sha256:e2ef8a51359d088511d34c725305c220294a1fcd5fe5e5dbe4d698c7239ce2c9", "sha256:ae2d55769c5efcb6230d27c88eef033128fa1d238bdafe50812402f471152bb7", "sha256:b23fedba7dbd91ff0cf23989e6ec582e5e9636cb15dd6bc0bd5e22a7135ce617", "sha256:df6c1b185b95a58b0f97c2840f10e3c309d284d17738975e3ce12ea04d84ba3b", "sha256:c5321f7f53ff660227c00c692b809d941be4bb642dced9bbfe2fb6d722bb0663", "sha256:f612799cb4bdcfff8a76f7cb3af8774f4f222ae06bfc849223bd35ef7403afc7", "sha256:27f0a6c885ab5604ac6bf7a0167ed8e2b2f744692b179650f04f236a6319f990", "sha256:19f7ebc7d6240c68ec7a1d1dbdf056ecdeed9b094b16ee00241980e4ff990e67", "sha256:6d3282dea002f0013d61526e3139ae88498fcda6aa6abaa0ba4f267533b43698", "sha256:06639872ccdd4128d7a36fc3a8da40bde95ee2a7a68bb7a813c052170365b18f", "sha256:4273f40abfdf254c84908d66617ea41efedec255ac3f06240a67a3845e8f78ab", "sha256:4273f40abfdf254c84908d66617ea41efedec255ac3f06240a67a3845e8f78ab"]}, "Metadata": {"LastTagTime": "2025-04-04T18:46:00.186752924+02:00"}}, {"Id": "sha256:a5b91d3a7b92693c4e593ee817d9b5083c69e875cafe4b3bf8543530665a4a46", "RepoTags": ["dockerwrapper_playwright:latest", "dockerwrapper_playwright:v1.0.0"], "RepoDigests": [], "Parent": "sha256:b88169fb37608890ae838fa6aab8b098da66adae39cf10483f61ad38c68a02a4", "Comment": "", "Created": "2025-04-04T16:24:32.772031282Z", "DockerVersion": "26.1.3", "Author": "", "Config": {"Hostname": "", "Domainname": "", "User": "turduser", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": ["PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "PLAYWRIGHT_BROWSERS_PATH=/ms-playwright", "NODE_ENV=test", "PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0"], "Cmd": ["npx", "playwright", "test"], "Image": "sha256:b88169fb37608890ae838fa6aab8b098da66adae39cf10483f61ad38c68a02a4", "Volumes": null, "WorkingDir": "/app", "Entrypoint": ["/app/docker-entrypoint.sh"], "OnBuild": null, "Labels": {"org.opencontainers.image.ref.name": "ubuntu", "org.opencontainers.image.version": "20.04"}}, "Architecture": "amd64", "Os": "linux", "Size": 3441164879, "GraphDriver": {"Data": {"LowerDir": "/var/lib/docker/overlay2/40876194bbb54028960b63ca773d11e33b567b4dc1b08e793794d1ca37daaf4b/diff:/var/lib/docker/overlay2/a8a88cc32c12274d717fdf2fb0174b9838700aea7725ef7cd6ee54d054635bef/diff:/var/lib/docker/overlay2/eff73345f2f45c0f298edec867228dde29426e4cc0b431d34bd0d6f7ff22e3d0/diff:/var/lib/docker/overlay2/065fd2bdd6cc391a24e5f1f3c0c05f2c5c95e1c7758801ab816950a8496227d4/diff:/var/lib/docker/overlay2/00989408ec4be7bb461c2ed1e298c83bea49999e0a1ec4a1b4046fec8d5e2df8/diff:/var/lib/docker/overlay2/3b5705895adbed860391c2dec4f76e0d7a774dba92d46450d6a04f652ab798b0/diff:/var/lib/docker/overlay2/2a60f3df2d04f2f43347e3a2931b21823550d3144d8249dfe9b141329e76f16c/diff:/var/lib/docker/overlay2/b5f782ec4237c7b659cb019b30d8770d748eb80163c2a0281f4e8ac532f10385/diff:/var/lib/docker/overlay2/dddc191fdeb7de2ab94cb0f841c2ffdaa1d4599c7e24ed78bce958f5d16cc133/diff:/var/lib/docker/overlay2/ba9a9fec58e250799328cdf3018ed933ff811da8238eece04f5161757bbe68f4/diff:/var/lib/docker/overlay2/71bc5ccd3d16afa449911b091538ee03124c8706046859689f42eee3f4fc2315/diff:/var/lib/docker/overlay2/39a8fb690fcc3a3c8571dbf51476b9be3eb809da8214b61dc011c5a870827787/diff:/var/lib/docker/overlay2/5ffac448549c11d540f0c37ba11b3ffad726cec83561614439c05e147b3c00bd/diff:/var/lib/docker/overlay2/809c8d23d64e341efa5be0f4aae795a018d64e3eb9fc1ac78ae4dd27a6c44d88/diff:/var/lib/docker/overlay2/7b5efc1b2605e011b502daca0db3eda227105428088bb35a6e32023cd87869b7/diff:/var/lib/docker/overlay2/5a97dca7359dbb0ba4f14554c2a4d481ae58dfabdc3f23486292a8107495f89d/diff", "MergedDir": "/var/lib/docker/overlay2/f3aac7081766c15ed8215d128a45525ed671e92a8a37b4a7a5fdb07eef544ed6/merged", "UpperDir": "/var/lib/docker/overlay2/f3aac7081766c15ed8215d128a45525ed671e92a8a37b4a7a5fdb07eef544ed6/diff", "WorkDir": "/var/lib/docker/overlay2/f3aac7081766c15ed8215d128a45525ed671e92a8a37b4a7a5fdb07eef544ed6/work"}, "Name": "overlay2"}, "RootFS": {"Type": "layers", "Layers": ["sha256:3a03f09d212915b240e9d216069aba5652ed4765c7e4b098c65e71860d47b8e1", "sha256:c9127f8ec91484a7ab7b0576e1441c49832bea6f47e51c57e815fef03c3b58ec", "sha256:e94f848ece1707bbaede97ef46185032187f14a99641bb6114041ad5170cd581", "sha256:aa95c3b3ac2207699b209d47c57e0739e9a37bfdf09007392b2f3b51151ada97", "sha256:4c5758042e4a4d6d029cd092d2cab941527383996a63218b0e4116c1fe1cbebd", "sha256:5baa5b6571970c74a7d01ceac442a5a2d533cc8b362dc61812d74957f070de56", "sha256:f047bb368880ce85bde513a114f5ac9d1150f92e61845972eff49a9c4a883deb", "sha256:d150158bf08e3e28784af1d9ba186ed7b31a1c29f97bc6e65e93c0e822c9627b", "sha256:c67b7a8eaa6ba4cf324fe70adf8be261583991556e9f8ecdccdc0df0bcb6a4f7", "sha256:eda811f0e60184d54e468cd46a57490f7f1afd9838a8648583feea9fd51ce009", "sha256:1fe55aa16c2880eca33af886f54acb5a49da46c94cbc47642bdf0f0d1a5cab5e", "sha256:e3b5fc63917da20d34cfc5074a1ef8c2c3e232ab9372f8bd6fe043acdd883e01", "sha256:db69ce679121a819b63947811561316bc5a09769e2a2450b8a903bacd9476346", "sha256:089a6e3c78d5cc8c425bbbffbf2196a605c6cf852d80cbabd9610077e08d2be1", "sha256:b911b86084c80472ceaca750acb7f8b1c6d1a8aa2a4ef894d59bacf59d263527", "sha256:5c99b320a5c9dc6e4be34ca093503c8c11e78131131b80555d9f114f580cd0e2", "sha256:12895be221a0188836bf035e5e225d9baf993f6cd263f973fe62d34fe0c76ecd"]}, "Metadata": {"LastTagTime": "2025-04-04T18:46:05.285299362+02:00"}}]