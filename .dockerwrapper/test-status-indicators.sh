#!/bin/bash

# Comprehensive Status Indicators Test Suite
echo "🧪 TurdParty Status Indicators Test Suite"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_test "Running: $test_name"
    
    if eval "$test_command"; then
        print_success "✅ $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_error "❌ $test_name"
        return 1
    fi
}

echo ""
print_status "🔍 Testing Status Indicators Implementation"
echo ""

# Test 1: Check if Cachet is accessible
run_test "Cachet Accessibility" \
    "curl -s -f http://localhost:8083 > /dev/null" \
    "HTTP 200"

# Test 2: Check if status indicators JavaScript is loaded
run_test "Status Indicators JavaScript" \
    "curl -s http://localhost:8083/js/status-indicators.js | grep -q 'Status Indicators Enhancement'" \
    "Contains enhancement code"

# Test 3: Check if status indicators CSS is loaded
run_test "Status Indicators CSS" \
    "curl -s http://localhost:8083/css/dark-theme.css | grep -q 'Status Icons for Components'" \
    "Contains status icon styles"

# Test 4: Check if components API is accessible
run_test "Components API Accessibility" \
    "curl -s http://localhost:8083/api/v1/components | grep -q '\"data\"'" \
    "Returns JSON data"

# Test 5: Check component count
COMPONENT_COUNT=$(curl -s http://localhost:8083/api/v1/components | grep -o '"name"' | wc -l)
run_test "Components Count (Expected: 6)" \
    "[ $COMPONENT_COUNT -eq 6 ]" \
    "6 components"

# Test 6: Check if test page is accessible
run_test "Test Page Accessibility" \
    "curl -s -f http://localhost:8083/test-status-indicators.html > /dev/null" \
    "Test page loads"

# Test 7: Check if status badge CSS exists
run_test "Status Badge CSS" \
    "curl -s http://localhost:8083/css/dark-theme.css | grep -q 'status-badge'" \
    "Contains status badge styles"

# Test 8: Check if component CSS exists
run_test "Component Display CSS" \
    "curl -s http://localhost:8083/css/dark-theme.css | grep -q 'components-section'" \
    "Contains component section styles"

# Test 9: Verify status colors in CSS
run_test "Status Color Definitions" \
    "curl -s http://localhost:8083/css/dark-theme.css | grep -q '#52c41a' && curl -s http://localhost:8083/css/dark-theme.css | grep -q '#faad14' && curl -s http://localhost:8083/css/dark-theme.css | grep -q '#ff7a45' && curl -s http://localhost:8083/css/dark-theme.css | grep -q '#ff4d4f'" \
    "All status colors defined"

# Test 10: Check if JavaScript includes test function
run_test "JavaScript Test Function" \
    "curl -s http://localhost:8083/js/status-indicators.js | grep -q 'testStatusIndicators'" \
    "Test function available"

echo ""
print_status "📊 Test Results Summary"
echo "======================="

SUCCESS_RATE=$(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l)

echo -e "Total Tests: ${CYAN}$TOTAL_TESTS${NC}"
echo -e "Passed Tests: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed Tests: ${RED}$((TOTAL_TESTS - PASSED_TESTS))${NC}"
echo -e "Success Rate: ${CYAN}$SUCCESS_RATE%${NC}"

echo ""

if (( $(echo "$SUCCESS_RATE >= 90" | bc -l) )); then
    print_success "🎉 Excellent! Status indicators are working correctly!"
elif (( $(echo "$SUCCESS_RATE >= 70" | bc -l) )); then
    print_warning "⚠️ Good! Status indicators are mostly working"
else
    print_error "❌ Issues detected! Status indicators need fixes"
fi

echo ""
print_status "🎯 Status Indicator Features"
echo "============================"
echo "   🟢 Green Icons: Operational services (status 1)"
echo "   🟡 Amber Icons: Performance issues (status 2)"
echo "   🟠 Orange Icons: Partial outages (status 3)"
echo "   🔴 Red Icons: Major outages (status 4) - with pulsing animation"
echo ""
echo "   📊 Status Badges: Text indicators with color coding"
echo "   🔄 Dynamic Loading: Components loaded from Cachet API"
echo "   🎨 Visual Effects: Glowing icons and hover animations"
echo "   📱 Responsive Design: Works on all screen sizes"
echo ""

print_status "🌐 Access Points"
echo "================"
echo "   Main Status Page: http://localhost:8083"
echo "   Test Page: http://localhost:8083/test-status-indicators.html"
echo "   Components API: http://localhost:8083/api/v1/components"
echo ""

print_status "🔧 Manual Testing"
echo "=================="
echo "   1. Open http://localhost:8083 in your browser"
echo "   2. Look for the 'Service Status' section below the architecture diagram"
echo "   3. Verify green status icons appear next to each service"
echo "   4. Open browser console and run: testStatusIndicators()"
echo "   5. Check the test page at: http://localhost:8083/test-status-indicators.html"
echo ""

print_status "🚀 Next Steps"
echo "=============="
echo "   • Change component status in Cachet admin to test different colors"
echo "   • Verify status icons update automatically"
echo "   • Test responsive design on mobile devices"
echo "   • Monitor console logs for any JavaScript errors"
echo ""

if (( $(echo "$SUCCESS_RATE >= 90" | bc -l) )); then
    echo -e "${GREEN}✅ Status indicators are ready for production use!${NC}"
else
    echo -e "${YELLOW}⚠️ Review failed tests and fix issues before production use.${NC}"
fi

echo ""
print_success "Test suite completed! 🎯"
