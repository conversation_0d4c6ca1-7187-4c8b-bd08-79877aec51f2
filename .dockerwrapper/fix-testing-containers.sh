#!/bin/bash

# Script to fix and restart all testing containers
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Fixing and Restarting Testing Containers           ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Navigate to the root directory
cd "$(dirname "$0")/.."

# Stop all running containers related to turdparty
echo -e "${YELLOW}Stopping all running containers related to turdparty...${NC}"
docker ps --filter "name=turdparty" -q | xargs -r docker stop

# Clean up any exited containers
echo -e "${YELLOW}Cleaning up any exited containers...${NC}"
docker ps -a --filter "name=turdparty" --filter "status=exited" -q | xargs -r docker rm

# Clean up any dangling volumes (optional - uncomment if needed)
# echo -e "${YELLOW}Cleaning up any dangling volumes...${NC}"
# docker volume ls -qf dangling=true | xargs -r docker volume rm

# Ensure necessary directories exist
echo -e "${YELLOW}Creating necessary directories...${NC}"
mkdir -p .dockerwrapper/{logs,scripts} test_screenshots test-results playwright-report uploads

# Show the available test environments
echo -e "${YELLOW}Available test environments:${NC}"
echo -e "1. Production Testing (docker compose.yml)"
echo -e "2. Development Testing (dev-compose.yml)"
echo -e "3. Playwright Testing (docker compose.playwright.yml)"

# Ask which environment to restart
read -p "Which environment would you like to restart? (1/2/3): " env_choice

case $env_choice in
    1)
        COMPOSE_FILE=".dockerwrapper/docker-compose.yml"
        echo -e "${YELLOW}Restarting production testing environment...${NC}"
        ;;
    2)
        COMPOSE_FILE=".dockerwrapper/dev-compose.yml"
        echo -e "${YELLOW}Restarting development testing environment...${NC}"
        ;;
    3)
        COMPOSE_FILE=".dockerwrapper/docker-compose.playwright.yml"
        echo -e "${YELLOW}Restarting Playwright testing environment...${NC}"
        ;;
    *)
        echo -e "${RED}Invalid choice. Exiting.${NC}"
        exit 1
        ;;
esac

# Rebuild and restart the containers
echo -e "${YELLOW}Rebuilding and restarting containers with ${COMPOSE_FILE}...${NC}"
docker compose -f $COMPOSE_FILE down
docker compose -f $COMPOSE_FILE build
docker compose -f $COMPOSE_FILE up -d

# Wait for containers to start
echo -e "${YELLOW}Waiting for containers to start up...${NC}"
sleep 10

# Check container status
echo -e "${YELLOW}Checking container status...${NC}"
docker compose -f $COMPOSE_FILE ps

# Test basic connectivity
echo -e "${YELLOW}Testing basic connectivity...${NC}"
if [[ $COMPOSE_FILE == *"playwright"* ]]; then
    echo -e "${YELLOW}Running simple playwright test...${NC}"
    bash .dockerwrapper/run-simple-test.sh
elif [[ $COMPOSE_FILE == *"dev"* ]]; then
    echo -e "${YELLOW}Testing API connection...${NC}"
    curl -s http://localhost:3050/health || echo -e "${RED}API not responding${NC}"
    echo -e "${YELLOW}Testing frontend connection...${NC}"
    curl -s -I http://localhost:3100 || echo -e "${RED}Frontend not responding${NC}"
else
    echo -e "${YELLOW}Testing API connection...${NC}"
    curl -s http://localhost:3050/health || echo -e "${RED}API not responding${NC}"
fi

echo -e "${GREEN}Container restart complete!${NC}"
echo -e "${YELLOW}If you experience issues, check the logs with:${NC}"
echo -e "docker compose -f $COMPOSE_FILE logs"

# Make the script executable
chmod +x .dockerwrapper/fix-testing-containers.sh