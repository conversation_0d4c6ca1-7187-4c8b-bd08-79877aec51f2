#!/bin/bash

# Script to run tests in the persistent Playwright container
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================================${NC}"
echo -e "${YELLOW}    Running Tests in Persistent Container              ${NC}"
echo -e "${YELLOW}=======================================================${NC}"

# Check if the container is running
if ! docker ps | grep -q "playwright-test\|turdparty_playwright"; then
  echo -e "${RED}Error: Playwright test container is not running${NC}"
  echo -e "${YELLOW}Start it with: .dockerwrapper/persistent-test-env.sh${NC}"
  exit 1
fi

# Determine which test to run
TEST_PATH=""
if [ "$1" == "vm" ]; then
  echo -e "${YELLOW}Running VM integration test${NC}"
  TEST_PATH="tests/playwright/vagrant-integration.spec.js"
elif [ "$1" == "file" ]; then
  echo -e "${YELLOW}Running file-to-VM integration test${NC}"
  TEST_PATH="tests/playwright/file-to-vm-integration.spec.js"
elif [ "$1" == "all" ]; then
  echo -e "${YELLOW}Running all tests${NC}"
  TEST_PATH="tests/playwright"
elif [ -n "$1" ]; then
  echo -e "${YELLOW}Running specific test: $1${NC}"
  TEST_PATH="$1"
else
  echo -e "${YELLOW}No test specified. Please specify:${NC}"
  echo -e "${YELLOW}  vm    - Run VM integration tests${NC}"
  echo -e "${YELLOW}  file  - Run file-to-VM integration tests${NC}"
  echo -e "${YELLOW}  all   - Run all tests${NC}"
  echo -e "${YELLOW}  path  - Specify custom test path${NC}"
  exit 1
fi

# Additional options
REPORTER=""
if [ "$2" == "html" ]; then
  REPORTER="--reporter=html"
else
  REPORTER="--reporter=list"
fi

# Clear previous results
if [ "$3" == "clear" ]; then
  echo -e "${YELLOW}Clearing previous test results...${NC}"
  rm -rf test-results/* test_screenshots/* playwright-report/*
fi

# Determine which container is running
if docker ps | grep -q "playwright-test"; then
  CONTAINER_NAME="playwright-test"
else
  CONTAINER_NAME="turdparty_playwright"
fi

# Set API URL - use our new service on port 3055
API_IP="api-test"
API_PORT="8000"
if ! docker ps | grep -q "api-test"; then
  echo -e "${YELLOW}Warning: API container not found. Tests requiring API will fail.${NC}"
  echo -e "${YELLOW}Consider starting the API container with: docker compose -f docker-compose.test.yml up -d api${NC}"
fi

# Run the test in the container with the right environment variables
echo -e "${YELLOW}Running Playwright test: ${TEST_PATH} ${REPORTER}${NC}"
docker exec -it \
  -e API_URL="http://${API_IP}:${API_PORT}" \
  -e FRONTEND_URL="http://localhost:3100" \
  -e HEADLESS=true \
  -e NODE_ENV=test \
  ${CONTAINER_NAME} bash -c "cd /app && npx playwright test ${TEST_PATH} ${REPORTER}"
TEST_RESULT=$?

# Check test result
if [ $TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}Tests completed successfully!${NC}"
  echo -e "${YELLOW}Test reports available at:${NC}"
  echo -e "  - test_screenshots/"
  echo -e "  - test-results/"
  echo -e "  - playwright-report/index.html"
else
  echo -e "${RED}Tests failed with code ${TEST_RESULT}. Check the output above for details.${NC}"
fi

exit $TEST_RESULT