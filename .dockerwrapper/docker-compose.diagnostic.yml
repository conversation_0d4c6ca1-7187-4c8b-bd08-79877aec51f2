services:
  diagnostic:
    container_name: turdparty_diagnostic
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.diagnostic
    volumes:
      - ..:/app
      - ../tests:/app/tests:ro
    environment:
      - PYTHONPATH=/app
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - DOCKER_NETWORK=true
    depends_on:
      - postgres
      - minio
    networks:
      - turdparty_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      
  postgres:
    container_name: turdparty_postgres_diagnostic
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=turdparty_test
    volumes:
      - turdparty_postgres_diagnostic_data:/var/lib/postgresql/data
    networks:
      - turdparty_network

  minio:
    container_name: turdparty_minio_diagnostic
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - turdparty_minio_diagnostic_data:/data
    networks:
      - turdparty_network

networks:
  turdparty_network:
    driver: bridge

volumes:
  turdparty_postgres_diagnostic_data:
  turdparty_minio_diagnostic_data: