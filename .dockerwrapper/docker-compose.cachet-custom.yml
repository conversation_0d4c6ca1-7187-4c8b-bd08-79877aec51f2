services:
  # Custom Cachet service with dark theme and service icons
  cachet:
    image: cachethq/docker:latest
    container_name: turdparty_cachet
    ports:
      - "3501:80"
    environment:
      - DB_DRIVER=pgsql
      - DB_HOST=postgres_cachet
      - DB_PORT=5432
      - DB_DATABASE=cachet
      - DB_USERNAME=postgres
      - DB_PASSWORD=turdparty_secure_postgres_password_2024
      - DB_PREFIX=chq_
      - APP_KEY=base64:O4qJhxlEaUKHJWG8ZVf8jKzlrFqQNzKjhxlEaUKHJWG=
      - APP_DEBUG=false
      - APP_URL=http://localhost:3501
      - APP_TIMEZONE=UTC
      - APP_LOCALE=en
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_DRIVER=redis
      - REDIS_HOST=turdparty_redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DATABASE=1
      - MAIL_DRIVER=log
      - MAIL_HOST=
      - MAIL_PORT=587
      - MAIL_USERNAME=
      - MAIL_PASSWORD=
      - MAIL_ADDRESS=<EMAIL>
      - "MAIL_NAME=TurdParty Status"
      - MAIL_ENCRYPTION=tls
    volumes:
      - cachet_data:/var/www/html/storage
      - ./cachet-customization/dark-theme.css:/var/www/html/public/css/dark-theme.css:ro
      - ./cachet-customization/service-icons.js:/var/www/html/public/js/service-icons.js:ro
    depends_on:
      - postgres_cachet
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/v1/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL database for Cachet
  postgres_cachet:
    image: postgres:15-alpine
    container_name: turdparty_cachet_postgres
    environment:
      - POSTGRES_DB=cachet
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=turdparty_secure_postgres_password_2024
    volumes:
      - postgres_cachet_data:/var/lib/postgresql/data
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d cachet"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  cachet_data:
    name: turdparty_cachet_data
  postgres_cachet_data:
    name: turdparty_postgres_cachet_data

networks:
  turdparty_network:
    external: true
    name: dockerwrapper_turdparty_network
