# Migration from docker/ to .dockerwrapper/

This document tracks the consolidation of Docker-related files from the `docker/` folder into `.dockerwrapper/` to maintain a cleaner project structure.

## Migration Summary

**Date**: January 2025  
**Branch**: `merge/inspector-gadget-functionality`  
**Objective**: Consolidate all Docker-related configurations into a single, well-organized directory

## Files Migrated

### Docker Compose Files
| Original Location | New Location | Status | Notes |
|-------------------|--------------|--------|-------|
| `docker/docker-compose.yml` | `.dockerwrapper/docker-compose.yml` | ✅ Merged | Updated with turdparty_ naming |
| `docker/docker-compose.test.yml` | `.dockerwrapper/docker-compose.test.yml` | ✅ Merged | Existing file updated |
| `docker/docker-compose.diagnostic.yml` | `.dockerwrapper/docker-compose.diagnostic.yml` | ✅ Moved | Updated naming patterns |
| `docker/docker-compose.testing.yml` | `.dockerwrapper/docker-compose.testing.yml` | ✅ Moved | Updated naming patterns |

### Dockerfiles
| Original Location | New Location | Status | Notes |
|-------------------|--------------|--------|-------|
| `docker/Dockerfile` | `.dockerwrapper/Dockerfile.node` | ✅ Moved | Node.js-based Dockerfile |
| `docker/Dockerfile.api` | `.dockerwrapper/Dockerfile.api` | ✅ Merged | Existing file updated |
| `docker/Dockerfile.diagnostic` | `.dockerwrapper/Dockerfile.diagnostic` | ✅ Moved | - |
| `docker/Dockerfile.simple` | `.dockerwrapper/Dockerfile.simple` | ✅ Moved | - |
| `docker/Dockerfile.test` | `.dockerwrapper/Dockerfile.test` | ✅ Compared | Existing file kept |
| `docker/Dockerfile.testing` | `.dockerwrapper/Dockerfile.testing` | ✅ Moved | - |

### Scripts and Utilities
| Original Location | New Location | Status | Notes |
|-------------------|--------------|--------|-------|
| `docker/docker-dashboard` | `.dockerwrapper/docker-dashboard` | ✅ Moved | Updated container names |
| `docker/turdparty-dashboard` | Symlink verified | ✅ Verified | Points to correct location |
| `docker/docker-lock.json` | `.dockerwrapper/docker-lock.json` | ✅ Moved | - |
| `docker/test_redirect_dockerfile` | `.dockerwrapper/test_redirect_dockerfile` | ✅ Moved | - |

## Key Changes Made

### 1. Container Naming Standardization
- All containers now use `turdparty_` prefix consistently
- Removed `_1` suffixes for cleaner naming
- Updated container names in scripts and documentation

### 2. Network Configuration
- Standardized network names to `turdparty_network`
- Updated diagnostic and testing networks
- Maintained network isolation for different environments

### 3. Volume Naming
- All volumes now use `turdparty_` prefix
- Environment-specific suffixes (e.g., `_testing`, `_diagnostic`)
- Consistent naming across all compose files

### 4. Path Updates
- Updated all `context` paths from `...` to `..`
- Updated all `dockerfile` paths to use `.dockerwrapper/`
- Fixed volume mount paths

## Port Mappings

The consolidated setup maintains the 3050-3400 port range:

| Service | Port | Environment |
|---------|------|-------------|
| API | 3050 | Production |
| Frontend | 3100 | Production |
| Dashboard | 3150 | Production |
| PostgreSQL | 3200 | Production |
| React Dev | 3250 | Development |
| MinIO | 3300-3301 | Production |

## Breaking Changes

### Scripts
- `docker-dashboard` script updated to use new container names
- Any scripts referencing old container names need updating

### Environment Variables
- Container names in environment variables updated
- Network references updated

### Documentation
- All references to `docker/` folder updated to `.dockerwrapper/`
- README files updated with new structure

## Verification Steps

1. **Container Names**: All containers use `turdparty_` prefix
2. **Networks**: All services use `turdparty_network` or environment-specific networks
3. **Volumes**: All volumes use `turdparty_` prefix
4. **Paths**: All Dockerfile and context paths point to correct locations
5. **Scripts**: All scripts reference correct container names

## Next Steps

1. **Testing**: Run comprehensive tests to ensure all configurations work
2. **Documentation**: Update any remaining references to old paths
3. **Cleanup**: Remove the `docker/` folder after verification
4. **CI/CD**: Update any CI/CD pipelines that reference old paths

## Rollback Plan

If issues arise, the original `docker/` folder structure can be restored from git history:

```bash
git checkout HEAD~1 -- docker/
```

## Inspector Gadget Integration

As part of this migration, Inspector Gadget functionality was also merged:

- ELK stack configuration added
- Logstash pipeline for Inspektor Gadget data
- Analysis models and UI components
- Comprehensive testing setup

## Contact

For questions about this migration, refer to the git commit history or project documentation.
