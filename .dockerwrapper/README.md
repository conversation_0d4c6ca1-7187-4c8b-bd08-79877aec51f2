# Docker Wrapper for TurdParty

This directory contains Docker configuration files and scripts for running the TurdParty application in both development and production environments.

## Directory Structure

### Dockerfiles
- `Dockerfile`: Main Dockerfile for the API container
- `Dockerfile.api`: API-specific Dockerfile
- `Dockerfile.dashboard`: Dockerfile for the dashboard container
- `Dockerfile.playwright`: Dockerfile for the Playwright testing container
- `Dockerfile.react-dev`: Dockerfile for React development environment
- `Dockerfile.celery`: Dockerfile for Celery workers
- `Dockerfile.minio`: Dockerfile for MinIO with SSH access
- `Dockerfile.test`: Dockerfile for testing environment
- `Dockerfile.diagnostic`: Dockerfile for diagnostic tools
- `Dockerfile.simple`: Simple Dockerfile for basic setups
- `Dockerfile.testing`: Dockerfile for comprehensive testing
- `Dockerfile.node`: Node.js-based Dockerfile

### Docker Compose Files
- `docker-compose.yml`: Main Docker Compose configuration for production
- `dev-compose.yml`: Docker Compose configuration for development
- `docker-compose.test.yml`: Testing environment configuration
- `docker-compose.diagnostic.yml`: Diagnostic tools configuration
- `docker-compose.testing.yml`: Comprehensive testing setup
- `docker-compose.cachet-custom.yml`: Custom Cachet status page
- `docker-compose.celery.yml`: Celery workers configuration
- `docker-compose.playwright.yml`: Playwright testing environment

### Scripts and Tools
- `restart-containers.sh`: Script to restart the Docker containers
- `docker-dashboard`: Docker dashboard management script
- `scripts/`: Directory containing initialization and utility scripts
- `config/`: Configuration files for various services

## Quick Start

### Development Environment

To start the development environment:

```bash
cd .dockerwrapper
./restart-containers.sh dev
```

This will:
1. Stop any existing containers
2. Create the necessary directories
3. Start the containers with the development configuration
4. Show the running containers

The development environment includes:
- API server with hot reloading at http://localhost:3050
- React frontend with hot reloading at http://localhost:3100
- PostgreSQL database at localhost:3200
- Dashboard for monitoring at http://localhost:3150
- Playwright for testing at http://localhost:3250

### Production Environment

To start the production environment:

```bash
cd .dockerwrapper
./restart-containers.sh
```

This will start the containers with the production configuration.

## File Upload Functionality

The application includes file upload functionality with the following features:

- API endpoints for uploading, downloading, and managing files
- Support for multiple file uploads
- Folder uploads with preserved directory structure
- File metadata storage
- Configurable upload directory and file size limits

### API Endpoints

All file upload endpoints are available at `/api/v1/file_upload/`:

- `GET /api/v1/file_upload/`: List all file uploads
- `POST /api/v1/file_upload/`: Upload a file
- `GET /api/v1/file_upload/{file_id}`: Get file metadata
- `GET /api/v1/file_upload/download/{file_id}`: Download a file
- `PUT /api/v1/file_upload/{file_id}`: Update file metadata
- `DELETE /api/v1/file_upload/{file_id}`: Delete a file
- `POST /api/v1/file_upload/folder`: Upload multiple files as a folder

## Configuration

The Docker environment can be configured using environment variables in the Docker Compose files:

- `DATABASE_URL`: PostgreSQL connection string
- `PORT`: API server port
- `TEST_MODE`: Enable test mode (bypasses authentication)
- `DEBUG`: Enable debug mode
- `FILE_UPLOAD_DIR`: Directory for file uploads
- `API_PREFIX`: API prefix for versioned endpoints

## Port Configuration

The application uses the following port mappings:

| Service    | Host Port | Container Port | Description                |
|------------|-----------|----------------|----------------------------|
| API        | 3050      | 8000           | FastAPI application        |
| Frontend   | 3100      | 3000           | React application          |
| Dashboard  | 3150      | 8080           | Monitoring dashboard       |
| PostgreSQL | 3200      | 5432           | PostgreSQL database        |
| Playwright | 3250      | 9323           | Playwright testing service |

All ports are configured to use the 3050-3400 range as required.

## Troubleshooting

If you encounter issues with the Docker containers:

1. Check the container logs:
   ```bash
   docker logs turdparty_api
   ```

2. Restart the containers:
   ```bash
   cd .dockerwrapper
   ./restart-containers.sh dev
   ```

3. Rebuild the containers:
   ```bash
   cd .dockerwrapper
   docker compose -f dev-compose.yml build
   ./restart-containers.sh dev
   ```

4. Check the API health:
   ```bash
   curl http://localhost:3050/health
   ```

5. Check the database connection:
   ```bash
   docker exec -it turdparty_postgres psql -U postgres -d app -c "SELECT 1"
   ```

# Docker Wrapper for FastAPI Application

This directory contains Docker configuration for running the application in a containerized environment.

## Prerequisites

- Docker and Docker Compose
- Access to a Vagrant host (optional, for VM management)
- SSH keys configured for Vagrant access (if using Vagrant features)

## Environment Configuration

The application uses environment variables for configuration. These can be set in the `.env` file, which is automatically generated from Replit secrets using the `extract-env` command.

### Key Environment Variables

- `VAGRANT_HOST`: The hostname/IP of the server hosting Vagrant VMs (default: localhost)
- `DEFAULT_VAGRANT_SSH_USER`: Username for SSH connections to Vagrant hosts
- `DEFAULT_VAGRANT_SERVER`: The hostname/IP address of the Vagrant server
- `DEFAULT_VAGRANT_SSH_KEY`: ED25519 SSH key for authentication
- `MINIO_ACCESS_KEY`: MinIO access key (default: minioadmin)
- `MINIO_SECRET_KEY`: MinIO secret key (default: minioadmin)

## Usage

All commands should be run from the `.dockerwrapper` directory.

### Starting the Application

```bash
# Extract environment variables from Replit
make extract-env

# Build the containers
make build

# Start all services in detached mode
make up

# Or start in foreground
make run
```

### Managing the Application

```bash
# View logs
make logs

# List running containers
make ps

# Run database migrations
make migrate

# Initialize the database
make init-db

# Run tests
make tests

# Format code
make format

# Lint code
make lint
```

### Accessing Containers

```bash
# SSH into the API container
make ssh-api

# Connect to Vagrant through the API container
make ssh-vagrant
```

### Stopping and Cleaning Up

```bash
# Stop containers
make down

# Clean containers and volumes
make volume-clean
```

### Database Operations

```bash
# Create a database backup
make backup

# Restore from a backup
make restore
```

### SSH Key Setup

If you're using Vagrant integration, you need to have SSH keys properly configured.

```bash
# Setup SSH keys for Vagrant access
make setup-ssh
```

## Logging and Troubleshooting

The Docker wrapper now logs all command outputs to a `.output` file to assist with troubleshooting. This file contains the output of all make commands run in the Docker wrapper, allowing for easy diagnosis of issues.

### Analyzing Issues

To analyze the `.output` file for issues and get recommendations:

```bash
make analyze
```

This will provide a detailed report of any errors, warnings, and suggestions for fixing detected issues.

### Automatic Issue Fixing

The Docker wrapper includes tools to automatically fix common issues:

```bash
# See what would be fixed without making changes
make fix-dry-run

# Attempt to automatically fix detected issues
make fix
```

### What Issues Can Be Detected?

The analysis tool can detect and provide recommendations for various issues:
- Missing environment variables
- SSH configuration problems
- Database connection issues 
- Container startup failures
- Port conflicts
- Monitoring setup problems

## Architecture

```mermaid
graph TD
    subgraph "Docker Environment"
        API[API Container] --> Postgres[PostgreSQL Database]
        API --> MinIO[MinIO Object Storage]
        API --> SSH[SSH Volume]
        
        subgraph "Volumes"
            PostgresVol[PostgreSQL Data]
            MinIOVol[MinIO Data]
            SSH
        end
    end
    
    subgraph "Host Machine"
        Makefile[Makefile Commands] --> DockerCompose[docker-compose.yml]
        DockerCompose --> API
        Scripts[Shell Scripts] --> API
        AppCode[Application Code] --> API
    end
    
    subgraph "External Connections"
        API --> VagrantServers[Vagrant Servers]
    end
    
    classDef container fill:#b8e0d2,stroke:#333,stroke-width:1px;
    classDef volume fill:#d5e8d4,stroke:#82b366,stroke-width:1px;
    classDef config fill:#dae8fc,stroke:#6c8ebf,stroke-width:1px;
    classDef external fill:#ffe6cc,stroke:#d79b00,stroke-width:1px;
    
    class API,Postgres,MinIO container;
    class PostgresVol,MinIOVol,SSH volume;
    class Makefile,DockerCompose,Scripts,AppCode config;
    class VagrantServers external;
```

The diagram above illustrates the architecture of our Docker-based development environment:

1. **Docker Environment**: Contains three main containers:
   - **API Container**: The main application container running the Python API
   - **PostgreSQL Database**: For data persistence
   - **MinIO Object Storage**: For file storage and object management

2. **Volumes**:
   - **PostgreSQL Data**: Persistent storage for the database
   - **MinIO Data**: Persistent storage for object files
   - **SSH**: Contains SSH keys for secure connections to Vagrant servers

3. **Host Machine**:
   - **Makefile Commands**: Provides easy-to-use commands to manage the environment
   - **docker-compose.yml**: Defines the container configuration
   - **Shell Scripts**: Utility scripts for environment setup and management
   - **Application Code**: The actual application codebase mounted into the container

4. **External Connections**:
   - **Vagrant Servers**: Remote virtual machines that the API connects to

## Architecture

The Docker setup includes the following services:

1. **API**: The FastAPI application
2. **PostgreSQL**: Database for the application
3. **MinIO**: Object storage service

All services are connected through a Docker network, allowing them to communicate with each other.

## Customizing

You can modify the `docker-compose.yml` file to add or configure services as needed. The `Dockerfile` can also be customized to include additional dependencies or configuration.
## Monitoring with Prometheus and Grafana

The Docker wrapper supports optional monitoring with Prometheus and Grafana. This can be enabled by setting the appropriate environment variables in Replit Secrets.

### Required Environment Variables

To enable monitoring, add these to your Replit Secrets:

- `ENABLE_PROMETHEUS`: Set to "true" to enable Prometheus metrics collection
- `PROMETHEUS_PORT`: Port for exposing metrics (default: 8081)
- `PROMETHEUS_ENDPOINT`: Endpoint for metrics (default: /metrics)
- `ENABLE_GRAFANA`: Set to "true" to enable the Grafana dashboard
- `GRAFANA_ADMIN_USER`: Grafana admin username (default: admin)
- `GRAFANA_ADMIN_PASSWORD`: Grafana admin password (default: admin)

### Using Monitoring

When enabled, Prometheus will automatically collect metrics from your application, and Grafana will provide dashboards to visualize them.

- Prometheus UI: http://localhost:9090
- Grafana Dashboard: http://localhost:3000 (login with the configured admin credentials)

The application automatically exposes the following metrics:

- Request counts by path and method
- Request latency in seconds
- Error counts by path and status code
- Active request counts by path

### Enabling Monitoring

```bash
# Add required secrets in Replit
# Then extract environment and start services
make extract-env
make setup-monitoring
make up
```

### Custom Dashboards

You can create custom Grafana dashboards by adding JSON dashboard definitions to:

```
.dockerwrapper/config/grafana/provisioning/dashboards/
```
# Docker Wrapper

This directory contains Docker configuration and helper scripts for containerized environments.

## Contents

- `backup/`: Container backup scripts and files
- `config/`: Configuration for Docker containers
- `docs/`: Documentation for Docker wrapper functionality
- `scripts/`: Helper scripts for Docker operations
- `services/`: Service definitions for containerized applications
- `Dockerfile`: Main Docker image definition
- `docker-compose.yml`: Multi-container Docker application definition
- `Makefile`: Build and deployment automation
- `container_service_testing_script.sh`: Script for testing container services
- `dockerwrapper-diagnostic.sh`: Diagnostic script for Docker wrapper issues
- `simpleton_setup_guide.md`: Simple setup guide for Docker environment

## Usage

The Docker wrapper provides a consistent environment for development, testing, and deployment of the application.

Run `make help` to see available commands for managing containers.
