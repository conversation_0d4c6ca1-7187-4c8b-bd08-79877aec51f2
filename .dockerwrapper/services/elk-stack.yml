name: turdparty-elk

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: turdparty_elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - default
      - net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.elasticsearch.rule=Host(`elasticsearch.turdparty.localhost`)"
      - "traefik.http.routers.elasticsearch.entrypoints=web"
      - "traefik.http.services.elasticsearch.loadbalancer.server.port=9200"
      - "traefik.docker.network=net"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: turdparty_logstash
    volumes:
      - ./config/logstash/pipeline:/usr/share/logstash/pipeline
      - ./config/logstash/config:/usr/share/logstash/config
    environment:
      - "LS_JAVA_OPTS=-Xmx512m -Xms512m"
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - default
      - net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.logstash.rule=Host(`logstash.turdparty.localhost`)"
      - "traefik.http.routers.logstash.entrypoints=web"
      - "traefik.http.services.logstash.loadbalancer.server.port=9600"
      - "traefik.docker.network=net"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9600 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: turdparty_kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - SERVER_NAME=kibana.turdparty.localhost
    depends_on:
      elasticsearch:
        condition: service_healthy
    networks:
      - default
      - net
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.kibana.rule=Host(`kibana.turdparty.localhost`)"
      - "traefik.http.routers.kibana.entrypoints=web"
      - "traefik.http.services.kibana.loadbalancer.server.port=5601"
      - "traefik.docker.network=net"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: turdparty_filebeat
    user: root
    volumes:
      - ./config/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ../logs:/app/logs:ro
      - filebeat_data:/usr/share/filebeat/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - LOGSTASH_HOSTS=logstash:5044
    depends_on:
      elasticsearch:
        condition: service_healthy
      logstash:
        condition: service_healthy
    networks:
      - default
    restart: unless-stopped
    command: filebeat -e -strict.perms=false

volumes:
  elasticsearch_data:
    name: turdparty_elasticsearch_data
  filebeat_data:
    name: turdparty_filebeat_data

networks:
  default:
    name: turdparty_elk_network
    driver: bridge
  net:
    external: true
