#!/bin/bash

# Script to run MinIO-related tests inside the API container
# This script should be executed from the .dockerwrapper directory

set -e

# Text colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================${NC}"
echo -e "${YELLOW}   Running Fixed MinIO Tests           ${NC}"
echo -e "${YELLOW}=======================================${NC}"

# Check if API container is running
API_CONTAINER=$(docker ps -q -f "name=turdparty_api")
if [ -z "$API_CONTAINER" ]; then
    echo -e "${RED}API container is not running. Starting it...${NC}"
    docker compose -f docker-compose.playwright.yml up -d api
    # Wait for container to be ready
    echo -e "${YELLOW}Waiting for API container to be ready...${NC}"
    sleep 10
    API_CONTAINER=$(docker ps -q -f "name=turdparty_api")
    if [ -z "$API_CONTAINER" ]; then
        echo -e "${RED}Failed to start API container.${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}API container is running.${NC}"

# Copy fixed files into the container
echo -e "${YELLOW}Copying fixed files into container...${NC}"

# First create directories to ensure they exist
docker exec $API_CONTAINER mkdir -p /app/api/tests/mocks /app/api/routes /app/api/tests/routes

# Copy the fixed minio_ssh_wrapper.py
docker cp ../api/routes/minio_ssh_wrapper.py $API_CONTAINER:/app/api/routes/

# Copy the fixed test files
docker cp ../api/tests/routes/test_minio_health.py $API_CONTAINER:/app/api/tests/routes/
docker cp ../api/tests/routes/test_minio_status_routes.py $API_CONTAINER:/app/api/tests/routes/
docker cp ../api/tests/routes/test_storage_routes.py $API_CONTAINER:/app/api/tests/routes/

# Copy the fixed conftest.py to skip database setup
docker cp ../api/tests/conftest.py $API_CONTAINER:/app/api/tests/

# List of tests that were fixed
FIXED_TESTS=(
    "api/tests/routes/test_minio_health.py"
    "api/tests/routes/test_minio_status_routes.py"
    "api/tests/routes/test_storage_routes.py"
)

# Find other MinIO-related tests that need fixing
MINIO_TESTS=$(docker exec $API_CONTAINER sh -c "find api/tests -name '*.py' | grep -E 'minio|vm_injection'")

# Set proper environment variables for tests
echo -e "${YELLOW}Setting up test environment...${NC}"
docker exec -e USE_MOCK_MINIO=true -e API_TEST_MODE=true $API_CONTAINER sh -c "mkdir -p /app/test-results"

# Run pytest directly without using the conftest.py or SQLite for these specific tests
echo -e "${BLUE}Running fixed MinIO tests...${NC}"
for test in "${FIXED_TESTS[@]}"; do
    echo -e "${YELLOW}Running test: ${test}${NC}"
    # Execute test with USE_MOCK_MINIO=true to ensure tests use our mock implementation
    # Setting NO_DB_REQUIRED to true to force skipping the database setup
    docker exec -e USE_MOCK_MINIO=true -e API_TEST_MODE=true -e NO_DB_REQUIRED=true $API_CONTAINER sh -c "cd /app && PYTHONPATH=/app pytest ${test} -v"

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Test passed: ${test}${NC}"
    else
        echo -e "${RED}❌ Test failed: ${test}${NC}"
    fi
done

# Generate a list of remaining tests that might need fixing
echo -e "\n${BLUE}Other MinIO-related tests that might need fixing:${NC}"
for test in $MINIO_TESTS; do
    # Skip tests we already ran
    skip=false
    for fixed in "${FIXED_TESTS[@]}"; do
        if [[ "$test" == *"$fixed"* ]]; then
            skip=true
            break
        fi
    done

    if [ "$skip" = false ]; then
        echo "  - $test"
    fi
done

echo -e "\n${YELLOW}To run a specific test, use:${NC}"
echo "docker exec -e USE_MOCK_MINIO=true -e API_TEST_MODE=true -e NO_DB_REQUIRED=true $API_CONTAINER sh -c \"cd /app && PYTHONPATH=/app pytest <test_path> -v\""

echo -e "\n${GREEN}Test script completed!${NC}"