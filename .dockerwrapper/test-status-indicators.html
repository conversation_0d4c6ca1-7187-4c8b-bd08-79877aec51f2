<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TurdParty Status Indicators Test</title>
    <style>
        body {
            background-color: #1a202c;
            color: #e2e8f0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #2d3748;
            border-radius: 12px;
            padding: 30px;
            border: 1px solid #4a5568;
        }
        
        h1 {
            color: #4a90e2;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .component-group {
            background-color: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .component-group h4 {
            background: linear-gradient(90deg, #4a5568 0%, #2d3748 100%);
            padding: 15px 20px;
            margin: 0;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
            font-size: 1.1rem;
            color: #e2e8f0;
        }
        
        .component {
            background-color: #2d3748;
            border-bottom: 1px solid #4a5568;
            padding: 15px 20px;
            transition: background-color 0.2s ease;
            position: relative;
        }
        
        .component:hover {
            background-color: #4a5568;
        }
        
        .component:last-child {
            border-bottom: none;
            border-radius: 0 0 8px 8px;
        }
        
        /* Status Icons */
        .component::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid transparent;
            z-index: 10;
        }
        
        .component[data-status="1"]::before,
        .component.status-1::before {
            background-color: #52c41a;
            border-color: #389e0d;
            box-shadow: 0 0 8px rgba(82, 196, 26, 0.4);
        }
        
        .component[data-status="2"]::before,
        .component.status-2::before {
            background-color: #faad14;
            border-color: #d48806;
            box-shadow: 0 0 8px rgba(250, 173, 20, 0.4);
        }
        
        .component[data-status="3"]::before,
        .component.status-3::before {
            background-color: #ff7a45;
            border-color: #d4380d;
            box-shadow: 0 0 8px rgba(255, 122, 69, 0.4);
        }
        
        .component[data-status="4"]::before,
        .component.status-4::before {
            background-color: #ff4d4f;
            border-color: #cf1322;
            box-shadow: 0 0 8px rgba(255, 77, 79, 0.4);
        }
        
        .component-name {
            margin-left: 25px;
            font-weight: 500;
            color: #e2e8f0;
        }
        
        .component-description {
            margin-left: 25px;
            color: #a0aec0;
            font-size: 14px;
            margin-top: 4px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-left: 10px;
            border: 1px solid transparent;
        }
        
        .status-badge.status-1 {
            background-color: rgba(82, 196, 26, 0.15);
            color: #52c41a;
            border-color: rgba(82, 196, 26, 0.3);
        }
        
        .status-badge.status-2 {
            background-color: rgba(250, 173, 20, 0.15);
            color: #faad14;
            border-color: rgba(250, 173, 20, 0.3);
        }
        
        .status-badge.status-3 {
            background-color: rgba(255, 122, 69, 0.15);
            color: #ff7a45;
            border-color: rgba(255, 122, 69, 0.3);
        }
        
        .status-badge.status-4 {
            background-color: rgba(255, 77, 79, 0.15);
            color: #ff4d4f;
            border-color: rgba(255, 77, 79, 0.3);
        }
        
        .test-controls {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 10px;
            font-weight: 500;
        }
        
        .test-button:hover {
            background-color: #357abd;
        }
        
        .test-results {
            background-color: #1a202c;
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .warning { color: #faad14; }
        .info { color: #4a90e2; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 TurdParty Status Indicators Test</h1>
        
        <div class="test-controls">
            <button class="test-button" onclick="loadComponents()">Load Components from API</button>
            <button class="test-button" onclick="testStatusIndicators()">Test Status Indicators</button>
            <button class="test-button" onclick="simulateStatusChanges()">Simulate Status Changes</button>
        </div>
        
        <div id="components-container">
            <!-- Components will be loaded here -->
        </div>
        
        <div class="test-results" id="test-results">
            <div class="info">Click "Load Components from API" to start testing...</div>
        </div>
    </div>

    <script>
        let testResults = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push(`[${timestamp}] ${message}`);
            updateTestResults();
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateTestResults() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = testResults.map(result => {
                let className = 'info';
                if (result.includes('ERROR') || result.includes('Failed')) className = 'error';
                else if (result.includes('SUCCESS') || result.includes('✅')) className = 'success';
                else if (result.includes('WARNING') || result.includes('⚠️')) className = 'warning';
                
                return `<div class="${className}">${result}</div>`;
            }).join('');
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        async function loadComponents() {
            log('🔄 Loading components from Cachet API...');
            
            try {
                const response = await fetch('/api/v1/components');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const components = data.data || [];
                
                log(`✅ SUCCESS: Loaded ${components.length} components from API`);
                
                // Group components by group_id
                const groups = {};
                components.forEach(component => {
                    if (!groups[component.group_id]) {
                        groups[component.group_id] = [];
                    }
                    groups[component.group_id].push(component);
                });
                
                // Render components
                const container = document.getElementById('components-container');
                container.innerHTML = '';
                
                Object.keys(groups).forEach(groupId => {
                    const groupComponents = groups[groupId];
                    const groupDiv = document.createElement('div');
                    groupDiv.className = 'component-group';
                    
                    const groupName = getGroupName(parseInt(groupId));
                    groupDiv.innerHTML = `
                        <h4>${groupName}</h4>
                        ${groupComponents.map(component => `
                            <div class="component" data-status="${component.status}" data-component-id="${component.id}">
                                <div class="component-name">${component.name}</div>
                                <div class="component-description">
                                    ${component.description}
                                    <span class="status-badge status-${component.status}">${getStatusText(component.status)}</span>
                                </div>
                            </div>
                        `).join('')}
                    `;
                    
                    container.appendChild(groupDiv);
                });
                
                log(`✅ SUCCESS: Rendered ${components.length} components with status indicators`);
                
            } catch (error) {
                log(`❌ ERROR: Failed to load components - ${error.message}`, 'error');
            }
        }
        
        function getGroupName(groupId) {
            const groupNames = {
                1: '🌐 Frontend Services',
                2: '🚀 API Services', 
                3: '🗄️ Storage Services'
            };
            return groupNames[groupId] || `Group ${groupId}`;
        }
        
        function getStatusText(status) {
            const statusTexts = {
                1: 'Operational',
                2: 'Performance Issues',
                3: 'Partial Outage',
                4: 'Major Outage'
            };
            return statusTexts[status] || 'Unknown';
        }
        
        function testStatusIndicators() {
            log('🧪 Testing status indicators...');
            
            const components = document.querySelectorAll('.component');
            let passedTests = 0;
            let totalTests = 0;
            
            components.forEach((component, index) => {
                const status = component.getAttribute('data-status');
                const hasStatusIcon = window.getComputedStyle(component, '::before').content !== 'none';
                const statusBadge = component.querySelector('.status-badge');
                
                totalTests += 3;
                
                // Test 1: Component has status attribute
                if (status) {
                    log(`✅ Component ${index + 1}: Has status attribute (${status})`);
                    passedTests++;
                } else {
                    log(`❌ Component ${index + 1}: Missing status attribute`, 'error');
                }
                
                // Test 2: Status badge exists
                if (statusBadge) {
                    log(`✅ Component ${index + 1}: Has status badge`);
                    passedTests++;
                } else {
                    log(`❌ Component ${index + 1}: Missing status badge`, 'error');
                }
                
                // Test 3: Status icon styling applied
                const beforeStyles = window.getComputedStyle(component, '::before');
                const backgroundColor = beforeStyles.backgroundColor;
                if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)') {
                    log(`✅ Component ${index + 1}: Has status icon styling`);
                    passedTests++;
                } else {
                    log(`❌ Component ${index + 1}: Missing status icon styling`, 'error');
                }
            });
            
            const successRate = ((passedTests / totalTests) * 100).toFixed(1);
            log(`📊 Test Results: ${passedTests}/${totalTests} tests passed (${successRate}%)`);
            
            if (successRate >= 90) {
                log('🎉 Status indicators are working correctly!', 'success');
            } else if (successRate >= 70) {
                log('⚠️ Status indicators partially working', 'warning');
            } else {
                log('❌ Status indicators need fixes', 'error');
            }
        }
        
        function simulateStatusChanges() {
            log('🔄 Simulating status changes...');
            
            const components = document.querySelectorAll('.component');
            const statuses = [1, 2, 3, 4];
            
            components.forEach((component, index) => {
                setTimeout(() => {
                    const newStatus = statuses[Math.floor(Math.random() * statuses.length)];
                    component.setAttribute('data-status', newStatus);
                    component.className = `component status-${newStatus}`;
                    
                    const statusBadge = component.querySelector('.status-badge');
                    if (statusBadge) {
                        statusBadge.className = `status-badge status-${newStatus}`;
                        statusBadge.textContent = getStatusText(newStatus);
                    }
                    
                    log(`🔄 Component ${index + 1}: Status changed to ${getStatusText(newStatus)}`);
                }, index * 500);
            });
            
            setTimeout(() => {
                log('✅ Status simulation complete');
            }, components.length * 500 + 1000);
        }
        
        // Auto-load components when page loads
        window.addEventListener('load', () => {
            setTimeout(loadComponents, 1000);
        });
    </script>
</body>
</html>
