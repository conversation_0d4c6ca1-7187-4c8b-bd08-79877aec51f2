#!/bin/bash

# Script to start the TurdParty containers with the proper project name

# Change to the project root directory
cd "$(dirname "$0")/.."

# Stop any existing containers and remove volumes
echo "Stopping existing containers..."
docker compose -f .dockerwrapper/docker-compose.yml -p turdparty down

# Ask if volumes should be removed
read -p "Do you want to remove volumes (this will delete all data)? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
    echo "Removing volumes..."
    docker compose -f .dockerwrapper/docker-compose.yml -p turdparty down -v
fi

# Build the containers
echo "Building containers..."
docker compose -f .dockerwrapper/docker-compose.yml -p turdparty build

# Start the containers
echo "Starting containers..."
docker compose -f .dockerwrapper/docker-compose.yml -p turdparty up -d

# Show running containers
echo "Running containers:"
docker ps | grep -E 'api|frontend|db'

echo
echo "TurdParty started successfully!"
echo "Access the frontend at: http://localhost:3000"
echo "Access the API at: http://localhost:8000"
echo "PostgreSQL is available at localhost:5430"