#!/bin/bash
#
# Docker Dashboard Wrapper Script
# This script provides a convenient way to interact with the Docker Dashboard CLI tool.
#

# Check if the dashboard container is running
if ! docker ps | grep -q "turdparty_dashboard"; then
    echo "Dashboard container is not running. Starting it..."
    cd .dockerwrapper && docker compose up -d dashboard
    cd ..
fi

# Check if any arguments were provided
if [ $# -eq 0 ]; then
    # No arguments, show help
    docker exec -it turdparty_dashboard python /app/dashboard.py --help
else
    # Pass all arguments to the dashboard script
    docker exec -it turdparty_dashboard python /app/dashboard.py "$@"
fi