#!/bin/bash
set -e

# Check if docker compose is available
if ! command -v docker &> /dev/null; then
    echo "Error: docker is not installed or not in PATH"
    exit 1
fi

# Static port range starting from 8800 to avoid conflicts
API_PORT=8801
FRONTEND_PORT=8802
DASHBOARD_PORT=8803
POSTGRES_PORT=8804
MINIO_API_PORT=8805
MINIO_CONSOLE_PORT=8806
MINIO_SSH_PORT=8807

# Function to check if a port is in use
is_port_in_use() {
    local port=$1
    if lsof -i :$port > /dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to find an available port within range
find_available_port() {
    local start_port=$1
    local max_attempts=20
    local port=$start_port
    
    for (( i=0; i<$max_attempts; i++ )); do
        if ! is_port_in_use $port; then
            echo $port
            return 0
        fi
        port=$((port + 1))
    done
    
    echo "Error: No available ports found after $max_attempts attempts" >&2
    exit 1
}

# Ensure each port is available, otherwise find the next available port
for port_var in API_PORT FRONTEND_PORT DASHBOARD_PORT POSTGRES_PORT MINIO_API_PORT MINIO_CONSOLE_PORT MINIO_SSH_PORT; do
    # Get current port value
    eval "current_port=\$$port_var"
    
    # Check if port is in use
    if is_port_in_use $current_port; then
        echo "Port $current_port is in use. Finding an alternative port..."
        new_port=$(find_available_port $((current_port + 1)))
        eval "$port_var=$new_port"
        echo "Using port $new_port for $port_var"
    fi
done

# Function to check if TurdParty containers are running
are_turdparty_running() {
    if docker ps --format "{{.Names}}" | grep -q "turdparty_"; then
        return 0  # Containers are running
    else
        return 1  # Containers are not running
    fi
}

# Function to display running containers and their ports
display_running_containers() {
    echo "===== Running TurdParty Containers ====="
    docker ps --format "table {{.Names}}\t{{.Ports}}" | grep "turdparty_" || echo "No containers found"
    echo "========================================"
}

# Check if containers are already running
if are_turdparty_running; then
    echo "TurdParty containers are already running."
    display_running_containers
    exit 0
fi

# Create dynamic docker-compose.yml
cat > .dockerwrapper/docker-compose.dynamic.yml <<EOF
services:
  postgres:
    container_name: turdparty_postgres_1
    image: postgres:14-alpine
    ports:
      - "$POSTGRES_PORT:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=app
    volumes:
      - turdparty_postgres_data:/var/lib/postgresql/data
    networks:
      - turdparty_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  minio:
    container_name: turdparty_minio_1
    image: minio/minio:latest
    ports:
      - "$MINIO_API_PORT:9000"
      - "$MINIO_CONSOLE_PORT:9001"
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - turdparty_minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - turdparty_network
    restart: unless-stopped

  minio-ssh:
    container_name: turdparty_minio_ssh_1
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.minio
    ports:
      - "$MINIO_SSH_PORT:22"
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - turdparty_minio_data:/data
    networks:
      - turdparty_network
    restart: unless-stopped
    depends_on:
      minio:
        condition: service_healthy

  api:
    container_name: turdparty_api_1
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile
      args:
        - IMAGE_PREFIX=turdparty
    image: turdparty-api
    ports:
      - "$API_PORT:8000"
    depends_on:
      postgres:
        condition: service_healthy
      minio:
        condition: service_healthy
    environment:
      - DATABASE_URL=********************************************/app
      - DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@postgres:5432/app
      - PORT=8000
      - TEST_MODE=true
      - PYTHONPATH=/app
      - DEBUG=true
      - FILE_UPLOAD_DIR=/app/uploads
      - API_PREFIX=/api/v1
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_DIRECT=true
      - VAGRANT_TEST_MODE=1
    volumes:
      - ..:/app
      - ../logs:/app/logs
      - ../uploads:/app/uploads
      - turdparty_ssh_keys:/home/<USER>/.ssh
      - ../vagrant_exec.sh:/app/vagrant_exec.sh
    entrypoint: ["/usr/local/bin/container-init.sh"]
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
    extra_hosts:
      - "host.docker.internal:host-gateway"
    cap_add:
      - NET_ADMIN
    networks:
      - turdparty_network
    restart: unless-stopped

  react-app:
    container_name: turdparty_frontend_1
    image: nginx:alpine
    ports:
      - "$FRONTEND_PORT:80"
    depends_on:
      - api
    volumes:
      - ../turdparty-app/build:/usr/share/nginx/html
    networks:
      - turdparty_network
    restart: unless-stopped

  dashboard:
    container_name: turdparty_dashboard
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.dashboard
      args:
        - IMAGE_PREFIX=turdparty
    image: turdparty-dashboard
    ports:
      - "$DASHBOARD_PORT:8080"
    volumes:
      - ..:/app
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - PYTHONPATH=/app
      - CONTAINER_PREFIX=turdparty
    command: ["python", "/app/dashboard.py", "start"]
    user: root
    networks:
      - turdparty_network
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  turdparty_postgres_data:
  turdparty_ssh_keys:
  turdparty_frontend_node_modules:
  turdparty_minio_data:

networks:
  turdparty_network:
    driver: bridge
EOF

echo "==================================="
echo "Port configuration:"
echo "API: $API_PORT"
echo "Frontend: $FRONTEND_PORT"
echo "Dashboard: $DASHBOARD_PORT"
echo "PostgreSQL: $POSTGRES_PORT"
echo "MinIO API: $MINIO_API_PORT"
echo "MinIO Console: $MINIO_CONSOLE_PORT"
echo "MinIO SSH: $MINIO_SSH_PORT"
echo "==================================="

# Save port mappings to JSON for later reference
cat > .dockerwrapper/port_mappings.json <<EOF
{
    "api": $API_PORT,
    "frontend": $FRONTEND_PORT,
    "dashboard": $DASHBOARD_PORT,
    "postgres": $POSTGRES_PORT,
    "minio_api": $MINIO_API_PORT,
    "minio_console": $MINIO_CONSOLE_PORT,
    "minio_ssh": $MINIO_SSH_PORT
}
EOF

# Ask if volumes should be removed
read -p "Do you want to remove volumes (this will delete all data)? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Removing volumes..."
    docker compose -f .dockerwrapper/docker-compose.dynamic.yml -p turdparty down -v
else
    # Stop containers without removing volumes
    docker compose -f .dockerwrapper/docker-compose.dynamic.yml -p turdparty down || true
fi

# Start dependency containers first
echo "Starting dependency containers (Postgres, MinIO)..."
if ! docker compose -f .dockerwrapper/docker-compose.dynamic.yml -p turdparty up -d postgres minio; then
    echo "Error: Failed to start dependency containers"
    exit 1
fi

echo "Waiting for dependencies to be ready..."
sleep 10

# Start remaining containers
echo "Starting remaining containers..."
if ! docker compose -f .dockerwrapper/docker-compose.dynamic.yml -p turdparty up -d; then
    echo "Error: Failed to start containers"
    exit 1
fi

# Verify containers are running
sleep 10
if ! are_turdparty_running; then
    echo "Error: Containers failed to start properly"
    docker compose -f .dockerwrapper/docker-compose.dynamic.yml -p turdparty logs
    exit 1
fi

# Show running containers and their actual ports
echo "TurdParty started successfully!"
display_running_containers

# Success message with access URLs
echo "==================================="
echo "TurdParty is now running!"
echo "==================================="
echo "- API: http://localhost:$API_PORT"
echo "- Frontend: http://localhost:$FRONTEND_PORT"
echo "- Dashboard: http://localhost:$DASHBOARD_PORT"
echo "- PostgreSQL: localhost:$POSTGRES_PORT"
echo "- MinIO API: http://localhost:$MINIO_API_PORT"
echo "- MinIO Console: http://localhost:$MINIO_CONSOLE_PORT"
echo "- MinIO SSH: ssh root@localhost -p $MINIO_SSH_PORT (password: turdparty)"
echo "===================================" 