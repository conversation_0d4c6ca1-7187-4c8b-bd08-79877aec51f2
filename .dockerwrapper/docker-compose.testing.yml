services:
  test_runner:
    container_name: turdparty_test_runner
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.testing
    volumes:
      - ..:/app
      - turdparty_test_deps:/usr/local/lib/python3.10/site-packages/
      - turdparty_test_cache:/root/.cache/
    environment:
      - PYTHONPATH=/app
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - DOCKER_NETWORK=true
      - TESTING=true
      - TEST_MODE=true
      - DATABASE_URL=********************************************/test_app
      - SQLALCHEMY_DATABASE_URI=********************************************/test_app
    depends_on:
      postgres:
        condition: service_healthy
      minio:
        condition: service_started
    networks:
      - turdparty_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: tail -f /dev/null
      
  postgres:
    container_name: turdparty_postgres_testing
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=test_app
    volumes:
      - turdparty_postgres_testing_data:/var/lib/postgresql/data
    networks:
      - turdparty_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  minio:
    container_name: turdparty_minio_testing
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - turdparty_minio_testing_data:/data
    networks:
      - turdparty_network

networks:
  turdparty_network:
    driver: bridge

volumes:
  turdparty_postgres_testing_data:
  turdparty_minio_testing_data:
  turdparty_test_deps:
  turdparty_test_cache:
