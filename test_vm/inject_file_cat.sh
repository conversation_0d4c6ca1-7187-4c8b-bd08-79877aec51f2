#!/bin/bash

# Script to inject a file into the Vagrant VM using cat

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
SOURCE_FILE="test_file.txt"
TARGET_PATH="/tmp/injection_test/test_file.txt"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --source-file)
      SOURCE_FILE="$2"
      shift 2
      ;;
    --target-path)
      TARGET_PATH="$2"
      shift 2
      ;;
    *)
      echo -e "${RED}Unknown option: $1${NC}"
      exit 1
      ;;
  esac
done

# Get the absolute path to the source file
SOURCE_FILE_ABS=$(realpath "$SOURCE_FILE")

# Check if the source file exists
if [ ! -f "$SOURCE_FILE_ABS" ]; then
  echo -e "${RED}Source file does not exist: $SOURCE_FILE_ABS${NC}"
  exit 1
fi

echo -e "${YELLOW}Injecting file $SOURCE_FILE_ABS into VM at $TARGET_PATH${NC}"

# Get the VM's IP address
VM_IP=$(vagrant ssh -c "hostname -I | awk '{print \$1}'" | tr -d '\r\n')

if [ -z "$VM_IP" ]; then
  echo -e "${RED}Failed to get VM IP address${NC}"
  exit 1
fi

echo -e "${YELLOW}VM IP address: $VM_IP${NC}"

# Create the target directory on the VM if it doesn't exist
TARGET_DIR=$(dirname "$TARGET_PATH")
echo -e "${YELLOW}Creating target directory: $TARGET_DIR${NC}"
vagrant ssh -c "sudo mkdir -p $TARGET_DIR && sudo chmod 777 $TARGET_DIR"

# Create a temporary file on the VM with the content of the source file
echo -e "${YELLOW}Creating temporary file on VM...${NC}"
vagrant ssh -c "cat > /tmp/temp_file.txt" < "$SOURCE_FILE_ABS"

# Move the file to the target location
echo -e "${YELLOW}Moving file to target location...${NC}"
vagrant ssh -c "sudo mv /tmp/temp_file.txt $TARGET_PATH && sudo chmod 0755 $TARGET_PATH"

# Verify the file injection
echo -e "${YELLOW}Verifying file injection...${NC}"
vagrant ssh -c "ls -la $TARGET_PATH"

# Read the file content
echo -e "${YELLOW}Reading file content...${NC}"
vagrant ssh -c "cat $TARGET_PATH"

echo -e "${GREEN}File injection completed successfully!${NC}"
