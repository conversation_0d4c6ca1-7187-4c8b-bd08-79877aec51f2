# Cachet Dark Mode & Service Icons - Build-Time Management

This document explains how the Cachet dark mode theme and service icons are managed at build time for the TurdParty project.

## 🎨 Overview

The TurdParty project includes a custom Cachet status page with:
- **🌙 Dark mode theme** - Professional dark interface with improved contrast
- **🎯 Service icons** - SVG icons for each service type
- **😊 Emoji service names** - Enhanced visual appeal
- **📊 Organized groups** - Services grouped by function
- **📱 Responsive design** - Mobile-friendly interface

## 🏗️ Build-Time Architecture

### Directory Structure
```
.dockerwrapper/
├── cachet-customization/
│   ├── Dockerfile                    # Custom Cachet image
│   ├── dark-theme.css               # Dark mode stylesheet
│   ├── service-icons.js             # Service icons JavaScript
│   ├── customize-layout.sh          # Layout customization script
│   ├── init-cachet-customization.sh # Database initialization
│   └── entrypoint-custom.sh         # Custom container entrypoint
├── docker-compose.cachet-custom.yml # Standalone custom Cachet
├── build-custom-cachet.sh           # Build script
├── integrate-custom-cachet.sh       # Integration script
└── start-turdparty-with-custom-cachet.sh # Integrated startup
```

## 🔧 Build Process

### 1. Custom Docker Image
The customizations are baked into a custom Docker image that extends `cachethq/docker:latest`:

```dockerfile
FROM cachethq/docker:latest

# Copy theme files
COPY dark-theme.css /var/www/html/public/css/dark-theme.css
COPY service-icons.js /var/www/html/public/js/service-icons.js

# Copy and setup customization scripts
COPY customize-layout.sh /usr/local/bin/
COPY init-cachet-customization.sh /usr/local/bin/
COPY entrypoint-custom.sh /usr/local/bin/

# Set custom entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint-custom.sh"]
```

### 2. Automatic Initialization
The custom entrypoint handles:
- **Database setup** - Creates component groups and default components
- **Layout modification** - Injects CSS and JS references into Blade templates
- **Cache management** - Clears caches after customizations

### 3. Theme Integration
The dark theme is automatically applied through:
- **CSS injection** - Dark theme stylesheet linked in `<head>`
- **JavaScript injection** - Service icons script loaded with `defer`
- **Immediate styling** - Inline CSS ensures dark theme loads instantly

## 🚀 Usage

### Quick Start
```bash
# Build and start custom Cachet
cd .dockerwrapper
./build-custom-cachet.sh

# Or integrate into main setup
./integrate-custom-cachet.sh
./start-turdparty-with-custom-cachet.sh
```

### Manual Build
```bash
# Build custom image
cd .dockerwrapper
docker build -t turdparty/cachet-custom:latest ./cachet-customization/

# Start with custom compose
docker compose -f docker-compose.cachet-custom.yml up -d
```

### Integration with Main Setup
```bash
# Integrate custom Cachet into main docker-compose.yml
cd .dockerwrapper
./integrate-custom-cachet.sh

# Start all services with custom Cachet
docker compose up -d
```

## 🎨 Customization Details

### Dark Theme Features
- **Improved contrast** - Text colors optimized for readability
- **Comprehensive coverage** - All UI elements styled for dark mode
- **Bottom bar fix** - Footer and navigation elements properly themed
- **Mobile responsive** - Dark theme works on all screen sizes

### Service Icons
- **Automatic detection** - Icons added based on service names
- **Emoji support** - Works with both emoji and plain service names
- **Dynamic updates** - Icons added to dynamically loaded content
- **Fallback support** - Graceful degradation if icons fail to load

### Component Organization
- **🌐 Frontend Services** - API, Frontend
- **⚙️ Worker Services** - Redis, Celery workers, Flower
- **🗄️ Backend Services** - PostgreSQL, MinIO

## 🔄 Status Monitoring

The build includes integration with real-time status monitoring:

```bash
# Update service statuses
nix-shell -p python3 -p python3Packages.requests --run "python3 scripts/update_cachet_status.py"

# Monitor service health
nix-shell -p python3 -p python3Packages.requests --run "python3 scripts/cachet_service_manager.py"
```

## 🛠️ Development

### Modifying the Theme
1. Edit `.dockerwrapper/cachet-customization/dark-theme.css`
2. Rebuild the image: `./build-custom-cachet.sh`
3. Restart services: `docker compose restart cachet`

### Adding New Service Icons
1. Edit `.dockerwrapper/cachet-customization/service-icons.js`
2. Add new service to the `serviceIcons` object
3. Rebuild and restart

### Database Customizations
1. Edit `.dockerwrapper/cachet-customization/init-cachet-customization.sh`
2. Add new component groups or components
3. Rebuild the image

## 🔍 Troubleshooting

### Theme Not Loading
```bash
# Check if CSS file exists
curl http://localhost:3501/css/dark-theme.css

# Check container logs
docker logs turdparty_cachet

# Rebuild image
./build-custom-cachet.sh
```

### Icons Not Appearing
```bash
# Check if JS file exists
curl http://localhost:3501/js/service-icons.js

# Check browser console for JavaScript errors
# Verify service names match the icon mapping
```

### Database Issues
```bash
# Check database connection
docker exec turdparty_postgres_cachet psql -U postgres -d cachet -c "SELECT 1;"

# Reinitialize database
docker exec turdparty_cachet /usr/local/bin/init-cachet-customization.sh
```

## 📊 Monitoring

### Health Checks
The custom Cachet includes health checks:
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost/api/v1/ping"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 60s
```

### Log Monitoring
```bash
# View initialization logs
docker logs turdparty_cachet | grep "customization"

# Monitor real-time logs
docker logs -f turdparty_cachet
```

## 🎯 Benefits of Build-Time Management

1. **Consistency** - Theme always applied, no manual intervention needed
2. **Performance** - No runtime theme switching overhead
3. **Reliability** - Customizations survive container restarts
4. **Maintainability** - All customizations version-controlled
5. **Scalability** - Easy to deploy across environments

## 🔮 Future Enhancements

- **Theme variants** - Multiple color schemes
- **Custom branding** - Logo and color customization
- **Advanced monitoring** - Integration with Prometheus/Grafana
- **API automation** - Automatic service registration
- **Backup/restore** - Configuration backup system

---

**Note**: This build-time approach ensures that all Cachet customizations are properly managed, version-controlled, and automatically applied without requiring manual intervention after container startup.
