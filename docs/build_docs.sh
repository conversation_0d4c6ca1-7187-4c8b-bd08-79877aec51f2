#!/bin/bash
# Script to build the documentation

set -e

# Change to the docs directory
cd "$(dirname "$0")"

# Check if Sphinx is installed
if ! command -v sphinx-build &> /dev/null; then
    echo "Sphinx is not installed. Installing required packages..."
    pip install -r requirements.txt
fi

# Build the HTML documentation
echo "Building HTML documentation..."
make html

# Check if the build was successful
if [ $? -eq 0 ]; then
    echo "HTML documentation built successfully."
    echo "You can view it by opening build/html/index.html in your browser."
else
    echo "Error building HTML documentation."
    exit 1
fi

# Ask if PDF documentation should be built
read -p "Do you want to build the PDF documentation? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Building PDF documentation..."
    make pdf
    
    # Check if the build was successful
    if [ $? -eq 0 ]; then
        echo "PDF documentation built successfully."
        echo "You can find it at build/pdf/TurdParty_Documentation.pdf."
    else
        echo "Error building PDF documentation."
        exit 1
    fi
fi

echo "Documentation build complete."
