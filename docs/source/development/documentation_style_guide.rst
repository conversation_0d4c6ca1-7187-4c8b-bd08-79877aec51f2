Documentation Style Guide
========================

This guide provides guidelines for writing documentation for TurdParty.

File Formats
-----------

TurdParty documentation supports two file formats:

- **reStructuredText (.rst)**: The native format for Sphinx documentation
- **Markdown (.md)**: Supported through the MyST parser extension

When to use each format:

- Use **.rst** for:
  - Index files
  - Complex documentation with advanced features
  - Documentation that requires cross-references
  
- Use **.md** for:
  - Simple documentation
  - Documentation that already exists in Markdown format
  - Documentation that needs to be readable on GitHub

Structure
--------

Documentation should be organized into logical sections:

- **Getting Started**: Installation and basic usage
- **Architecture**: System architecture and design
- **Development**: Development guides and procedures
- **Deployment**: Deployment instructions
- **Troubleshooting**: Common issues and solutions
- **User Guides**: Detailed guides for users
- **Reference**: Reference materials and API documentation

Each section should have an index file (index.rst) that lists the documents in that section.

Style Guidelines
--------------

1. **Headings**:
   - Use title case for headings
   - Use consistent heading levels
   - In RST, use the following heading styles:
     - ``=`` for top-level headings (with overline)
     - ``=`` for section headings
     - ``-`` for subsection headings
     - ``^`` for subsubsection headings
     - ``"`` for paragraph headings

2. **Code Blocks**:
   - Use syntax highlighting for code blocks
   - Specify the language for code blocks
   - In RST:
     
     .. code-block:: python
        
        def example_function():
            """This is an example function."""
            return True
   
   - In Markdown:
     
     ```python
     def example_function():
         """This is an example function."""
         return True
     ```

3. **Links**:
   - Use relative links for internal documentation
   - Use absolute links for external resources
   - In RST, use:
     
     .. code-block:: rst
        
        :doc:`relative/path/to/document`
        `External Link <https://example.com>`_
   
   - In Markdown:
     
     ```markdown
     [Document Title](relative/path/to/document.md)
     [External Link](https://example.com)
     ```

4. **Images**:
   - Store images in the ``_static`` directory
   - Use relative paths for images
   - Specify alt text for all images
   - In RST:
     
     .. code-block:: rst
        
        .. image:: /_static/image.png
           :alt: Description of image
           :width: 400px
   
   - In Markdown:
     
     ```markdown
     ![Description of image](/_static/image.png)
     ```

5. **Notes and Warnings**:
   - Use admonitions for notes, warnings, and tips
   - In RST:
     
     .. code-block:: rst
        
        .. note::
           This is a note.
        
        .. warning::
           This is a warning.
        
        .. tip::
           This is a tip.
   
   - In Markdown (with MyST extensions):
     
     ```markdown
     ```{note}
     This is a note.
     ```
     
     ```{warning}
     This is a warning.
     ```
     
     ```{tip}
     This is a tip.
     ```
     ```

Building Documentation
--------------------

To build the documentation:

1. Install the required dependencies:
   
   .. code-block:: bash
      
      pip install -r docs/requirements.txt

2. Build the HTML documentation:
   
   .. code-block:: bash
      
      cd docs
      make html

3. Build the PDF documentation:
   
   .. code-block:: bash
      
      cd docs
      make pdf

4. View the documentation:
   - HTML: Open ``docs/build/html/index.html`` in a web browser
   - PDF: Open ``docs/build/pdf/TurdParty_Documentation.pdf`` in a PDF viewer
