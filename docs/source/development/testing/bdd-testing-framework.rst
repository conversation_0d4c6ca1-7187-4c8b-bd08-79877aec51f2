BDD Testing Framework
=====================

The TurdParty BDD (Behavior-Driven Development) Testing Framework provides **comprehensive user experience flow validation** using **Behave** for **industry-leading behavior-driven testing**.

Overview
--------

The BDD Testing Framework implements **Gherkin-based scenarios** that validate user experience flows across the entire TurdParty platform:

- **User Authentication Workflows** - Complete authentication and authorization flows
- **File Management Workflows** - File upload, processing, and management flows
- **VM Management Workflows** - Virtual machine lifecycle and monitoring flows
- **Multilingual User Experience** - 37 languages with international UX validation
- **End-to-End Workflows** - Complete cybersecurity analysis workflows

Framework Architecture
---------------------

Directory Structure
~~~~~~~~~~~~~~~~~~

.. code-block:: text

   tests/behave/
   ├── features/
   │   ├── user_authentication.feature          # Authentication workflows
   │   ├── file_management_workflow.feature     # File management flows
   │   ├── vm_management_workflow.feature       # VM management flows
   │   ├── multilingual_user_experience.feature # Multilingual UX testing
   │   └── end_to_end_workflows.feature        # Complete analysis workflows
   ├── steps/
   │   ├── authentication_steps.py              # Auth behavior implementations
   │   ├── file_management_steps.py             # File behavior implementations
   │   ├── vm_management_steps.py               # VM behavior implementations
   └── multilingual_steps.py                # Multilingual implementations
   ├── environment.py                           # Test environment setup
   ├── run_behave_tests.py                      # Advanced BDD test runner
   ├── behave.ini                               # Configuration management
   ├── requirements.txt                         # Comprehensive dependencies
   └── README.md                                # Framework documentation

Feature Files Overview
~~~~~~~~~~~~~~~~~~~~~

**1. User Authentication (user_authentication.feature)**

- Successful/Failed Login Flows
- API Token Authentication
- Session Management & Timeouts
- Multilingual Login Interfaces
- Security & Brute Force Protection
- Accessibility & Mobile Support
- Multi-Factor Authentication

**2. File Management Workflow (file_management_workflow.feature)**

- Single File Upload Scenarios
- Drag-and-Drop Upload Flows
- Bulk File Upload Operations
- File Search & Filtering
- Security Validation & Quarantine
- Download & Export Operations
- Permission Management & Sharing

**3. VM Management Workflow (vm_management_workflow.feature)**

- VM Creation & Configuration
- Template Selection & Customization
- VM Lifecycle Management
- Performance Monitoring
- Security Isolation & Network Controls
- File Injection & Analysis
- Snapshot Management & Recovery

**4. Multilingual User Experience (multilingual_user_experience.feature)**

- 37 Language Workflow Testing
- Dynamic Language Switching
- Form Validation in Multiple Languages
- Content Localization & Cultural Adaptation
- Unicode & Special Character Support
- Performance Impact Analysis
- EU Compliance Validation

**5. End-to-End Workflows (end_to_end_workflows.feature)**

- Complete Malware Analysis Workflows
- Team Collaboration Scenarios
- Emergency Incident Response
- Bulk Processing & Automation
- Compliance & Audit Trail Workflows
- Integration with External Systems
- Disaster Recovery & Business Continuity

Quick Start
----------

Prerequisites
~~~~~~~~~~~~

.. code-block:: bash

   # Install Python dependencies
   cd tests/behave
   pip install -r requirements.txt

   # Install Chrome browser (for Selenium)
   # Ubuntu/Debian:
   sudo apt-get install google-chrome-stable

   # macOS:
   brew install --cask google-chrome

Run All BDD Tests
~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run comprehensive Behave BDD test suite
   make test-behave

   # Generate detailed BDD test report
   make test-behave-report

Run Specific Test Categories
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # User interface and interaction tests
   make test-behave-ui

   # Authentication workflow tests
   make test-behave-auth

   # File management workflow tests
   make test-behave-files

   # VM management workflow tests
   make test-behave-vms

   # Multilingual user experience tests
   make test-behave-multilingual

   # End-to-end workflow tests
   make test-behave-e2e

Browser Mode Testing
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run tests in headless browser mode (default)
   make test-behave-headless

   # Run tests with visible browser (for debugging)
   make test-behave-headed

Feature File Examples
--------------------

User Authentication Feature
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: gherkin

   Feature: User Authentication and Authorization
     As a cybersecurity professional
     I want to securely authenticate and access the TurdParty platform
     So that I can perform malware analysis tasks with proper authorization

     @authentication @ui @critical
     Scenario: Successful user login through web interface
       Given I am on the login page
       When I enter valid credentials
         | username | <EMAIL> |
         | password | password123       |
       And I click the login button
       Then I should be redirected to the dashboard
       And I should see a welcome message
       And my session should be authenticated

File Management Workflow Feature
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: gherkin

   Feature: File Management and Analysis Workflow
     As a cybersecurity analyst
     I want to upload, manage, and analyze files through the TurdParty platform
     So that I can efficiently process malware samples and suspicious files

     @file_upload @ui @critical
     Scenario: Upload a single file through web interface
       Given I am on the file upload page
       When I select a file to upload
         | filename        | type      | size    |
         | malware.exe     | binary    | 2.5 MB  |
       And I provide file metadata
         | description | Suspicious executable from email attachment |
         | source      | Email investigation                         |
         | priority    | High                                        |
       And I click the upload button
       Then the file should be uploaded successfully
       And I should see a success confirmation
       And the file should appear in my files list

Multilingual User Experience Feature
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: gherkin

   Feature: Multilingual User Experience
     As an international cybersecurity professional
     I want to use the TurdParty platform in my native language
     So that I can efficiently perform malware analysis without language barriers

     @multilingual @ui @european_languages
     Scenario Outline: Complete workflow in European languages
       Given the interface language is set to "<language>"
       When I perform a complete malware analysis workflow
         | step                | action                           |
         | login               | authenticate with credentials    |
         | file_upload         | upload suspicious file           |
         | vm_creation         | create analysis virtual machine  |
         | analysis_execution  | run malware analysis             |
         | report_generation   | generate analysis report         |
       Then all interface elements should be in "<language>"
       And all functionality should work correctly
       And error messages should be in "<language>"
       
       Examples: Germanic Languages
         | language      |
         | German        |
         | English       |
         | Dutch         |
         | Swedish       |
         | Danish        |

Step Definitions
---------------

Authentication Steps
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @given('I am on the login page')
   def step_on_login_page(context):
       """Navigate to the login page."""
       login_url = f"{context.frontend_url}/login"
       context.driver.get(login_url)
       
       WebDriverWait(context.driver, context.test_timeout).until(
           EC.presence_of_element_located((By.TAG_NAME, "body"))
       )

   @when('I enter valid credentials')
   def step_enter_valid_credentials(context):
       """Enter valid login credentials."""
       credentials = {}
       for row in context.table:
           credentials[row['username']] = row['password']
       
       username_field = context.driver.find_element(By.CSS_SELECTOR, 
           "input[name='username'], input[name='email']")
       username_field.clear()
       username_field.send_keys(list(credentials.keys())[0])
       
       password_field = context.driver.find_element(By.CSS_SELECTOR, 
           "input[name='password'], input[type='password']")
       password_field.clear()
       password_field.send_keys(list(credentials.values())[0])

File Management Steps
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   @given('I am on the file upload page')
   def step_on_file_upload_page(context):
       """Navigate to the file upload page."""
       upload_url = f"{context.frontend_url}/file_upload"
       context.driver.get(upload_url)
       
       WebDriverWait(context.driver, context.test_timeout).until(
           EC.presence_of_element_located((By.TAG_NAME, "body"))
       )

   @when('I select a file to upload')
   def step_select_file_to_upload(context):
       """Select a file for upload based on the provided specifications."""
       file_specs = {}
       for row in context.table:
           file_specs['filename'] = row['filename']
           file_specs['type'] = row['type']
           file_specs['size'] = row['size']
       
       # Determine which test file to use based on type
       if file_specs['type'] == 'binary':
           selected_file = context.test_files['appimage']
       elif file_specs['type'] == 'document':
           selected_file = context.test_files['json']
       else:
           selected_file = context.test_files['text']
       
       context.selected_file = selected_file

Configuration
------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Browser Configuration
   export HEADLESS=true                     # Run in headless mode
   export BROWSER=chrome                    # Browser type (chrome, firefox)
   export TIMEOUT=30                        # Default timeout in seconds

   # Application URLs
   export API_URL=http://localhost:3050     # API base URL
   export FRONTEND_URL=http://localhost:3000 # Frontend base URL

   # Test Configuration
   export SCREENSHOT_ON_FAILURE=true       # Take screenshots on failure
   export TEST_DATA_DIR=./test_data        # Test data directory
   export REPORTS_DIR=./reports             # Reports output directory

Behave Configuration (behave.ini)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: ini

   [behave]
   # Test discovery and execution
   paths = features
   step_definitions = steps

   # Output formatting
   format = pretty
   outfiles = reports/behave_results.txt
   junit = true
   junit_directory = reports/junit

   # Tag configuration for test organization
   default_tags = -@wip -@skip

   # Browser and environment configuration
   browser = chrome
   headless = true
   timeout = 30

Test Tags and Organization
-------------------------

Functional Tags
~~~~~~~~~~~~~

- ``@authentication`` - Authentication and authorization tests
- ``@file_upload`` - File upload and management tests
- ``@vm_management`` - Virtual machine management tests
- ``@multilingual`` - Multilingual and internationalization tests
- ``@e2e`` - End-to-end workflow tests

Technical Tags
~~~~~~~~~~~~~

- ``@ui`` - User interface interaction tests
- ``@api`` - API integration tests
- ``@security`` - Security and vulnerability tests
- ``@performance`` - Performance and load tests
- ``@accessibility`` - Accessibility compliance tests

Priority Tags
~~~~~~~~~~~~

- ``@critical`` - Critical functionality tests
- ``@high`` - High priority tests
- ``@medium`` - Medium priority tests
- ``@low`` - Low priority tests

Browser Tags
~~~~~~~~~~~

- ``@browser`` - Browser-based tests
- ``@mobile`` - Mobile device tests
- ``@responsive`` - Responsive design tests

Test Reporting
-------------

Report Types Generated
~~~~~~~~~~~~~~~~~~~~~

- **Pretty Console Output** - Real-time test execution feedback
- **JSON Reports** - Machine-readable detailed results
- **JUnit XML** - CI/CD integration compatible reports
- **HTML Reports** - Visual test results and screenshots
- **Allure Reports** - Advanced test analytics and trends

Screenshot Management
~~~~~~~~~~~~~~~~~~~~

- **Automatic Screenshots** - Captured on test failures
- **Step Screenshots** - Optional screenshots for each step
- **Comparison Screenshots** - Visual regression testing
- **Mobile Screenshots** - Device-specific screenshot capture

Advanced Features
----------------

Parallel Execution
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run tests in parallel (requires behave-parallel)
   cd tests/behave && python run_behave_tests.py --parallel

Custom Test Runner
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run with custom environment
   cd tests/behave && python run_behave_tests.py --environment staging

   # Run specific tags
   cd tests/behave && python run_behave_tests.py --tags @critical,@ui

   # Run with verbose output
   cd tests/behave && python run_behave_tests.py --verbose

Debugging Support
~~~~~~~~~~~~~~~~

- **Interactive Debugging** - Step-by-step test execution
- **Screenshot Capture** - Visual debugging and verification
- **Browser DevTools** - Network and console log analysis
- **Verbose Logging** - Detailed execution information

The TurdParty BDD Testing Framework establishes **industry leadership** in behavior-driven testing, ensuring **exceptional user experience validation** and **international excellence** for the cybersecurity platform.
