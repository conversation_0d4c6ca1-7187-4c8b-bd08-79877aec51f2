# VM Injection Integration Testing PRD

## 1. Executive Summary

This Product Requirements Document (PRD) outlines the specifications and testing requirements for verifying the end-to-end workflow from file upload to successful VM injection using the recently fixed authentication system in TurdParty. The integration testing will ensure that files uploaded through the API can be successfully injected into Vagrant VMs, with proper user authentication and authorization throughout the process.

## 2. Purpose and Scope

### 2.1 Purpose
To verify that the file upload functionality and VM injection system work correctly together with the fixed authentication mechanisms, ensuring a seamless user experience from file upload to file injection in virtual machines.

### 2.2 Scope
This testing covers:
- Authentication token generation and validation
- File upload via API endpoints
- File retrieval and listing
- VM selection and status verification
- File injection into VMs
- Status reporting of the injection process

## 3. Target Users and Use Cases

### 3.1 Target Users
- Security researchers analyzing malware samples
- Developers testing applications in isolated environments
- QA engineers performing automated testing
- System administrators deploying configurations to VMs

### 3.2 Primary Use Cases
1. Upload a file and inject it into a running VM for analysis
2. Upload multiple files/folder and inject the contents into a VM
3. Upload a configuration file and apply it to multiple VMs
4. Upload a testing script and execute it within a VM environment

## 4. Functional Requirements

### 4.1 Authentication Requirements
- **REQ-AUTH-01:** System must authenticate users via the `/api/v1/auth/test-token` endpoint
- **REQ-AUTH-02:** Authentication tokens must contain a valid user ID that exists in the database
- **REQ-AUTH-03:** All file operations must validate the token and associate uploaded files with the correct user

### 4.2 File Upload Requirements
- **REQ-UPLOAD-01:** System must support uploading single files via `/api/v1/file_upload/`
- **REQ-UPLOAD-02:** System must support uploading multiple files with folder structure via `/api/v1/file_upload/folder/`
- **REQ-UPLOAD-03:** File metadata must be correctly stored in the database with proper owner_id
- **REQ-UPLOAD-04:** Uploaded files must be retrievable via the download endpoint

### 4.3 VM Injection Requirements
- **REQ-INJECT-01:** System must allow selection of target VM for injection
- **REQ-INJECT-02:** System must verify VM status before attempting injection
- **REQ-INJECT-03:** System must transfer files from storage to VM
- **REQ-INJECT-04:** System must report injection status and results
- **REQ-INJECT-05:** System must handle errors during injection gracefully

### 4.4 Integration Requirements
- **REQ-INT-01:** End-to-end workflow must maintain consistent user context
- **REQ-INT-02:** File ownership must be preserved through the entire workflow
- **REQ-INT-03:** Status updates must be available at each step of the process
- **REQ-INT-04:** System must handle large files and multiple concurrent operations

## 5. Test Scenarios

### 5.1 Basic End-to-End Flow

#### 5.1.1 Test Steps
1. Obtain authentication token using test user endpoint
2. Upload a single test file with description
3. Verify file metadata in database
4. List available VMs
5. Select a running VM
6. Initiate file injection to the VM
7. Monitor injection status
8. Verify file presence in the VM

#### 5.1.2 Expected Results
- Authentication token received with status 200
- File upload successful with status 201
- File appears in user's uploaded files list
- Injection initiated with status 200
- Injection status shows "completed"
- File exists in the target location in VM

### 5.2 Folder Structure Injection

#### 5.2.1 Test Steps
1. Obtain authentication token
2. Create a test folder with multiple files and subfolders
3. Upload the folder structure
4. Verify folder metadata in database
5. Select a running VM
6. Initiate folder injection to the VM
7. Monitor injection status
8. Verify folder structure in the VM

#### 5.2.2 Expected Results
- Authentication token received with status 200
- Folder upload successful with status 201
- Folder structure appears in user's uploaded files list
- Injection initiated with status 200
- Injection status shows "completed"
- Full folder structure exists in the target location in VM

### 5.3 Error Handling and Edge Cases

#### 5.3.1 VM Not Running
Test injection when target VM is not running, expect appropriate error message and status code

#### 5.3.2 Large File Injection
Test injection of files >100MB, verify correct handling and progress reporting

#### 5.3.3 Concurrent Injections
Test multiple simultaneous injections to the same VM, verify correct handling and isolation

#### 5.3.4 Special Characters in Filenames
Test injection of files with special characters in names, verify correct handling in VM filesystem

#### 5.3.5 Injection with Invalid Permissions
Test injection using a token for a user who doesn't own the files, expect authorization error

## 6. Technical Implementation

### 6.1 Testing Environment
- Docker-based testing environment with:
  - API container
  - Database container (PostgreSQL)
  - MinIO container for file storage
  - 2-3 Vagrant VM instances for injection targets
  - Test runner container

### 6.2 Test Automation
- Implement automated test suite using pytest for backend and Playwright for frontend
- Create Docker-compose file for test environment setup
- Develop cleanup scripts to reset the environment between test runs

### 6.3 Monitoring and Logging
- Capture detailed logs at each step of the process
- Monitor database state during workflow execution
- Capture VM state and contents before and after injection
- Record performance metrics for each operation

### 6.4 Test Data
- Create varied test files including:
  - Text files (config files, scripts)
  - Binary files (executables, libraries)
  - Large files (>100MB)
  - Complex folder structures with varying depths
  - Files with special characters in names

## 7. Success Criteria

### 7.1 Functionality Success Criteria
- All test scenarios pass consistently
- End-to-end workflow completes successfully in 95% of test runs
- Error cases are handled gracefully with appropriate error messages
- File integrity is maintained throughout the process (verified by hash comparison)
- VM state is consistent after injection

### 7.2 Performance Success Criteria
- File upload to injection completion time < 60 seconds for files < 10MB
- System can handle at least 5 concurrent injection processes
- No memory leaks or resource exhaustion during extended testing
- VM performance not significantly impacted by injection process

### 7.3 Security Success Criteria
- All operations require valid authentication
- Files can only be injected by their owner
- No access to other users' files
- Failed authentication attempts are properly logged
- Injection operations leave no sensitive data exposed

## 8. Testing Timeline

### 8.1 Phase 1: Test Environment Setup (2 days)
- Configure Docker containers for testing
- Create base VM templates for injection targets
- Set up test database with known test users

### 8.2 Phase 2: Test Implementation (3 days)
- Develop automated test scripts for all scenarios
- Create test data generation utilities
- Implement logging and monitoring for tests

### 8.3 Phase 3: Test Execution and Analysis (2 days)
- Run all test scenarios
- Document results and any issues
- Analyze performance and reliability metrics

### 8.4 Phase 4: Issue Resolution and Regression Testing (3 days)
- Address any identified issues
- Re-run tests to verify fixes
- Document final test results

## 9. Resources Required

### 9.1 Personnel
- 1 Backend Developer (API and VM Injection)
- 1 QA Engineer (Test Automation)
- 1 DevOps Engineer (Test Environment Setup)

### 9.2 Infrastructure
- Development server with sufficient resources for multiple VMs
- CI/CD pipeline integration
- Storage for test results and logs

### 9.3 Tools
- pytest for backend testing
- Playwright for frontend testing
- Docker and Docker Compose for environment management
- PostgreSQL for test database
- MinIO for file storage
- Vagrant for VM management

## 10. Risks and Mitigations

### 10.1 Risks
- VM instability during testing
- Resource constraints when testing large files
- Test environment inconsistencies
- Foreign key constraints with test user data

### 10.2 Mitigations
- Implement VM snapshotting to restore clean state
- Scale test environment resources as needed
- Use consistent Docker environment setup scripts
- Ensure test users are properly created with fixed UUIDs

## 11. Dependencies

- Functioning file upload API with fixed authentication
- Operational Vagrant VM system
- MinIO storage configured and accessible
- Database with necessary schema (including the "user" view)
- Test user accounts created with known UUIDs

## 12. Documentation Requirements

- Detailed test plan with scenarios and expected results
- Test environment setup instructions
- API documentation updates for VM injection endpoints
- Test result reports with metrics
- Troubleshooting guide for common testing issues

## 13. Approval and Stakeholders

### 13.1 Approvers
- Engineering Manager
- Product Manager
- Security Lead

### 13.2 Stakeholders
- Development Team
- QA Team
- DevOps Team
- End Users (for feedback on workflow) 