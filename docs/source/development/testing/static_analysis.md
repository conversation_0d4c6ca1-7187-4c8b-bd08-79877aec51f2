
# Static Analysis Providers

This document outlines the third-party services integrated into our static analysis system, including their rate limits and our caching strategy.

## VirusTotal

VirusTotal is a service that analyzes files and URLs for malicious content. It aggregates multiple antivirus engines and website scanners to check for viruses, worms, trojans, and other kinds of malicious content.

### API Endpoints Used

- **File Hash Lookup**: `GET https://www.virustotal.com/api/v3/files/{hash}`
  - This endpoint retrieves analysis results for a specific file hash (MD5, SHA-1, or SHA-256).
- **URL Analysis**: `POST https://www.virustotal.com/api/v3/urls`
  - This endpoint submits URLs for analysis.
- **Analysis Report**: `GET https://www.virustotal.com/api/v3/analyses/{id}`
  - This endpoint retrieves detailed analysis reports.

### Rate Limits

VirusTotal's public API has the following rate limits:

- **Free API Key**: 4 requests per minute, 500 requests per day
- **Premium API Key**: Varies by subscription level, typically 1000+ requests per day

### Caching Strategy

Due to the strict rate limits, we implement the following caching strategy:

1. **Database Caching**: All VirusTotal lookup results are stored in the database using the `HashReport` model
2. **Cache Duration**: Reports are considered valid for 7 days by default
3. **Cache Invalidation**: Users can force a refresh of a cached report if needed, but this should be rate-limited
4. **Failed Lookups**: Even failed lookups are cached with the error message to prevent repeated attempts for invalid hashes

### Implementation Notes

- Always check the database for an existing report before making an API call
- Implement exponential backoff when rate limits are encountered
- Monitor API usage to avoid hitting daily limits
- Consider batch processing for multiple hash lookups to optimize API usage

## Current Providers

### MetaDefender

[MetaDefender](https://www.opswat.com/products/metadefender) is another multi-scanning engine that provides comprehensive malware analysis.

### API Endpoints Used

- **Hash Lookup**: `GET https://api.metadefender.com/v4/hash/{hash}`
- **File Upload**: `POST https://api.metadefender.com/v4/file`

### Rate Limits

- **Free tier**: 10 lookups per day
- **Professional tiers**: Various levels up to unlimited 

### Caching Strategy

The same caching strategy as VirusTotal is applied, with reports stored in the database.

### Implementation Notes

- Uses the same database model as VirusTotal with a different provider field
- Results are normalized to a common format for consistent UI display

## Future Providers

### HybridAnalysis (Planned)

[Hybrid Analysis](https://www.hybrid-analysis.com/) provides advanced malware analysis and can be used to complement VirusTotal results.

### Planned Endpoints

- **Search Hash**: `POST https://www.hybrid-analysis.com/api/v2/search/hash`
- **Submit File**: `POST https://www.hybrid-analysis.com/api/v2/submit/file`

### Rate Limits

- Free API: Limited to 100 requests per day
- Commercial API: Based on subscription level

### Caching Strategy

Will follow the same caching approach as other providers.

## ReversingLabs (Planned)

[ReversingLabs](https://www.reversinglabs.com/) offers advanced file analysis and threat intelligence.

## Best Practices for Static Analysis Services

1. **Always Cache Results**: Due to rate limits on most static analysis APIs, cache results aggressively
2. **Normalize Data**: Convert the different API responses to a common format in our application
3. **Handle Errors Gracefully**: Implement comprehensive error handling for API failures
4. **Respect Rate Limits**: Implement proper backoff mechanisms to avoid being blocked
5. **Provide Alternatives**: When one service is unavailable, try to fall back to another service

## Implementation Guidelines

When adding new static analysis providers:

1. Create a dedicated service class that handles API communication
2. Update the database models to store results with provider information
3. Implement proper caching and error handling
4. Add the provider to the `StaticAnalysisService` main class
5. Update UI components to display the new provider's results
6. Write comprehensive tests for the new integration

## Monitoring and Usage Statistics

A monitoring dashboard is available for tracking API usage and cache effectiveness:

- Number of API calls per provider
- Cache hit/miss ratio
- Error rates
- Daily quota usage

## Rate Limit Handling

The system implements a token bucket algorithm for rate limiting:

1. Each provider has a separate bucket of tokens
2. Tokens regenerate based on the provider's rate limits
3. Each request consumes one token
4. When no tokens are available, requests are queued or rejected

This ensures fair usage of API resources and prevents rate limit violations.
