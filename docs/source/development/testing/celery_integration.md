# Celery Integration Testing

This document describes how to run and write integration tests for the Celery task processing system in TurdParty.

## Overview

Celery integration tests verify that:

1. Celery tasks can be created and executed
2. Task results are properly stored and retrieved
3. Task status updates are correctly tracked
4. Workers process tasks from the correct queues
5. Error handling and retries work as expected

## Test Environment

The Celery integration tests use a Docker-based test environment with the following components:

- `turdparty_redis` - Redis broker and result backend
- `turdparty_db` - PostgreSQL database for task status storage
- `turdparty_minio` - MinIO object storage for file operations
- `turdparty_celery_file_ops` - Celery worker for file operations
- `turdparty_celery_vm_ops` - Celery worker for VM operations
- `turdparty_celery_monitoring` - Celery worker for monitoring operations
- `turdparty_api` - FastAPI application that creates tasks
- `turdparty_test_runner` - Container for running tests

## Running Celery Integration Tests

### Running All Celery Tests

To run all Celery integration tests:

```bash
cd /path/to/turdparty
./run_celery_tests.sh
```

This script will:
1. Start the Docker test environment
2. Run all Celery integration tests
3. Display the test results
4. Keep the environment running for debugging

### Running a Specific Celery Test

To run a specific Celery integration test:

```bash
cd /path/to/turdparty
.dockerwrapper/run_single_test.sh tests/integration/test_celery_integration.py::test_get_content_type
```

## Writing Celery Integration Tests

### Test Structure

Celery integration tests are located in `tests/integration/test_celery_integration.py`. Each test function should:

1. Create a task
2. Wait for the task to complete
3. Verify the task result
4. Check the task status in the database

### Example Test

```python
import pytest
from tasks.file_tasks import get_content_type

@pytest.mark.skipif(not os.environ.get("IN_DOCKER"), reason="Test requires Docker environment")
def test_get_content_type():
    """Test the get_content_type task."""
    # Create a test file
    file_path = "test.txt"
    
    # Call the task
    content_type = get_content_type(file_path)
    
    # Verify the result
    assert content_type == "text/plain"
    
    # Create a PDF file
    file_path = "test.pdf"
    
    # Call the task
    content_type = get_content_type(file_path)
    
    # Verify the result
    assert content_type == "application/pdf"
```

### Testing Asynchronous Tasks

For asynchronous tasks, you need to:

1. Submit the task
2. Get the task ID
3. Wait for the task to complete
4. Check the task result

```python
import pytest
import time
from tasks.file_tasks import upload_file

@pytest.mark.skipif(not os.environ.get("IN_DOCKER"), reason="Test requires Docker environment")
def test_upload_file():
    """Test the upload_file task."""
    # Create a test file
    file_path = "test.txt"
    with open(file_path, "w") as f:
        f.write("Test content")
    
    # Submit the task
    task = upload_file.delay(file_path, "text/plain")
    task_id = task.id
    
    # Wait for the task to complete
    max_wait = 30  # seconds
    start_time = time.time()
    while time.time() - start_time < max_wait:
        if task.ready():
            break
        time.sleep(1)
    
    # Check the task status
    assert task.successful()
    
    # Verify the result
    result = task.get()
    assert result["status"] == "success"
    assert "file_id" in result
```

## Troubleshooting

### Common Issues

1. **Task Timeout**: If a task takes too long to complete, increase the `max_wait` value in the test.

2. **Worker Not Running**: Ensure that the Celery worker for the task queue is running:
   ```bash
   docker ps | grep turdparty_celery
   ```

3. **Redis Connection Issues**: Check the Redis connection:
   ```bash
   docker exec -it turdparty_redis redis-cli ping
   ```

4. **Database Connection Issues**: Verify the database connection:
   ```bash
   docker exec -it turdparty_db pg_isready -U turdparty
   ```

5. **Missing Dependencies**: If a task fails due to missing dependencies, install them in the worker container:
   ```bash
   docker exec -it turdparty_celery_file_ops pip install <package>
   ```

### Debugging Tips

1. **Check Worker Logs**: View the worker logs to see task execution details:
   ```bash
   docker logs turdparty_celery_file_ops
   ```

2. **Inspect Task Status**: Check the task status in the database:
   ```bash
   docker exec -it turdparty_db psql -U turdparty -d turdparty -c "SELECT * FROM task_status WHERE task_id = '<task_id>';"
   ```

3. **Run with Verbose Output**: Run tests with verbose output:
   ```bash
   .dockerwrapper/run_single_test.sh tests/integration/test_celery_integration.py::test_function -v
   ```

4. **Interactive Debugging**: Enter the test container for interactive debugging:
   ```bash
   docker exec -it turdparty_test_runner bash
   ```
