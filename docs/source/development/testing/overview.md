# TurdParty Testing Guide

This document outlines the testing strategy for the TurdParty project and provides instructions for running tests, analyzing test coverage, and interpreting results.

## Testing Strategy

The TurdParty testing strategy includes:

1. **Unit Testing**: Validates individual components in isolation
2. **Integration Testing**: Validates interactions between components
3. **End-to-End Testing**: Validates complete workflows from UI to backend
4. **Performance Testing**: Measures system performance under various conditions
5. **Security Testing**: Validates application security measures
6. **Test Coverage Analysis**: Ensures comprehensive test coverage

## Test Directory Structure

```
├── api/tests/                     # Backend tests
│   ├── test_minio_wrapper.py      # MinIO client wrapper tests
│   ├── test_file_upload_service.py # File upload service tests
│   ├── test_file_upload_api.py    # File upload API tests
│   ├── test_minio_e2e.py          # End-to-end MinIO tests
│   ├── test_coverage_analysis.py  # Coverage analysis utility
│   └── ...                        # Other backend tests
├── scripts/                       # Test runner scripts
│   ├── run_minio_tests.sh         # MinIO tests runner
│   ├── ci_test_coverage.sh        # CI coverage script
│   ├── perf-test-minio.py         # MinIO performance test
│   └── ...                        # Other test scripts
├── scripts/playwright-tests/      # Frontend UI tests
│   ├── form-inputs.spec.js        # Form input tests
│   ├── ui-components.spec.js      # UI component tests
│   └── ...                        # Other UI tests
└── test-results/                  # Generated test reports
    ├── coverage/                  # Coverage reports
    ├── performance/               # Performance test results
    └── ...                        # Other test results
```

## Prerequisites

Before running tests, ensure you have:

1. Python 3.10+ installed
2. Node.js 16+ installed (for UI tests)
3. Required Python packages:
   ```
   pip install pytest pytest-asyncio pytest-cov coverage httpx
   ```
4. Required Node.js packages:
   ```
   npm install -g playwright
   npx playwright install
   ```
5. MinIO server running and accessible
6. Environment variables set:
   ```
   MINIO_HOST=localhost
   MINIO_PORT=9000
   MINIO_ACCESS_KEY=minioadmin
   MINIO_SECRET_KEY=minioadmin
   ```

## Running Backend Tests

### Using Docker Containers

TurdParty uses Docker containers for testing, with a consistent naming convention:

```
turdparty_<service_name>
```

For example:
- `turdparty_api` - The API service
- `turdparty_db` - The PostgreSQL database
- `turdparty_redis` - The Redis service
- `turdparty_minio` - The MinIO object storage
- `turdparty_test_runner` - The test runner service

To run a single test using the Docker test environment:

```bash
.dockerwrapper/run_single_test.sh tests/path/to/test_file.py::test_function
```

This script will:
1. Start the Docker test environment if it's not already running
2. Run database migrations
3. Initialize MinIO buckets
4. Run the specified test
5. Keep the environment running for debugging

### MinIO Integration Tests

To run all MinIO-related tests:

```bash
./scripts/run_minio_tests.sh
```

This script will:
1. Check MinIO connection
2. Run unit tests for the MinIO wrapper
3. Run tests for the file upload service
4. Run tests for the file upload API
5. Run end-to-end tests
6. Generate coverage reports

To include performance tests:

```bash
./scripts/run_minio_tests.sh --perf
```

### Running Individual Test Modules

To run a specific test module:

```bash
pytest api/tests/test_minio_wrapper.py -v
```

### Running Tests with Coverage Analysis

To run tests with coverage analysis:

```bash
./scripts/ci_test_coverage.sh
```

This script:
1. Runs tests with coverage measurement
2. Checks if coverage meets defined thresholds (80% statement, 70% branch)
3. Generates coverage reports in HTML, XML, and JSON formats
4. Creates a coverage badge for README display

## Running Frontend Tests

To run UI tests:

```bash
cd scripts/playwright-tests
npx playwright test
```

To run tests with a specific configuration:

```bash
npx playwright test --config=playwright.config.js --project=chromium
```

To run a specific test file:

```bash
npx playwright test ui-components.spec.js
```

## Test Coverage Analysis

Coverage reports are generated in the `test-results/coverage` directory:

1. `html/` - Interactive HTML coverage report
2. `coverage.xml` - XML report for CI tools
3. `coverage.json` - JSON report for badge generation

To view the HTML coverage report:

```bash
open test-results/coverage/html/index.html
```

## Performance Testing

The MinIO performance testing script tests file upload and download performance with different file sizes and concurrency levels:

```bash
python scripts/perf-test-minio.py
```

Custom options:

```bash
python scripts/perf-test-minio.py \
    --file-sizes 1000,10000,100000,1000000 \
    --concurrency 1,5,10,20 \
    --iterations 3 \
    --output-dir test-results/performance
```

Performance test results include:
1. JSON data with raw metrics
2. Visualizations of throughput vs file size
3. Visualizations of throughput vs concurrency
4. Performance optimization recommendations

## CI/CD Integration

The testing scripts are designed to be integrated with CI/CD pipelines:

1. `ci_test_coverage.sh` - Runs tests and checks coverage thresholds
2. Coverage badge generation for README display
3. XML reports compatible with CI platforms

Example GitHub Actions workflow:

```yaml
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: "3.10"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-asyncio pytest-cov coverage
      - name: Start MinIO container
        run: |
          docker run -d -p 9000:9000 -p 9001:9001 \
            -e "MINIO_ROOT_USER=minioadmin" \
            -e "MINIO_ROOT_PASSWORD=minioadmin" \
            minio/minio server /data --console-address ":9001"
      - name: Run tests with coverage
        run: ./scripts/ci_test_coverage.sh
        env:
          MINIO_HOST: localhost
          MINIO_PORT: 9000
          MINIO_ACCESS_KEY: minioadmin
          MINIO_SECRET_KEY: minioadmin
      - name: Upload coverage reports
        uses: actions/upload-artifact@v2
        with:
          name: coverage-reports
          path: test-results/coverage/
```

## Interpreting Test Results

### Test Status

Test results include:
- ✅ PASSED - Test succeeded
- ❌ FAILED - Test failed (examine error message)
- ⚠️ SKIPPED - Test was skipped (check skip reason)

### Coverage Metrics

- **Statement Coverage**: Percentage of code statements executed by tests
  - Target: ≥80%
- **Branch Coverage**: Percentage of code branches executed by tests
  - Target: ≥70%

### Performance Metrics

- **Throughput**: Data processed per second (MB/s)
- **Latency**: Time to complete operations (seconds)
- **Concurrency Impact**: How performance scales with concurrent operations

## Troubleshooting

### Common Issues

1. **MinIO Connection Errors**:
   - Ensure MinIO server is running
   - Check environment variables are correctly set
   - Verify network connectivity to MinIO server

2. **Test Failures**:
   - Check test logs in `test-results/`
   - Verify test dependencies are installed
   - Check for changes in API behavior

3. **Coverage Issues**:
   - Look for modules with low coverage
   - Examine untested code paths in HTML report
   - Add tests for uncovered edge cases

4. **Performance Test Issues**:
   - Reduce file size for quicker testing
   - Lower concurrency if experiencing timeouts
   - Check system resources during testing

## Adding New Tests

### Backend Tests

1. Create a new test file in `api/tests/`
2. Follow the naming convention `test_*.py`
3. Use pytest fixtures for setup/teardown
4. Use appropriate markers (`@pytest.mark.asyncio`, `@pytest.mark.e2e`, etc.)
5. Update coverage configuration if needed

Example:

```python
import pytest

@pytest.mark.asyncio
async def test_new_feature():
    # Test implementation
    assert result == expected
```

### Frontend Tests

1. Create a new test file in `scripts/playwright-tests/`
2. Follow the naming convention `*.spec.js`
3. Use Playwright test fixtures
4. Group related tests using `test.describe`

Example:

```javascript
import { test, expect } from '@playwright/test';

test.describe('New Feature', () => {
  test('should perform expected action', async ({ page }) => {
    // Test implementation
    await expect(page.locator('.result')).toHaveText('Expected Result');
  });
});
```

## Contributing to Test Suite

When contributing to the test suite:

1. Ensure all tests are deterministic and isolated
2. Avoid dependencies between tests
3. Clean up test resources after execution
4. Document test purpose and edge cases
5. Maintain coverage thresholds (≥80% statement, ≥70% branch)
6. Avoid hardcoded credentials or environment-specific values