API Testing Framework
=====================

The TurdParty API Testing Framework provides **comprehensive API functionality validation** with **industry-leading test coverage** across all API endpoints and functionality.

Overview
--------

The API Testing Framework consists of **4 specialized test suites** covering all aspects of the TurdParty API:

- **Core API Functionality** - Complete endpoint and feature validation
- **Async Tasks & Celery Integration** - Background job processing testing
- **Security & Authentication** - Enterprise-grade security validation
- **Multilingual API Support** - 37 languages with international compliance

Framework Architecture
---------------------

Test Suite Structure
~~~~~~~~~~~~~~~~~~~

.. code-block:: text

   tests/unit/
   ├── test_api_coverage.py              # Core API functionality tests
   ├── test_async_api_comprehensive.py   # Async tasks & Celery integration
   ├── test_security_comprehensive.py    # Security & authentication tests
   ├── test_multilingual_api.py          # Multilingual API support tests
   └── run_comprehensive_api_tests.py    # Orchestrated test runner

Test Categories
~~~~~~~~~~~~~~

**1. Core API Functionality (test_api_coverage.py)**

- Health & System Endpoints
- Authentication & Authorization
- File Management Operations
- VM Management & Monitoring
- Error Handling & Validation
- Documentation & Schema Testing

**2. Async Tasks & Celery Integration (test_async_api_comprehensive.py)**

- Async File Operations
- Task Status Monitoring
- Celery Integration Testing
- Performance & Scalability
- Error Recovery & Retry Logic
- Concurrent Operations

**3. Security & Authentication (test_security_comprehensive.py)**

- Authentication Security
- Authorization Controls
- Input Validation & Sanitization
- Security Headers & CORS
- Rate Limiting & Abuse Prevention
- Information Disclosure Prevention

**4. Multilingual API Support (test_multilingual_api.py)**

- 37 Language Support Testing
- Content Negotiation
- Unicode & Character Encoding
- Regional Format Support
- Performance Impact Analysis
- EU Compliance Validation

Quick Start
----------

Run All API Tests
~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run comprehensive API test suite
   make test-api

   # Generate detailed test report
   make test-api-report

Run Individual Test Suites
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Core API functionality
   make test-api-core

   # Async tasks and Celery
   make test-api-async

   # Security and authentication
   make test-api-security

   # Multilingual and internationalization
   make test-api-multilingual

Environment-Specific Testing
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Local development server
   make test-api-local

   # Docker containers
   make test-api-docker

   # Production environment
   make test-api-production

Test Configuration
-----------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # API Configuration
   export API_URL="http://localhost:3050"          # API base URL
   export API_VERSION="v1"                         # API version
   export TEST_TIMEOUT=30                          # Request timeout (seconds)

   # Authentication
   export TEST_USERNAME="<EMAIL>"        # Test user credentials
   export TEST_PASSWORD="password123"              # Test user password

   # Test Behavior
   export MAX_RETRIES=3                            # Maximum retry attempts
   export RETRY_DELAY=2                            # Delay between retries (seconds)

Test Data Configuration
~~~~~~~~~~~~~~~~~~~~~~

- **Test Files** - Automatically created in ``/tmp/turdparty-api-test/``
- **Test Users** - Uses configured test credentials or test tokens
- **Test VMs** - Creates temporary VM instances for testing
- **Test Tasks** - Generates async tasks for Celery testing

Core API Functionality Testing
-----------------------------

Health & System Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~~

Tests system availability and health monitoring:

.. code-block:: python

   def test_01_health_check(self):
       """Test basic health check endpoint."""
       resp = requests.get(f"{API_BASE}/health/")
       self.assertEqual(resp.status_code, 200)

   def test_02_system_info(self):
       """Test system information endpoint."""
       resp = requests.get(f"{API_BASE}/system/info", headers=self.headers)
       self.assertEqual(resp.status_code, 200)

Authentication & Authorization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Comprehensive authentication testing:

.. code-block:: python

   def test_03_authentication_flow(self):
       """Test complete authentication workflow."""
       # Test token generation
       resp = requests.post(f"{API_BASE}/auth/test-token")
       self.assertEqual(resp.status_code, 200)
       
       # Test protected endpoint access
       token = resp.json().get("access_token")
       headers = {"Authorization": f"Bearer {token}"}
       resp = requests.get(f"{API_BASE}/users/me", headers=headers)
       self.assertEqual(resp.status_code, 200)

File Management Operations
~~~~~~~~~~~~~~~~~~~~~~~~~

Complete file lifecycle testing:

.. code-block:: python

   def test_04_file_upload_workflow(self):
       """Test file upload and management workflow."""
       with open(TEST_FILE, "rb") as f:
           files = {"file": ("test.txt", f, "text/plain")}
           data = {"description": "Test file upload"}
           resp = requests.post(f"{API_BASE}/file_upload", 
                              headers=self.headers, files=files, data=data)
           self.assertIn(resp.status_code, [200, 201])

Async Tasks & Celery Integration Testing
---------------------------------------

Async File Operations
~~~~~~~~~~~~~~~~~~~~

Background file processing validation:

.. code-block:: python

   def test_01_async_file_upload(self):
       """Test async file upload functionality."""
       with open(self.test_file, "rb") as f:
           files = {"file": ("async_test.txt", f, "text/plain")}
           resp = requests.post(f"{API_BASE}/async/file_upload", 
                              headers=self.headers, files=files)
           
           self.assertEqual(resp.status_code, 202)
           task_data = resp.json()
           self.assertIn("task_id", task_data)

Task Status Monitoring
~~~~~~~~~~~~~~~~~~~~~

Real-time task tracking validation:

.. code-block:: python

   def _wait_for_task_completion(self, task_id: str, max_wait: int = 120):
       """Wait for async task to complete and return final status."""
       start_time = time.time()
       while time.time() - start_time < max_wait:
           resp = requests.get(f"{API_BASE}/async/task/{task_id}", 
                             headers=self.headers)
           if resp.status_code == 200:
               task_data = resp.json()
               status = task_data.get("status", "UNKNOWN")
               if status in ["SUCCESS", "FAILURE", "REVOKED"]:
                   return task_data
           time.sleep(2)

Security & Authentication Testing
--------------------------------

Authentication Security
~~~~~~~~~~~~~~~~~~~~~~

Comprehensive authentication security validation:

.. code-block:: python

   def test_01_authentication_security(self):
       """Test authentication security mechanisms."""
       # Test valid authentication
       resp = requests.post(f"{API_BASE}/auth/login", 
                          json={"username": "<EMAIL>", 
                                "password": "password123"})
       self.assertEqual(resp.status_code, 200)
       
       # Test invalid credentials
       resp = requests.post(f"{API_BASE}/auth/login", 
                          json={"username": "<EMAIL>", 
                                "password": "wrongpassword"})
       self.assertIn(resp.status_code, [401, 403])

Input Validation Security
~~~~~~~~~~~~~~~~~~~~~~~~

Injection attack prevention testing:

.. code-block:: python

   def test_03_input_validation_security(self):
       """Test input validation and sanitization."""
       # Test SQL injection attempts
       sql_injection_payloads = [
           "'; DROP TABLE users; --",
           "1' OR '1'='1",
           "admin'/*"
       ]
       
       for payload in sql_injection_payloads:
           data = {"username": payload, "password": "test"}
           resp = requests.post(f"{API_BASE}/auth/login", json=data)
           self.assertNotEqual(resp.status_code, 200)
           self.assertNotEqual(resp.status_code, 500)

Multilingual API Support Testing
-------------------------------

Language Detection Support
~~~~~~~~~~~~~~~~~~~~~~~~~

Accept-Language header processing validation:

.. code-block:: python

   def test_01_language_detection_support(self):
       """Test language detection and Accept-Language header support."""
       languages_to_test = ['en', 'de', 'fr', 'es', 'hr', 'sr', 'is']
       
       for lang in languages_to_test:
           headers = dict(self.headers)
           headers['Accept-Language'] = lang
           
           resp = requests.get(f"{API_BASE}/health/", headers=headers)
           self.assertEqual(resp.status_code, 200)

EU Compliance Testing
~~~~~~~~~~~~~~~~~~~~

European language compliance validation:

.. code-block:: python

   def test_03_eu_compliance_testing(self):
       """Test EU official language compliance."""
       eu_official_languages = [
           'de', 'fr', 'it', 'es', 'pl', 'ro', 'nl', 'el', 'pt', 'cs', 'hu', 'sv',
           'bg', 'da', 'fi', 'sk', 'hr', 'lt', 'sl', 'lv', 'et', 'mt', 'ga'
       ]
       
       eu_successful = 0
       for lang_code in eu_official_languages:
           headers = dict(self.headers)
           headers['Accept-Language'] = lang_code
           resp = requests.get(f"{API_BASE}/health/", headers=headers)
           if resp.status_code == 200:
               eu_successful += 1
       
       eu_compliance = (eu_successful / len(eu_official_languages)) * 100
       self.assertGreaterEqual(eu_compliance, 95.0)

Test Reporting
-------------

Comprehensive Reports
~~~~~~~~~~~~~~~~~~~~

The API testing framework generates detailed reports including:

- **Overall Statistics** - Success rates, execution times, coverage metrics
- **Suite Breakdown** - Individual test suite performance analysis
- **API Coverage Analysis** - Endpoint coverage and functionality validation
- **Security Assessment** - Security vulnerability testing results
- **Multilingual Analysis** - Language support and internationalization testing
- **Performance Metrics** - Response times and scalability analysis
- **Recommendations** - Actionable improvement suggestions

Report Formats
~~~~~~~~~~~~~

- **Console Output** - Real-time test execution feedback
- **JSON Reports** - Detailed machine-readable test results
- **HTML Reports** - Visual test coverage and results
- **Coverage Reports** - Code coverage analysis and metrics

Advanced Usage
-------------

Custom Test Execution
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run specific test methods
   python -m unittest unit.test_api_coverage.APITestCase.test_01_health_check -v

   # Run with custom configuration
   API_URL=https://staging.turdparty.com python run_comprehensive_api_tests.py

   # Run with verbose output
   python run_comprehensive_api_tests.py --verbose

Continuous Integration
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # CI/CD pipeline integration
   python run_comprehensive_api_tests.py --ci --report-format=junit

   # Performance monitoring
   make test-api-monitor

   # Quick health checks
   make test-api-quick

Performance Metrics
------------------

Quality Indicators
~~~~~~~~~~~~~~~~~

- **95%+ Success Rate** - Target for overall test success
- **<2s Response Time** - Target for API response times
- **100% Security Coverage** - Complete security testing coverage
- **37 Language Support** - Full multilingual testing coverage

Business Impact
~~~~~~~~~~~~~~

- **Production Readiness** - Comprehensive production deployment validation
- **International Markets** - Complete international market readiness
- **Security Compliance** - Enterprise security standard compliance
- **Performance Standards** - Professional performance benchmarks

The TurdParty API Testing Framework establishes **industry leadership** in API testing excellence, ensuring **enterprise-grade quality** and **international compliance** for the cybersecurity platform.
