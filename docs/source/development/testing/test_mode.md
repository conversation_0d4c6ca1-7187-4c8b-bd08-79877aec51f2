
# Test Mode Documentation

## Authentication Bypass for Testing

This project includes a test mode feature that allows you to bypass authentication requirements during development and testing. This makes it easier to test API endpoints without having to include authentication tokens with every request.

## How It Works

Test mode is controlled by the `TestSettings` class in the `api/core/test_config.py` module. When enabled, authentication middleware will detect the test mode and skip authentication verification.

## Using Test Mode

### Via CLI Script

The `scripts/toggle_test_mode.py` script provides a command-line interface to enable, disable, or check the status of test mode:

```bash
# Enable test mode (bypass authentication)
python scripts/toggle_test_mode.py --enable

# Disable test mode (enforce authentication)
python scripts/toggle_test_mode.py --disable

# Check current test mode status
python scripts/toggle_test_mode.py --status
```

### Programmatically

You can also control test mode programmatically in your application code:

```python
from api.core.test_config import test_settings

# Enable test mode
test_settings.enable_test_mode()

# Disable test mode
test_settings.disable_test_mode()

# Check test mode status
is_in_test_mode = test_settings.is_testing()
```

### Direct JSON Configuration

For more advanced use cases, you can directly edit the test configuration file:

```bash
# View current configuration
cat .test_settings.json

# Example of manually editing (not recommended - use the script instead)
echo '{"test_mode": true}' > .test_settings.json 
```

## Security Warning

**Important**: Test mode should never be enabled in production environments as it completely bypasses authentication checks, which would allow unauthorized access to protected endpoints.

## Test Authentication Tokens

For scenarios where you need to test with actual authentication, you can use the token generator script:

```bash
# Generate a user token
python scripts/generate_test_token.py

# Generate an admin token
python scripts/generate_test_token.py --admin

# Generate a token for a specific user
python scripts/generate_test_token.py --username john_doe

# Generate a token with custom expiration (in minutes)
python scripts/generate_test_token.py --expire 120
```

This will generate a JWT token you can use with the Authorization header:
```
Authorization: Bearer your_token_here
```

## Environment Variables for Test Mode

You can also control test mode using environment variables:

- `TEST_MODE`: Set to "true" to enable test mode
- `TEST_TOKEN_EXPIRY`: Set default expiration time for test tokens (in minutes)
- `TEST_ADMIN_USERNAME`: Default username for admin test tokens

## Recommended Testing Workflow

1. For initial API development, enable test mode to work without authentication
2. For integration testing, disable test mode and use generated test tokens
3. For production deployment, ensure test mode is disabled

By following this workflow, you can efficiently develop and test your API while ensuring proper security in production.

## Testing with Authentication Middleware

When test mode is disabled, the authentication middleware (`api/middleware/auth_middleware.py`) will validate JWT tokens for protected endpoints. You can use the test token generator to create valid tokens for testing.

## Integration with Testing Framework

The test mode can be integrated with pytest using the following fixture:

```python
@pytest.fixture(scope="function")
def enable_test_mode():
    from api.core.test_config import test_settings
    original_state = test_settings.is_testing()
    test_settings.enable_test_mode()
    yield
    # Restore original state after test
    if original_state:
        test_settings.enable_test_mode()
    else:
        test_settings.disable_test_mode()
```

## Test Mode Status API

For convenience during development, a test mode status endpoint is available:

```
GET /api/test-mode/status
```

This endpoint returns the current test mode status:

```json
{
  "test_mode": true,
  "message": "Test mode is currently enabled. Authentication is bypassed."
}
```
