Frida Analysis Development Guide
=================================

This guide provides comprehensive information for developers working on the Frida Analysis system, including architecture patterns, extension points, and development workflows.

Development Environment Setup
------------------------------

Prerequisites
~~~~~~~~~~~~~

Before developing Frida Analysis components, ensure you have:

**Required Tools**:
   * Python 3.9+ with virtual environment support
   * Docker and Docker Compose
   * Node.js 16+ for frontend development
   * Git with proper SSH key configuration

**Development Dependencies**:
   * Frida development tools and libraries
   * Elasticsearch development cluster
   * MinIO development instance
   * PostgreSQL development database

**IDE Configuration**:
   * VS Code with Python and JavaScript extensions
   * Proper linting and formatting configuration
   * Debugging configuration for multi-component system

Local Development Setup
~~~~~~~~~~~~~~~~~~~~~~~

1. **Clone and Setup Repository**:

.. code-block:: bash

   git clone https://github.com/forkrul/replit-10baht-TurdParty-simplified.git
   cd turdparty
   git checkout feature/frida_analysis_agent

2. **Setup Python Environment**:

.. code-block:: bash

   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # or
   venv\Scripts\activate     # Windows
   
   pip install -r requirements.txt
   pip install -r requirements-dev.txt

3. **Setup Development Services**:

.. code-block:: bash

   # Start development infrastructure
   docker compose -f docker-compose.dev.yml up -d
   
   # Initialize databases
   python manage.py migrate
   python manage.py create-test-data

4. **Configure Environment Variables**:

.. code-block:: bash

   cp .env.example .env.dev
   # Edit .env.dev with development settings

5. **Start Development Servers**:

.. code-block:: bash

   # Terminal 1: API Server
   python -m uvicorn api.application:app --reload --port 8000
   
   # Terminal 2: Frontend Development Server
   cd turdparty-app
   npm install
   npm start
   
   # Terminal 3: Frida Analysis Server
   python -m vendor.frida.server.frida_server --dev-mode

Component Architecture
----------------------

Core Components Overview
~~~~~~~~~~~~~~~~~~~~~~~~~

The Frida Analysis system follows a modular architecture with clear separation of concerns:

**API Layer** (``api/v1/routes/frida_analysis.py``)
   * REST API endpoints for external integration
   * Request validation and authentication
   * Response formatting and error handling
   * Rate limiting and security controls

**Service Layer** (``vendor/frida/server/``)
   * Business logic and orchestration
   * Component coordination and lifecycle management
   * Error handling and recovery mechanisms
   * Configuration management

**Data Layer** (``api/models/frida_analysis.py``)
   * Database models and relationships
   * Data validation and constraints
   * Migration scripts and schema management
   * Query optimization and indexing

**Infrastructure Layer** (``vendor/frida/``)
   * VM coordination and management
   * Event streaming and storage
   * Artifact management and storage
   * Monitoring and health checks

Extension Points
~~~~~~~~~~~~~~~~

The system provides several extension points for customization:

**Analysis Profiles**
   * Custom analysis configurations
   * Specialized API hooking patterns
   * Inspector Gadget integration settings
   * VM template and resource specifications

**Frida Scripts**
   * Custom instrumentation scripts
   * API hooking implementations
   * Event generation and formatting
   * Error handling and recovery

**VM Templates**
   * Custom analysis environments
   * Specialized software installations
   * Security configurations
   * Performance optimizations

**Event Processors**
   * Custom event analysis and correlation
   * Real-time processing pipelines
   * Machine learning integration
   * Threat intelligence correlation

Adding New Analysis Profiles
-----------------------------

Creating Custom Profiles
~~~~~~~~~~~~~~~~~~~~~~~~~

Analysis profiles define the behavior and configuration for analysis sessions. To create a new profile:

1. **Define Profile Configuration**:

.. code-block:: json

   {
     "profiles": {
       "custom_malware_analysis": {
         "name": "Custom Malware Analysis",
         "description": "Specialized analysis for advanced persistent threats",
         "duration": 900,
         "api_hooks": [
           "file_io",
           "network",
           "process",
           "registry",
           "crypto",
           "memory",
           "injection",
           "persistence"
         ],
         "inspector_gadget": {
           "enabled": true,
           "gadgets": [
             "file_monitor",
             "network_monitor",
             "process_monitor",
             "registry_monitor",
             "memory_monitor"
           ],
           "correlation": {
             "enabled": true,
             "threshold": 0.8
           }
         },
         "vm_config": {
           "memory": "8GB",
           "cpus": 4,
           "disk": "50GB",
           "isolation": "air_gapped",
           "snapshots": true
         },
         "frida_config": {
           "spawn_timeout": 30,
           "script_timeout": 60,
           "memory_limit": "2GB",
           "event_buffer_size": 10000
         }
       }
     }
   }

2. **Add Profile Validation**:

.. code-block:: python

   # vendor/frida/config/profile_validator.py
   
   def validate_custom_profile(profile_config):
       """Validate custom malware analysis profile."""
       required_fields = ['name', 'description', 'duration', 'api_hooks']
       
       for field in required_fields:
           if field not in profile_config:
               raise ValueError(f"Missing required field: {field}")
       
       # Validate duration
       if not 60 <= profile_config['duration'] <= 3600:
           raise ValueError("Duration must be between 60 and 3600 seconds")
       
       # Validate API hooks
       valid_hooks = ['file_io', 'network', 'process', 'registry', 'crypto', 'memory', 'injection', 'persistence']
       for hook in profile_config['api_hooks']:
           if hook not in valid_hooks:
               raise ValueError(f"Invalid API hook: {hook}")
       
       return True

3. **Implement Profile Logic**:

.. code-block:: python

   # vendor/frida/profiles/custom_malware_analysis.py
   
   class CustomMalwareAnalysisProfile:
       """Custom malware analysis profile implementation."""
       
       def __init__(self, config):
           self.config = config
           self.name = "custom_malware_analysis"
       
       async def setup_analysis(self, session_id, vm_coordinator):
           """Setup analysis environment for custom malware analysis."""
           # Configure VM with enhanced security
           vm_config = {
               'memory': self.config['vm_config']['memory'],
               'cpus': self.config['vm_config']['cpus'],
               'isolation': 'air_gapped',
               'monitoring': 'enhanced'
           }
           
           # Setup Inspector Gadget with custom gadgets
           gadget_config = self.config['inspector_gadget']
           await self._setup_inspector_gadgets(session_id, gadget_config)
           
           return vm_config
       
       async def get_frida_scripts(self):
           """Get Frida scripts for custom malware analysis."""
           scripts = []
           
           for hook_category in self.config['api_hooks']:
               script_path = f"vendor/frida/scripts/{hook_category}.js"
               scripts.append({
                   'name': hook_category,
                   'path': script_path,
                   'config': self._get_hook_config(hook_category)
               })
           
           return scripts
       
       def _get_hook_config(self, hook_category):
           """Get configuration for specific API hook category."""
           configs = {
               'persistence': {
                   'monitor_registry_run_keys': True,
                   'monitor_startup_folders': True,
                   'monitor_service_creation': True,
                   'monitor_scheduled_tasks': True
               },
               'injection': {
                   'monitor_dll_injection': True,
                   'monitor_process_hollowing': True,
                   'monitor_code_injection': True,
                   'monitor_thread_injection': True
               }
           }
           
           return configs.get(hook_category, {})

Developing Custom Frida Scripts
-------------------------------

Script Development Workflow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Frida scripts are JavaScript files that define instrumentation behavior:

1. **Create Script Template**:

.. code-block:: javascript

   // vendor/frida/scripts/custom_analysis.js
   
   /**
    * Custom Analysis Frida Script
    * Monitors specific API patterns for advanced threat detection
    */
   
   // Global configuration
   const CONFIG = {
       enableLogging: true,
       bufferSize: 1000,
       correlationEnabled: true
   };
   
   // Event buffer for batch processing
   let eventBuffer = [];
   
   // Main instrumentation function
   function instrumentAPIs() {
       console.log("[Custom Analysis] Starting instrumentation...");
       
       // Hook specific APIs
       hookFileOperations();
       hookNetworkOperations();
       hookProcessOperations();
       
       // Setup event flushing
       setupEventFlushing();
   }
   
   function hookFileOperations() {
       // Hook CreateFileW for file access monitoring
       const createFileW = Module.findExportByName("kernel32.dll", "CreateFileW");
       if (createFileW) {
           Interceptor.attach(createFileW, {
               onEnter: function(args) {
                   this.filename = args[0].readUtf16String();
                   this.access = args[1].toInt32();
                   this.timestamp = Date.now();
               },
               onLeave: function(retval) {
                   if (this.filename) {
                       const event = {
                           type: "file_operation",
                           api: "CreateFileW",
                           timestamp: this.timestamp,
                           parameters: {
                               filename: this.filename,
                               access: this.access
                           },
                           return_value: retval.toString(),
                           thread_id: Process.getCurrentThreadId(),
                           process_id: Process.id
                       };
                       
                       addEvent(event);
                   }
               }
           });
       }
   }
   
   function addEvent(event) {
       eventBuffer.push(event);
       
       if (eventBuffer.length >= CONFIG.bufferSize) {
           flushEvents();
       }
   }
   
   function flushEvents() {
       if (eventBuffer.length > 0) {
           send({
               type: "events_batch",
               events: eventBuffer,
               timestamp: Date.now()
           });
           
           eventBuffer = [];
       }
   }
   
   function setupEventFlushing() {
       // Flush events every 5 seconds
       setInterval(flushEvents, 5000);
   }
   
   // Message handler for communication with Python
   recv('config', function(message) {
       Object.assign(CONFIG, message.payload);
       console.log("[Custom Analysis] Configuration updated:", CONFIG);
   });
   
   // Start instrumentation
   instrumentAPIs();

2. **Add Script Configuration**:

.. code-block:: python

   # vendor/frida/scripts/script_manager.py
   
   class CustomAnalysisScript:
       """Manager for custom analysis Frida script."""
       
       def __init__(self, session_id, config):
           self.session_id = session_id
           self.config = config
           self.script = None
       
       async def load_script(self, frida_session):
           """Load and configure the custom analysis script."""
           script_path = "vendor/frida/scripts/custom_analysis.js"
           
           with open(script_path, 'r') as f:
               script_source = f.read()
           
           self.script = await frida_session.create_script(script_source)
           self.script.on('message', self._on_message)
           
           await self.script.load()
           
           # Send configuration to script
           await self.script.post({
               'type': 'config',
               'payload': self.config
           })
       
       def _on_message(self, message, data):
           """Handle messages from Frida script."""
           if message['type'] == 'send':
               payload = message['payload']
               
               if payload['type'] == 'events_batch':
                   # Process batch of events
                   asyncio.create_task(
                       self._process_events_batch(payload['events'])
                   )
       
       async def _process_events_batch(self, events):
           """Process a batch of events from the script."""
           # Add session metadata
           for event in events:
               event['session_id'] = self.session_id
               event['script_name'] = 'custom_analysis'
           
           # Send to Elasticsearch
           await self._send_to_elasticsearch(events)
       
       async def _send_to_elasticsearch(self, events):
           """Send events to Elasticsearch for storage and analysis."""
           # Implementation depends on your Elasticsearch client
           pass

VM Template Development
-----------------------

Creating Custom VM Templates
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

VM templates define the analysis environment configuration:

1. **Docker Template Example**:

.. code-block:: dockerfile

   # vendor/frida/vm_templates/custom_ubuntu/Dockerfile
   
   FROM ubuntu:22.04
   
   # Install system dependencies
   RUN apt-get update && apt-get install -y \
       python3 \
       python3-pip \
       curl \
       wget \
       unzip \
       gdb \
       strace \
       ltrace \
       && rm -rf /var/lib/apt/lists/*
   
   # Install Frida
   RUN pip3 install frida-tools
   
   # Install Inspector Gadget
   RUN curl -L https://github.com/inspektor-gadget/inspektor-gadget/releases/latest/download/ig-linux-amd64 -o /usr/local/bin/ig
   RUN chmod +x /usr/local/bin/ig
   
   # Setup analysis environment
   COPY setup_analysis.sh /usr/local/bin/
   RUN chmod +x /usr/local/bin/setup_analysis.sh
   
   # Create analysis user
   RUN useradd -m -s /bin/bash analysis
   USER analysis
   WORKDIR /home/<USER>
   
   # Setup Frida server
   COPY frida_server.py /home/<USER>/
   
   EXPOSE 27042
   CMD ["/usr/local/bin/setup_analysis.sh"]

2. **Vagrant Template Example**:

.. code-block:: ruby

   # vendor/frida/vm_templates/custom_windows/Vagrantfile
   
   Vagrant.configure("2") do |config|
     config.vm.box = "microsoft/windows-10-enterprise"
     config.vm.hostname = "frida-analysis-windows"
     
     # VM Configuration
     config.vm.provider "virtualbox" do |vb|
       vb.memory = "8192"
       vb.cpus = 4
       vb.gui = false
       
       # Enhanced security settings
       vb.customize ["modifyvm", :id, "--clipboard", "disabled"]
       vb.customize ["modifyvm", :id, "--draganddrop", "disabled"]
       vb.customize ["modifyvm", :id, "--usb", "off"]
     end
     
     # Network configuration for isolation
     config.vm.network "private_network", type: "dhcp"
     
     # Disable default shared folder
     config.vm.synced_folder ".", "/vagrant", disabled: true
     
     # Provisioning scripts
     config.vm.provision "shell", path: "scripts/install_frida.ps1"
     config.vm.provision "shell", path: "scripts/setup_analysis.ps1"
     config.vm.provision "shell", path: "scripts/configure_security.ps1"
   end

3. **Template Configuration**:

.. code-block:: json

   {
     "templates": {
       "custom_windows_analysis": {
         "name": "Custom Windows Analysis Environment",
         "description": "Enhanced Windows 10 environment for malware analysis",
         "type": "vagrant",
         "base_box": "microsoft/windows-10-enterprise",
         "config": {
           "memory": "8GB",
           "cpus": 4,
           "disk": "100GB",
           "network": "isolated"
         },
         "software": [
           "frida-tools",
           "inspector-gadget",
           "process-monitor",
           "wireshark",
           "volatility"
         ],
         "security": {
           "isolation": "air_gapped",
           "snapshots": true,
           "auto_revert": true,
           "monitoring": "enhanced"
         },
         "setup_scripts": [
           "install_frida.ps1",
           "setup_analysis.ps1",
           "configure_security.ps1",
           "install_tools.ps1"
         ]
       }
     }
   }

Testing and Quality Assurance
------------------------------

Unit Testing
~~~~~~~~~~~~

Write comprehensive unit tests for all components:

.. code-block:: python

   # tests/unit/test_frida_analysis.py
   
   import pytest
   import asyncio
   from unittest.mock import Mock, AsyncMock
   
   from vendor.frida.server.frida_server import FridaAnalysisServer
   from vendor.frida.server.session_manager import AnalysisSessionManager
   
   @pytest.fixture
   async def frida_server():
       """Create a test Frida analysis server."""
       config = {
           'frida': {'max_sessions': 5},
           'vm': {'max_concurrent': 3},
           'storage': {'minio': {'bucket': 'test-bucket'}}
       }
       
       server = FridaAnalysisServer(config)
       await server.start()
       yield server
       await server.stop()
   
   @pytest.mark.asyncio
   async def test_start_analysis_session(frida_server):
       """Test starting a new analysis session."""
       session_id = await frida_server.start_analysis(
           binary_uuid="test-binary-uuid",
           analysis_type="runtime",
           profile="default",
           vm_template="ubuntu_22",
           user_id="test-user"
       )
       
       assert session_id is not None
       assert len(session_id) == 36  # UUID length
       
       # Verify session was created
       session_data = await frida_server.get_session_status(session_id)
       assert session_data['binary_uuid'] == "test-binary-uuid"
       assert session_data['status'] == "starting"
   
   @pytest.mark.asyncio
   async def test_session_lifecycle(frida_server):
       """Test complete session lifecycle."""
       # Start session
       session_id = await frida_server.start_analysis(
           binary_uuid="test-binary-uuid",
           analysis_type="runtime",
           profile="default",
           vm_template="ubuntu_22",
           user_id="test-user"
       )
       
       # Check initial status
       session_data = await frida_server.get_session_status(session_id)
       assert session_data['status'] == "starting"
       
       # Stop session
       await frida_server.stop_analysis(session_id, reason="test")
       
       # Check final status
       session_data = await frida_server.get_session_status(session_id)
       assert session_data['status'] in ["stopping", "cancelled"]

Integration Testing
~~~~~~~~~~~~~~~~~~~

Test component interactions and data flow:

.. code-block:: python

   # tests/integration/test_frida_integration.py
   
   import pytest
   import asyncio
   from pathlib import Path
   
   from vendor.frida.server.frida_server import FridaAnalysisServer
   from tests.helpers.test_data import create_test_binary
   
   @pytest.mark.integration
   @pytest.mark.asyncio
   async def test_complete_analysis_workflow():
       """Test complete analysis workflow from start to finish."""
       # Setup test environment
       test_binary = create_test_binary("test_malware.exe")
       
       # Start analysis
       server = FridaAnalysisServer()
       await server.start()
       
       try:
           session_id = await server.start_analysis(
               binary_uuid=test_binary.uuid,
               analysis_type="malware_analysis",
               profile="malware_analysis",
               vm_template="windows_10",
               duration=60,  # Short duration for testing
               user_id="test-user"
           )
           
           # Wait for analysis to complete
           timeout = 300  # 5 minutes timeout
           start_time = asyncio.get_event_loop().time()
           
           while True:
               session_data = await server.get_session_status(session_id)
               
               if session_data['status'] in ['completed', 'failed', 'timeout']:
                   break
               
               if asyncio.get_event_loop().time() - start_time > timeout:
                   pytest.fail("Analysis timed out")
               
               await asyncio.sleep(5)
           
           # Verify results
           assert session_data['status'] == 'completed'
           assert session_data['artifacts_count'] > 0
           assert session_data['events_count'] > 0
           
           # Test artifact retrieval
           artifacts = await server.list_session_artifacts(session_id)
           assert len(artifacts) > 0
           
           # Test event search
           events = await server.search_session_events(
               session_id,
               limit=100
           )
           assert len(events['events']) > 0
           
       finally:
           await server.stop()

Performance Testing
~~~~~~~~~~~~~~~~~~~

Test system performance under load:

.. code-block:: python

   # tests/performance/test_frida_performance.py
   
   import pytest
   import asyncio
   import time
   from concurrent.futures import ThreadPoolExecutor
   
   @pytest.mark.performance
   @pytest.mark.asyncio
   async def test_concurrent_analysis_sessions():
       """Test system performance with multiple concurrent sessions."""
       server = FridaAnalysisServer()
       await server.start()
       
       try:
           # Start multiple sessions concurrently
           session_count = 5
           tasks = []
           
           for i in range(session_count):
               task = server.start_analysis(
                   binary_uuid=f"test-binary-{i}",
                   analysis_type="quick_scan",
                   profile="quick_scan",
                   vm_template="ubuntu_22",
                   duration=60,
                   user_id=f"test-user-{i}"
               )
               tasks.append(task)
           
           # Measure startup time
           start_time = time.time()
           session_ids = await asyncio.gather(*tasks)
           startup_time = time.time() - start_time
           
           # Verify all sessions started
           assert len(session_ids) == session_count
           assert startup_time < 30  # Should start within 30 seconds
           
           # Monitor resource usage
           for session_id in session_ids:
               session_data = await server.get_session_status(session_id)
               assert session_data['status'] in ['starting', 'running']
           
       finally:
           await server.stop()

Debugging and Troubleshooting
------------------------------

Debugging Techniques
~~~~~~~~~~~~~~~~~~~~

**Logging Configuration**:

.. code-block:: python

   # vendor/frida/utils/logging_config.py
   
   import logging
   import sys
   from pathlib import Path
   
   def setup_development_logging():
       """Setup detailed logging for development."""
       logging.basicConfig(
           level=logging.DEBUG,
           format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
           handlers=[
               logging.StreamHandler(sys.stdout),
               logging.FileHandler('logs/frida_analysis.log'),
               logging.FileHandler('logs/frida_debug.log')
           ]
       )
       
       # Set specific log levels
       logging.getLogger('vendor.frida').setLevel(logging.DEBUG)
       logging.getLogger('elasticsearch').setLevel(logging.INFO)
       logging.getLogger('minio').setLevel(logging.INFO)

**Debug Mode Configuration**:

.. code-block:: python

   # vendor/frida/server/frida_server.py
   
   class FridaAnalysisServer:
       def __init__(self, config=None, debug_mode=False):
           self.debug_mode = debug_mode
           
           if debug_mode:
               self._setup_debug_mode()
       
       def _setup_debug_mode(self):
           """Setup debug mode with enhanced logging and monitoring."""
           # Enable detailed logging
           setup_development_logging()
           
           # Enable debug endpoints
           self.debug_endpoints_enabled = True
           
           # Reduce timeouts for faster iteration
           self.config['vm']['provision_timeout'] = 60
           self.config['frida']['startup_timeout'] = 30
           
           # Enable test mode for components
           self.config['test_mode'] = True

Common Issues and Solutions
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**VM Provisioning Failures**:
   * Check Docker/Vagrant installation and configuration
   * Verify network connectivity and firewall settings
   * Ensure sufficient system resources (CPU, memory, disk)
   * Review VM template configuration and dependencies

**Frida Connection Issues**:
   * Verify Frida server is running in analysis VM
   * Check network connectivity between components
   * Ensure proper port configuration and firewall rules
   * Review Frida version compatibility

**Event Streaming Problems**:
   * Check Elasticsearch cluster health and connectivity
   * Verify index templates and mappings
   * Monitor event buffer sizes and processing rates
   * Review network bandwidth and latency

**Artifact Storage Issues**:
   * Verify MinIO server accessibility and credentials
   * Check bucket permissions and policies
   * Monitor storage capacity and quotas
   * Review artifact generation and upload processes

Contributing Guidelines
-----------------------

Code Standards
~~~~~~~~~~~~~~

**Python Code Style**:
   * Follow PEP 8 style guidelines
   * Use type hints for all function signatures
   * Write comprehensive docstrings
   * Maintain test coverage above 80%

**JavaScript Code Style**:
   * Follow ESLint configuration
   * Use modern ES6+ syntax
   * Document all functions and classes
   * Handle errors gracefully

**Documentation Standards**:
   * Update documentation for all changes
   * Include code examples and usage patterns
   * Maintain architectural decision records
   * Write clear commit messages

Pull Request Process
~~~~~~~~~~~~~~~~~~~~

1. **Create Feature Branch**:
   * Use descriptive branch names (e.g., `feature/custom-analysis-profiles`)
   * Base branches on latest `main` branch
   * Keep changes focused and atomic

2. **Development and Testing**:
   * Write tests for all new functionality
   * Ensure all existing tests pass
   * Update documentation as needed
   * Follow code review guidelines

3. **Submit Pull Request**:
   * Provide clear description of changes
   * Include testing instructions
   * Reference related issues or requirements
   * Request appropriate reviewers

4. **Code Review and Merge**:
   * Address all review feedback
   * Ensure CI/CD pipeline passes
   * Squash commits if requested
   * Merge after approval

Release Process
~~~~~~~~~~~~~~~

**Version Management**:
   * Follow semantic versioning (SemVer)
   * Tag releases with appropriate version numbers
   * Maintain changelog with release notes
   * Document breaking changes and migration guides

**Deployment Pipeline**:
   * Automated testing and validation
   * Staging environment deployment
   * Production deployment with rollback capability
   * Post-deployment monitoring and validation
