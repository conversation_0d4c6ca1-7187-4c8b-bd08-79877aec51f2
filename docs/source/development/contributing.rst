Contributing
============

This guide provides information on how to contribute to TurdParty.

Getting Started
--------------

1. **Fork the repository**:
   
   Fork the repository on GitHub to your own account.

2. **Clone the repository**:
   
   .. code-block:: bash
      
      git clone https://github.com/your-username/replit-10baht-TurdParty-simplified.git
      cd replit-10baht-TurdParty-simplified

3. **Install dependencies**:
   
   .. code-block:: bash
      
      pip install -r requirements.txt

4. **Create a branch**:
   
   .. code-block:: bash
      
      git checkout -b feature/your-feature-name

Development Workflow
------------------

1. **Make your changes**:
   
   Make the necessary changes to the codebase.

2. **Run tests**:
   
   .. code-block:: bash
      
      python -m pytest

3. **Update documentation**:
   
   If your changes require documentation updates, update the relevant documentation files.

4. **Commit your changes**:
   
   .. code-block:: bash
      
      git add .
      git commit -m "Description of your changes"

5. **Push your changes**:
   
   .. code-block:: bash
      
      git push origin feature/your-feature-name

6. **Create a pull request**:
   
   Create a pull request on GitHub from your fork to the main repository.

Code Style
---------

TurdParty follows the following code style guidelines:

- **Python**: PEP 8
- **JavaScript**: ESLint with Airbnb configuration
- **HTML/CSS**: 2-space indentation

Documentation
------------

All new features should be documented. See the :doc:`documentation_style_guide` for more information.

Testing
------

All new features should include tests. See the :doc:`testing/overview` for more information.

Pull Request Process
------------------

1. **Create a pull request**:
   
   Create a pull request on GitHub from your fork to the main repository.

2. **Describe your changes**:
   
   Provide a clear description of the changes you've made.

3. **Link to related issues**:
   
   If your pull request addresses an issue, link to it in the description.

4. **Wait for review**:
   
   Wait for a maintainer to review your pull request.

5. **Address feedback**:
   
   Address any feedback from the maintainer.

6. **Merge**:
   
   Once your pull request is approved, it will be merged into the main repository.
