
# MinIO Storage Documentation

## Overview

This document provides detailed information about the MinIO storage implementation, which allows secure access to a MinIO object storage server through an SSH tunnel. The implementation includes a service client, REST API endpoints, status monitoring, and comprehensive testing utilities.

## Architecture

### SSH Tunneling Approach

Our implementation uses SSH tunneling to securely connect to a remote MinIO server. This approach offers several advantages:

1. **Security**: All data is encrypted through the SSH tunnel
2. **Simplicity**: No need to expose MinIO publicly
3. **Authentication**: Leverages existing SSH infrastructure
4. **Flexibility**: Works across network boundaries and firewalls

The process works as follows:

1. Establish an SSH tunnel to the remote server using SSH keys
2. Map the remote MinIO port (typically 9000) to a local port
3. Connect to MinIO via the local port using the S3 API
4. Perform operations using the boto3 client

### Component Overview

- **MinIOSSHClient**: Core service that manages SSH tunnels and MinIO operations
- **Storage API**: REST endpoints for bucket and object operations
- **Status API**: Endpoints for monitoring MinIO server status
- **Testing Utilities**: Scripts and test suites for verification

## MinIOSSHClient Service

The `MinIOSSHClient` class in `api/services/minio_ssh_client.py` provides the following functionality:

- Establishing and managing SSH tunnels
- Connecting to MinIO using boto3 S3 client
- Performing bucket operations (create, delete, list)
- Managing objects (upload, download, delete, list)
- Retrieving object metadata

### Key Features

- **Environment-based Configuration**: Uses environment variables for SSH and MinIO credentials
- **Tunnel Management**: Automatic establishment and cleanup of SSH tunnels
- **Error Handling**: Comprehensive error management for both SSH and MinIO operations
- **Asynchronous Operations**: All methods are async-compatible

### Configuration Parameters

| Parameter | Environment Variable | Default | Description |
|-----------|---------------------|---------|-------------|
| SSH Key Path | `SSH_KEY_PATH` | `~/.ssh/replit` | Path to SSH private key |
| SSH User | `VAGRANT_SSH_USER` | `vagrant` | SSH username |
| MinIO Access Key | `MINIO_ACCESS_KEY` | `minioadmin` | MinIO access key |
| MinIO Secret Key | `MINIO_SECRET_KEY` | `minioadmin` | MinIO secret key |
| Local Port | N/A | `9001` | Local port for SSH tunnel |

## API Endpoints

### Storage API

**Base Path**: `/api/storage`

| Endpoint | Method | Parameters | Description |
|----------|--------|------------|-------------|
| `/buckets` | GET | `hostname` (query) | List all buckets |
| `/buckets` | POST | `hostname` (query), `bucket_name` (body) | Create a new bucket |
| `/buckets/{bucket_name}` | DELETE | `hostname` (query) | Delete a bucket |
| `/objects/{bucket_name}` | GET | `hostname` (query), `prefix` (query, optional) | List objects in a bucket |
| `/objects/{bucket_name}/{object_key}` | GET | `hostname` (query), `download_path` (query, optional) | Download an object |
| `/objects/{bucket_name}/{object_key}` | PUT | `hostname` (query), `file` (form) | Upload an object |
| `/objects/{bucket_name}/{object_key}` | DELETE | `hostname` (query) | Delete an object |
| `/metadata/{bucket_name}/{object_key}` | GET | `hostname` (query) | Get object metadata |

### Status API

**Base Path**: `/api/minio-status`

| Endpoint | Method | Parameters | Description |
|----------|--------|------------|-------------|
| `/health` | GET | `hostname` (query) | Check MinIO server health |
| `/stats` | GET | `hostname` (query) | Get MinIO storage statistics |
| `/buckets/count` | GET | `hostname` (query) | Get bucket count |
| `/objects/count` | GET | `hostname` (query), `bucket_name` (query, optional) | Get object count |

### Health API

**Base Path**: `/api/minio-health`

| Endpoint | Method | Parameters | Description |
|----------|--------|------------|-------------|
| `/health` | GET | `hostname` (query) | Basic health check of MinIO server |
| `/detailed-health` | GET | `hostname` (query) | Detailed health information including bucket status |

Additionally, the main API health endpoint at `/api/health` can include MinIO status by providing the `hostname` query parameter.

## Testing Infrastructure

### Test Scripts

The following scripts are provided for testing MinIO functionality:

- **`scripts/test_minio_ssh.py`**: Command-line utility for testing SSH connection and basic MinIO operations
- **`scripts/test_minio.py`**: Comprehensive test script for MinIO operations
- **`scripts/run_minio_tests.py`**: Script to run all MinIO-related tests with coverage reporting
- **`scripts/minio_coverage_report.py`**: Script to generate MinIO-specific test coverage reports

### Test Suites

The following test suites are included:

1. **Unit Tests**:
   - `api/tests/services/test_minio_ssh_client.py`: Basic unit tests
   - `api/tests/services/test_minio_ssh_client_advanced.py`: Advanced unit tests
   - `api/tests/services/test_minio_ssh_client_mock.py`: Mocked tests without SSH

2. **API Route Tests**:
   - `api/tests/routes/test_storage_routes.py`: Storage API tests
   - `api/tests/routes/test_minio_status_routes.py`: Status API tests
   - `api/tests/routes/test_minio_operations.py`: Advanced operation tests
   - `api/tests/routes/test_minio_status_advanced.py`: Advanced status tests

3. **Integration Tests**:
   - `api/tests/integration/test_minio_integration.py`: Basic integration tests
   - `api/tests/integration/test_minio_ssh_integration.py`: SSH integration tests

### Running Tests

```bash
# Run all MinIO tests with coverage
python scripts/run_minio_tests.py

# Run tests without coverage
python scripts/run_minio_tests.py --no-coverage

# Generate MinIO-specific coverage report
python scripts/minio_coverage_report.py
```

## Best Practices

1. **SSH Key Management**:
   - Store SSH keys securely
   - Use environment variables for key paths or content
   - Rotate keys regularly

2. **Error Handling**:
   - Always check the `success` field in API responses
   - Log detailed error information
   - Implement proper retries for transient errors

3. **Resource Cleanup**:
   - Always call `stop_ssh_tunnel()` when done
   - Use the `cleanup()` method to remove temporary files
   - Implement proper connection pooling for production use

4. **Testing**:
   - Run tests regularly against a test MinIO instance
   - Verify both positive and negative test cases
   - Check error handling and edge cases

## Troubleshooting

### Common Issues

1. **SSH Connection Failures**:
   - Verify SSH key exists and has correct permissions
   - Check that the remote server is reachable
   - Ensure the SSH user has appropriate access

2. **MinIO Operation Errors**:
   - Verify MinIO is running on the remote server
   - Check access and secret keys
   - Ensure bucket and object names are valid

3. **Performance Issues**:
   - SSH tunneling adds overhead, not suitable for high-throughput scenarios
   - Consider direct MinIO access for production workloads
   - Use async operations for better concurrency

### Debugging Tools

- Use `scripts/test_minio_ssh.py --verbose` for detailed logging
- Check application logs for SSH and MinIO errors
- Run integration tests to verify end-to-end functionality

## Additional Resources

- [MinIO Documentation](https://docs.min.io/)
- [Boto3 S3 Documentation](https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/s3.html)
- [SSH Tunneling Guide](https://www.ssh.com/academy/ssh/tunneling)
