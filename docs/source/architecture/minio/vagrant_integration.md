
# MinIO Integration with Vagrant Architecture

## Overview

This document describes the architecture for accessing MinIO object storage running on the same remote server that hosts our Vagrant virtual machines. Rather than using AWS S3, we're leveraging a self-hosted MinIO server accessed through SSH tunneling.

## Architecture Diagram

```
+----------------+                 +------------------+
|                |                 |                  |
|  API Server    |      SSH        |  Remote Server   |
|  (FastAPI)     +---------------->|  (Vagrant Host)  |
|                |    Tunnel       |                  |
+----------------+                 +--------+---------+
       |                                    |
       |                                    |
       v                                    v
+----------------+                 +------------------+
|                |                 |                  |
|  MinIOSSHClient|      Port       |  MinIO Server    |
|  (Local Port   +---------------->|  (Port 9000)     |
|   9001)        |    Forwarding   |                  |
+----------------+                 +------------------+
```

## Component Descriptions

### MinIOSSHClient

The `MinIOSSHClient` class provides a secure method to connect to the MinIO server running on the remote Vagrant host. Key features include:

- Establishes an SSH tunnel to the remote server
- Maps remote MinIO port (9000) to a local port (9001)
- Provides a programmatic interface to S3-compatible storage operations
- Handles authentication, connection management, and error handling

### SSH Tunneling Mechanism

SSH tunneling is used to securely access the MinIO service without exposing it directly to the internet:

1. A tunnel is established from the local machine to the remote server using SSH keys
2. Local port 9001 is forwarded to the remote MinIO port 9000
3. All MinIO operations are then performed through this encrypted tunnel
4. The same SSH credentials used for Vagrant operations are reused for MinIO access

### Integration with Vagrant Infrastructure

The MinIO service runs alongside Vagrant on the same remote host:

- Shares the same SSH authentication mechanism
- Uses the same credentials and SSH key setup
- Leverages existing Vagrant SSH setup scripts

## Configuration

### Environment Variables

- `SSH_KEY_PATH`: Path to the SSH private key (default: `~/.ssh/replit`)
- `VAGRANT_SSH_USER`: SSH username for the remote server (default: `vagrant`)
- `MINIO_ACCESS_KEY`: MinIO access key (default: `minioadmin`)
- `MINIO_SECRET_KEY`: MinIO secret key (default: `minioadmin`)

### SSH Authentication

The application uses the same SSH key infrastructure set up for Vagrant operations:

- Private key stored at `~/.ssh/replit`
- Configuration applied through the `setup_vagrant_ssh.py` script
- SSH connection parameters match those used for Vagrant operations

## API Endpoints

API endpoints for MinIO operations include:

- Storage management (`/api/storage/...`)
- Status monitoring (`/api/minio-status/...`)
- Health checking (`/api/minio-health/...`)

See the full API documentation in `docs/minio_storage.md`.

## Security Considerations

1. **SSH Key Management**: Private keys should be stored securely and not committed to version control
2. **Credential Rotation**: MinIO access and secret keys should be rotated regularly
3. **Principle of Least Privilege**: SSH users should have only the permissions necessary for their functions
4. **Tunnel Management**: SSH tunnels should be properly closed when not in use

## Testing

Testing infrastructure includes:

- Unit tests for the MinIOSSHClient service
- Integration tests that verify end-to-end functionality
- Command-line testing utilities for manual verification

Testing scripts are located in the `scripts/` directory and test suites in `api/tests/`.
