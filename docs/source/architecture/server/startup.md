``mermaid
    sequenceDiagram
    participant Main as main.py
    participant Logging as Logging System
    participant Dependencies as Dependency Checker
    participant Application as FastAPI App
    participant Container as DI Container
    participant Database as Database
    
    Main->>Logging: setup_logging()
    Main->>Dependencies: verify_critical_dependencies()
    alt Dependency Check Failed
        Dependencies-->>Main: Return False
        Main-->>Main: sys.exit(1)
    else Dependency Check Passed
        Dependencies-->>Main: Return True
        Main->>Application: get_application()
        Application->>Application: create_app()
        Application->>Logging: setup_logging()
        Application->>Application: Add middleware
        Application->>Application: Register exception handlers
        Application->>Application: Include routers
        Application->>Container: Register startup event to initialize_container()
        Application-->>Main: Return app instance
        Main->>Main: app = main()
        Main->>Main: uvicorn.run(app)
        Main->>Database: Connect on first request
    end