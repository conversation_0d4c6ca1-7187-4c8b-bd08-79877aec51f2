```mermaid
  flowchart TD
  subgraph "Client Layer"
      WebClient["Web Browser"]
      CLIClient["CLI Client"]
      APIClient["API Client"]
  end
  
  subgraph "API Layer"
      FastAPI["FastAPI Application"]
      Middleware["Middleware Components"]
      Routers["API Routers"]
      DependencyInjection["Dependency Injection"]
  end
  
  subgraph "Service Layer"
      Services["Business Services"]
      ErrorHandling["Error Handling"]
      Logging["Structured Logging"]
  end
  
  subgraph "Data Layer"
      Repositories["Repository Pattern"]
      BaseRepository["Base Repository"]
      Models["SQLAlchemy Models"]
      BaseModel["Soft-Delete Base Model"]
  end
  
  subgraph "Database Layer"
      PostgreSQL["PostgreSQL Database"]
      Migrations["Alembic Migrations"]
  end
  
  subgraph "Infrastructure"
      Config["Configuration"]
      Schemas["Pydantic Schemas"]
      Dependencies["Dependency Checking"]
  end
  
  WebClient --> FastAPI
  CLIClient --> FastAPI
  APIClient --> FastAPI
  
  FastAPI --> Middleware
  Middleware --> Routers
  Routers --> Services
  Services --> Repositories
  Repositories --> PostgreSQL
  
  Middleware --> ErrorHandling
  Middleware --> Logging
  
  Services --> DependencyInjection
  Repositories --> DependencyInjection
  
  Repositories --> BaseRepository
  Models --> BaseModel
  
  PostgreSQL --> Migrations
  
  FastAPI --> Config
  Services --> Schemas
  FastAPI --> Dependencies