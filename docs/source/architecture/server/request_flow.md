```mermaid
  flowchart LR
  subgraph "Request Lifecycle"
      direction TB
      Client["Client"]
      Request["HTTP Request"]
      CorrelationID["CorrelationID Middleware"]
      RequestLogging["Request Logging Middleware"]
      CORS["CORS Middleware"]
      Router["API Router"]
      Dependency["Dependency Injection"]
      Service["Service Layer"]
      Repository["Repository Layer"]
      Database["PostgreSQL Database"]
      Response["HTTP Response"]
  end

  subgraph "Error Handling"
      direction TB
      RequestValidation["Request Validation Error"]
      HTTPException["HTTP Exception"]
      APIError["API Error"]
      DatabaseError["Database Error"]
      GeneralException["General Exception"]
      ErrorResponse["Standardized Error Response"]
      ErrorLogging["Error Logging"]
  end

  Client --> Request
  Request --> CorrelationID
  CorrelationID --> RequestLogging
  RequestLogging --> CORS
  CORS --> Router
  Router --> Dependency
  Dependency --> Service
  Service --> Repository
  Repository --> Database
  Database --> Repository
  Repository --> Service
  Service --> Router
  Router --> Response
  Response --> Client

  RequestValidation --> ErrorResponse
  HTTPException --> ErrorResponse
  APIError --> ErrorResponse
  DatabaseError --> ErrorResponse
  GeneralException --> ErrorResponse
  ErrorResponse --> ErrorLogging
  ErrorResponse --> Response