Inspektor Gadget Integration
============================

Overview
--------

TurdParty integrates Inspektor Gadget for comprehensive real-time eBPF-based monitoring and analysis of binary execution in isolated Linux environments. This integration provides deep observability into system calls, network activity, file operations, and process behavior.

Architecture
------------

The Inspektor Gadget integration follows a streaming architecture that captures data in real-time and forwards it to an ELK stack for analysis and machine learning processing.

.. code-block:: text

    ┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
    │   File Upload   │───▶│   MinIO Storage  │───▶│ VM Provisioning │
    │      API        │    │                  │    │    Service      │
    └─────────────────┘    └──────────────────┘    └─────────────────┘
                                                            │
    ┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
    │  Kibana         │◀───│  Elasticsearch   │◀───│ Vagrant Linux   │
    │  Dashboard      │    │                  │    │      VMs        │
    └─────────────────┘    └──────────────────┘    └─────────────────┘
                                    ▲                        │
    ┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
    │  Analysis API   │───▶│    Logstash      │◀───│ Inspektor       │
    │                 │    │                  │    │   Gadget        │
    └─────────────────┘    └──────────────────┘    └─────────────────┘

Key Components
--------------

Gadget Collection
~~~~~~~~~~~~~~~~~

The system utilizes multiple Inspektor Gadget tools for comprehensive monitoring:

- **trace_exec**: Process execution tracking
- **trace_tcp**: TCP connection monitoring  
- **trace_dns**: DNS query analysis
- **trace_open**: File system access monitoring
- **trace_mount**: Mount operation tracking
- **trace_signal**: Signal delivery monitoring

Data Streaming
~~~~~~~~~~~~~~

Real-time data streaming is implemented through:

- **Logstash Pipeline**: Processes and enriches eBPF data
- **UUID Correlation**: Links all events to original file uploads
- **Schema Validation**: Ensures data consistency across the pipeline
- **Buffer Management**: Handles high-volume data streams efficiently

ELK Stack Integration
~~~~~~~~~~~~~~~~~~~~~

The ELK stack provides:

- **Elasticsearch**: Stores and indexes observability data
- **Logstash**: Processes and transforms eBPF events
- **Kibana**: Visualizes analysis results and system behavior

Data Schema
-----------

Process Events
~~~~~~~~~~~~~~

.. code-block:: json

    {
      "timestamp": "2025-01-21T10:30:45.123Z",
      "file_uuid": "550e8400-e29b-41d4-a716-************",
      "vm_id": "vm-ubuntu-001",
      "event_type": "process_exec",
      "pid": 1234,
      "ppid": 1000,
      "comm": "malware.exe",
      "filename": "/tmp/malware.exe",
      "args": ["./malware.exe", "--payload"],
      "uid": 1000,
      "gid": 1000
    }

Network Events
~~~~~~~~~~~~~~

.. code-block:: json

    {
      "timestamp": "2025-01-21T10:30:46.456Z",
      "file_uuid": "550e8400-e29b-41d4-a716-************",
      "vm_id": "vm-ubuntu-001",
      "event_type": "network_tcp",
      "src_ip": "*************",
      "dst_ip": "*******",
      "src_port": 45678,
      "dst_port": 53,
      "protocol": "UDP",
      "dns_query": "malicious-domain.com",
      "dns_response": "*******"
    }

File System Events
~~~~~~~~~~~~~~~~~~

.. code-block:: json

    {
      "timestamp": "2025-01-21T10:30:47.789Z",
      "file_uuid": "550e8400-e29b-41d4-a716-************",
      "vm_id": "vm-ubuntu-001",
      "event_type": "file_open",
      "pid": 1234,
      "comm": "malware.exe",
      "filename": "/etc/passwd",
      "flags": "O_RDONLY",
      "mode": "0644"
    }

API Integration
---------------

Analysis Endpoints
~~~~~~~~~~~~~~~~~~

Start Binary Analysis
^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: http

    POST /api/v1/analysis/start
    Content-Type: application/json

    {
      "file_uuid": "550e8400-e29b-41d4-a716-************",
      "vm_template": "ubuntu-22.04",
      "analysis_config": {
        "timeout": 300,
        "gadgets": ["trace_exec", "trace_tcp", "trace_open"],
        "capture_network": true,
        "capture_filesystem": true
      }
    }

Response:

.. code-block:: json

    {
      "analysis_id": "analysis-123",
      "status": "started",
      "vm_id": "vm-ubuntu-001",
      "estimated_completion": "2025-01-21T10:35:00.000Z"
    }

Get Analysis Results
^^^^^^^^^^^^^^^^^^^^

.. code-block:: http

    GET /api/v1/analysis/{analysis_id}/results

Response:

.. code-block:: json

    {
      "analysis_id": "analysis-123",
      "status": "completed",
      "file_uuid": "550e8400-e29b-41d4-a716-************",
      "vm_id": "vm-ubuntu-001",
      "start_time": "2025-01-21T10:30:00.000Z",
      "end_time": "2025-01-21T10:35:00.000Z",
      "events_captured": 1247,
      "summary": {
        "processes_spawned": 15,
        "network_connections": 8,
        "files_accessed": 23,
        "suspicious_activities": 3
      }
    }

Configuration
-------------

VM Templates
~~~~~~~~~~~~

Supported Linux distributions with Inspektor Gadget pre-installed:

- Ubuntu 22.04 LTS
- Ubuntu 20.04 LTS  
- Debian 11 (Bullseye)
- CentOS 8 Stream
- Fedora 37

Gadget Configuration
~~~~~~~~~~~~~~~~~~~~

Configure which gadgets to run during analysis:

.. code-block:: yaml

    analysis_config:
      default_gadgets:
        - trace_exec
        - trace_tcp
        - trace_dns
        - trace_open
      optional_gadgets:
        - trace_mount
        - trace_signal
        - trace_bind
      timeout: 300
      capture_network: true
      capture_filesystem: true

Performance Considerations
--------------------------

Data Volume
~~~~~~~~~~~

- **High-frequency events**: Process execution, file access
- **Medium-frequency events**: Network connections, DNS queries
- **Low-frequency events**: Mount operations, signal delivery

Buffer Management
~~~~~~~~~~~~~~~~~

- **Ring buffer size**: 64MB per gadget
- **Batch processing**: 1000 events per batch
- **Flush interval**: 5 seconds maximum
- **Compression**: gzip compression for network transfer

Security
--------

Isolation
~~~~~~~~~

- **VM isolation**: Each analysis runs in a dedicated VM
- **Network isolation**: Controlled network access with monitoring
- **File system isolation**: Temporary file systems for analysis

Data Protection
~~~~~~~~~~~~~~~

- **Encryption in transit**: TLS 1.3 for all data streams
- **Access control**: Role-based access to analysis results
- **Data retention**: Configurable retention policies
- **Audit logging**: Complete audit trail of all operations

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Gadget not starting**
  Check VM kernel version compatibility and eBPF support

**High data volume**
  Adjust buffer sizes and sampling rates

**Network connectivity issues**
  Verify Logstash endpoint configuration and network policies

**Missing events**
  Check gadget permissions and kernel module loading

Monitoring
~~~~~~~~~~

- **Gadget health**: Monitor gadget process status
- **Data flow**: Track event processing rates
- **Resource usage**: Monitor CPU and memory consumption
- **Error rates**: Track failed event processing
