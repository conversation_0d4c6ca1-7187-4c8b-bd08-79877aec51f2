
# Vagrant Service Architecture

## Overview

The Vagrant service provides a comprehensive API for managing Vagrant virtual machines. It supports two communication methods:

1. **SSH-based communication** (primary) - Direct communication with Vagrant hosts via SSH
2. **gRPC-based communication** (fallback) - Communication through a custom gRPC server

## Component Diagram

```mermaid
graph TD
    Client[Client/UI] --> API[FastAPI Application]
    API --> VR[Vagrant Routes]
    VR --> VC[VagrantClient]
    
    subgraph "Communication Methods"
        VC --> |Primary| SSH[SSH Client]
        VC --> |Fallback| GRPC[gRPC Client]
    end
    
    SSH --> |Secure Shell| VH[Vagrant Hosts]
    GRPC --> GS[gRPC Server]
    GS --> VS[Vagrant Service]
    VS --> VH
    
    VH --> VMS[Vagrant VMs]
```

## Setup Process

```mermaid
sequenceDiagram
    participant U as User
    participant S as Setup Script
    participant L as Local System
    participant V as Vagrant Hosts
    
    U->>S: Run setup_ssh.py
    S->>L: Generate SSH keys
    S->>L: Configure SSH config
    U->>S: Run setup_vagrant_ssh.py
    S->>V: Copy public key to hosts
    S->>V: Add to authorized_keys
    S->>U: Confirm setup complete
```

## VM Operation Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant A as API
    participant VC as VagrantClient
    participant SSH as SSH Client
    participant VH as Vagrant Host
    participant VM as Virtual Machine
    
    C->>A: Request VM operation (e.g., /vagrant/up/{vm_id})
    A->>VC: Forward request
    VC->>SSH: Connect and send command
    SSH->>VH: Execute vagrant command
    VH->>VM: Perform operation
    VM->>VH: Return status
    VH->>SSH: Return result
    SSH->>VC: Process result
    VC->>A: Return formatted response
    A->>C: Return API response
```

## Component Descriptions

### VagrantClient

The `VagrantClient` class handles communication with Vagrant hosts through either SSH or gRPC. It determines which communication method to use based on configuration.

Key methods:
- `connect()`: Establishes connection to Vagrant hosts
- `status()`: Retrieves VM status
- `up()`: Starts a VM
- `halt()`: Stops a VM
- `destroy()`: Destroys a VM
- `execute_command()`: Runs commands on a VM

### Vagrant Routes

The routes module defines API endpoints that clients can use to interact with Vagrant VMs.

Key endpoints:
- GET `/vagrant/vms`: List all VMs
- GET `/vagrant/vms/{vm_id}`: Get VM status
- POST `/vagrant/vms/{vm_id}/up`: Start a VM
- POST `/vagrant/vms/{vm_id}/halt`: Stop a VM
- POST `/vagrant/vms/{vm_id}/destroy`: Destroy a VM
- POST `/vagrant/vms/{vm_id}/execute`: Execute command on a VM

### gRPC Server

For scenarios where direct SSH access is not possible, the application includes a gRPC server implementation that can be deployed on Vagrant hosts. This server provides a standardized interface for VM operations.

## Configuration

### Environment Variables

- `USE_VAGRANT_SSH`: Set to "true" to use SSH communication (default)
- `DEFAULT_VAGRANT_SERVER`: Default hostname for Vagrant hosts
- `VAGRANT_GRPC_SERVER`: gRPC server address (for gRPC mode)

### SSH Configuration

SSH keys are stored in:
- Private key: `~/.ssh/replit`
- Public key: `~/.ssh/replit.pub`

Vagrant host configurations can be stored in `config/vagrant_hosts.json`.
