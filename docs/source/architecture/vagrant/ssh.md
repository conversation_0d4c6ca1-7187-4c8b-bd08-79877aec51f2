
# Vagrant SSH Integration

This document explains how to set up SSH-based communication with Vagrant virtual machines using Fathom.

## Overview

The application can connect to Vagrant VMs using two methods:
- gRPC service (original implementation)
- SSH via Fathom (current implementation)

The SSH approach allows for direct communication with Vagrant hosts without the need for a custom gRPC server.

## Architecture

```
+-----------------+                  +-------------------+
|                 |                  |                   |
|  FastAPI Server | <-- SSH/HTTPS -- | Vagrant Host      |
|                 |                  |                   |
+-----------------+                  +-------------------+
        |                                     |
        v                                     v
+-----------------+                  +-------------------+
|                 |                  |                   |
| SSH Client      | --- Commands --> | Vagrant CLI       |
|                 |                  |                   |
+-----------------+                  +-------------------+
                                              |
                                              v
                                     +-------------------+
                                     |                   |
                                     | Virtual Machines  |
                                     |                   |
                                     +-------------------+
```

## Setup

### 1. Generate SSH Keys

Run the following script to generate SSH keys:

```bash
python scripts/setup_ssh.py
```

This will:
- Create an SSH key pair in `~/.ssh/replit` and `~/.ssh/replit.pub`
- Set up proper permissions
- Create a configuration in `~/.ssh/config` for Vagrant hosts

### 2. Configure Vagrant Hosts

You have two options to configure your Vagrant hosts:

#### Option 1: Manual Setup

Copy the public key to your Vagrant hosts' `authorized_keys` file:

```bash
# Get the public key
cat ~/.ssh/replit.pub

# Then manually add it to your Vagrant host's authorized_keys file
```

#### Option 2: Automated Setup

Use the provided script:

```bash
python scripts/setup_vagrant_ssh.py --host vagrant.example.com --user vagrant --password vagrant
```

Or with a configuration file:

```bash
python scripts/setup_vagrant_ssh.py --config config/vagrant_hosts.json
```

### 3. Configure Environment Variables

Set the following environment variables:

- `USE_VAGRANT_SSH`: Set to "true" to use SSH (default is "true")
- `DEFAULT_VAGRANT_SERVER`: The default Vagrant server to connect to
- `SSH_KEY_PATH`: Path to the SSH private key (default: "~/.ssh/replit")
- `VAGRANT_SSH_USER`: Username for SSH connection (default: "vagrant")
- `VAGRANT_SSH_KEY`: Direct SSH key content as a string (takes precedence over `SSH_KEY_PATH`)

## Usage

The `VagrantClient` class will automatically use SSH when `USE_VAGRANT_SSH` is set to "true".

### Examples

```python
from api.services.vagrant_client import VagrantClient

async def example():
    client = VagrantClient()
    await client.connect()
    
    # Get VM status
    status = await client.status("default")
    print(f"VM status: {status}")
    
    # Execute command on VM
    result = await client.execute_command("default", "ls -la")
    print(f"Command output: {result['stdout']}")
    
    # Start a VM
    up_result = await client.up("default")
    print(f"VM started: {up_result['success']}")
    
    # Stop a VM
    halt_result = await client.halt("default")
    print(f"VM stopped: {halt_result['success']}")
```

## API Endpoints

The following REST API endpoints are available for Vagrant operations:

- `GET /api/vagrant/vms` - List all VMs
- `GET /api/vagrant/vms/{vm_id}` - Get VM status
- `POST /api/vagrant/vms/{vm_id}/up` - Start a VM
- `POST /api/vagrant/vms/{vm_id}/halt` - Stop a VM
- `POST /api/vagrant/vms/{vm_id}/destroy` - Destroy a VM
- `POST /api/vagrant/vms/{vm_id}/execute` - Execute a command on the VM
- `GET /api/vagrant/vms/{vm_id}/status` - Get detailed VM status

See [Vagrant Operations](vagrant_operations.md) for detailed API documentation.

## Testing

Run the tests for the SSH implementation:

```bash
python scripts/run_tests.py --file api/tests/test_fathom_ssh_client.py
```

For more comprehensive testing:

```bash
python scripts/test_fathom_connection.py --host vagrant.example.com
```

## Troubleshooting

### SSH Connection Issues

1. Verify SSH key setup:
   ```bash
   ls -la ~/.ssh/replit ~/.ssh/replit.pub
   ```

2. Test SSH connection manually:
   ```bash
   ssh -i ~/.ssh/replit <EMAIL> "echo Connection successful"
   ```

3. Check SSH debug logs:
   ```bash
   ssh -i ~/.ssh/replit -vvv <EMAIL>
   ```

4. Validate SSH key format:
   ```bash
   python scripts/test_key_format.py
   ```

### Permission Issues

1. Check key permissions:
   ```bash
   chmod 600 ~/.ssh/replit
   chmod 644 ~/.ssh/replit.pub
   ```

2. Verify authorized_keys on the Vagrant host:
   ```bash
   ssh <EMAIL> "cat ~/.ssh/authorized_keys"
   ```

### Advanced Diagnostics

For more detailed diagnostics:

```bash
python scripts/diagnose_fathom_connection.py
```

This script will check:
- SSH key existence and permissions
- Network connectivity
- SSH configuration
- Vagrant installation on the remote host
- Command execution permissions

## Integrations

The Vagrant SSH communication mechanism is also used for:

- MinIO object storage access
- Remote file operations
- System monitoring

See [MinIO Vagrant Integration](minio_vagrant_integration.md) for details on how the same SSH infrastructure is used for MinIO access.
