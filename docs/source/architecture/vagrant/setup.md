
# Vagrant Service Setup Guide

## Prerequisites

- Python 3.8+
- SSH access to Vagrant hosts
- Vagrant installed on remote hosts

## Initial Setup

### 1. Install Dependencies

Ensure all required dependencies are installed:

```bash
pip install paramiko cryptography grpcio grpcio-tools
```

### 2. Generate SSH Keys

Run the SSH setup script to generate keys:

```bash
python scripts/setup_ssh.py
```

This script:
- Creates SSH key pair in `~/.ssh/replit` and `~/.ssh/replit.pub`
- Sets proper file permissions
- Updates SSH configuration

### 3. Configure Vagrant Hosts

#### Option 1: Manual Configuration

Copy the public key to each Vagrant host:

```bash
# View your public key
cat ~/.ssh/replit.pub

# Manually add this key to your Vagrant host's authorized_keys file
```

#### Option 2: Automated Configuration

Use the provided script to configure hosts:

```bash
# For a single host
python scripts/setup_vagrant_ssh.py --host vagrant.example.com --user vagrant --password vagrant

# For multiple hosts using a configuration file
python scripts/setup_vagrant_ssh.py --config config/vagrant_hosts.json
```

Create a host configuration file at `config/vagrant_hosts.json`:

```json
[
  {
    "hostname": "vagrant1.example.com",
    "username": "vagrant",
    "password": "vagrant"
  },
  {
    "hostname": "vagrant2.example.com",
    "username": "vagrant",
    "password": "vagrant"
  }
]
```

### 4. Test the SSH Connection

Verify the SSH connection:

```bash
python scripts/test_fathom_connection.py --host vagrant.example.com
```

### 5. Configure Environment Variables

Update your environment with:

```
USE_VAGRANT_SSH=true
DEFAULT_VAGRANT_SERVER=vagrant.example.com
```

## Alternative: gRPC Setup

If SSH is not an option, you can use the gRPC server:

### 1. Generate gRPC Code

```bash
python scripts/generate_grpc.py
```

### 2. Start the gRPC Server

On your Vagrant host machine:

```bash
python scripts/run_grpc_server.py
```

### 3. Configure Environment Variables

```
USE_VAGRANT_SSH=false
VAGRANT_GRPC_SERVER=vagrant.example.com:50051
```

## Troubleshooting

### SSH Connection Issues

If you're having SSH connection problems:

1. Verify SSH keys are correctly set up:
   ```bash
   ls -la ~/.ssh/replit*
   ```

2. Check SSH configuration:
   ```bash
   cat ~/.ssh/config
   ```

3. Test direct SSH connection:
   ```bash
   ssh -i ~/.ssh/replit <EMAIL>
   ```

### gRPC Connection Issues

If gRPC connection fails:

1. Verify the gRPC server is running:
   ```bash
   curl -v telnet://vagrant.example.com:50051
   ```

2. Check for firewall issues:
   ```bash
   sudo iptables -L | grep 50051
   ```

3. Verify generated gRPC code exists:
   ```bash
   ls -la api/grpc/vagrant_pb2*.py
   ```
