# Vagrant Operations Guide

## Overview

This guide explains how to use the Vagrant service API to manage virtual machines.

## API Endpoints

### VM Management

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/vagrant/vms` | GET | List all available VMs |
| `/vagrant/vms/{vm_id}` | GET | Get status of specific VM |
| `/vagrant/vms/{vm_id}/up` | POST | Start a VM |
| `/vagrant/vms/{vm_id}/halt` | POST | Stop a VM |
| `/vagrant/vms/{vm_id}/destroy` | POST | Destroy a VM |
| `/vagrant/vms/{vm_id}/execute` | POST | Execute a command on the VM |
| `/vagrant/vms/{vm_id}/status` | GET | Get detailed VM status |
| `/vagrant/vms/{vm_id}/suspend` | POST | Suspend a VM |
| `/vagrant/boxes` | GET | List available Vagrant boxes |

## Usage Examples

### Starting a VM

```bash
curl -X POST "http://localhost:8080/vagrant/vms/my-vm/up" \
  -H "Content-Type: application/json" \
  -d '{"provision": true}'
```

### Executing a Command

```bash
curl -X POST "http://localhost:8080/vagrant/vms/my-vm/execute" \
  -H "Content-Type: application/json" \
  -d '{"command": "ls -la", "sudo": false}'
```

### Getting VM Status

```bash
curl -X GET "http://localhost:8080/vagrant/vms/my-vm/status"
```

## Data Models

### VagrantVM

```json
{
  "vm_id": "my-vm"
}
```

### VagrantVMInfo

```json
{
  "vm_id": "my-vm",
  "state": "running",
  "name": "my-ubuntu-vm",
  "provider": "virtualbox",
  "directory": "/home/<USER>/vagrant/my-vm",
  "network": {
    "private_network": "*************"
  }
}
```

### VagrantCommandResult

```json
{
  "success": true,
  "message": "VM started successfully",
  "error": null
}
```

### VagrantExecuteCommandResult

```json
{
  "success": true,
  "stdout": "file1.txt\nfile2.txt",
  "stderr": "",
  "exit_code": 0
}
```

## Error Handling

The API returns appropriate HTTP status codes for errors:

- `400 Bad Request`: Invalid input parameters
- `404 Not Found`: VM or resource not found
- `500 Internal Server Error`: Server-side error

Response body for errors:

```json
{
  "detail": "Error message describing the issue"
}
```

## Authentication

All API endpoints require authentication. Use the Authorization header with a valid token:

```bash
curl -X GET "http://localhost:8080/vagrant/vms" \
  -H "Authorization: Bearer your_token_here"
```

## Available Operations

### Getting VM Status

To retrieve the status of a VM:

```http
GET /vagrant/vms/{vm_id}
```

### Starting VMs

To start a VM:

```http
POST /vagrant/vms/{vm_id}/up
```

Optional query parameters:
- `provision`: Boolean to control whether provisioners run (default: true)

### Stopping VMs

To stop a VM:

```http
POST /vagrant/vms/{vm_id}/halt
```

### Suspending VMs

To suspend a VM (save its state and stop it):

```http
POST /vagrant/vms/{vm_id}/suspend
```

### Destroying VMs

To destroy a VM:

```http
POST /vagrant/vms/{vm_id}/destroy
```

Optional query parameters:
- `force`: Boolean to force destroy without confirmation (default: false)