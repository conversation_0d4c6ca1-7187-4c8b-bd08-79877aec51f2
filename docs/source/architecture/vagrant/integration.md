# Vagrant Integration Documentation

This document provides detailed information about the Vagrant integration in our system.

## Overview

The Vagrant integration allows the system to create, manage, and interact with Vagrant virtual machines. It provides a RESTful API for VM operations and includes methods for executing commands and deploying applications on VMs.

## Components

### 1. VagrantVMService

Located in `api/services/vagrant_vm.py`, this service handles all VM operations:

- VM creation, starting, stopping, restarting, and destroying
- VM status retrieval
- Command execution on VMs
- Error handling and fallback mechanisms

### 2. Vagrant CLI Wrapper Script

Located at `vagrant_exec.sh`, this script provides a bridge between the containerized application and the host's Vagrant installation. It handles:

- Command execution on VMs via Vagrant CLI
- Test mode for simulated responses
- Error handling and logging

### 3. API Routes

Located in `api/routes/vagrant_vm.py`, these routes expose VM operations as REST endpoints:

- `/vagrant_vm/` - VM listing and creation
- `/vagrant_vm/{id}` - VM details and deletion
- `/vagrant_vm/{id}/action` - VM actions (start, stop, restart, destroy)
- `/vagrant_vm/{id}/exec` - Command execution on VMs
- `/vagrant_vm/{id}/status` - VM status retrieval

### 4. Test Script

Located at `vm_exec_test.py`, this script provides a command-line interface for testing VM operations:

- Command execution on VMs
- AppImage upload and execution
- Multiple execution methods
- Interactive VM selection

## Execution Methods

The system supports three methods for executing commands on VMs:

### 1. SSH Connection

Direct SSH connection to the VM using the Paramiko library. This is the preferred method when the VM is accessible via SSH.

```python
# In VagrantVMService._log_command_execution
client = paramiko.SSHClient()
client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
client.connect(
    hostname=ssh_host,
    port=ssh_port,
    username=ssh_username,
    password=ssh_password,
    timeout=connection_timeout
)
stdin, stdout, stderr = client.exec_command(command)
```

### 2. Vagrant CLI

When direct SSH connection is not possible, the system falls back to using the host's Vagrant CLI via the `vagrant_exec.sh` script.

```python
# In VagrantVMService._log_command_execution
vagrant_cmd = f"{host_script} {vm.name} {escaped_command}"
process = subprocess.Popen(
    vagrant_cmd,
    shell=True,
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    text=True
)
```

### 3. Simulation

As a final fallback or for testing, the system can simulate command execution without a real VM.

```python
# In vagrant_exec.sh
if [ -n "$VAGRANT_TEST_MODE" ] || ! command -v vagrant &> /dev/null; then
    log "Running in test mode (real Vagrant not available)"
    
    # Simulate Vagrant output
    echo "=== OUTPUT FROM VAGRANT-CLI FALLBACK ==="
    echo "VM Name: $VM_NAME"
    echo "Command Executed: $COMMAND"
    
    # Command-specific simulation logic...
fi
```

## Configuration

### 1. Docker Compose

The `docker-compose.yml` file includes configuration for the Vagrant integration:

```yaml
api:
  environment:
    - VAGRANT_TEST_MODE=1  # Enable test mode
  volumes:
    - ../vagrant_exec.sh:/app/vagrant_exec.sh  # Mount the host-side script
```

### 2. Environment Variables

- `VAGRANT_TEST_MODE`: When set to a non-empty value, enables test mode for simulated responses.

## Usage Examples

### 1. Execute Command on VM

```bash
curl -X POST http://localhost:3050/api/v1/vagrant_vm/{id}/exec \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"command": "echo Hello from VM", "execution_method": "vagrant_cli"}'
```

### 2. Start VM

```bash
curl -X POST http://localhost:3050/api/v1/vagrant_vm/{id}/action \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"action": "start"}'
```

### 3. Upload and Execute AppImage

```bash
# First, upload the file
curl -X POST http://localhost:3050/api/v1/file_upload/ \
  -H "Authorization: Bearer {token}" \
  -F "file=@/path/to/your/app.AppImage"

# Then, execute it on the VM
curl -X POST http://localhost:3050/api/v1/vagrant_vm/{id}/exec \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"command": "wget http://localhost:3050/api/file_upload/download/{file_id} -O /home/<USER>/app.AppImage && chmod +x /home/<USER>/app.AppImage && /home/<USER>/app.AppImage"}'
```

## Error Handling

The Vagrant integration includes robust error handling:

1. SSH connection failures are caught and logged
2. Vagrant CLI execution errors are caught and logged
3. Each method has a fallback mechanism to the next method
4. Final fallback to simulation ensures the system remains functional in any environment

## Security Considerations

- SSH connections use username/password authentication (should be enhanced with key-based auth)
- Commands are executed as the Vagrant user, which typically has sudo access
- The host-side script should be secured with appropriate permissions 