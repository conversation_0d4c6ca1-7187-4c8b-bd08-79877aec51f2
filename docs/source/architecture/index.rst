Architecture
============

This section provides comprehensive information on the architecture of TurdParty.

.. toctree::
   :maxdepth: 2
   :caption: Overview:

   overview

.. toctree::
   :maxdepth: 2
   :caption: Core Components:

   server/overview
   server/infrastructure
   server/startup
   server/request_flow
   combined_server

.. toctree::
   :maxdepth: 2
   :caption: Vagrant:

   vagrant/architecture
   vagrant/integration
   vagrant/operations
   vagrant/setup
   vagrant/ssh
   vagrant/management

.. toctree::
   :maxdepth: 2
   :caption: Storage:

   minio/storage
   minio/vagrant_integration

.. toctree::
   :maxdepth: 2
   :caption: Analysis Systems:

   frida-analysis

.. toctree::
   :maxdepth: 2
   :caption: Asynchronous Processing:

   celery/index
