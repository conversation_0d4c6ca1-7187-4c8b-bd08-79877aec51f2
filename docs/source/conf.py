# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

import os
import sys
import datetime

# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

project = 'TurdParty'
copyright = f'{datetime.datetime.now().year}, TurdParty Team'
author = 'TurdParty Team'
release = '1.0.0'  # Update with actual version

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.viewcode',
    'sphinx.ext.napoleon',
    'sphinx_rtd_theme',
    'myst_parser',
    'rst2pdf.pdfbuilder',
]

templates_path = ['_templates']
exclude_patterns = []

# -- MyST Parser configuration -----------------------------------------------
# https://myst-parser.readthedocs.io/en/latest/configuration.html

myst_enable_extensions = [
    'amsmath',
    'colon_fence',
    'deflist',
    'dollarmath',
    'html_admonition',
    'html_image',
    'linkify',
    'replacements',
    'smartquotes',
    'substitution',
    'tasklist',
]

# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

html_theme = 'sphinx_rtd_theme'
html_static_path = ['_static']
html_logo = None  # We'll add a logo later
html_favicon = None  # We'll add a favicon later

# Theme options
html_theme_options = {
    'logo_only': False,
    'display_version': True,
    'prev_next_buttons_location': 'bottom',
    'style_external_links': True,
    'style_nav_header_background': '#2980B9',
    # Toc options
    'collapse_navigation': False,
    'sticky_navigation': True,
    'navigation_depth': 4,
    'includehidden': True,
    'titles_only': False
}

# -- Options for LaTeX output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-latex-output

latex_elements = {
    # The paper size ('letterpaper' or 'a4paper')
    'papersize': 'a4paper',

    # The font size ('10pt', '11pt' or '12pt')
    'pointsize': '11pt',

    # Additional stuff for the LaTeX preamble
    'preamble': r'''
        \usepackage{charter}
        \usepackage[defaultsans]{lato}
        \usepackage{inconsolata}
    ''',

    # Latex figure (float) alignment
    'figure_align': 'htbp',
}

# Grouping the document tree into LaTeX files
latex_documents = [
    (
        'index',                          # source start file
        'TurdParty_Documentation.tex',    # target name
        'TurdParty Documentation',        # title
        'TurdParty Team',                 # author
        'manual',                         # documentclass
    ),
]

# -- Options for PDF output using rinohtype ---------------------------------
# https://www.sphinx-doc.org/en/master/usage/builders/index.html#sphinx.builders.rinoh.RinohBuilder

rinoh_documents = [
    (
        'index',                          # source start file
        'TurdParty_Documentation',        # output file name (without extension)
        'TurdParty Documentation',        # title
        'TurdParty Team',                 # author
    ),
]

# -- Options for PDF output using rst2pdf ---------------------------------
# https://rst2pdf.org/static/manual.html

pdf_documents = [
    ('index', 'TurdParty_Documentation', 'TurdParty Documentation', 'TurdParty Team'),
]
pdf_stylesheets = ['sphinx', 'a4']
pdf_style_path = ['.', '_styles']
pdf_compressed = True
pdf_font_path = ['/usr/share/fonts', '/usr/local/share/fonts']
pdf_default_dpi = 96
pdf_use_toc = True
pdf_toc_depth = 3
pdf_use_numbered_links = False
pdf_fit_background_mode = 'scale'
pdf_break_level = 1
