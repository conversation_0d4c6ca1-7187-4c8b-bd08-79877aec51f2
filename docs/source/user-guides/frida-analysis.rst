Frida Binary Analysis Guide
============================

This guide provides comprehensive instructions for using TurdParty's Frida-based binary analysis system to perform dynamic analysis of executable files.

Overview
--------

The Frida Analysis system provides powerful dynamic binary analysis capabilities including:

* **Real-time Binary Instrumentation**: Monitor API calls, system interactions, and behavior patterns
* **Install Footprint Analysis**: Track all file system and registry changes during installation
* **Network Behavior Analysis**: Monitor network communications and protocol usage
* **Multi-platform Support**: Analyze Windows PE and Linux ELF binaries
* **Air-gapped Security**: Isolated analysis environments with no network access
* **Inspector Gadget Integration**: Correlate Frida events with eBPF system monitoring

Getting Started
---------------

Prerequisites
~~~~~~~~~~~~~

Before using the Frida Analysis system, ensure you have:

1. **Valid TurdParty Account**: Authentication is required for all analysis operations
2. **Uploaded Binary**: The binary file must be uploaded to TurdParty first
3. **Sufficient Permissions**: Analysis permissions in your user role
4. **Available Resources**: Check system capacity before starting analysis

Step 1: Upload Your Binary
~~~~~~~~~~~~~~~~~~~~~~~~~~~

First, upload the binary file you want to analyze:

1. Navigate to the **File Upload** section
2. Select your binary file (PE, ELF, or other executable formats)
3. Add a descriptive name and description
4. Click **Upload** and note the returned UUID

.. note::
   The binary UUID is required to start analysis sessions. Keep this value for reference.

Step 2: Choose Analysis Type
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Select the appropriate analysis type based on your goals:

**Runtime Analysis** (Default)
   * Monitors API calls and system interactions during execution
   * Best for: Understanding program behavior, API usage patterns
   * Duration: 5-30 minutes
   * VM Templates: All supported

**Install Footprint Analysis**
   * Tracks all file system and registry changes during installation
   * Best for: Installer analysis, persistence mechanism detection
   * Duration: 15-30 minutes
   * VM Templates: Windows recommended for installers

**Malware Analysis**
   * Intensive monitoring with enhanced security and detection
   * Best for: Suspected malicious files, comprehensive threat analysis
   * Duration: 10-30 minutes
   * VM Templates: All supported with enhanced isolation

**Network Analysis**
   * Focuses on network communications and protocol analysis
   * Best for: Network behavior, C2 communication detection
   * Duration: 5-15 minutes
   * VM Templates: All supported

**Quick Scan**
   * Fast analysis for initial assessment
   * Best for: Rapid triage, basic behavior overview
   * Duration: 2-5 minutes
   * VM Templates: All supported

**File Analysis**
   * Detailed file system operation monitoring
   * Best for: File manipulation behavior, data exfiltration detection
   * Duration: 5-15 minutes
   * VM Templates: All supported

Step 3: Select Analysis Profile
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Choose an analysis profile that matches your requirements:

**Default Profile**
   * Balanced analysis with standard API hooking
   * Monitors: File I/O, Network, Process, Registry, Crypto
   * Inspector Gadget: Enabled
   * Best for: General purpose analysis

**Malware Analysis Profile**
   * Comprehensive monitoring with enhanced detection
   * Monitors: All API categories plus memory and injection detection
   * Inspector Gadget: Enabled with advanced gadgets
   * Best for: Suspected malware, advanced persistent threats

**Quick Scan Profile**
   * Minimal monitoring for fast results
   * Monitors: File I/O, Network, Process
   * Inspector Gadget: Basic gadgets only
   * Best for: Initial triage, resource-constrained environments

**Network Analysis Profile**
   * Specialized for network behavior analysis
   * Monitors: Network, Crypto, Process
   * Inspector Gadget: Network-focused gadgets
   * Best for: Network protocol analysis, C2 detection

**File Analysis Profile**
   * Specialized for file system behavior
   * Monitors: File I/O, Process
   * Inspector Gadget: File system gadgets
   * Best for: Data manipulation, file encryption analysis

Step 4: Choose VM Template
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Select the appropriate virtual machine template:

**Windows Templates**
   * ``windows_10``: Windows 10 Professional (recommended for PE files)
   * ``windows_11``: Windows 11 Professional (latest Windows features)

**Linux Templates**
   * ``ubuntu_22``: Ubuntu 22.04 LTS (recommended for ELF files)
   * ``debian_12``: Debian 12 (stable Linux environment)

.. tip::
   Choose the VM template that matches your binary's target platform for best results.

Step 5: Start Analysis
~~~~~~~~~~~~~~~~~~~~~~

Using the Web Interface:

1. Navigate to **Frida Analysis** section
2. Click **New Analysis Session**
3. Enter the binary UUID
4. Select analysis type, profile, and VM template
5. Set duration (optional, uses profile default)
6. Click **Start Analysis**

Using the API:

.. code-block:: bash

   curl -X POST "https://api.turdparty.localhost/api/v1/frida_analysis/sessions" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "binary_uuid": "550e8400-e29b-41d4-a716-************",
       "analysis_type": "malware_analysis",
       "profile": "malware_analysis",
       "vm_template": "windows_10",
       "duration": 600
     }'

Monitoring Analysis Progress
----------------------------

Session Status
~~~~~~~~~~~~~~

Monitor your analysis session through multiple channels:

**Web Interface**
   * Real-time status updates
   * Progress indicators
   * Live event streaming
   * Resource utilization graphs

**API Polling**
   * Check session status programmatically
   * Get detailed progress information
   * Monitor resource usage

**WebSocket Streaming**
   * Real-time event notifications
   * Live analysis data
   * Instant status updates

Session Phases
~~~~~~~~~~~~~~

Analysis sessions progress through several phases:

1. **Pending**: Session queued for processing
2. **Provisioning VM**: Creating isolated analysis environment
3. **Transferring Binary**: Uploading binary to analysis VM
4. **Starting Frida**: Initializing instrumentation framework
5. **Running**: Active analysis in progress
6. **Stopping**: Finalizing analysis and extracting artifacts
7. **Completed**: Analysis finished successfully

.. note::
   Each phase may take different amounts of time depending on system load and analysis complexity.

Understanding Results
---------------------

Analysis Artifacts
~~~~~~~~~~~~~~~~~~

Completed analysis sessions generate various artifacts:

**File Changes** (``file_changes_*.json``)
   * Complete list of file system modifications
   * File creation, modification, and deletion events
   * Permission changes and metadata updates
   * Timestamps and file hashes

**Registry Changes** (``registry_changes_*.json``) [Windows only]
   * Registry key creation and modification
   * Value changes and permission updates
   * Registry hive access patterns

**Network Data** (``network_data_*.json``)
   * Network connection attempts
   * Protocol usage and data transfers
   * DNS queries and responses
   * Failed connection attempts

**Memory Dumps** (``memory_dump_*.bin``) [Optional]
   * Process memory snapshots
   * Heap and stack analysis data
   * Injected code detection

**Screenshots** (``screenshot_*.png``) [Optional]
   * Visual evidence of program execution
   * User interface interactions
   * Desktop changes and artifacts

**Analysis Report** (``analysis_report_*.json``)
   * Comprehensive analysis summary
   * Behavioral patterns and indicators
   * Risk assessment and recommendations
   * Correlation with Inspector Gadget data

Runtime Events
~~~~~~~~~~~~~~

Real-time events are stored in Elasticsearch and include:

**API Call Events**
   * Function name and parameters
   * Return values and error codes
   * Timing and frequency analysis
   * Call stack information

**File Operation Events**
   * File access patterns
   * Read/write operations
   * Permission changes
   * File metadata modifications

**Network Operation Events**
   * Connection establishment
   * Data transmission details
   * Protocol analysis
   * Destination information

**Process Events**
   * Process creation and termination
   * Thread management
   * DLL loading and injection
   * Memory allocation patterns

**Registry Events** [Windows]
   * Key access and modification
   * Value creation and updates
   * Permission changes
   * Registry hive operations

Event Search and Analysis
~~~~~~~~~~~~~~~~~~~~~~~~~

Use the search interface to analyze events:

**Time-based Filtering**
   * Filter events by time range
   * Analyze specific execution phases
   * Correlate events with external activities

**Event Type Filtering**
   * Focus on specific event categories
   * API call analysis
   * File operation patterns
   * Network communication analysis

**Advanced Queries**
   * Elasticsearch query syntax
   * Complex filtering conditions
   * Aggregation and statistics
   * Pattern recognition

Inspector Gadget Correlation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

When enabled, Inspector Gadget provides additional system-level insights:

**eBPF System Monitoring**
   * Kernel-level event tracking
   * System call monitoring
   * Network packet analysis
   * Process lifecycle tracking

**Cross-correlation**
   * Frida events correlated with eBPF data
   * Comprehensive system view
   * Enhanced detection capabilities
   * Reduced false positives

Best Practices
--------------

Analysis Planning
~~~~~~~~~~~~~~~~~

**Choose Appropriate Duration**
   * Short analysis (2-5 minutes): Quick triage, basic behavior
   * Medium analysis (5-15 minutes): Standard malware analysis
   * Long analysis (15-30 minutes): Complex installers, persistent threats

**Select Optimal VM Template**
   * Match binary architecture and target OS
   * Consider required dependencies and libraries
   * Use Windows for PE files, Linux for ELF files

**Profile Selection**
   * Use specific profiles for targeted analysis
   * Malware profile for suspected threats
   * Network profile for communication analysis
   * File profile for data manipulation analysis

Resource Management
~~~~~~~~~~~~~~~~~~~

**Monitor System Capacity**
   * Check available VM slots before starting
   * Consider analysis duration and resource usage
   * Plan multiple analyses during off-peak hours

**Artifact Management**
   * Download important artifacts promptly
   * Use descriptive names for analysis sessions
   * Organize artifacts by analysis type and date

**Session Cleanup**
   * Stop unnecessary running sessions
   * Clean up old artifacts periodically
   * Monitor storage usage and quotas

Security Considerations
~~~~~~~~~~~~~~~~~~~~~~~

**Air-gapped Analysis**
   * Analysis VMs have no network access by default
   * Malware cannot communicate with external systems
   * Safe environment for dangerous samples

**Data Sanitization**
   * Configure appropriate sanitization levels
   * Remove sensitive information from artifacts
   * Consider data sharing and export policies

**Access Control**
   * Limit analysis permissions to authorized users
   * Use strong authentication and authorization
   * Monitor analysis activities and access logs

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Analysis Fails to Start**
   * Check binary file accessibility
   * Verify sufficient system resources
   * Ensure valid authentication token
   * Check VM template availability

**Analysis Times Out**
   * Increase analysis duration
   * Check VM resource allocation
   * Monitor system load and capacity
   * Consider using quick scan profile

**Missing Artifacts**
   * Verify analysis completed successfully
   * Check artifact generation settings
   * Ensure sufficient storage space
   * Review analysis profile configuration

**No Events Captured**
   * Verify binary execution in VM
   * Check Frida instrumentation status
   * Review API hooking configuration
   * Ensure binary compatibility with VM

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~

**Reduce Analysis Time**
   * Use quick scan profile for initial triage
   * Focus on specific event types
   * Limit analysis duration appropriately
   * Use targeted API hooking

**Improve Event Quality**
   * Use appropriate analysis profiles
   * Enable Inspector Gadget correlation
   * Configure comprehensive API hooking
   * Ensure proper VM template selection

**Optimize Resource Usage**
   * Schedule analyses during off-peak hours
   * Use batch processing for multiple files
   * Monitor and manage concurrent sessions
   * Clean up completed sessions promptly

Getting Help
~~~~~~~~~~~~

If you encounter issues or need assistance:

1. **Check System Status**: Verify all components are healthy
2. **Review Documentation**: Consult API reference and troubleshooting guides
3. **Contact Support**: Submit detailed issue reports with session IDs
4. **Community Forums**: Engage with other users and experts

.. note::
   Include session IDs, error messages, and analysis configurations when reporting issues for faster resolution.
