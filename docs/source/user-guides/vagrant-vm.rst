Vagrant VM Management
====================

This guide provides information on how to manage Vagrant virtual machines using TurdParty.

Prerequisites
------------

Before you can use the Vagrant VM management features, you need to have the following installed:

- Vagrant
- VirtualBox or another supported provider
- TurdParty

Creating a VM
-----------

To create a new Vagrant VM:

1. Navigate to the VM Management page:
   
   .. code-block:: text
      
      http://localhost:8000/api/v1/vagrant/

2. Click the "Create VM" button.

3. Fill in the VM details:
   
   - **Name**: A name for the VM
   - **Box**: The Vagrant box to use (e.g., ``ubuntu/focal64``)
   - **Provider**: The provider to use (e.g., ``virtualbox``)
   - **Memory**: The amount of memory to allocate to the VM (in MB)
   - **CPUs**: The number of CPUs to allocate to the VM

4. Click the "Create" button.

5. Wait for the VM to be created.

Managing VMs
-----------

To manage existing VMs:

1. Navigate to the VM Management page:
   
   .. code-block:: text
      
      http://localhost:8000/api/v1/vagrant/

2. You will see a list of existing VMs.

3. Click on a VM to view its details.

4. From the VM details page, you can:
   
   - **Start** the VM
   - **Stop** the VM
   - **Restart** the VM
   - **Delete** the VM
   - **SSH** into the VM
   - **Inject files** into the VM

Injecting Files
-------------

To inject files into a VM:

1. Navigate to the VM details page.

2. Click the "Inject File" button.

3. Select the file to inject.

4. Specify the destination path on the VM.

5. Click the "Inject" button.

6. Wait for the file to be injected.

SSH Access
---------

To SSH into a VM:

1. Navigate to the VM details page.

2. Click the "SSH" button.

3. You will be connected to the VM via SSH in a web terminal.

Troubleshooting
-------------

If you encounter issues with VM management, see the :doc:`../troubleshooting/index` section for more information.
