Changelog
=========

Version 2.0.0 (2025-01-21)
--------------------------

Major Release: Inspector Gadget Integration & Docker Consolidation

New Features
~~~~~~~~~~~~

**Inspektor Gadget Integration**
  - Real-time eBPF-based system monitoring
  - ELK stack integration for data processing
  - Comprehensive binary behavior analysis
  - Multi-gadget orchestration (trace_exec, trace_tcp, trace_dns, etc.)
  - UUID-based event correlation across analysis lifecycle
  - Support for 5 popular Linux distributions

**Docker Consolidation**
  - Unified Docker configuration under ``.dockerwrapper/``
  - Standardized ``turdparty_`` container naming
  - Consistent network and volume naming
  - Port standardization (3050-3400 range)
  - Environment-specific configurations
  - Verification and management tools

**Security Enhancements**
  - Comprehensive security middleware
  - Rate limiting with per-IP and per-endpoint controls
  - Security headers (CSP, HSTS, X-Frame-Options, etc.)
  - Input validation and sanitization
  - Secure file upload handling
  - JWT token management with blacklisting

**Celery Integration**
  - Asynchronous task processing
  - Dedicated workers for file operations, VM lifecycle, and monitoring
  - Task status tracking and monitoring
  - Celery Flower integration for monitoring

**API Improvements**
  - New analysis endpoints for Inspector Gadget
  - Enhanced file upload API with metadata tracking
  - VM management API improvements
  - Comprehensive OpenAPI documentation
  - API-first architecture with automated testing

Improvements
~~~~~~~~~~~~

**Architecture**
  - Enhanced FastAPI application structure
  - Improved error handling and logging
  - Better separation of concerns
  - Modular component design

**Testing**
  - Comprehensive test suite with Playwright
  - Integration tests for all major components
  - Security testing framework
  - Automated CI/CD pipeline improvements

**Documentation**
  - Complete Sphinx documentation overhaul
  - New architecture documentation
  - Security documentation
  - Deployment guides
  - API reference documentation

**Monitoring**
  - Cachet status page integration
  - Real-time health checks
  - Enhanced logging and monitoring
  - Performance metrics collection

Security Fixes
~~~~~~~~~~~~~~

**Dependency Updates**
  - Updated python-multipart from 0.0.7 to 0.0.18 (CVE-2024-53981)
  - Updated h11 to >=0.16.0 (CVE-2025-43859)
  - Updated starlette to >=0.40.0 (CVE-2024-47874)
  - Updated paramiko to >=3.4.0 (CVE-2023-48795)
  - Updated pytest to >=7.2.0 (CVE-2022-42969)
  - Updated black to >=24.3.0 (CVE-2024-21503)
  - Updated webpack-dev-server to >=5.2.1 (CVE-2025-30360)

**Security Hardening**
  - Implemented comprehensive security headers
  - Added rate limiting middleware
  - Enhanced input validation
  - Secure authentication mechanisms
  - CORS configuration improvements

Breaking Changes
~~~~~~~~~~~~~~~

**Docker Configuration**
  - Container names changed to use ``turdparty_`` prefix
  - Port mappings updated to 3050-3400 range
  - Docker configurations moved from ``docker/`` to ``.dockerwrapper/``
  - Network names standardized with ``turdparty_`` prefix

**API Changes**
  - New analysis endpoints require updated client code
  - Authentication token format changes
  - Some endpoint URLs updated for consistency

Migration Guide
~~~~~~~~~~~~~~

**Docker Migration**
  1. Update container references to use new ``turdparty_`` names
  2. Update port mappings to new ranges
  3. Use ``.dockerwrapper/`` for all Docker operations
  4. Run verification script: ``./verify-consolidation.sh``

**API Migration**
  1. Update client code to use new analysis endpoints
  2. Update authentication token handling
  3. Review and update any hardcoded endpoint URLs

**Security Migration**
  1. Review and update CORS configurations
  2. Update rate limiting settings if customized
  3. Review security header configurations

Version 1.5.0 (2024-12-15)
--------------------------

Features
~~~~~~~~

**Enhanced VM Management**
  - Improved Vagrant VM lifecycle management
  - Better error handling for VM operations
  - Enhanced VM status monitoring

**File Upload Improvements**
  - Better file validation
  - Improved error messages
  - Enhanced metadata handling

**UI/UX Enhancements**
  - Improved React frontend
  - Better user feedback
  - Enhanced error handling

Bug Fixes
~~~~~~~~~

- Fixed file upload timeout issues
- Resolved VM injection failures
- Improved database connection handling
- Fixed frontend routing issues

Version 1.4.0 (2024-11-20)
--------------------------

Features
~~~~~~~~

**MinIO Integration**
  - Enhanced object storage capabilities
  - Better file organization
  - Improved metadata handling

**Database Improvements**
  - Schema optimizations
  - Better query performance
  - Enhanced data integrity

Bug Fixes
~~~~~~~~~

- Fixed MinIO connection issues
- Resolved database migration problems
- Improved error handling

Version 1.3.0 (2024-10-25)
--------------------------

Features
~~~~~~~~

**Frida Integration**
  - Dynamic binary analysis capabilities
  - Real-time instrumentation
  - API hooking functionality

**Testing Framework**
  - Comprehensive test suite
  - Automated testing pipeline
  - Performance testing

Bug Fixes
~~~~~~~~~

- Fixed memory leaks in analysis engine
- Resolved concurrency issues
- Improved error reporting

Version 1.2.0 (2024-09-30)
--------------------------

Features
~~~~~~~~

**API Enhancements**
  - RESTful API design
  - OpenAPI documentation
  - Better error responses

**Security Improvements**
  - Basic authentication
  - Input validation
  - Error handling

Bug Fixes
~~~~~~~~~

- Fixed API endpoint issues
- Resolved authentication problems
- Improved input validation

Version 1.1.0 (2024-08-15)
--------------------------

Features
~~~~~~~~

**Docker Support**
  - Docker containerization
  - Docker Compose configurations
  - Development environment setup

**Documentation**
  - Initial documentation
  - Setup guides
  - API documentation

Bug Fixes
~~~~~~~~~

- Fixed deployment issues
- Resolved configuration problems
- Improved documentation

Version 1.0.0 (2024-07-01)
--------------------------

Initial Release
~~~~~~~~~~~~~~

**Core Features**
  - Basic file upload functionality
  - Vagrant VM management
  - MinIO storage integration
  - FastAPI backend
  - React frontend

**Components**
  - File upload API
  - VM management system
  - Storage backend
  - Web interface

**Infrastructure**
  - PostgreSQL database
  - Redis caching
  - Basic Docker support
