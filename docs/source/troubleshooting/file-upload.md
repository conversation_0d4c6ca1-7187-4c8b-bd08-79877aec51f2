# File Upload Troubleshooting Guide

This document provides detailed instructions for troubleshooting and fixing the file upload functionality in the TurdParty application.

## Current Issues

The file upload functionality is currently experiencing the following issues:

1. **404 Errors**: The file upload endpoint is returning 404 errors in some cases.
2. **Path Inconsistencies**: The frontend is using the new versioned API paths (`/api/v1/file_upload`), but some backend routes may still be using the old paths.
3. **Multiple File Upload**: The folder upload functionality needs additional testing and debugging.

## Implemented Solutions

To address these issues, we've implemented the following solutions:

1. **API Version Middleware**: Added a new middleware (`APIVersionMiddleware`) that redirects requests from old API paths (`/api/*`) to new versioned paths (`/api/v1/*`). This ensures backward compatibility while encouraging the use of versioned endpoints.

2. **Frontend Error Handling**: Enhanced the file upload component to provide more detailed error messages and better logging. The component now:
   - Logs detailed error information to the console
   - Displays specific error messages to the user based on the error type
   - Logs errors to the backend via the `/api/v1/logs/ui-error` endpoint
   - Includes progress tracking for uploads

3. **Backend Testing**: Created comprehensive test suites to verify API functionality:
   - `api/tests/test_file_upload_api.py`: Tests the file upload API endpoints with detailed logging
   - `turdparty-app/tests/playwright/file-upload-debug.spec.ts`: Tests the file upload UI with network monitoring
   - `turdparty-app/tests/playwright/file-upload-api-debug.spec.ts`: Tests both old and new API paths to ensure compatibility

4. **Form Data Handling**: Fixed the form data handling in the frontend to match the backend expectations:
   - Changed the field name from `files` to `file` to match the backend parameter name
   - Ensured proper handling of file paths for folder uploads

## Debugging Process

### 1. Run the Debug Test Suite

The new debug test suite provides detailed logging and network monitoring to help identify the issues:

```bash
# Run the backend API tests
cd /home/<USER>/turdparty
python -m pytest api/tests/test_file_upload_api.py -v

# Run the frontend debug tests with headed browser
cd turdparty-app
npx playwright test tests/playwright/file-upload-debug.spec.ts --headed

# Run the API debug tests
npx playwright test tests/playwright/file-upload-api-debug.spec.ts --headed
```

### 2. Check API Path Consistency

Ensure that all API paths are consistent across the application:

#### Backend Routes

1. Verify that the file upload router in `api/routes/file_upload.py` has the correct prefix:

```python
router = APIRouter(
    prefix="/file_upload",  # This should NOT include /api/v1
    tags=["file_upload"],
    responses={404: {"description": "File upload not found"}},
)
```

2. Check that the main router in `api/application.py` includes the file upload router with the correct prefix:

```python
# Handle file_upload router prefix
if file_upload.router.prefix.startswith("/file_upload"):
    api_v1_router.include_router(file_upload.router, tags=["file_upload"])
else:
    api_v1_router.include_router(file_upload.router, prefix="/file_upload", tags=["file_upload"])
```

3. Verify that the API version middleware is registered in `api/application.py`:

```python
# Add API version middleware to handle redirects from old API paths
app.add_middleware(
    APIVersionMiddleware,
    api_prefix=settings.API_V1_STR
)
```

#### Frontend API Calls

1. Ensure that the file upload component in `turdparty-app/src/pages/File_upload/index.tsx` uses the API endpoints from the config:

```typescript
// Use this:
const response = await axios.get<FileUploadListResponse>(API_ENDPOINTS.FILE_UPLOAD.BASE, {
  headers: {
    Authorization: `Bearer ${token}`
  }
});

// Instead of hardcoded paths like:
// const response = await axios.get<FileUploadListResponse>('/api/file_upload', {...});
```

2. Verify that the API endpoints in `turdparty-app/src/utils/apiConfig.ts` are correctly defined:

```typescript
export const API_ENDPOINTS = {
  // ...
  FILE_UPLOAD: {
    BASE: getApiUrl('file_upload'),
    FOLDER: getApiUrl('file_upload/folder'),
    BY_ID: (id: string) => getApiUrl(`file_upload/${id}`),
  },
  // ...
};
```

### 3. Check Network Requests

Use the browser's developer tools to inspect network requests:

1. Open the application in a browser
2. Open the developer tools (F12 or right-click > Inspect)
3. Go to the Network tab
4. Filter for "file_upload" or "api"
5. Attempt to upload a file
6. Check the request URL, method, headers, and payload
7. Examine the response status and body

Look for:
- Incorrect URL paths
- Missing or incorrect headers
- Malformed request payloads
- Error responses from the server

### 4. Server-Side Logging

Enable detailed logging on the server side:

1. Update the logging configuration in `api/core/logging.py` to include DEBUG level logs:

```python
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "DEBUG",  # Set to DEBUG for more detailed logs
            "formatter": "default",
        },
    },
    "loggers": {
        "api": {"handlers": ["console"], "level": "DEBUG", "propagate": False},
        "api.routes.file_upload": {"handlers": ["console"], "level": "DEBUG", "propagate": False},
    },
}
```

2. Add more detailed logging in the file upload service:

```python
# In api/services/file_upload.py
logger.debug(f"Attempting to upload file: {upload_file.filename}")
logger.debug(f"Content type: {content_type}, file size: {file_size}")
```

## Testing the Fixes

After implementing the fixes, test the file upload functionality:

1. Run the debug test suite again to verify that the issues are resolved
2. Test the file upload functionality manually in the application
3. Check the server logs for any remaining errors or warnings

## Preventative Measures

To prevent similar issues in the future:

1. **Consistent API Path Handling**: Always use the `apiConfig.ts` utility for API URLs in the frontend
2. **Comprehensive Testing**: Add more tests for the file upload functionality, including edge cases
3. **Error Logging**: Ensure that all errors are properly logged and reported
4. **Documentation**: Keep the API documentation up to date with any changes to endpoints or parameters
5. **Middleware**: Use the `APIVersionMiddleware` to handle redirects from old API paths to new versioned paths

## Additional Resources

- [FastAPI File Upload Documentation](https://fastapi.tiangolo.com/tutorial/request-files/)
- [Axios File Upload Guide](https://www.npmjs.com/package/axios#posting-data-as-a-form-multipart-form)
- [Ant Design Upload Component Documentation](https://ant.design/components/upload/) 