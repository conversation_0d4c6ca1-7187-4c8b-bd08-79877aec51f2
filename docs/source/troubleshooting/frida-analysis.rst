Frida Analysis Troubleshooting
===============================

This guide provides comprehensive troubleshooting information for the Frida Analysis system, including common issues, diagnostic procedures, and resolution steps.

System Health Monitoring
-------------------------

Health Check Endpoints
~~~~~~~~~~~~~~~~~~~~~~~

Use the health check endpoint to verify system status:

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_TOKEN" \
        https://api.turdparty.localhost/api/v1/frida_analysis/health

**Expected Response**:

.. code-block:: json

   {
     "status": "healthy",
     "timestamp": "2024-12-19T10:45:00Z",
     "components": {
       "elasticsearch": {
         "status": "green",
         "nodes": 3,
         "indices": 15
       },
       "minio": {
         "status": "healthy",
         "buckets": 1
       },
       "vm_coordinator": {
         "status": "healthy",
         "active_vms": 2,
         "max_concurrent_vms": 5
       },
       "session_manager": {
         "status": "healthy",
         "active_sessions": 2,
         "total_sessions": 45
       }
     }
   }

Component Status Indicators
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Elasticsearch Status**:
   * ``green``: All shards allocated, cluster healthy
   * ``yellow``: Primary shards allocated, some replicas missing
   * ``red``: Some primary shards not allocated, data loss possible

**MinIO Status**:
   * ``healthy``: Service accessible, buckets available
   * ``degraded``: Service accessible but with issues
   * ``unhealthy``: Service not accessible

**VM Coordinator Status**:
   * ``healthy``: All VMs manageable, resources available
   * ``degraded``: Some VMs unreachable or resource constraints
   * ``unhealthy``: VM management not functional

Common Issues and Solutions
---------------------------

Analysis Session Issues
~~~~~~~~~~~~~~~~~~~~~~~

**Issue: Analysis Session Fails to Start**

*Symptoms*:
   * Session remains in "pending" status
   * Error message: "Failed to provision VM"
   * No VM created for the session

*Diagnostic Steps*:

.. code-block:: bash

   # Check system resources
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        https://api.turdparty.localhost/api/v1/frida_analysis/health
   
   # Check active sessions
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        https://api.turdparty.localhost/api/v1/frida_analysis/sessions
   
   # Check VM coordinator logs
   docker logs turdparty_frida_analysis

*Common Causes and Solutions*:

1. **Insufficient Resources**:
   * Check available CPU, memory, and disk space
   * Reduce concurrent session limits
   * Clean up old VMs and sessions

2. **VM Template Issues**:
   * Verify VM template exists and is accessible
   * Check Docker/Vagrant configuration
   * Update VM templates if corrupted

3. **Network Configuration**:
   * Verify Docker network configuration
   * Check firewall rules and port accessibility
   * Ensure proper DNS resolution

**Issue: Analysis Session Times Out**

*Symptoms*:
   * Session status changes to "timeout"
   * Analysis incomplete with partial results
   * VM remains running after timeout

*Diagnostic Steps*:

.. code-block:: bash

   # Check session details
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        https://api.turdparty.localhost/api/v1/frida_analysis/sessions/SESSION_ID
   
   # Check VM status
   docker ps | grep frida-analysis
   
   # Check Frida server logs in VM
   docker exec CONTAINER_ID cat /var/log/frida-server.log

*Solutions*:

1. **Increase Analysis Duration**:
   * Use longer duration for complex binaries
   * Consider binary complexity and analysis type
   * Monitor typical analysis times for optimization

2. **Optimize Analysis Configuration**:
   * Use appropriate analysis profile
   * Reduce API hooking scope if needed
   * Disable unnecessary monitoring features

3. **Check VM Performance**:
   * Increase VM memory allocation
   * Verify VM is not resource-constrained
   * Monitor CPU and I/O usage

**Issue: No Events Captured During Analysis**

*Symptoms*:
   * Analysis completes successfully
   * Event count is zero or very low
   * No meaningful data in Elasticsearch

*Diagnostic Steps*:

.. code-block:: bash

   # Check Frida instrumentation status
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        https://api.turdparty.localhost/api/v1/frida_analysis/sessions/SESSION_ID
   
   # Check Elasticsearch indices
   curl -X GET "localhost:9200/frida-runtime-*/_search?size=0"
   
   # Check binary execution in VM
   docker exec CONTAINER_ID ps aux | grep BINARY_NAME

*Common Causes and Solutions*:

1. **Binary Not Executing**:
   * Verify binary is compatible with VM template
   * Check binary permissions and dependencies
   * Ensure binary is not corrupted

2. **Frida Instrumentation Issues**:
   * Verify Frida server is running in VM
   * Check Frida script loading and execution
   * Review API hooking configuration

3. **Event Streaming Problems**:
   * Check Elasticsearch connectivity
   * Verify event buffer configuration
   * Monitor network connectivity between components

VM Management Issues
~~~~~~~~~~~~~~~~~~~~

**Issue: VM Provisioning Fails**

*Symptoms*:
   * Error message: "Failed to create VM"
   * Docker/Vagrant errors in logs
   * VM creation timeout

*Diagnostic Steps*:

.. code-block:: bash

   # Check Docker daemon status
   docker info
   
   # Check available resources
   df -h
   free -h
   
   # Check VM coordinator logs
   docker logs turdparty_frida_analysis | grep "VM Coordinator"

*Solutions*:

1. **Docker Issues**:
   * Restart Docker daemon if needed
   * Clean up unused containers and images
   * Check Docker storage driver configuration

2. **Resource Constraints**:
   * Free up disk space
   * Increase available memory
   * Reduce concurrent VM limits

3. **Template Configuration**:
   * Verify VM template configuration
   * Update base images if needed
   * Check template dependencies

**Issue: VM Network Isolation Problems**

*Symptoms*:
   * Analysis VM can access external networks
   * Security warnings in logs
   * Unexpected network traffic

*Diagnostic Steps*:

.. code-block:: bash

   # Check VM network configuration
   docker network ls
   docker network inspect frida-net-SESSION_ID
   
   # Test network isolation
   docker exec CONTAINER_ID ping *******

*Solutions*:

1. **Network Configuration**:
   * Verify air-gapped network settings
   * Check firewall rules and iptables
   * Ensure proper network isolation

2. **Container Security**:
   * Review container security options
   * Verify capability restrictions
   * Check SELinux/AppArmor policies

Data Storage Issues
~~~~~~~~~~~~~~~~~~~

**Issue: Artifact Storage Failures**

*Symptoms*:
   * Error message: "Failed to store artifact"
   * Missing artifacts after analysis
   * MinIO connection errors

*Diagnostic Steps*:

.. code-block:: bash

   # Check MinIO status
   curl -I http://localhost:9000/minio/health/live
   
   # Check bucket accessibility
   mc ls minio/frida-artifacts
   
   # Check storage space
   df -h /path/to/minio/data

*Solutions*:

1. **MinIO Connectivity**:
   * Verify MinIO server is running
   * Check network connectivity
   * Validate credentials and permissions

2. **Storage Space**:
   * Free up disk space
   * Implement artifact cleanup policies
   * Monitor storage usage trends

3. **Bucket Configuration**:
   * Verify bucket exists and is accessible
   * Check bucket policies and permissions
   * Review lifecycle management settings

**Issue: Elasticsearch Indexing Problems**

*Symptoms*:
   * Events not appearing in search results
   * Elasticsearch cluster status "red"
   * Index creation failures

*Diagnostic Steps*:

.. code-block:: bash

   # Check cluster health
   curl -X GET "localhost:9200/_cluster/health"
   
   # Check indices status
   curl -X GET "localhost:9200/_cat/indices/frida-runtime-*"
   
   # Check index templates
   curl -X GET "localhost:9200/_index_template/frida-runtime-template"

*Solutions*:

1. **Cluster Health**:
   * Restart Elasticsearch nodes if needed
   * Check disk space and memory usage
   * Review cluster configuration

2. **Index Management**:
   * Recreate corrupted indices
   * Update index templates if needed
   * Implement proper index lifecycle policies

3. **Performance Optimization**:
   * Adjust refresh intervals
   * Optimize mapping configurations
   * Monitor query performance

Performance Issues
------------------

Slow Analysis Performance
~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue: Analysis Takes Too Long**

*Symptoms*:
   * Analysis duration exceeds expected time
   * High CPU or memory usage
   * VM performance degradation

*Diagnostic Steps*:

.. code-block:: bash

   # Monitor system resources
   top
   htop
   iotop
   
   # Check VM resource usage
   docker stats CONTAINER_ID
   
   # Monitor Frida performance
   docker exec CONTAINER_ID frida-ps

*Optimization Strategies*:

1. **Resource Allocation**:
   * Increase VM memory and CPU allocation
   * Optimize host system resources
   * Reduce concurrent analysis sessions

2. **Analysis Configuration**:
   * Use appropriate analysis profiles
   * Reduce API hooking scope
   * Optimize event buffer sizes

3. **Infrastructure Optimization**:
   * Use SSD storage for better I/O performance
   * Optimize network configuration
   * Implement caching strategies

High Memory Usage
~~~~~~~~~~~~~~~~~

**Issue: Excessive Memory Consumption**

*Symptoms*:
   * System running out of memory
   * VM creation failures due to memory
   * Performance degradation

*Diagnostic Steps*:

.. code-block:: bash

   # Check memory usage
   free -h
   cat /proc/meminfo
   
   # Check container memory usage
   docker stats --no-stream
   
   # Monitor memory leaks
   valgrind --tool=memcheck PROCESS

*Solutions*:

1. **Memory Optimization**:
   * Reduce VM memory allocation
   * Implement memory limits for containers
   * Optimize event buffer sizes

2. **Garbage Collection**:
   * Implement proper cleanup procedures
   * Monitor for memory leaks
   * Restart services periodically if needed

3. **System Configuration**:
   * Increase system swap space
   * Optimize kernel memory settings
   * Monitor memory usage trends

Network Performance Issues
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue: Slow Event Streaming**

*Symptoms*:
   * Delayed event appearance in Elasticsearch
   * High network latency
   * Event buffer overflows

*Diagnostic Steps*:

.. code-block:: bash

   # Check network performance
   iperf3 -c elasticsearch-server
   ping elasticsearch-server
   
   # Monitor network traffic
   nethogs
   iftop
   
   # Check Elasticsearch performance
   curl -X GET "localhost:9200/_nodes/stats/transport"

*Solutions*:

1. **Network Optimization**:
   * Optimize network configuration
   * Use dedicated network interfaces
   * Implement network QoS policies

2. **Event Batching**:
   * Increase event batch sizes
   * Optimize batching intervals
   * Implement compression for event data

3. **Infrastructure Scaling**:
   * Use multiple Elasticsearch nodes
   * Implement load balancing
   * Optimize cluster configuration

Security Issues
---------------

Access Control Problems
~~~~~~~~~~~~~~~~~~~~~~~

**Issue: Unauthorized Access to Analysis Data**

*Symptoms*:
   * Users accessing other users' sessions
   * Unauthorized artifact downloads
   * Security audit failures

*Diagnostic Steps*:

.. code-block:: bash

   # Check authentication logs
   grep "frida_analysis" /var/log/auth.log
   
   # Verify JWT token validation
   curl -H "Authorization: Bearer INVALID_TOKEN" \
        https://api.turdparty.localhost/api/v1/frida_analysis/sessions
   
   # Check access control implementation
   grep -r "get_current_user" api/v1/routes/frida_analysis.py

*Solutions*:

1. **Authentication Strengthening**:
   * Implement proper JWT validation
   * Use strong token signing keys
   * Implement token expiration policies

2. **Authorization Enhancement**:
   * Verify user ownership of sessions
   * Implement role-based access control
   * Add audit logging for all access

3. **Security Monitoring**:
   * Monitor for suspicious access patterns
   * Implement rate limiting
   * Add security alerts and notifications

VM Security Issues
~~~~~~~~~~~~~~~~~~

**Issue: VM Escape or Privilege Escalation**

*Symptoms*:
   * Malware affecting host system
   * Unauthorized access to host resources
   * Security scanner alerts

*Diagnostic Steps*:

.. code-block:: bash

   # Check container security
   docker inspect CONTAINER_ID | grep -i security
   
   # Verify capability restrictions
   docker exec CONTAINER_ID capsh --print
   
   # Check for privilege escalation
   docker exec CONTAINER_ID id
   docker exec CONTAINER_ID cat /proc/self/status | grep Cap

*Solutions*:

1. **Container Hardening**:
   * Use minimal base images
   * Implement proper capability restrictions
   * Enable security modules (SELinux/AppArmor)

2. **Network Isolation**:
   * Ensure proper air-gapped configuration
   * Implement network monitoring
   * Use firewall rules for additional protection

3. **Regular Security Updates**:
   * Keep base images updated
   * Apply security patches promptly
   * Monitor security advisories

Diagnostic Tools and Commands
-----------------------------

System Monitoring Commands
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Resource Monitoring**:

.. code-block:: bash

   # CPU and memory usage
   top -p $(pgrep -d',' frida)
   
   # Disk I/O monitoring
   iotop -o
   
   # Network monitoring
   netstat -tulpn | grep :27042
   ss -tulpn | grep frida

**Container Monitoring**:

.. code-block:: bash

   # Container resource usage
   docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
   
   # Container logs
   docker logs --tail 100 -f CONTAINER_ID
   
   # Container processes
   docker exec CONTAINER_ID ps aux

**Service Health Checks**:

.. code-block:: bash

   # Elasticsearch health
   curl -X GET "localhost:9200/_cluster/health?pretty"
   
   # MinIO health
   curl -I http://localhost:9000/minio/health/live
   
   # Frida server health
   curl -X GET "http://VM_IP:27042/health"

Log Analysis
~~~~~~~~~~~~

**Important Log Locations**:

.. code-block:: bash

   # Frida Analysis Server logs
   tail -f logs/frida_analysis.log
   
   # VM Coordinator logs
   tail -f logs/vm_coordinator.log
   
   # Elasticsearch logs
   tail -f /var/log/elasticsearch/elasticsearch.log
   
   # MinIO logs
   tail -f /var/log/minio/minio.log

**Log Analysis Commands**:

.. code-block:: bash

   # Search for errors
   grep -i error logs/frida_analysis.log | tail -20
   
   # Monitor session lifecycle
   grep "session_id.*123e4567" logs/frida_analysis.log
   
   # Check performance metrics
   grep "duration\|performance" logs/frida_analysis.log

Getting Help and Support
-------------------------

Documentation Resources
~~~~~~~~~~~~~~~~~~~~~~~

* **API Reference**: Complete API documentation with examples
* **User Guides**: Step-by-step instructions for common tasks
* **Architecture Documentation**: System design and component interactions
* **Development Guides**: Information for developers and contributors

Community Support
~~~~~~~~~~~~~~~~~

* **GitHub Issues**: Report bugs and request features
* **Discussion Forums**: Community discussions and Q&A
* **Documentation Wiki**: Community-maintained documentation
* **Code Examples**: Sample implementations and integrations

Professional Support
~~~~~~~~~~~~~~~~~~~~

For enterprise deployments and critical issues:

* **Priority Support**: Dedicated support channels
* **Custom Development**: Tailored solutions and integrations
* **Training Services**: On-site training and workshops
* **Consulting Services**: Architecture review and optimization

Reporting Issues
~~~~~~~~~~~~~~~~

When reporting issues, please include:

1. **System Information**:
   * Operating system and version
   * Docker/container runtime version
   * TurdParty version and configuration

2. **Issue Details**:
   * Clear description of the problem
   * Steps to reproduce the issue
   * Expected vs. actual behavior

3. **Diagnostic Information**:
   * Relevant log excerpts
   * Error messages and stack traces
   * System resource usage information

4. **Session Information** (if applicable):
   * Session ID and configuration
   * Analysis type and profile used
   * Binary information (if safe to share)

**Issue Template**:

.. code-block:: text

   **Issue Description**
   Brief description of the problem
   
   **Environment**
   - OS: Ubuntu 22.04
   - Docker: 24.0.7
   - TurdParty: v2.1.0
   
   **Steps to Reproduce**
   1. Step one
   2. Step two
   3. Step three
   
   **Expected Behavior**
   What should happen
   
   **Actual Behavior**
   What actually happens
   
   **Logs and Error Messages**
   ```
   Relevant log excerpts
   ```
   
   **Additional Context**
   Any other relevant information
