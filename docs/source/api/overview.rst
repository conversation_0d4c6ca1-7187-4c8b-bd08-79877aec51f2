API Overview
===========

The TurdParty API is a RESTful API that provides access to the TurdParty functionality.

Base URL
-------

The base URL for the API is:

.. code-block:: text

   http://localhost:8000/api/v1/

Authentication
------------

The API uses token-based authentication. See the :doc:`authentication` section for more information.

Endpoints
--------

The API provides the following endpoints:

- **File Upload**: Upload and manage files
- **Vagrant VM**: Manage Vagrant virtual machines
- **VM Injection**: Inject files into Vagrant VMs
- **Static Analysis**: Analyze files for security and quality

See the :doc:`endpoints` section for more information.

Response Format
-------------

The API returns responses in JSON format. The response format is:

.. code-block:: json

   {
     "status": "success",
     "data": {
       // Response data
     }
   }

Error responses have the format:

.. code-block:: json

   {
     "status": "error",
     "error": {
       "code": "ERROR_CODE",
       "message": "Error message"
     }
   }

See the :doc:`errors` section for more information on error codes.

Rate Limiting
-----------

The API has rate limiting to prevent abuse. The rate limits are:

- 100 requests per minute per IP address
- 1000 requests per hour per IP address

If you exceed the rate limit, you will receive a 429 Too Many Requests response.
