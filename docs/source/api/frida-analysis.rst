Frida Analysis API
==================

The Frida Analysis API provides comprehensive dynamic binary analysis capabilities using Frida instrumentation integrated with Inspector Gadget for eBPF-based system monitoring.

Overview
--------

The Frida Analysis system enables:

* **Dynamic Binary Analysis**: Real-time instrumentation and monitoring of binary execution
* **Install Footprint Analysis**: Comprehensive tracking of file system and registry changes during installation
* **Runtime Behavior Analysis**: API call monitoring, network activity tracking, and system interaction analysis
* **Air-gapped Security**: Isolated VM analysis environments with no network access
* **Multi-platform Support**: Windows and Linux binary analysis with dedicated VM templates
* **Inspector Gadget Integration**: Correlation between Frida events and eBPF system monitoring
* **Elasticsearch Integration**: Real-time event streaming and advanced search capabilities
* **Artifact Management**: Individual file extraction and storage in MinIO

Authentication
--------------

All Frida Analysis API endpoints require authentication using Bearer tokens:

.. code-block:: http

   Authorization: Bearer <your-token>

Base URL
--------

All Frida Analysis endpoints are prefixed with:

.. code-block:: text

   /api/v1/frida_analysis

Analysis Sessions
-----------------

Start Analysis Session
~~~~~~~~~~~~~~~~~~~~~~~

Start a new Frida analysis session for a previously uploaded binary.

**POST** ``/api/v1/frida_analysis/sessions``

   **Request Body:**

   .. code-block:: json

      {
        "binary_uuid": "550e8400-e29b-41d4-a716-************",
        "analysis_type": "runtime",
        "profile": "malware_analysis",
        "vm_template": "windows_10",
        "duration": 600
      }

   **Parameters:**

   * ``binary_uuid`` (string, required): UUID of the uploaded binary file
   * ``analysis_type`` (string, optional): Type of analysis (``runtime``, ``install_footprint``, ``network_analysis``, ``file_analysis``, ``quick_scan``, ``malware_analysis``)
   * ``profile`` (string, optional): Analysis profile (``default``, ``malware_analysis``, ``quick_scan``, ``network_analysis``, ``file_analysis``, ``install_footprint``, ``runtime_behavior``)
   * ``vm_template`` (string, optional): VM template (``ubuntu_22``, ``windows_10``, ``windows_11``, ``debian_12``)
   * ``duration`` (integer, optional): Analysis duration in seconds (60-3600)

   **Response:**

   .. code-block:: json

      {
        "session_id": "123e4567-e89b-12d3-a456-************",
        "binary_uuid": "550e8400-e29b-41d4-a716-************",
        "analysis_type": "runtime",
        "profile": "malware_analysis",
        "vm_template": "windows_10",
        "status": "starting",
        "created_at": "2024-12-19T10:30:00Z"
      }

   **Status Codes:**

   * ``201``: Analysis session created successfully
   * ``400``: Invalid request parameters
   * ``404``: Binary file not found
   * ``500``: Internal server error

List Analysis Sessions
~~~~~~~~~~~~~~~~~~~~~~~

List analysis sessions for the current user with optional filtering.

**GET** ``/api/v1/frida_analysis/sessions``

   **Query Parameters:**

   * ``skip`` (integer, optional): Number of records to skip for pagination (default: 0)
   * ``limit`` (integer, optional): Maximum number of records to return (default: 100)
   * ``status_filter`` (string, optional): Filter by status (``running``, ``completed``, ``failed``, etc.)

   **Response:**

   .. code-block:: json

      {
        "items": [
          {
            "session_id": "123e4567-e89b-12d3-a456-************",
            "binary_uuid": "550e8400-e29b-41d4-a716-************",
            "analysis_type": "runtime",
            "profile": "malware_analysis",
            "status": "completed",
            "created_at": "2024-12-19T10:30:00Z",
            "completed_at": "2024-12-19T10:40:00Z",
            "artifacts_count": 15,
            "events_count": 2847
          }
        ],
        "total": 1,
        "skip": 0,
        "limit": 100
      }

Get Analysis Session
~~~~~~~~~~~~~~~~~~~~

Get detailed information about a specific analysis session.

**GET** ``/api/v1/frida_analysis/sessions/{session_id}``

   **Path Parameters:**

   * ``session_id`` (string, required): UUID of the analysis session

   **Response:**

   .. code-block:: json

      {
        "session_id": "123e4567-e89b-12d3-a456-************",
        "binary_uuid": "550e8400-e29b-41d4-a716-************",
        "analysis_type": "runtime",
        "profile": "malware_analysis",
        "vm_template": "windows_10",
        "duration": 600,
        "status": "completed",
        "created_at": "2024-12-19T10:30:00Z",
        "started_at": "2024-12-19T10:32:00Z",
        "completed_at": "2024-12-19T10:42:00Z",
        "vm_id": "frida-analysis-123e4567-a1b2c3d4",
        "vm_ip": "*************",
        "frida_pid": 1234,
        "artifacts_count": 15,
        "events_count": 2847,
        "inspector_gadget_enabled": true,
        "inspector_gadget_session_id": "ig-456e7890-b12c-34d5-e678-************"
      }

Stop Analysis Session
~~~~~~~~~~~~~~~~~~~~~

Stop a running analysis session.

**DELETE** ``/api/v1/frida_analysis/sessions/{session_id}``

   **Path Parameters:**

   * ``session_id`` (string, required): UUID of the analysis session

   **Query Parameters:**

   * ``reason`` (string, optional): Reason for stopping (default: "manual")

   **Status Codes:**

   * ``204``: Session stopped successfully
   * ``404``: Session not found
   * ``500``: Internal server error

Artifacts Management
--------------------

List Session Artifacts
~~~~~~~~~~~~~~~~~~~~~~~

List all artifacts generated by an analysis session.

**GET** ``/api/v1/frida_analysis/sessions/{session_id}/artifacts``

   **Path Parameters:**

   * ``session_id`` (string, required): UUID of the analysis session

   **Query Parameters:**

   * ``artifact_type`` (string, optional): Filter by artifact type (``file_changes``, ``registry_changes``, ``network_data``, ``memory_dump``, ``screenshot``, ``log_file``, ``analysis_report``)

   **Response:**

   .. code-block:: json

      {
        "session_id": "123e4567-e89b-12d3-a456-************",
        "artifacts": [
          {
            "name": "file_changes_0001.json",
            "size": 15420,
            "last_modified": "2024-12-19T10:42:00Z",
            "artifact_type": "file_changes"
          },
          {
            "name": "network_data_0001.json",
            "size": 8934,
            "last_modified": "2024-12-19T10:42:00Z",
            "artifact_type": "network_data"
          }
        ],
        "total_count": 15
      }

Download Session Artifact
~~~~~~~~~~~~~~~~~~~~~~~~~~

Download a specific artifact from an analysis session.

**GET** ``/api/v1/frida_analysis/sessions/{session_id}/artifacts/{artifact_name}``

   **Path Parameters:**

   * ``session_id`` (string, required): UUID of the analysis session
   * ``artifact_name`` (string, required): Name of the artifact to download

   **Response:**

   Binary file download with appropriate ``Content-Disposition`` header.

   **Status Codes:**

   * ``200``: Artifact downloaded successfully
   * ``404``: Artifact not found
   * ``500``: Internal server error

Event Search and Analysis
--------------------------

Search Session Events
~~~~~~~~~~~~~~~~~~~~~~

Search runtime events for an analysis session using Elasticsearch.

**GET** ``/api/v1/frida_analysis/sessions/{session_id}/events``

   **Path Parameters:**

   * ``session_id`` (string, required): UUID of the analysis session

   **Query Parameters:**

   * ``event_type`` (string, optional): Filter by event type
   * ``api_function`` (string, optional): Filter by API function name
   * ``start_time`` (string, optional): Start time filter (ISO 8601 format)
   * ``end_time`` (string, optional): End time filter (ISO 8601 format)
   * ``limit`` (integer, optional): Maximum number of events to return (default: 100)

   **Response:**

   .. code-block:: json

      {
        "session_id": "123e4567-e89b-12d3-a456-************",
        "events": [
          {
            "_source": {
              "timestamp": "2024-12-19T10:35:15.123Z",
              "event_type": "api_call",
              "api_function": "CreateFileW",
              "process_id": 1234,
              "parameters": {
                "filename": "C:\\temp\\malware.exe",
                "access": "GENERIC_READ"
              },
              "return_value": "0x00000001"
            }
          }
        ],
        "total_count": 2847
      }

Get Session Summary
~~~~~~~~~~~~~~~~~~~

Get analysis summary with statistics and aggregations.

**GET** ``/api/v1/frida_analysis/sessions/{session_id}/summary``

   **Path Parameters:**

   * ``session_id`` (string, required): UUID of the analysis session

   **Response:**

   .. code-block:: json

      {
        "session_id": "123e4567-e89b-12d3-a456-************",
        "total_events": 2847,
        "event_types": [
          {"key": "api_call", "doc_count": 2156},
          {"key": "file_operation", "doc_count": 421},
          {"key": "network_operation", "doc_count": 270}
        ],
        "api_functions": [
          {"key": "CreateFileW", "doc_count": 156},
          {"key": "RegOpenKeyExW", "doc_count": 89},
          {"key": "InternetOpenW", "doc_count": 45}
        ],
        "timeline": [
          {
            "key_as_string": "2024-12-19T10:35:00.000Z",
            "doc_count": 234
          }
        ]
      }

Analysis Profiles
-----------------

List Analysis Profiles
~~~~~~~~~~~~~~~~~~~~~~~

List all available analysis profiles with their configurations.

**GET** ``/api/v1/frida_analysis/profiles``

   **Response:**

   .. code-block:: json

      {
        "profiles": [
          {
            "name": "default",
            "display_name": "Default Analysis",
            "description": "Standard comprehensive analysis for unknown binaries",
            "duration": 300,
            "api_hooks": ["file_io", "network", "process", "registry", "crypto"],
            "inspector_gadget_enabled": true
          },
          {
            "name": "malware_analysis",
            "display_name": "Malware Analysis",
            "description": "Intensive analysis for suspected malware",
            "duration": 600,
            "api_hooks": ["file_io", "network", "process", "registry", "crypto", "memory", "injection"],
            "inspector_gadget_enabled": true
          }
        ]
      }

System Health
-------------

Get Frida Health Status
~~~~~~~~~~~~~~~~~~~~~~~~

Get health status of all Frida analysis system components.

**GET** ``/api/v1/frida_analysis/health``

   **Response:**

   .. code-block:: json

      {
        "status": "healthy",
        "timestamp": "2024-12-19T10:45:00Z",
        "components": {
          "elasticsearch": {
            "status": "green",
            "nodes": 3
          },
          "minio": {
            "status": "healthy"
          },
          "vm_coordinator": {
            "status": "healthy",
            "active_vms": 2,
            "max_concurrent_vms": 5
          }
        }
      }

Error Handling
--------------

The Frida Analysis API uses standard HTTP status codes and returns detailed error information:

.. code-block:: json

   {
     "detail": "Binary file 550e8400-e29b-41d4-a716-************ not found or access denied",
     "status_code": 404,
     "timestamp": "2024-12-19T10:30:00Z"
   }

Common error codes:

* ``400``: Bad Request - Invalid parameters or request format
* ``401``: Unauthorized - Missing or invalid authentication token
* ``403``: Forbidden - Insufficient permissions
* ``404``: Not Found - Resource not found or access denied
* ``409``: Conflict - Resource already exists or conflicting state
* ``429``: Too Many Requests - Rate limit exceeded
* ``500``: Internal Server Error - Server-side error

Rate Limiting
-------------

The Frida Analysis API implements rate limiting to ensure fair usage:

* **Analysis Sessions**: Maximum 10 concurrent sessions per user
* **API Requests**: 1000 requests per hour per user
* **Artifact Downloads**: 100 downloads per hour per user

Rate limit headers are included in responses:

.. code-block:: http

   X-RateLimit-Limit: 1000
   X-RateLimit-Remaining: 999
   X-RateLimit-Reset: 1640000000

Integration Examples
--------------------

Python Example
~~~~~~~~~~~~~~

.. code-block:: python

   import requests
   import time

   # Configuration
   API_BASE = "https://api.turdparty.localhost/api/v1"
   TOKEN = "your-auth-token"
   HEADERS = {"Authorization": f"Bearer {TOKEN}"}

   # Start analysis session
   response = requests.post(
       f"{API_BASE}/frida_analysis/sessions",
       json={
           "binary_uuid": "550e8400-e29b-41d4-a716-************",
           "analysis_type": "malware_analysis",
           "profile": "malware_analysis",
           "vm_template": "windows_10",
           "duration": 600
       },
       headers=HEADERS
   )
   
   session_data = response.json()
   session_id = session_data["session_id"]
   print(f"Started analysis session: {session_id}")

   # Monitor session progress
   while True:
       response = requests.get(
           f"{API_BASE}/frida_analysis/sessions/{session_id}",
           headers=HEADERS
       )
       session = response.json()
       
       print(f"Status: {session['status']}")
       
       if session["status"] in ["completed", "failed", "cancelled"]:
           break
           
       time.sleep(30)

   # Get analysis summary
   response = requests.get(
       f"{API_BASE}/frida_analysis/sessions/{session_id}/summary",
       headers=HEADERS
   )
   summary = response.json()
   print(f"Analysis completed with {summary['total_events']} events")

   # Download artifacts
   response = requests.get(
       f"{API_BASE}/frida_analysis/sessions/{session_id}/artifacts",
       headers=HEADERS
   )
   artifacts = response.json()
   
   for artifact in artifacts["artifacts"]:
       print(f"Downloading {artifact['name']}")
       download_response = requests.get(
           f"{API_BASE}/frida_analysis/sessions/{session_id}/artifacts/{artifact['name']}",
           headers=HEADERS
       )
       
       with open(artifact["name"], "wb") as f:
           f.write(download_response.content)

JavaScript Example
~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const API_BASE = "https://api.turdparty.localhost/api/v1";
   const TOKEN = "your-auth-token";
   const HEADERS = {
       "Authorization": `Bearer ${TOKEN}`,
       "Content-Type": "application/json"
   };

   async function startAnalysis(binaryUuid) {
       const response = await fetch(`${API_BASE}/frida_analysis/sessions`, {
           method: "POST",
           headers: HEADERS,
           body: JSON.stringify({
               binary_uuid: binaryUuid,
               analysis_type: "runtime",
               profile: "default",
               vm_template: "ubuntu_22",
               duration: 300
           })
       });
       
       const sessionData = await response.json();
       console.log(`Started analysis session: ${sessionData.session_id}`);
       return sessionData.session_id;
   }

   async function monitorSession(sessionId) {
       while (true) {
           const response = await fetch(
               `${API_BASE}/frida_analysis/sessions/${sessionId}`,
               { headers: HEADERS }
           );
           const session = await response.json();
           
           console.log(`Status: ${session.status}`);
           
           if (["completed", "failed", "cancelled"].includes(session.status)) {
               return session;
           }
           
           await new Promise(resolve => setTimeout(resolve, 30000));
       }
   }

   async function getSessionEvents(sessionId, eventType = null) {
       const params = new URLSearchParams();
       if (eventType) params.append("event_type", eventType);
       params.append("limit", "1000");
       
       const response = await fetch(
           `${API_BASE}/frida_analysis/sessions/${sessionId}/events?${params}`,
           { headers: HEADERS }
       );
       
       return await response.json();
   }

   // Usage
   (async () => {
       const sessionId = await startAnalysis("550e8400-e29b-41d4-a716-************");
       const completedSession = await monitorSession(sessionId);
       const events = await getSessionEvents(sessionId, "api_call");
       
       console.log(`Analysis completed with ${events.total_count} API call events`);
   })();
