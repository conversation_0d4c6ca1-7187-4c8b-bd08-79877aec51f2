Security Middleware
==================

TurdParty implements comprehensive security middleware to protect against common web application vulnerabilities and attacks.

Overview
--------

The security middleware provides multiple layers of protection:

1. **Security Headers Middleware**: Adds security headers to all responses
2. **Rate Limiting Middleware**: Protects against abuse and DoS attacks
3. **Input Validation Middleware**: Validates and sanitizes input data
4. **CORS Middleware**: Manages cross-origin resource sharing securely

Security Headers Middleware
---------------------------

Implementation
~~~~~~~~~~~~~~

The security headers middleware is implemented in ``utils/security_middleware.py`` and automatically adds security headers to all HTTP responses.

Headers Applied
~~~~~~~~~~~~~~~

**X-Content-Type-Options: nosniff**
  Prevents browsers from MIME-sniffing a response away from the declared content-type

**X-Frame-Options: DENY**
  Prevents the page from being displayed in a frame, iframe, embed or object

**X-XSS-Protection: 1; mode=block**
  Enables the Cross-site scripting (XSS) filter built into most recent web browsers

**Strict-Transport-Security**
  Forces browsers to use HTTPS connections only

**Content-Security-Policy**
  Prevents code injection attacks by controlling which resources can be loaded

**Referrer-Policy: strict-origin-when-cross-origin**
  Controls how much referrer information is included with requests

**Permissions-Policy**
  Restricts access to browser features and APIs

Configuration
~~~~~~~~~~~~~

Configure security headers in your environment:

.. code-block:: python

    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'",
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
    }

Rate Limiting Middleware
-----------------------

Implementation
~~~~~~~~~~~~~~

The rate limiting middleware protects against abuse by limiting the number of requests per IP address within a time window.

Features
~~~~~~~~

- **Per-IP rate limiting**: Configurable requests per minute
- **Burst protection**: Allows short bursts while maintaining overall limits
- **Whitelist support**: Bypass rate limiting for trusted IP addresses
- **Custom limits per endpoint**: Different limits for different API endpoints

Configuration
~~~~~~~~~~~~~

.. code-block:: python

    RATE_LIMIT_CONFIG = {
        'enabled': True,
        'requests_per_minute': 60,
        'burst_size': 10,
        'whitelist': ['127.0.0.1', '::1'],
        'custom_limits': {
            '/api/v1/auth/login': 5,  # Stricter limit for login endpoint
            '/api/v1/files/upload': 10  # Moderate limit for file uploads
        }
    }

Usage
~~~~~

The rate limiting middleware automatically applies to all endpoints. Custom limits can be set using decorators:

.. code-block:: python

    from utils.security_middleware import rate_limit

    @rate_limit(requests_per_minute=30)
    @app.route('/api/v1/sensitive-endpoint')
    def sensitive_endpoint():
        return {"message": "This endpoint has custom rate limiting"}

Input Validation Middleware
---------------------------

Implementation
~~~~~~~~~~~~~~

The input validation middleware validates and sanitizes all incoming data to prevent injection attacks.

Validation Types
~~~~~~~~~~~~~~~~

**JSON Schema Validation**
  Validates JSON payloads against predefined schemas

**SQL Injection Prevention**
  Sanitizes input to prevent SQL injection attacks

**XSS Prevention**
  Sanitizes input to prevent cross-site scripting attacks

**Path Traversal Prevention**
  Validates file paths to prevent directory traversal attacks

**Command Injection Prevention**
  Sanitizes input used in system commands

Configuration
~~~~~~~~~~~~~

Define validation schemas for your endpoints:

.. code-block:: python

    VALIDATION_SCHEMAS = {
        '/api/v1/files/upload': {
            'type': 'object',
            'properties': {
                'filename': {
                    'type': 'string',
                    'pattern': '^[a-zA-Z0-9._-]+$',
                    'maxLength': 255
                },
                'description': {
                    'type': 'string',
                    'maxLength': 1000
                }
            },
            'required': ['filename']
        }
    }

CORS Middleware
--------------

Implementation
~~~~~~~~~~~~~~

The CORS middleware manages cross-origin resource sharing securely, allowing controlled access from web applications.

Configuration
~~~~~~~~~~~~~

.. code-block:: python

    CORS_CONFIG = {
        'allow_origins': ['http://localhost:3000', 'https://yourdomain.com'],
        'allow_methods': ['GET', 'POST', 'PUT', 'DELETE'],
        'allow_headers': ['Content-Type', 'Authorization'],
        'expose_headers': ['X-Total-Count'],
        'allow_credentials': True,
        'max_age': 86400  # 24 hours
    }

Security Best Practices
----------------------

Middleware Order
~~~~~~~~~~~~~~~

The middleware is applied in the following order (important for security):

1. **CORS Middleware**: Handle preflight requests first
2. **Security Headers Middleware**: Add security headers early
3. **Rate Limiting Middleware**: Apply rate limits before processing
4. **Input Validation Middleware**: Validate input before business logic
5. **Authentication Middleware**: Authenticate users
6. **Authorization Middleware**: Check permissions

Custom Middleware
~~~~~~~~~~~~~~~~

When creating custom middleware, follow these security principles:

.. code-block:: python

    from fastapi import Request, Response
    from fastapi.middleware.base import BaseHTTPMiddleware

    class CustomSecurityMiddleware(BaseHTTPMiddleware):
        async def dispatch(self, request: Request, call_next):
            # Pre-processing security checks
            if not self.is_request_safe(request):
                return Response(status_code=403, content="Forbidden")
            
            # Process request
            response = await call_next(request)
            
            # Post-processing security headers
            response.headers["X-Custom-Security"] = "enabled"
            
            return response
        
        def is_request_safe(self, request: Request) -> bool:
            # Implement custom security checks
            return True

Testing Security Middleware
---------------------------

Unit Tests
~~~~~~~~~~

Test security middleware functionality:

.. code-block:: python

    import pytest
    from fastapi.testclient import TestClient
    from app.main import app

    client = TestClient(app)

    def test_security_headers():
        response = client.get("/")
        assert response.headers["X-Content-Type-Options"] == "nosniff"
        assert response.headers["X-Frame-Options"] == "DENY"
        assert "Strict-Transport-Security" in response.headers

    def test_rate_limiting():
        # Make multiple requests to trigger rate limiting
        for _ in range(70):  # Exceed the 60 requests per minute limit
            response = client.get("/api/v1/test")
        
        assert response.status_code == 429  # Too Many Requests

Integration Tests
~~~~~~~~~~~~~~~~

Test middleware integration with the application:

.. code-block:: python

    def test_middleware_order():
        # Test that middleware is applied in the correct order
        response = client.post("/api/v1/auth/login", json={
            "username": "test",
            "password": "invalid"
        })
        
        # Should have security headers even on failed authentication
        assert "X-Content-Type-Options" in response.headers
        assert response.status_code == 401

Monitoring and Logging
---------------------

Security Events
~~~~~~~~~~~~~~

The middleware logs security-relevant events:

- **Rate limit violations**: IP addresses exceeding rate limits
- **Input validation failures**: Malicious input attempts
- **CORS violations**: Unauthorized cross-origin requests
- **Security header bypasses**: Attempts to bypass security headers

Log Format
~~~~~~~~~~

Security events are logged in structured format:

.. code-block:: json

    {
        "timestamp": "2025-01-21T10:30:45.123Z",
        "level": "WARNING",
        "event_type": "rate_limit_violation",
        "ip_address": "*************",
        "endpoint": "/api/v1/files/upload",
        "requests_count": 65,
        "limit": 60,
        "user_agent": "Mozilla/5.0..."
    }

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~~

**CORS errors in browser**
  Check that your frontend domain is included in ``allow_origins``

**Rate limiting blocking legitimate users**
  Adjust rate limits or add IP to whitelist

**Content Security Policy blocking resources**
  Update CSP to allow necessary resources

**Input validation rejecting valid data**
  Review and update validation schemas

Performance Considerations
~~~~~~~~~~~~~~~~~~~~~~~~~

- **Rate limiting storage**: Use Redis for distributed rate limiting
- **Input validation caching**: Cache validation results for repeated patterns
- **Header optimization**: Minimize header size while maintaining security
- **Middleware ordering**: Place lightweight checks before expensive operations
