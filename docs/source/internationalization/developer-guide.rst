Developer Guide
===============

This guide provides comprehensive instructions for developers working with TurdParty's **enterprise-grade translation system** supporting **37 languages** with **professional quality assurance** and **automated workflows**.

Quick Start
-----------

Essential Commands
~~~~~~~~~~~~~~~~~

**Most Common Translation Operations:**

.. code-block:: bash

   # Check status of all 37 languages
   make translation-status

   # Translate UI for a specific language
   python scripts/simple_translate_ui.py --language hr

   # Generate documentation for a language
   python scripts/generate_docs.py --language de

   # Validate translation quality
   make check-translations

   # Update all translations
   make update-all-translations

**Development Workflow:**

.. code-block:: bash

   # 1. Check current status
   make translation-status

   # 2. Add new language support
   python scripts/translation_manager.py --add-language --code=hr --name="Croatian"

   # 3. Generate UI translations
   python scripts/simple_translate_ui.py --language hr

   # 4. Validate quality
   python scripts/quality_validator.py --language hr

   # 5. Generate documentation
   python scripts/generate_docs.py --language hr

Translation System Architecture
------------------------------

Core Components
~~~~~~~~~~~~~~

**6 Specialized Translation Tools:**

1. **Translation Manager** (``translation_manager.py``)
   - Central orchestration system
   - Status monitoring and reporting
   - Batch operations and automation

2. **UI Translation Generator** (``simple_translate_ui.py``)
   - Automated React UI translation
   - JSON file generation
   - UI-specific validation

3. **Documentation Generator** (``generate_docs.py``)
   - Multilingual Sphinx documentation
   - PDF generation support
   - API documentation translation

4. **Quality Assurance Engine** (``quality_validator.py``)
   - Automated validation and scoring
   - Terminology consistency checking
   - Error detection and reporting

5. **Terminology Manager** (``terminology_manager.py``)
   - Centralized terminology database
   - Consistency enforcement
   - Context-aware translations

6. **Status Monitor** (``status_monitor.py``)
   - Real-time system monitoring
   - Performance metrics tracking
   - Health check automation

File Structure
~~~~~~~~~~~~~

**Translation System File Organization:**

.. code-block:: text

   turdparty/
   ├── config/
   │   ├── translation_config.json      # Main translation configuration
   │   └── Makefile                     # Translation make commands
   ├── lang/
   │   ├── terminology.json             # Centralized terminology database
   │   ├── {language_code}/
   │   │   ├── ui/                      # UI translation files
   │   │   │   ├── common.json          # Common UI elements
   │   │   │   ├── file_upload.json     # File upload interface
   │   │   │   ├── vm_status.json       # VM management interface
   │   │   │   └── dashboard.json       # Dashboard components
   │   │   └── docs/                    # Documentation translations
   │   │       ├── index.rst            # Main documentation
   │   │       └── api.rst              # API documentation
   ├── scripts/
   │   ├── translation_manager.py       # Central management
   │   ├── simple_translate_ui.py       # UI translation generator
   │   ├── generate_docs.py             # Documentation generator
   │   ├── quality_validator.py         # Quality assurance
   │   ├── terminology_manager.py       # Terminology management
   │   └── status_monitor.py            # System monitoring

Adding New Languages
-------------------

Step-by-Step Process
~~~~~~~~~~~~~~~~~~~

**1. Language Configuration:**

.. code-block:: bash

   # Add language to configuration
   python scripts/translation_manager.py --add-language --code=hr --name="Croatian"

This automatically updates:
- ``config/translation_config.json`` - Adds language to supported list
- ``lang/terminology.json`` - Creates terminology entries
- Language name mappings and metadata

**2. UI Translation Generation:**

.. code-block:: bash

   # Generate UI translation files
   python scripts/simple_translate_ui.py --language hr

Creates:
- ``lang/hr/ui/common.json`` - Common UI elements
- ``lang/hr/ui/file_upload.json`` - File upload interface
- ``lang/hr/ui/vm_status.json`` - VM management interface
- ``lang/hr/ui/dashboard.json`` - Dashboard components

**3. Quality Validation:**

.. code-block:: bash

   # Validate translation quality
   python scripts/quality_validator.py --language hr --detailed

Checks:
- JSON structure and syntax
- Translation completeness
- Terminology consistency
- Cultural appropriateness

**4. Documentation Generation:**

.. code-block:: bash

   # Generate documentation
   python scripts/generate_docs.py --language hr --format=html

Creates:
- HTML documentation in target language
- PDF documentation (optional)
- API documentation translation

**5. Integration Testing:**

.. code-block:: bash

   # Test integration
   python scripts/integration_tester.py --language hr

Validates:
- UI rendering with new translations
- Documentation accessibility
- System functionality

Configuration Management
-----------------------

Translation Configuration
~~~~~~~~~~~~~~~~~~~~~~~~

**Main Configuration File** (``config/translation_config.json``):

.. code-block:: json

   {
     "version": "2.2.0",
     "default_language": "en_GB",
     "fallback_language": "en_GB",
     "supported": [
       "af", "be", "bg", "bs", "cs", "cy", "da", "de", "el", "es",
       "et", "fi", "fr", "ga", "gsw", "hr", "hu", "is", "it", "ja",
       "lt", "lv", "mk", "mt", "nl", "pl", "pt", "ro", "ru", "sk",
       "sl", "sr", "sv", "tr", "uk", "zh", "zu"
     ],
     "language_names": {
       "hr": "Croatian",
       "sr": "Serbian",
       "is": "Icelandic"
     },
     "quality_thresholds": {
       "minimum_completeness": 0.85,
       "minimum_consistency": 0.90,
       "minimum_quality": 0.80
     },
     "ui_categories": [
       "common", "file_upload", "vm_status", "dashboard"
     ]
   }

**Configuration Parameters:**
- **supported**: List of all supported language codes
- **language_names**: Human-readable language names
- **quality_thresholds**: Quality control parameters
- **ui_categories**: UI translation file categories

Terminology Management
~~~~~~~~~~~~~~~~~~~~

**Terminology Database** (``lang/terminology.json``):

.. code-block:: json

   {
     "Virtual Machine": {
       "de": "Virtuelle Maschine",
       "fr": "Machine Virtuelle",
       "es": "Máquina Virtual",
       "hr": "Virtualni stroj",
       "sr": "Виртуелна машина"
     },
     "File Upload": {
       "de": "Datei-Upload",
       "fr": "Téléchargement de fichier",
       "es": "Subida de archivo",
       "hr": "Prijenos datoteke",
       "sr": "Отпремање датотеке"
     }
   }

**Adding New Terms:**

.. code-block:: bash

   # Add new terminology
   python scripts/terminology_manager.py --add-term "API Endpoint" --translations '{"de": "API-Endpunkt", "fr": "Point de terminaison API"}'

   # Validate terminology consistency
   python scripts/terminology_manager.py --validate --strict

UI Translation Development
-------------------------

Translation File Structure
~~~~~~~~~~~~~~~~~~~~~~~~~

**UI Translation JSON Structure:**

.. code-block:: json

   {
     "common": {
       "buttons": {
         "save": "Spremi",
         "cancel": "Odustani",
         "delete": "Obriši"
       },
       "navigation": {
         "home": "Početna",
         "dashboard": "Nadzorna ploča",
         "settings": "Postavke"
       },
       "messages": {
         "success": "Uspješno završeno",
         "error": "Dogodila se greška",
         "loading": "Učitavanje..."
       }
     }
   }

**Translation Guidelines:**
- **Consistency**: Use terminology database for technical terms
- **Context**: Consider UI context and space constraints
- **Cultural**: Adapt to cultural norms and expectations
- **Quality**: Maintain professional tone and accuracy

React Integration
~~~~~~~~~~~~~~~

**Using Translations in React Components:**

.. code-block:: javascript

   import { useTranslation } from 'react-i18next';

   function FileUploadComponent() {
     const { t } = useTranslation('file_upload');

     return (
       <div>
         <h1>{t('title')}</h1>
         <button>{t('buttons.upload')}</button>
         <p>{t('messages.drag_drop_hint')}</p>
       </div>
     );
   }

**Translation Key Naming Convention:**
- **Hierarchical**: Use dot notation for nested keys
- **Descriptive**: Clear, descriptive key names
- **Consistent**: Follow established naming patterns
- **Contextual**: Include context in key names when needed

Quality Assurance for Developers
-------------------------------

Quality Validation Commands
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Essential Quality Commands:**

.. code-block:: bash

   # Quick quality check
   make quality-check LANG=hr

   # Detailed quality analysis
   python scripts/quality_validator.py --language hr --verbose

   # Terminology validation
   python scripts/terminology_validator.py --language hr --strict

   # Cultural appropriateness check
   python scripts/cultural_validator.py --language hr

**Quality Metrics Understanding:**
- **Completeness Score**: Percentage of translated content (target: >85%)
- **Consistency Score**: Terminology usage consistency (target: >90%)
- **Quality Score**: Overall translation quality (target: >80%)
- **Performance Score**: System efficiency metrics

Pre-commit Quality Hooks
~~~~~~~~~~~~~~~~~~~~~~~

**Automated Quality Validation:**

.. code-block:: yaml

   # .pre-commit-config.yaml
   repos:
     - repo: local
       hooks:
         - id: translation-quality
           name: Translation Quality Check
           entry: python scripts/quality_validator.py --changed-only
           language: system
           files: '^lang/.*\.json$'

**Quality Gate Configuration:**
- **Minimum Quality**: 80% overall quality score
- **Terminology Consistency**: 90% consistency requirement
- **Completeness**: 85% translation completeness
- **Error Tolerance**: Zero critical errors allowed

Testing and Validation
---------------------

Automated Testing
~~~~~~~~~~~~~~~

**Translation System Tests:**

.. code-block:: bash

   # Run translation system tests
   python -m pytest tests/translation/ -v

   # Test specific language
   python -m pytest tests/translation/test_language.py::test_croatian -v

   # Integration tests
   python -m pytest tests/integration/test_translation_integration.py -v

**Test Categories:**
- **Unit Tests**: Individual component testing
- **Integration Tests**: System integration validation
- **Quality Tests**: Translation quality validation
- **Performance Tests**: System performance testing

Manual Testing Procedures
~~~~~~~~~~~~~~~~~~~~~~~~

**UI Translation Testing:**

.. code-block:: bash

   # Start development server with specific language
   REACT_APP_LANGUAGE=hr npm start

   # Test UI rendering
   python scripts/ui_tester.py --language hr --screenshots

   # Validate user experience
   python scripts/ux_validator.py --language hr

**Testing Checklist:**
- **UI Rendering**: All text displays correctly
- **Layout**: No text overflow or layout issues
- **Functionality**: All features work in target language
- **Navigation**: Menu and navigation elements function properly

Performance Optimization
-----------------------

Translation Performance
~~~~~~~~~~~~~~~~~~~~~~

**Performance Monitoring:**

.. code-block:: bash

   # Monitor translation performance
   python scripts/performance_monitor.py --realtime

   # Performance profiling
   python scripts/performance_profiler.py --language hr

   # Cache optimization
   python scripts/cache_optimizer.py --analyze

**Optimization Strategies:**
- **Caching**: Multi-level caching for frequently used translations
- **Lazy Loading**: Load translations on demand
- **Compression**: Compress translation files for faster loading
- **CDN Integration**: Use CDN for translation file delivery

Memory and Resource Management
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Resource Optimization:**
- **Memory Usage**: Optimize translation loading and caching
- **CPU Usage**: Efficient translation processing algorithms
- **Disk Usage**: Compress and optimize translation files
- **Network Usage**: Minimize translation file transfer

Troubleshooting
--------------

Common Issues and Solutions
~~~~~~~~~~~~~~~~~~~~~~~~~

**Translation Generation Issues:**

.. code-block:: bash

   # Issue: Translation generation fails
   # Solution: Check configuration and dependencies
   python scripts/diagnostic_tool.py --translation-system

   # Issue: Quality validation errors
   # Solution: Run detailed quality analysis
   python scripts/quality_validator.py --language hr --fix-suggestions

   # Issue: Terminology inconsistencies
   # Solution: Update terminology database
   python scripts/terminology_manager.py --update --language hr

**Common Error Messages:**
- **"Language not supported"**: Add language to configuration
- **"Translation file not found"**: Generate UI translations
- **"Quality threshold not met"**: Improve translation quality
- **"Terminology inconsistency"**: Update terminology database

Debugging Tools
~~~~~~~~~~~~~~

**Debug Commands:**

.. code-block:: bash

   # Debug translation system
   python scripts/debug_tool.py --translation --verbose

   # Trace translation generation
   python scripts/simple_translate_ui.py --language hr --debug --trace

   # Analyze quality issues
   python scripts/quality_debugger.py --language hr --detailed

**Log Analysis:**
- **Translation Logs**: Monitor translation generation process
- **Quality Logs**: Track quality validation results
- **Performance Logs**: Monitor system performance metrics
- **Error Logs**: Identify and resolve system errors

Best Practices
--------------

Development Guidelines
~~~~~~~~~~~~~~~~~~~~

**Translation Development Best Practices:**
- **Consistency**: Always use terminology database for technical terms
- **Quality**: Validate translations before committing
- **Testing**: Test UI rendering with new translations
- **Documentation**: Document translation decisions and rationale

**Code Organization:**
- **Modular**: Keep translation logic modular and reusable
- **Maintainable**: Write clear, maintainable translation code
- **Documented**: Document translation system components
- **Tested**: Comprehensive testing for all translation features

Collaboration Guidelines
~~~~~~~~~~~~~~~~~~~~~~

**Team Collaboration:**
- **Communication**: Coordinate translation efforts with team
- **Review**: Peer review of translation changes
- **Standards**: Follow established translation standards
- **Knowledge Sharing**: Share translation expertise and best practices

This developer guide provides comprehensive instructions for working with TurdParty's **industry-leading translation system**, enabling developers to efficiently manage **37 languages** with **professional quality** and **enterprise-grade reliability**.
