Docker Consolidation
===================

Overview
--------

TurdParty has undergone a comprehensive Docker consolidation to improve organization, standardize naming conventions, and enhance maintainability. All Docker-related configurations have been unified under the ``.dockerwrapper/`` directory.

Consolidation Benefits
---------------------

Unified Structure
~~~~~~~~~~~~~~~~

- **Single source of truth**: All Docker configurations in one location
- **Consistent naming**: Standardized ``turdparty_`` prefix for all containers
- **Better organization**: Logical grouping of related files
- **Easier maintenance**: Simplified updates and modifications

Standardized Naming
~~~~~~~~~~~~~~~~~~

All containers now follow the ``turdparty_`` naming convention:

- ``turdparty_api`` - FastAPI backend service
- ``turdparty_frontend`` - React frontend application
- ``turdparty_postgres`` - PostgreSQL database
- ``turdparty_redis`` - Redis cache and message broker
- ``turdparty_minio`` - MinIO object storage
- ``turdparty_celery_worker`` - Celery task workers

Directory Structure
------------------

The consolidated ``.dockerwrapper/`` directory contains:

.. code-block:: text

    .dockerwrapper/
    ├── Dockerfile                      # Main API container
    ├── Dockerfile.api                  # API-specific build
    ├── Dockerfile.dashboard            # Dashboard container
    ├── Dockerfile.playwright           # Testing container
    ├── Dockerfile.react-dev            # React development
    ├── Dockerfile.celery               # Celery workers
    ├── Dockerfile.minio                # MinIO with SSH
    ├── Dockerfile.test                 # Testing environment
    ├── Dockerfile.diagnostic           # Diagnostic tools
    ├── Dockerfile.simple               # Simple setups
    ├── Dockerfile.testing              # Comprehensive testing
    ├── Dockerfile.node                 # Node.js applications
    ├── docker-compose.yml              # Production configuration
    ├── dev-compose.yml                 # Development configuration
    ├── docker-compose.test.yml         # Testing environment
    ├── docker-compose.diagnostic.yml   # Diagnostic tools
    ├── docker-compose.testing.yml      # Comprehensive testing
    ├── docker-compose.cachet-custom.yml # Status page
    ├── docker-compose.celery.yml       # Celery workers
    ├── docker-compose.playwright.yml   # Playwright testing
    ├── restart-containers.sh           # Container management
    ├── docker-dashboard                # Dashboard script
    ├── verify-consolidation.sh         # Verification tool
    ├── config/                         # Service configurations
    ├── scripts/                        # Initialization scripts
    └── services/                       # Service definitions

Container Naming Standards
-------------------------

Naming Convention
~~~~~~~~~~~~~~~~

All containers follow the pattern: ``turdparty_{service}_{environment}``

Examples:
- ``turdparty_api`` (production)
- ``turdparty_postgres_testing`` (testing environment)
- ``turdparty_minio_diagnostic`` (diagnostic environment)

Network Configuration
~~~~~~~~~~~~~~~~~~~~

Standardized network naming:
- ``turdparty_network`` (default)
- ``turdparty_test_network`` (testing)
- ``turdparty_diagnostic_network`` (diagnostics)

Volume Naming
~~~~~~~~~~~~~

Consistent volume prefixes:
- ``turdparty_postgres_data``
- ``turdparty_minio_data``
- ``turdparty_redis_data``

Port Management
--------------

Standardized Port Ranges
~~~~~~~~~~~~~~~~~~~~~~~~

Core services use the 3050-3400 range:

.. list-table:: Port Assignments
   :header-rows: 1
   :widths: 20 15 15 50

   * - Service
     - Host Port
     - Container Port
     - Description
   * - API
     - 3050
     - 8000
     - FastAPI application
   * - Frontend
     - 3100
     - 3000
     - React application
   * - Dashboard
     - 3150
     - 8080
     - Monitoring dashboard
   * - PostgreSQL
     - 3200
     - 5432
     - PostgreSQL database
   * - React Dev
     - 3250
     - 3000
     - React development server
   * - MinIO
     - 3300-3301
     - 9000-9001
     - Object storage and console
   * - Redis
     - 3350
     - 6379
     - Redis cache/message broker
   * - Celery Flower
     - 3360
     - 5555
     - Celery monitoring

Environment-Specific Configurations
----------------------------------

Development Environment
~~~~~~~~~~~~~~~~~~~~~~

File: ``dev-compose.yml``

Features:
- Hot reloading for development
- Debug mode enabled
- Volume mounts for live code editing
- Development-specific environment variables

.. code-block:: yaml

    services:
      api:
        container_name: turdparty_api
        build:
          context: ..
          dockerfile: .dockerwrapper/Dockerfile.api
        ports:
          - "3050:8000"
        environment:
          - DEBUG=true
          - TEST_MODE=false
        volumes:
          - ..:/app
        networks:
          - turdparty_network

Production Environment
~~~~~~~~~~~~~~~~~~~~~

File: ``docker-compose.yml``

Features:
- Optimized for production
- Security hardening
- Resource limits
- Health checks

.. code-block:: yaml

    services:
      api:
        container_name: turdparty_api
        build:
          context: ..
          dockerfile: .dockerwrapper/Dockerfile.api
        ports:
          - "3050:8000"
        environment:
          - DEBUG=false
          - TEST_MODE=false
        restart: unless-stopped
        healthcheck:
          test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
          interval: 30s
          timeout: 10s
          retries: 3
        networks:
          - turdparty_network

Testing Environment
~~~~~~~~~~~~~~~~~~

File: ``docker-compose.test.yml``

Features:
- Isolated testing environment
- Test-specific configurations
- Temporary data volumes

.. code-block:: yaml

    services:
      test_runner:
        container_name: turdparty_test_runner
        build:
          context: ..
          dockerfile: .dockerwrapper/Dockerfile.testing
        environment:
          - TESTING=true
          - TEST_MODE=true
        networks:
          - turdparty_test_network

Migration Process
----------------

Files Migrated
~~~~~~~~~~~~~~

The following files were moved from ``docker/`` to ``.dockerwrapper/``:

**Docker Compose Files:**
- ``docker-compose.yml`` → Updated with new naming
- ``docker-compose.test.yml`` → Updated with new naming
- ``docker-compose.diagnostic.yml`` → Moved and updated
- ``docker-compose.testing.yml`` → Moved and updated

**Dockerfiles:**
- ``Dockerfile`` → Renamed to ``Dockerfile.node``
- ``Dockerfile.api`` → Updated with new paths
- ``Dockerfile.diagnostic`` → Moved
- ``Dockerfile.simple`` → Moved
- ``Dockerfile.testing`` → Moved

**Scripts and Utilities:**
- ``docker-dashboard`` → Updated container references
- ``docker-lock.json`` → Moved
- ``test_redirect_dockerfile`` → Moved

Path Updates
~~~~~~~~~~~

All Dockerfile and docker-compose references updated:
- Context paths: ``...`` → ``..``
- Dockerfile paths: ``docker/`` → ``.dockerwrapper/``
- Volume mount paths: Updated to new structure

Verification Tools
-----------------

Consolidation Verification
~~~~~~~~~~~~~~~~~~~~~~~~~

Use the verification script to check the consolidation:

.. code-block:: bash

    cd .dockerwrapper
    ./verify-consolidation.sh

This script verifies:
- Docker Compose file syntax
- Dockerfile validity
- Container naming patterns
- Network configurations
- Volume naming
- Port mappings
- Script permissions

Container Management
~~~~~~~~~~~~~~~~~~~

Use the restart script for container management:

.. code-block:: bash

    # Start development environment
    cd .dockerwrapper
    ./restart-containers.sh dev

    # Start production environment
    cd .dockerwrapper
    ./restart-containers.sh

Dashboard Management
~~~~~~~~~~~~~~~~~~~

Use the dashboard script for monitoring:

.. code-block:: bash

    # Start dashboard
    cd .dockerwrapper
    ./docker-dashboard start

    # View container status
    ./docker-dashboard status

    # View logs
    ./docker-dashboard logs api

Best Practices
-------------

Container Naming
~~~~~~~~~~~~~~~

- Always use ``turdparty_`` prefix
- Use descriptive service names
- Include environment suffix when needed
- Avoid version suffixes (``_v2``, ``_new``)

Network Configuration
~~~~~~~~~~~~~~~~~~~~

- Use dedicated networks for isolation
- Name networks with ``turdparty_`` prefix
- Separate networks for different environments
- Document network topology

Volume Management
~~~~~~~~~~~~~~~~

- Use named volumes with ``turdparty_`` prefix
- Include service name in volume names
- Use environment-specific volume names
- Document data persistence requirements

Port Management
~~~~~~~~~~~~~~

- Use the 3050-3400 range for core services
- Document all port assignments
- Avoid port conflicts between environments
- Use consistent port mappings

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~

**Container naming conflicts**
  Ensure all containers use ``turdparty_`` prefix

**Port conflicts**
  Check port assignments and update conflicting services

**Network connectivity issues**
  Verify network configurations and container connections

**Volume mount problems**
  Check volume paths and permissions

**Build failures**
  Verify Dockerfile paths and context directories

Verification Commands
~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

    # Check container naming
    docker ps --format "table {{.Names}}\t{{.Status}}"

    # Verify networks
    docker network ls | grep turdparty

    # Check volumes
    docker volume ls | grep turdparty

    # Test connectivity
    docker exec turdparty_api curl http://turdparty_postgres:5432

Migration Rollback
~~~~~~~~~~~~~~~~~

If issues arise, the original structure can be restored:

.. code-block:: bash

    # Restore from git history
    git checkout HEAD~1 -- docker/

    # Or use backup if available
    cp -r docker.backup/ docker/

Future Considerations
-------------------

Planned Improvements
~~~~~~~~~~~~~~~~~~~

- **Kubernetes migration**: Prepare for container orchestration
- **Multi-environment support**: Enhanced environment management
- **Service mesh integration**: Advanced networking capabilities
- **Monitoring integration**: Enhanced observability

Maintenance
~~~~~~~~~~

- Regular verification of container configurations
- Periodic review of naming conventions
- Updates to documentation as configurations change
- Testing of all environments after changes
