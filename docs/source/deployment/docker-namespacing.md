# Docker Container Namespacing

This document describes the Docker container namespacing conventions used in the TurdParty project.

## Container Naming Convention

All Docker containers in the TurdParty project follow a consistent naming convention to improve organization and avoid conflicts with other projects:

```
turdparty_<service_name>
```

For example:
- `turdparty_api` - The API service
- `turdparty_db` - The PostgreSQL database service
- `turdparty_redis` - The Redis service
- `turdparty_minio` - The MinIO object storage service
- `turdparty_celery_file_ops` - The Celery worker for file operations
- `turdparty_celery_vm_ops` - The Celery worker for VM operations
- `turdparty_celery_monitoring` - The Celery worker for monitoring operations
- `turdparty_test_runner` - The test runner service

## Network Namespacing

In addition to container naming, the Docker networks are also namespaced:

```
turdparty_<environment>_network
```

For example:
- `turdparty_test_network` - The network for the test environment
- `turdparty_dev_network` - The network for the development environment
- `turdparty_prod_network` - The network for the production environment

## Docker Compose Configuration

The Docker Compose files have been updated to use the new naming convention:

```yaml
services:
  db:
    container_name: turdparty_db
    # ...

  redis:
    container_name: turdparty_redis
    # ...

  minio:
    container_name: turdparty_minio
    # ...
```

The network configuration has also been updated:

```yaml
networks:
  default:
    name: turdparty_test_network
    external: false
```

## Scripts and Tools

All scripts and tools that interact with Docker containers have been updated to use the new naming convention:

### run_single_test.sh

```bash
# Check if the test environment is running
if ! docker compose -f docker-compose.test.yml ps | grep -q "turdparty_test_runner"; then
    # Start the test environment
    # ...
fi

# Run the test
docker compose -f docker-compose.test.yml exec -e IN_DOCKER=true test-runner pytest "$1" -v
```

### run_celery_tests.sh

```bash
# Start the test environment
docker compose -f docker-compose.test.yml up -d

# Run the Celery integration tests
docker compose -f docker-compose.test.yml exec -e IN_DOCKER=true test-runner pytest tests/integration/test_celery_integration.py -v
```

## Benefits of Namespacing

The new namespacing convention provides several benefits:

1. **Clarity**: Container names clearly indicate which project they belong to
2. **Conflict Avoidance**: Prevents conflicts with other Docker projects running on the same host
3. **Organization**: Makes it easier to identify and manage containers
4. **Consistency**: Provides a consistent naming scheme across all environments

## Migration from Old Naming Convention

If you have existing containers using the old naming convention (`dockerwrapper-*`), you should stop and remove them before using the new containers:

```bash
# Stop and remove old containers
docker stop $(docker ps -a -q --filter "name=dockerwrapper-")
docker rm $(docker ps -a -q --filter "name=dockerwrapper-")

# Remove old networks
docker network rm dockerwrapper_network

# Start containers with new naming convention
cd .dockerwrapper
docker compose -f docker-compose.test.yml up -d
```

## Troubleshooting

If you encounter issues with the Docker containers:

1. Check the container logs:
   ```bash
   docker logs turdparty_api
   ```

2. Restart the containers:
   ```bash
   cd .dockerwrapper
   docker compose -f docker-compose.test.yml down
   docker compose -f docker-compose.test.yml up -d
   ```

3. Check the container status:
   ```bash
   docker ps -a --filter "name=turdparty_"
   ```

4. Check the network status:
   ```bash
   docker network ls --filter "name=turdparty_"
   ```
