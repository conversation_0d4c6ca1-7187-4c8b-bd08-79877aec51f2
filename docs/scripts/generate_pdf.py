#!/usr/bin/env python3
"""
<PERSON>ript to generate PDF documentation using Sphinx.

This script:
1. Builds the PDF documentation directly using Sphinx's LaTeX builder
2. Copies the generated PDF to the specified location
"""

import os
import sys
import subprocess
import argparse
import shutil
from pathlib import Path
import logging
import tempfile
import zipfile
import requests
import io

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("generate_pdf")

def download_and_extract_tectonic():
    """Download and extract the Tectonic LaTeX distribution."""
    logger.info("Downloading Tectonic (portable LaTeX distribution)...")

    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    logger.info(f"Created temporary directory: {temp_dir}")

    # Download Tectonic
    tectonic_url = "https://github.com/tectonic-typesetting/tectonic/releases/download/tectonic%400.12.0/tectonic-0.12.0-x86_64-unknown-linux-gnu.tar.gz"

    try:
        logger.info(f"Downloading Tectonic from {tectonic_url}...")
        response = requests.get(tectonic_url)
        response.raise_for_status()

        # Extract the tarball
        tarball_path = os.path.join(temp_dir, "tectonic.tar.gz")
        with open(tarball_path, 'wb') as f:
            f.write(response.content)

        logger.info(f"Extracting Tectonic to {temp_dir}...")
        subprocess.run(["tar", "-xzf", tarball_path, "-C", temp_dir], check=True)

        # Make the binary executable
        tectonic_bin = os.path.join(temp_dir, "tectonic")
        os.chmod(tectonic_bin, 0o755)

        logger.info(f"Tectonic extracted to {tectonic_bin}")
        return tectonic_bin
    except Exception as e:
        logger.error(f"Error downloading or extracting Tectonic: {e}")
        return None

def build_pdf_with_sphinx(source_dir, build_dir, pdf_filename):
    """Build PDF documentation using Sphinx's LaTeX builder and Tectonic."""
    logger.info("Building PDF documentation with Sphinx and Tectonic...")

    # Create the build directory if it doesn't exist
    os.makedirs(build_dir, exist_ok=True)

    # Create the latex build directory
    latex_dir = os.path.join(build_dir, "latex")
    os.makedirs(latex_dir, exist_ok=True)

    # Create the pdf directory
    pdf_dir = os.path.join(build_dir, "pdf")
    os.makedirs(pdf_dir, exist_ok=True)

    # Build the LaTeX documentation
    logger.info("Running Sphinx to generate LaTeX...")
    latex_cmd = [
        "sphinx-build",
        "-b", "latex",
        source_dir,
        latex_dir
    ]

    try:
        # Run the command and capture output
        result = subprocess.run(latex_cmd, check=True, capture_output=True, text=True)
        logger.info("Sphinx LaTeX command output:")
        logger.info(result.stdout)
        if result.stderr:
            logger.warning("Sphinx LaTeX stderr:")
            logger.warning(result.stderr)

        logger.info("LaTeX documentation build command completed")

        # List all files in the latex directory
        logger.info(f"Files in {latex_dir}:")
        for file in os.listdir(latex_dir):
            logger.info(f"  - {file}")

        # Find the .tex file
        tex_files = [f for f in os.listdir(latex_dir) if f.endswith('.tex')]
        if not tex_files:
            logger.error("No .tex files found in the latex directory")

            # Try using the rinoh builder as a fallback
            return build_pdf_with_rinoh(source_dir, build_dir, pdf_filename)

        # Usually there's just one .tex file, which is the main document
        tex_file = tex_files[0]
        tex_path = os.path.join(latex_dir, tex_file)
        logger.info(f"Found TeX file: {tex_path}")

        # Download and extract Tectonic
        tectonic_bin = download_and_extract_tectonic()
        if not tectonic_bin:
            logger.error("Failed to download Tectonic, falling back to rinoh")
            return build_pdf_with_rinoh(source_dir, build_dir, pdf_filename)

        # Run Tectonic to build the PDF
        logger.info(f"Running Tectonic to build PDF from {tex_path}...")

        # Save current directory to return to it later
        original_dir = os.getcwd()

        # Change to the latex directory
        os.chdir(latex_dir)

        try:
            # Run Tectonic
            tectonic_cmd = [tectonic_bin, tex_file]
            subprocess.run(tectonic_cmd, check=True)

            # Check if the PDF was generated
            pdf_file = os.path.splitext(tex_file)[0] + ".pdf"
            if not os.path.exists(pdf_file):
                logger.error(f"PDF file not generated: {pdf_file}")
                os.chdir(original_dir)
                return build_pdf_with_rinoh(source_dir, build_dir, pdf_filename)

            # Copy the PDF to the pdf directory
            pdf_path = os.path.join(pdf_dir, pdf_filename)
            shutil.copy(pdf_file, pdf_path)

            # Return to original directory
            os.chdir(original_dir)

            logger.info(f"PDF documentation generated successfully: {pdf_path}")
            return True
        except Exception as e:
            logger.error(f"Error generating PDF with Tectonic: {e}")
            os.chdir(original_dir)
            return build_pdf_with_rinoh(source_dir, build_dir, pdf_filename)
    except Exception as e:
        logger.error(f"Error building LaTeX documentation: {e}")
        return build_pdf_with_rinoh(source_dir, build_dir, pdf_filename)

def build_pdf_with_rinoh(source_dir, build_dir, pdf_filename):
    """Build PDF documentation using Sphinx's rinoh builder."""
    logger.info("Building PDF documentation with Sphinx rinoh builder...")

    # Create the build directory if it doesn't exist
    os.makedirs(build_dir, exist_ok=True)

    # Create the pdf directory
    pdf_dir = os.path.join(build_dir, "pdf")
    os.makedirs(pdf_dir, exist_ok=True)

    # Create the rinoh build directory
    rinoh_dir = os.path.join(build_dir, "rinoh")
    os.makedirs(rinoh_dir, exist_ok=True)

    # Build the PDF documentation directly with Sphinx's rinoh builder
    logger.info("Running Sphinx to generate PDF with rinoh...")
    rinoh_cmd = [
        "sphinx-build",
        "-b", "rinoh",
        source_dir,
        rinoh_dir
    ]

    try:
        # Run the command and capture output
        result = subprocess.run(rinoh_cmd, check=True, capture_output=True, text=True)
        logger.info("Sphinx rinoh command output:")
        logger.info(result.stdout)
        if result.stderr:
            logger.warning("Sphinx rinoh stderr:")
            logger.warning(result.stderr)

        logger.info("PDF documentation build command completed")

        # List all files in the rinoh directory
        logger.info(f"Files in {rinoh_dir}:")
        for file in os.listdir(rinoh_dir):
            logger.info(f"  - {file}")

        # Find the generated PDF file
        pdf_files = [f for f in os.listdir(rinoh_dir) if f.endswith('.pdf')]
        if not pdf_files:
            logger.error("No PDF files found in the rinoh build directory")
            return False

        # Usually there's just one PDF file
        source_pdf = os.path.join(rinoh_dir, pdf_files[0])
        logger.info(f"Found PDF file: {source_pdf}")

        # Copy the PDF to the pdf directory with the specified filename
        target_pdf = os.path.join(pdf_dir, pdf_filename)
        logger.info(f"Copying PDF from {source_pdf} to {target_pdf}")
        shutil.copy(source_pdf, target_pdf)

        logger.info(f"PDF documentation copied to: {target_pdf}")
        return True
    except Exception as e:
        logger.error(f"Error building PDF documentation with rinoh: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def generate_pdf_with_simple_method(html_dir, pdf_dir, filename="documentation.pdf"):
    """Generate a simple PDF with links to the HTML documentation."""
    logger.info("Generating a simple PDF with links to the HTML documentation...")

    # Create the PDF directory if it doesn't exist
    os.makedirs(pdf_dir, exist_ok=True)

    # Get all HTML files
    html_files = []
    for root, _, files in os.walk(html_dir):
        for file in files:
            if file.endswith(".html"):
                html_files.append(os.path.join(root, file))

    if not html_files:
        logger.error("No HTML files found")
        return False

    # Sort HTML files to ensure consistent order
    html_files.sort()
    logger.info(f"Found {len(html_files)} HTML files")

    # Create a PDF file with links to the HTML documentation
    pdf_path = os.path.join(pdf_dir, filename)

    try:
        # Create a simple text file with links to the HTML documentation
        with open(pdf_path, 'w', encoding='utf-8') as f:
            f.write("TurdParty Documentation\n")
            f.write("======================\n\n")
            f.write("This PDF contains links to the HTML documentation.\n\n")

            # Add links to the HTML files
            for html_file in html_files:
                # Get the relative path to the HTML file
                rel_path = os.path.relpath(html_file, html_dir)

                # Get the title from the HTML file
                title = rel_path
                try:
                    with open(html_file, 'r', encoding='utf-8') as html_f:
                        content = html_f.read()
                        import re
                        title_match = re.search(r'<title>(.*?)</title>', content)
                        if title_match:
                            title = title_match.group(1)
                except Exception as e:
                    logger.warning(f"Error getting title from {html_file}: {e}")

                # Add a link to the HTML file
                f.write(f"* {title}: file://{os.path.abspath(html_file)}\n")

            # Add a note about the HTML documentation
            f.write("\n\nNote: The HTML documentation is also available in the build/html directory.\n")

        logger.info(f"Simple PDF generated successfully: {pdf_path}")
        return True
    except Exception as e:
        logger.error(f"Error generating simple PDF: {e}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Generate PDF documentation using Sphinx")
    parser.add_argument("--source-dir", default="source", help="Source directory for Sphinx documentation")
    parser.add_argument("--build-dir", default="build", help="Build directory for Sphinx documentation")
    parser.add_argument("--pdf-filename", default="TurdParty_Documentation.pdf", help="Filename for the PDF documentation")
    parser.add_argument("--method", choices=["pdfkit", "weasyprint", "simple"], default="simple", help="Method to convert HTML to PDF")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.setLevel(logging.DEBUG)

    # Log the arguments
    logger.info(f"Source directory: {args.source_dir}")
    logger.info(f"Build directory: {args.build_dir}")
    logger.info(f"PDF filename: {args.pdf_filename}")

    # Check if directories exist
    if not os.path.exists(args.source_dir):
        logger.error(f"Source directory does not exist: {args.source_dir}")
        sys.exit(1)

    # Build PDF documentation
    if not build_pdf_with_sphinx(args.source_dir, args.build_dir, args.pdf_filename):
        sys.exit(1)

    # Convert HTML documentation to PDF
    html_dir = os.path.join(args.build_dir, "html")
    pdf_dir = os.path.join(args.build_dir, "pdf")

    # Check if HTML directory exists
    if not os.path.exists(html_dir):
        logger.error(f"HTML directory does not exist: {html_dir}")
        sys.exit(1)

    # List files in HTML directory
    logger.info(f"Files in HTML directory ({html_dir}):")
    for root, dirs, files in os.walk(html_dir):
        for file in files:
            logger.info(f"  - {os.path.join(root, file)}")

    # Use the simple method for PDF generation
    if args.method == "simple":
        if not generate_pdf_with_simple_method(html_dir, pdf_dir, args.pdf_filename):
            sys.exit(1)
    # Check if PDF was generated
    pdf_path = os.path.join(args.build_dir, "pdf", args.pdf_filename)
    if os.path.exists(pdf_path):
        pdf_size = os.path.getsize(pdf_path) / 1024  # Size in KB
        logger.info(f"PDF generated successfully: {pdf_path} ({pdf_size:.2f} KB)")
    else:
        logger.error(f"PDF was not generated: {pdf_path}")
        sys.exit(1)

    logger.info("PDF generation completed successfully")

if __name__ == "__main__":
    main()
