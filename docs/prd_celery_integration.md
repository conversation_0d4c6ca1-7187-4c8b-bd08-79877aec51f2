# Product Requirements Document: Celery Integration for TurdParty API

## Overview

This PRD outlines the requirements for integrating Celery with the TurdParty API to enable asynchronous task processing. The integration will allow for long-running operations to be executed in the background, improving the user experience and system scalability.

## Goals

- Enable asynchronous processing of resource-intensive operations
- Provide a consistent API for task submission, monitoring, and management
- Distribute workload across specialized workers for different operation types
- Improve system scalability and responsiveness

## API Structure

```mermaid
graph TD
    A["API Root"] --> B["/api/v1"]
    B --> C["/async"]
    C --> D["/tasks"]
    C --> G["/workers"]

    D --> D1["POST /tasks - Submit generic task"]
    D --> D2["GET /tasks - List all tasks"]
    D --> D3["GET /tasks/{task_id} - Get task status"]
    D --> D4["DELETE /tasks/{task_id} - Cancel task"]
    D --> E["/tasks/file-ops"]
    D --> F["/tasks/vm-ops"]

    E --> E1["POST /tasks/file-ops/process - Process file"]
    E --> E2["POST /tasks/file-ops/analyze - Analyze file"]

    F --> F1["POST /tasks/vm-ops/create - Create VM"]
    F --> F2["POST /tasks/vm-ops/inject - Inject file into VM"]

    G --> G1["GET /workers - List all workers"]
    G --> G2["GET /workers/stats - Get worker statistics"]
```

## Technical Architecture

### Components

1. **Celery Application**
   - Central configuration for task queues and workers
   - Integration with Redis as message broker and result backend

2. **Task Queues**
   - Default queue for general tasks
   - File operations queue for file processing and analysis
   - VM operations queue for VM management and file injection
   - Analysis queue for malware analysis and report generation
   - Monitoring queue for health checks and maintenance tasks

3. **Celery Workers**
   - Specialized workers for each queue type
   - Configurable concurrency and resource limits

4. **API Endpoints**
   - Task submission, monitoring, and management endpoints
   - Worker status and statistics endpoints

## Detailed Requirements

### 1. Celery Configuration

- **Redis Integration**
  - Use Redis as the message broker and result backend
  - Configure connection parameters via environment variables
  - Implement fallback mechanisms for connection failures

- **Task Queue Configuration**
  - Define separate queues for different operation types
  - Configure routing rules based on task type
  - Set appropriate task time limits and retry policies

### 2. Task Modules

- **File Operations Tasks**
  - `process_file`: Process an uploaded file (format conversion, validation)
  - `analyze_file`: Analyze a file for security threats or other properties

- **VM Operations Tasks**
  - `create_vm`: Create a new VM with specified configuration
  - `start_vm`: Start an existing VM
  - `stop_vm`: Stop a running VM
  - `inject_file`: Inject a file into a VM at a specified path

- **Analysis Tasks**
  - `analyze_malware`: Perform detailed malware analysis on a file
  - `generate_report`: Generate a comprehensive analysis report

- **Monitoring Tasks**
  - `check_vm_status`: Periodic task to check the status of all VMs
  - `cleanup_expired_tasks`: Remove expired task results and temporary files

### 3. API Endpoints

- **Task Management Endpoints**
  - `POST /api/v1/async/tasks`: Submit a generic task
  - `GET /api/v1/async/tasks`: List all tasks (with filtering and pagination)
  - `GET /api/v1/async/tasks/{task_id}`: Get task status and result
  - `DELETE /api/v1/async/tasks/{task_id}`: Cancel a running task

- **File Operations Endpoints**
  - `POST /api/v1/async/tasks/file-ops/process`: Process a file asynchronously
  - `POST /api/v1/async/tasks/file-ops/analyze`: Analyze a file asynchronously

- **VM Operations Endpoints**
  - `POST /api/v1/async/tasks/vm-ops/create`: Create a VM asynchronously
  - `POST /api/v1/async/tasks/vm-ops/inject`: Inject a file into a VM asynchronously

- **Worker Management Endpoints**
  - `GET /api/v1/async/workers`: List all active workers
  - `GET /api/v1/async/workers/stats`: Get worker statistics

## Implementation Tracking

| Component | Status | Test Status | Notes |
|-----------|--------|-------------|-------|
| Celery Configuration | Completed | N/A | Implemented in api/celery_app.py and api/core/celery_config.py |
| Task Modules | Completed | Not Started | Implemented file_ops.py and vm_ops.py task modules |
| API Endpoints | Completed | Completed | Implemented async_routes.py with all required endpoints |
| Integration Tests | Completed | N/A | Created test_async_routes.py with tests for all endpoints |

## Next Steps

1. Set up Redis service for the Celery broker and result backend
2. Configure Docker Compose to include Celery workers
3. Create worker startup scripts for different queue types
4. Add documentation for using the async API endpoints
