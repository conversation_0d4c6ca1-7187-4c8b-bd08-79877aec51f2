# Inspektor Gadget ML Data Collection Integration PRD

## Executive Summary

This PRD outlines the integration of Inspektor Gadget with TurdParty for comprehensive ML data collection from binary execution in isolated Linux environments. The system will stream real-time eBPF-based observability data to an ELK stack for analysis and machine learning processing.

## 1. Project Overview

### 1.1 Objectives
- Integrate Inspektor Gadget for comprehensive binary behavior analysis
- Stream real-time observability data to ELK stack
- Correlate data across binary execution lifecycle using file UUIDs
- Support 5 most popular Linux distributions for analysis
- Provide API-first architecture with comprehensive testing

### 1.2 Success Criteria
- Real-time data streaming from Vagrant VMs to ELK stack
- Complete system call, network, and file operation tracking
- UUID-based correlation across all data points
- Sub-second latency for critical security events
- 99.9% data capture reliability

## 2. Architecture Overview

### 2.1 System Components

```mermaid
graph TB
    A[File Upload API] --> B[MinIO Storage]
    B --> C[VM Provisioning Service]
    C --> D[Vagrant Linux VMs]
    D --> E[Inspektor Gadget]
    E --> F[Data Streaming Service]
    F --> G[Logstash]
    G --> H[Elasticsearch]
    H --> I[Kibana Dashboard]
    J[Analysis API] --> H
    K[ML Processing] --> H
```

### 2.2 Data Flow
1. Binary uploaded via API with UUID assignment
2. VM provisioned with Inspektor Gadget pre-installed
3. Binary transferred to VM and executed
4. Inspektor Gadget captures eBPF data in real-time
5. Data streamed to Logstash with UUID correlation
6. Processed data indexed in Elasticsearch
7. Analysis results available via API and Kibana

## 3. Technical Requirements

### 3.1 Linux Distribution Support
- Ubuntu 22.04 LTS (Primary)
- CentOS Stream 9
- Debian 12
- Fedora 39
- Alpine Linux 3.19

### 3.2 Data Collection Categories

#### 3.2.1 System Call Monitoring
- **Gadgets**: `trace_exec`, `trace_open`, `trace_mount`
- **Data Points**:
  - Process execution (PID, PPID, command line, environment)
  - File operations (open, read, write, close, permissions)
  - Mount operations (filesystem changes)
  - Signal handling and process lifecycle

#### 3.2.2 Network Activity
- **Gadgets**: `trace_tcp`, `trace_dns`, `trace_bind`
- **Data Points**:
  - TCP connections (source/destination IPs, ports)
  - DNS queries and responses
  - Socket binding operations
  - Network protocol analysis

#### 3.2.3 File System Operations
- **Gadgets**: `trace_fsslower`, `top_file`, `fsnotify`
- **Data Points**:
  - File access patterns
  - I/O performance metrics
  - Directory monitoring
  - File modification events

#### 3.2.4 Security Events
- **Gadgets**: `trace_capabilities`, `audit_seccomp`, `trace_lsm`
- **Data Points**:
  - Capability checks and privilege escalation
  - Seccomp policy violations
  - Linux Security Module events
  - Permission changes

#### 3.2.5 Performance Metrics
- **Gadgets**: `top_process`, `profile_cpu`, `top_blockio`
- **Data Points**:
  - CPU usage and profiling
  - Memory consumption
  - Block I/O statistics
  - Process resource utilization

### 3.3 Data Schema

#### 3.3.1 Base Event Structure
```json
{
  "timestamp": "2025-01-21T10:30:00.000Z",
  "file_uuid": "550e8400-e29b-41d4-a716-************",
  "vm_id": "vm-ubuntu-001",
  "gadget_type": "trace_exec",
  "event_category": "process",
  "event_type": "execution",
  "correlation_id": "550e8400-e29b-41d4-a716-************"
}
```

#### 3.3.2 Process Event Schema
```json
{
  "pid": 1234,
  "ppid": 1000,
  "uid": 1000,
  "gid": 1000,
  "comm": "malware.exe",
  "cmdline": "/tmp/malware.exe --config /tmp/config.json",
  "cwd": "/tmp",
  "environment": ["PATH=/usr/bin", "HOME=/home/<USER>"]
}
```

#### 3.3.3 Network Event Schema
```json
{
  "src_ip": "*************",
  "dst_ip": "*******",
  "src_port": 45678,
  "dst_port": 53,
  "protocol": "UDP",
  "dns_query": "malicious-domain.com",
  "dns_response": "*******"
}
```

## 4. API Design

### 4.1 Analysis Endpoints

#### 4.1.1 Start Binary Analysis
```
POST /api/v1/analysis/start
Content-Type: application/json

{
  "file_uuid": "550e8400-e29b-41d4-a716-************",
  "vm_template": "ubuntu-22.04",
  "analysis_config": {
    "timeout": 300,
    "gadgets": ["trace_exec", "trace_tcp", "trace_open"],
    "capture_network": true,
    "capture_filesystem": true
  }
}

Response:
{
  "analysis_id": "analysis-123",
  "status": "started",
  "vm_id": "vm-ubuntu-001",
  "estimated_completion": "2025-01-21T10:35:00.000Z"
}
```

#### 4.1.2 Get Analysis Status
```
GET /api/v1/analysis/{analysis_id}/status

Response:
{
  "analysis_id": "analysis-123",
  "status": "running",
  "progress": 65,
  "events_captured": 1247,
  "vm_status": "running",
  "data_stream_status": "active"
}
```

#### 4.1.3 Get Analysis Results
```
GET /api/v1/analysis/{analysis_id}/results

Response:
{
  "analysis_id": "analysis-123",
  "file_uuid": "550e8400-e29b-41d4-a716-************",
  "summary": {
    "total_events": 2456,
    "process_events": 45,
    "network_events": 123,
    "file_events": 567,
    "security_events": 12
  },
  "elasticsearch_indices": [
    "inspektor-gadget-2025.01.21"
  ],
  "kibana_dashboard_url": "https://kibana.turdparty.localhost/app/dashboards#/view/analysis-123"
}
```

### 4.2 VM Management Integration

#### 4.2.1 List Analysis-Ready VMs
```
GET /api/v1/virtual-machines/analysis-ready

Response:
{
  "vms": [
    {
      "template": "ubuntu-22.04",
      "status": "available",
      "inspektor_gadget_version": "v0.37.0",
      "capabilities": ["trace_exec", "trace_tcp", "trace_open"]
    }
  ]
}
```

## 5. Implementation Plan

### 5.1 Phase 1: Infrastructure Setup (Week 1-2)
- [ ] ELK stack integration with Docker Compose
- [ ] Vagrant VM templates with Inspektor Gadget pre-installed
- [ ] Basic data streaming pipeline
- [ ] API endpoint scaffolding

### 5.2 Phase 2: Core Functionality (Week 3-4)
- [ ] Binary upload and VM provisioning integration
- [ ] Inspektor Gadget data collection implementation
- [ ] Real-time streaming to Logstash
- [ ] UUID correlation across all data points

### 5.3 Phase 3: Advanced Features (Week 5-6)
- [ ] Multi-gadget orchestration
- [ ] Security event prioritization
- [ ] Performance optimization
- [ ] Comprehensive error handling

### 5.4 Phase 4: Testing and Documentation (Week 7-8)
- [ ] Unit tests for all API endpoints
- [ ] Integration tests for data pipeline
- [ ] Playwright UI tests for analysis dashboard
- [ ] Performance and load testing

## 6. Testing Strategy

### 6.1 Unit Tests
- API endpoint validation
- Data transformation logic
- UUID correlation mechanisms
- Error handling scenarios

### 6.2 Integration Tests
- End-to-end data flow validation
- ELK stack integration testing
- VM provisioning and cleanup
- Real-time streaming verification

### 6.3 Playwright UI Tests
- Analysis dashboard functionality
- Real-time data visualization
- Export and reporting features
- Error state handling

## 7. Security Considerations

### 7.1 VM Isolation
- Network segmentation for analysis VMs
- Automated VM cleanup after analysis
- Resource limits and monitoring
- Malware containment protocols

### 7.2 Data Security
- Encrypted data transmission
- Access control for analysis results
- Data retention policies
- Audit logging for all operations

## 8. Monitoring and Alerting

### 8.1 System Health Metrics
- Data streaming latency
- VM resource utilization
- ELK stack performance
- Analysis completion rates

### 8.2 Security Alerts
- Suspicious binary behavior detection
- Network anomaly identification
- Privilege escalation attempts
- Data exfiltration patterns

## 9. Success Metrics

### 9.1 Performance KPIs
- Analysis completion time: < 5 minutes for standard binaries
- Data streaming latency: < 100ms
- System availability: 99.9%
- Concurrent analysis capacity: 10 VMs

### 9.2 Quality KPIs
- Data capture completeness: 99.9%
- False positive rate: < 1%
- Test coverage: > 90%
- Documentation completeness: 100%
