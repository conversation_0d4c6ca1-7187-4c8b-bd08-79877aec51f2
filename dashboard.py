#!/usr/bin/env python3
"""
TurdParty Service Dashboard

A simple dashboard to monitor TurdParty services and provide quick access to service URLs.
"""

import sys
import time
import json
import subprocess
import argparse
from typing import Dict, List, Any
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

class TurdPartyDashboard:
    def __init__(self):
        self.services = {
            "API": {
                "url": "http://localhost:3050/api/v1/docs/",
                "health": "http://localhost:3050/api/v1/health/",
                "description": "FastAPI Backend with Swagger Documentation"
            },
            "Frontend (Production)": {
                "url": "http://localhost:3100",
                "health": "http://localhost:3100",
                "description": "React Frontend Production Build"
            },
            "Frontend (Development)": {
                "url": "http://localhost:3250",
                "health": "http://localhost:3250",
                "description": "React Frontend Development Server"
            },
            "MinIO Storage": {
                "url": "http://localhost:3301",
                "health": "http://localhost:3300/minio/health/live",
                "description": "MinIO Object Storage Console"
            },
            "Flower Monitoring": {
                "url": "http://localhost:5556",
                "health": "http://localhost:5556",
                "description": "Celery Task Monitoring"
            },
            "Cachet Status": {
                "url": "http://localhost:3501",
                "health": "http://localhost:3501",
                "description": "Service Status Page"
            }
        }

    def get_container_status(self) -> Dict[str, Any]:
        """Get status of all TurdParty containers."""
        try:
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=turdparty", "--format", "json"],
                capture_output=True,
                text=True,
                check=True
            )
            
            containers = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    containers.append(json.loads(line))
            
            return {"status": "success", "containers": containers}
        except Exception as e:
            return {"status": "error", "error": str(e)}

    def generate_html(self) -> str:
        """Generate HTML dashboard."""
        container_status = self.get_container_status()
        
        html = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TurdParty Service Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .services { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .service-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .service-card h3 { margin-top: 0; color: #333; }
        .service-card a { color: #007bff; text-decoration: none; }
        .service-card a:hover { text-decoration: underline; }
        .containers { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .container-item { padding: 10px; border-bottom: 1px solid #eee; }
        .status-running { color: #28a745; }
        .status-error { color: #dc3545; }
        .refresh-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        .refresh-btn:hover { background: #0056b3; }
    </style>
    <script>
        function refreshPage() {
            location.reload();
        }
        // Auto-refresh every 30 seconds
        setTimeout(refreshPage, 30000);
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 TurdParty Service Dashboard</h1>
            <p>Monitor and access all TurdParty services</p>
            <button class="refresh-btn" onclick="refreshPage()">Refresh Status</button>
        </div>
        
        <div class="services">
"""
        
        # Add service cards
        for name, service in self.services.items():
            html += f"""
            <div class="service-card">
                <h3>{name}</h3>
                <p>{service['description']}</p>
                <p><strong>URL:</strong> <a href="{service['url']}" target="_blank">{service['url']}</a></p>
                <p><strong>Health:</strong> <a href="{service['health']}" target="_blank">{service['health']}</a></p>
            </div>
"""
        
        html += """
        </div>
        
        <div class="containers">
            <h2>Container Status</h2>
"""
        
        # Add container status
        if container_status["status"] == "success":
            for container in container_status["containers"]:
                name = container.get("Names", "Unknown")
                status = container.get("Status", "Unknown")
                ports = container.get("Ports", "")
                
                status_class = "status-running" if "Up" in status else "status-error"
                
                html += f"""
            <div class="container-item">
                <strong>{name}</strong> - <span class="{status_class}">{status}</span>
                {f"<br>Ports: {ports}" if ports else ""}
            </div>
"""
        else:
            html += f'<p class="status-error">Error getting container status: {container_status["error"]}</p>'
        
        html += """
        </div>
    </div>
</body>
</html>
"""
        return html

class DashboardHandler(BaseHTTPRequestHandler):
    def __init__(self, dashboard, *args, **kwargs):
        self.dashboard = dashboard
        super().__init__(*args, **kwargs)

    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(self.dashboard.generate_html().encode())

    def log_message(self, format, *args):
        # Suppress default logging
        pass

def create_handler(dashboard):
    def handler(*args, **kwargs):
        return DashboardHandler(dashboard, *args, **kwargs)
    return handler

def main():
    parser = argparse.ArgumentParser(description='TurdParty Service Dashboard')
    parser.add_argument('command', choices=['start'], help='Command to run')
    parser.add_argument('--port', type=int, default=8080, help='Port to run on')
    parser.add_argument('--dev', action='store_true', help='Development mode')
    
    args = parser.parse_args()
    
    if args.command == 'start':
        dashboard = TurdPartyDashboard()
        handler = create_handler(dashboard)
        
        server = HTTPServer(('0.0.0.0', args.port), handler)
        print(f"TurdParty Dashboard starting on port {args.port}")
        print(f"Access at: http://localhost:{args.port}")
        
        try:
            server.serve_forever()
        except KeyboardInterrupt:
            print("\nShutting down dashboard...")
            server.shutdown()

if __name__ == "__main__":
    main()
