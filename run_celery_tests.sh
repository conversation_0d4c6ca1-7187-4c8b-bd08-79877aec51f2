#!/bin/bash

# Run Celery tests and generate coverage report
echo "Running Celery tests with coverage..."

# Set the working directory to the script's directory
cd "$(dirname "$0")"

# Check if we should use Docker or local environment
if [ "$1" == "--local" ]; then
  # Run tests with coverage using nix-shell
  echo "Running tests in local environment..."
  nix-shell simple-shell.nix --run "python -m pytest tests/unit/test_task_status.py tests/unit/test_file_tasks.py::test_get_content_type -v"
else
  # Run tests in Docker environment
  echo "Running tests in Docker environment..."

  # Start the test environment if it's not already running
  if ! docker compose -f .dockerwrapper/docker-compose.test.yml ps | grep -q "turdparty_test_runner"; then
    echo "Starting test environment..."
    cd .dockerwrapper
    docker compose -f docker-compose.test.yml up -d

    # Wait for services to be ready
    echo "Waiting for services to be ready..."
    sleep 30  # Increased wait time to ensure all services are ready

    # Run database migrations
    echo "Running database migrations..."
    docker compose -f docker-compose.test.yml exec -T api alembic upgrade head

    # Initialize MinIO buckets
    echo "Initializing MinIO buckets..."
    docker compose -f docker-compose.test.yml exec -T minio-setup /bin/sh -c "
      /usr/bin/mc config host add myminio http://minio:9000 minioadmin minioadmin;
      /usr/bin/mc mb myminio/uploads --ignore-existing;
      /usr/bin/mc policy set public myminio/uploads;
    "

    cd ..
  fi

  # Run the Celery integration tests
  echo "Running Celery integration tests..."
  .dockerwrapper/run_single_test.sh tests/integration/test_celery_integration.py -v
fi

echo "Done!"
