import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Alert,
  Tab,
  Tabs,
  CircularProgress,
} from '@mui/material';
import {
  Security as SecurityIcon,
  BugReport as BugIcon,
  VerifiedUser as VerifiedUserIcon,
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import SecurityTestingService, { SecurityTest } from '../../services/securityTesting';
import SecurityTestDetails from './SecurityTestDetails';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`security-tabpanel-${index}`}
      aria-labelledby={`security-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const SecurityTestingDashboard: React.FC = () => {
  const [tests, setTests] = useState<SecurityTest[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [selectedTestId, setSelectedTestId] = useState<string | null>(null);
  const { token } = useAuth();
  const securityService = React.useMemo(() => {
    if (!token) return null;
    return new SecurityTestingService(token);
  }, [token]);

  useEffect(() => {
    if (!securityService) {
      setError('Authentication required');
      return;
    }
    loadSecurityTests();
  }, [securityService]);

  const loadSecurityTests = async () => {
    if (!securityService) return;
    
    setLoading(true);
    try {
      const response = await securityService.getTests();
      setTests(response.items);
      setError(null);
    } catch (err) {
      setError('Failed to load security tests');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRunTest = async (testId: string) => {
    if (!securityService) return;
    
    try {
      setLoading(true);
      await securityService.runTest(testId);
      await loadSecurityTests(); // Refresh the list
    } catch (err) {
      setError('Failed to run security test');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = (testId: string) => {
    setSelectedTestId(testId);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success.main';
      case 'running':
        return 'info.main';
      case 'failed':
        return 'error.main';
      default:
        return 'warning.main';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <SecurityIcon sx={{ mr: 1 }} />
        <Typography variant="h4">Security Testing Dashboard</Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab
            icon={<VerifiedUserIcon />}
            iconPosition="start"
            label="Overview"
            id="security-tab-0"
          />
          <Tab
            icon={<BugIcon />}
            iconPosition="start"
            label="Test Results"
            id="security-tab-1"
          />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Paper>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Test Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Last Run</TableCell>
                    <TableCell>Result</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {tests.map((test) => (
                    <TableRow key={test.id}>
                      <TableCell>{test.name}</TableCell>
                      <TableCell>{test.type}</TableCell>
                      <TableCell>
                        <Typography color={getStatusColor(test.status)}>
                          {test.status.charAt(0).toUpperCase() + test.status.slice(1)}
                        </Typography>
                      </TableCell>
                      <TableCell>{test.lastRun}</TableCell>
                      <TableCell>{test.result}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            variant="contained"
                            size="small"
                            onClick={() => handleRunTest(test.id)}
                            disabled={test.status === 'running'}
                          >
                            Run Test
                          </Button>
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={() => handleViewDetails(test.id)}
                          >
                            View Details
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Paper>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Paper sx={{ p: 3 }}>
          {selectedTestId ? (
            <SecurityTestDetails
              testId={selectedTestId}
              onClose={() => setSelectedTestId(null)}
              onRerun={() => handleRunTest(selectedTestId)}
            />
          ) : (
            <Typography>Select a test to view its details</Typography>
          )}
        </Paper>
      </TabPanel>
    </Box>
  );
};

export default SecurityTestingDashboard; 