import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  LinearProgress,
  Alert,
  TextField,
} from '@mui/material';
import { CloudUpload } from '@mui/icons-material';
import axios, { AxiosError, AxiosProgressEvent } from 'axios';
import { API_ENDPOINTS } from '../../utils/apiConfig';

export interface FileUploadProps {
  onUploadSuccess?: (response: any) => void;
  onUploadError?: (error: any) => void;
  maxFileSize?: number; // in bytes
  acceptedFileTypes?: string[];
  multiple?: boolean;
  directory?: boolean;
  description?: string;
  token: string | null;
}

interface UploadError extends AxiosError {
  response?: {
    status: number;
    statusText: string;
    headers: Record<string, string>;
    config: any;
    data: {
      detail?: string;
    };
  };
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onUploadSuccess,
  onUploadError,
  maxFileSize = 10 * 1024 * 1024, // 10MB default
  acceptedFileTypes = [],
  multiple = false,
  directory = false,
  description = '',
  token
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [fileDescription, setFileDescription] = useState('');

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles(files);
    setError(null);
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      setError('Please select at least one file');
      return;
    }

    if (!token) {
      setError('Authentication token is missing');
      return;
    }

    const formData = new FormData();
    setUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      selectedFiles.forEach(file => {
        formData.append('file', file);
      });

      if (fileDescription) {
        formData.append('description', fileDescription);
      }

      const endpoint = multiple ? API_ENDPOINTS.FILE_UPLOAD.FOLDER : API_ENDPOINTS.FILE_UPLOAD.BASE;

      const response = await axios.post(endpoint, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent: AxiosProgressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setUploadProgress(percentCompleted);
          }
        },
      });

      setSelectedFiles([]);
      setFileDescription('');
      setUploadProgress(0);

      if (onUploadSuccess) {
        onUploadSuccess(response.data);
      }
    } catch (err) {
      const error = err as AxiosError;
      setError(error.message || 'Upload failed');

      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setUploading(false);
    }
  };

  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h5" gutterBottom>
        File Upload
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TextField
        fullWidth
        label="Description (optional)"
        value={fileDescription}
        onChange={(e) => setFileDescription(e.target.value)}
        sx={{ mb: 2 }}
      />

      <Box sx={{ mb: 2 }}>
        <input
          type="file"
          multiple={multiple}
          onChange={handleFileSelect}
          style={{ display: 'none' }}
          id="file-upload"
        />
        <label htmlFor="file-upload">
          <Button
            variant="outlined"
            component="span"
            startIcon={<CloudUpload />}
            fullWidth
            sx={{ p: 2 }}
          >
            Select Files
          </Button>
        </label>
      </Box>

      {selectedFiles.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2">Selected Files:</Typography>
          {selectedFiles.map((file, index) => (
            <Typography key={index} variant="body2">
              {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
            </Typography>
          ))}
        </Box>
      )}

      {uploading && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress variant="determinate" value={uploadProgress} />
          <Typography variant="body2" sx={{ mt: 1 }}>
            {uploadProgress}% uploaded
          </Typography>
        </Box>
      )}

      <Button
        variant="contained"
        onClick={handleUpload}
        disabled={uploading || selectedFiles.length === 0}
        fullWidth
      >
        {uploading ? 'Uploading...' : 'Upload Files'}
      </Button>
    </Paper>
  );
}; 