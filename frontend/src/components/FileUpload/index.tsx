import React, { useState, useEffect } from 'react';
import { Upload, Form, Input, message, Progress, Alert, Button, Typography, Card, Space, Statistic } from 'antd';
import { InboxOutlined, FolderOutlined, ReloadOutlined, InfoCircleOutlined, ClockCircleOutlined, BugOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import axios, { AxiosError, AxiosProgressEvent } from 'axios';
import { API_ENDPOINTS } from '../../utils/apiConfig';
import { useAuth } from '../../hooks/useAuth';
import './styles.css';

const { Dragger } = Upload;
const { Text, Title, Paragraph } = Typography;

export interface FileUploadProps {
  onUploadSuccess?: (response: any) => void;
  onUploadError?: (error: any) => void;
  maxFileSize?: number; // in bytes
  acceptedFileTypes?: string[];
  multiple?: boolean;
  directory?: boolean;
  description?: string;
  token: string | null;
}

interface UploadError extends AxiosError {
  response?: {
    status: number;
    statusText: string;
    headers: Record<string, string>;
    config: any;
    data: {
      detail?: string;
    };
  };
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onUploadSuccess,
  onUploadError,
  maxFileSize = 10 * 1024 * 1024, // 10MB default
  acceptedFileTypes = [],
  multiple = false,
  directory = false,
  description = '',
  token
}) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedBytes, setUploadedBytes] = useState(0);
  const [uploadStartTime, setUploadStartTime] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);
  const [refreshingToken, setRefreshingToken] = useState(false);

  const validateFile = (file: File): boolean => {
    // Check file size (10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      message.error(`File ${file.name} is too large. Maximum size is ${maxSize / 1024 / 1024}MB`);
      return false;
    }

    // Define allowed MIME types
    const allowedMimeTypes = [
      // Documents
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
      'text/markdown',
      
      // Images
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/svg+xml',
      'image/webp',
      'image/tiff',
      
      // Archives
      'application/zip',
      'application/x-tar',
      'application/x-gzip',
      'application/x-bzip2',
      
      // Other
      'application/json',
      'application/xml',
      'text/xml'
    ];

    // Check file type
    const fileType = file.type || '';
    if (!allowedMimeTypes.includes(fileType)) {
      message.error(`File type ${fileType || 'unknown'} is not allowed`);
      return false;
    }

    // Check file extension
    const dangerousExtensions = [
      '.exe', '.dll', '.so', '.dylib',  // Executables
      '.js', '.php', '.py', '.rb', '.pl',  // Scripts
      '.sh', '.bash', '.zsh', '.fish',  // Shell scripts
      '.bat', '.cmd', '.ps1',  // Windows scripts
      '.jar', '.war',  // Java archives
      '.msi', '.app',  // Installers
      '.apk', '.ipa',  // Mobile apps
    ];

    const extension = file.name.toLowerCase().split('.').pop();
    if (extension && dangerousExtensions.includes(`.${extension}`)) {
      message.error(`File extension .${extension} is not allowed for security reasons`);
      return false;
    }

    return true;
  };

  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.error('Please select at least one file');
      return;
    }

    if (!token) {
      setError('Authentication token is missing. Please try refreshing the page or log in again.');
      message.error('Authentication token is missing');
      if (onUploadError) {
        onUploadError(new Error('Authentication token is missing'));
      }
      return;
    }

    const description = form.getFieldValue('description');
    const formData = new FormData();

    // Reset states
    setUploading(true);
    setUploadProgress(0);
    setUploadedBytes(0);
    setUploadStartTime(Date.now());
    setError(null);

    try {
      // Use different endpoints based on upload type
      const endpoint = multiple ? API_ENDPOINTS.FILE_UPLOAD.FOLDER : API_ENDPOINTS.FILE_UPLOAD.BASE;
      
      console.log(`[FileUpload] Uploading to endpoint: ${endpoint}`);
      console.log(`[FileUpload] Files to upload:`, fileList.map(f => ({ name: f.name, size: f.size, type: f.type })));

      // Append files and paths
      for (const file of fileList) {
        // Validate each file before upload
        if (!validateFile(file instanceof File ? file : file.originFileObj as File)) {
          setUploading(false);
          return;
        }

        console.log(`[FileUpload] Appending file to FormData:`, { name: file.name, type: file.type || 'unknown' });
        // Extract the actual File object from UploadFile
        if (file.originFileObj) {
          formData.append('file', file.originFileObj);
        } else if (file instanceof File) {
          formData.append('file', file);
        } else {
          console.error('[FileUpload] Unable to extract file from', file);
          continue;
        }
        
        if (multiple) {
          formData.append('paths', file.name);
        }
      }

      // Add description if provided
      if (description) {
        formData.append('description', description);
        console.log(`[FileUpload] Added description to form data: ${description.substring(0, 30)}${description.length > 30 ? '...' : ''}`);
      }

      // Make API request with progress tracking
      const response = await axios.post(endpoint, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent: AxiosProgressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setUploadProgress(percentCompleted);
            setUploadedBytes(progressEvent.loaded);
          }
        },
      });

      console.log('[FileUpload] Upload successful:', response.data);
      message.success('File uploaded successfully');
      
      // Reset form and states
      form.resetFields();
      setFileList([]);
      setUploadProgress(0);
      setUploadedBytes(0);
      
      if (onUploadSuccess) {
        onUploadSuccess(response.data);
      }
    } catch (err) {
      console.error('[FileUpload] Upload error:', err);
      let errorMessage = 'Failed to upload file(s)';
      
      const error = err as UploadError;
      
      // Enhanced error message based on status codes
      if (error.response) {
        const status = error.response.status;
        if (status === 400) {
          errorMessage = error.response.data?.detail || 'Invalid request format. Please check file and try again.';
        } else if (status === 401) {
          errorMessage = 'Authentication error: Your session has expired. Please refresh your token.';
        } else if (status === 403) {
          errorMessage = 'You do not have permission to upload files.';
        } else if (status === 404) {
          errorMessage = 'Upload endpoint not found. This may be a configuration issue.';
        } else if (status === 413) {
          errorMessage = 'File too large. Please try uploading a smaller file.';
        } else if (status === 415) {
          errorMessage = error.response.data?.detail || 'File type is not allowed.';
        } else if (status === 500) {
          errorMessage = 'Server error occurred while processing your upload.';
        } else if (status >= 500) {
          errorMessage = 'Server is experiencing issues. Please try again later.';
        } else {
          errorMessage = error.response.data?.detail || error.message || 'Failed to upload file(s)';
        }
      } else if (error.message && error.message.includes('timeout')) {
        errorMessage = 'Upload timed out. This may be due to a large file or slow connection.';
      } else if (error.message && error.message.includes('Network Error')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      }
      
      setError(errorMessage);
      message.error(errorMessage);
      
      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <div>
      {/* Render your component content here */}
    </div>
  );
}; 