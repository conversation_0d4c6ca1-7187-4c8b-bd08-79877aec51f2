.file-upload-container {
  margin-bottom: 24px;
}

.upload-card {
  margin-bottom: 16px;
}

.upload-progress {
  margin-top: 16px;
}

.file-list {
  margin-top: 16px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.file-list ul {
  list-style-type: none;
  padding-left: 0;
}

.file-list li {
  margin-bottom: 8px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ant-upload-drag-icon {
  font-size: 48px;
  color: #1890ff;
}

.ant-upload-text {
  font-size: 16px;
  margin: 8px 0;
}

.ant-upload-hint {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.45);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .file-list {
    background-color: #1f1f1f;
  }
  
  .file-list li {
    background-color: #2a2a2a;
    box-shadow: 0 1px 2px rgba(255, 255, 255, 0.05);
  }
}
