#!/usr/bin/env python3
"""
Enhanced Translation Script

This script provides improved translation capabilities with:
1. Better error handling and retry logic
2. Context-aware translations
3. Terminology consistency
4. Quality validation
5. Support for multiple translation services
"""

import os
import json
import logging
import time
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import requests
from dataclasses import dataclass

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TranslationContext:
    """Context information for translations."""
    file_type: str  # 'ui', 'docs', 'api'
    component: str  # component name or file name
    key: str       # translation key
    source_text: str
    target_language: str
    context_hints: List[str] = None

class EnhancedTranslator:
    def __init__(self, config_file: str = "config/translation_config.json"):
        self.config = self.load_config(config_file)
        self.terminology = self.load_terminology()
        self.translation_cache = {}
        self.load_cache()
    
    def load_config(self, config_file: str) -> Dict:
        """Load translation configuration."""
        default_config = {
            "services": {
                "google": {
                    "enabled": True,
                    "api_key_env": "GOOGLE_TRANSLATE_API_KEY",
                    "project_id_env": "GOOGLE_CLOUD_PROJECT_ID"
                },
                "deepl": {
                    "enabled": False,
                    "api_key_env": "DEEPL_API_KEY"
                },
                "claude": {
                    "enabled": True,
                    "fallback": True
                }
            },
            "quality": {
                "min_length_ratio": 0.3,
                "max_length_ratio": 3.0,
                "forbidden_patterns": ["[Translation failed]", "ERROR", "FAILED"]
            },
            "retry": {
                "max_attempts": 3,
                "delay_seconds": 1
            }
        }
        
        config_path = Path(config_file)
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                # Merge with defaults
                default_config.update(user_config)
        else:
            # Create default config file
            config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2)
            logger.info(f"Created default config file: {config_path}")
        
        return default_config
    
    def load_terminology(self) -> Dict:
        """Load terminology dictionary."""
        terminology_file = Path("lang/terminology.json")
        if terminology_file.exists():
            with open(terminology_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def load_cache(self):
        """Load translation cache."""
        cache_file = Path("lang/.translation_cache.json")
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    self.translation_cache = json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load translation cache: {e}")
                self.translation_cache = {}
    
    def save_cache(self):
        """Save translation cache."""
        cache_file = Path("lang/.translation_cache.json")
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.translation_cache, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"Failed to save translation cache: {e}")
    
    def get_cache_key(self, context: TranslationContext) -> str:
        """Generate cache key for translation."""
        return f"{context.target_language}:{hash(context.source_text)}:{context.file_type}"
    
    def validate_translation(self, source: str, translation: str, target_lang: str) -> Tuple[bool, List[str]]:
        """Validate translation quality."""
        issues = []
        
        # Check length ratio
        if len(translation) == 0:
            issues.append("Empty translation")
            return False, issues
        
        length_ratio = len(translation) / len(source)
        min_ratio = self.config["quality"]["min_length_ratio"]
        max_ratio = self.config["quality"]["max_length_ratio"]
        
        if length_ratio < min_ratio:
            issues.append(f"Translation too short (ratio: {length_ratio:.2f})")
        elif length_ratio > max_ratio:
            issues.append(f"Translation too long (ratio: {length_ratio:.2f})")
        
        # Check forbidden patterns
        for pattern in self.config["quality"]["forbidden_patterns"]:
            if pattern.lower() in translation.lower():
                issues.append(f"Contains forbidden pattern: {pattern}")
        
        # Check if translation is just the source text (no actual translation)
        if translation.strip() == source.strip():
            issues.append("Translation identical to source")
        
        return len(issues) == 0, issues
    
    def apply_terminology(self, text: str, target_lang: str) -> str:
        """Apply consistent terminology to translation."""
        if not self.terminology:
            return text
        
        result = text
        for category in self.terminology.values():
            if isinstance(category, dict):
                for term, translations in category.items():
                    if isinstance(translations, dict) and target_lang in translations:
                        # Simple replacement - in production, you'd want more sophisticated matching
                        result = result.replace(term, translations[target_lang])
        
        return result
    
    def translate_with_claude(self, context: TranslationContext) -> Optional[str]:
        """Translate using Claude (simulated - replace with actual API call)."""
        logger.info(f"Translating with Claude: {context.source_text[:50]}...")
        
        # This is a placeholder - in a real implementation, you would call Claude's API
        # For now, we'll return a marked translation to indicate it needs real implementation
        return f"[CLAUDE_TRANSLATION_NEEDED] {context.source_text}"
    
    def translate_with_google(self, context: TranslationContext) -> Optional[str]:
        """Translate using Google Translate API."""
        api_key = os.getenv(self.config["services"]["google"]["api_key_env"])
        if not api_key:
            logger.warning("Google Translate API key not found")
            return None
        
        try:
            url = "https://translation.googleapis.com/language/translate/v2"
            params = {
                'key': api_key,
                'q': context.source_text,
                'target': context.target_language,
                'source': 'en'
            }
            
            response = requests.post(url, data=params, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if 'data' in result and 'translations' in result['data']:
                translation = result['data']['translations'][0]['translatedText']
                return self.apply_terminology(translation, context.target_language)
            
        except Exception as e:
            logger.error(f"Google Translate error: {e}")
        
        return None
    
    def translate_with_deepl(self, context: TranslationContext) -> Optional[str]:
        """Translate using DeepL API."""
        api_key = os.getenv(self.config["services"]["deepl"]["api_key_env"])
        if not api_key:
            logger.warning("DeepL API key not found")
            return None
        
        try:
            url = "https://api-free.deepl.com/v2/translate"
            headers = {"Authorization": f"DeepL-Auth-Key {api_key}"}
            data = {
                'text': context.source_text,
                'target_lang': context.target_language.upper(),
                'source_lang': 'EN'
            }
            
            response = requests.post(url, headers=headers, data=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if 'translations' in result and result['translations']:
                translation = result['translations'][0]['text']
                return self.apply_terminology(translation, context.target_language)
            
        except Exception as e:
            logger.error(f"DeepL error: {e}")
        
        return None
    
    def translate_text(self, context: TranslationContext) -> Optional[str]:
        """Translate text using available services with fallback."""
        # Check cache first
        cache_key = self.get_cache_key(context)
        if cache_key in self.translation_cache:
            logger.debug(f"Using cached translation for: {context.source_text[:30]}...")
            return self.translation_cache[cache_key]
        
        translation = None
        
        # Try services in order of preference
        services = [
            ("deepl", self.translate_with_deepl),
            ("google", self.translate_with_google),
            ("claude", self.translate_with_claude)
        ]
        
        for service_name, service_func in services:
            if not self.config["services"][service_name]["enabled"]:
                continue
            
            for attempt in range(self.config["retry"]["max_attempts"]):
                try:
                    translation = service_func(context)
                    if translation:
                        # Validate translation
                        is_valid, issues = self.validate_translation(
                            context.source_text, translation, context.target_language
                        )
                        
                        if is_valid:
                            # Cache successful translation
                            self.translation_cache[cache_key] = translation
                            logger.info(f"Successfully translated with {service_name}")
                            return translation
                        else:
                            logger.warning(f"Translation validation failed: {issues}")
                            translation = None
                    
                except Exception as e:
                    logger.error(f"Translation attempt {attempt + 1} failed with {service_name}: {e}")
                
                if attempt < self.config["retry"]["max_attempts"] - 1:
                    time.sleep(self.config["retry"]["delay_seconds"])
            
            if translation:
                break
        
        if not translation:
            logger.error(f"All translation services failed for: {context.source_text[:50]}...")
        
        return translation
    
    def translate_ui_file(self, source_file: Path, target_file: Path, target_lang: str):
        """Translate a UI JSON file."""
        logger.info(f"Translating UI file: {source_file} -> {target_file}")
        
        with open(source_file, 'r', encoding='utf-8') as f:
            source_data = json.load(f)
        
        translated_data = {}
        
        for key, value in source_data.items():
            if isinstance(value, str):
                context = TranslationContext(
                    file_type="ui",
                    component=source_file.stem,
                    key=key,
                    source_text=value,
                    target_language=target_lang
                )
                
                translation = self.translate_text(context)
                translated_data[key] = translation if translation else f"[TRANSLATION_FAILED] {value}"
            else:
                translated_data[key] = value
        
        # Ensure target directory exists
        target_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Save translated file
        with open(target_file, 'w', encoding='utf-8') as f:
            json.dump(translated_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved translated file: {target_file}")
    
    def __del__(self):
        """Save cache when object is destroyed."""
        self.save_cache()

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Enhanced translation script")
    parser.add_argument("--language", help="Target language code")
    parser.add_argument("--file", help="Specific file to translate")
    parser.add_argument("--type", choices=["ui", "docs", "all"], default="all", help="Translation type")
    
    args = parser.parse_args()
    
    translator = EnhancedTranslator()
    
    if args.type in ["ui", "all"]:
        # Translate UI files
        base_ui_dir = Path("lang/en_GB/ui")
        if base_ui_dir.exists():
            for ui_file in base_ui_dir.rglob("*.json"):
                if args.language:
                    target_file = Path("lang") / args.language / "ui" / ui_file.relative_to(base_ui_dir)
                    translator.translate_ui_file(ui_file, target_file, args.language)
                else:
                    # Translate for all languages
                    for lang_dir in Path("lang").iterdir():
                        if lang_dir.is_dir() and lang_dir.name != "en_GB":
                            target_file = lang_dir / "ui" / ui_file.relative_to(base_ui_dir)
                            translator.translate_ui_file(ui_file, target_file, lang_dir.name)
    
    logger.info("Translation completed!")

if __name__ == "__main__":
    main()
