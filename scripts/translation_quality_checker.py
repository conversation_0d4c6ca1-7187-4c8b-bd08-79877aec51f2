#!/usr/bin/env python3
"""
Translation Quality Checker

This script checks the quality of translations by:
1. Validating translation completeness
2. Checking for consistency in terminology
3. Identifying potential translation errors
4. Generating quality reports
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Set, Tuple
import re
from collections import defaultdict, Counter

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TranslationQualityChecker:
    def __init__(self, lang_dir: str = "lang"):
        self.lang_dir = Path(lang_dir)
        self.base_language = "en_GB"
        self.issues = defaultdict(list)
        self.terminology = {}
        self.load_terminology()
    
    def load_terminology(self):
        """Load terminology dictionary for consistency checking."""
        terminology_file = self.lang_dir / "terminology.json"
        if terminology_file.exists():
            with open(terminology_file, 'r', encoding='utf-8') as f:
                self.terminology = json.load(f)
        else:
            # Create default terminology
            self.terminology = {
                "technical_terms": {
                    "API": "API",
                    "Docker": "Docker", 
                    "FastAPI": "FastAPI",
                    "VM": "VM",
                    "Virtual Machine": {"de": "Virtuelle Maschine", "fr": "Machine Virtuelle"},
                    "Upload": {"de": "Hochladen", "fr": "Télécharger"},
                    "Download": {"de": "Herunterladen", "fr": "Télécharger"}
                },
                "ui_terms": {
                    "Home": {"de": "Startseite", "fr": "Accueil"},
                    "Files": {"de": "Dateien", "fr": "Fichiers"},
                    "Status": {"de": "Status", "fr": "Statut"}
                }
            }
            self.save_terminology()
    
    def save_terminology(self):
        """Save terminology dictionary."""
        terminology_file = self.lang_dir / "terminology.json"
        with open(terminology_file, 'w', encoding='utf-8') as f:
            json.dump(self.terminology, f, indent=2, ensure_ascii=False)
    
    def check_ui_translations(self) -> Dict[str, List[str]]:
        """Check UI translation files for completeness and quality."""
        logger.info("Checking UI translations...")
        
        base_ui_dir = self.lang_dir / self.base_language / "ui"
        if not base_ui_dir.exists():
            logger.warning(f"Base UI directory not found: {base_ui_dir}")
            return {}
        
        issues = defaultdict(list)
        
        # Get all base translation files
        base_files = list(base_ui_dir.rglob("*.json"))
        
        for lang_dir in self.lang_dir.iterdir():
            if not lang_dir.is_dir() or lang_dir.name == self.base_language:
                continue
                
            lang_code = lang_dir.name
            lang_ui_dir = lang_dir / "ui"
            
            if not lang_ui_dir.exists():
                issues[lang_code].append(f"Missing UI directory: {lang_ui_dir}")
                continue
            
            # Check each base file
            for base_file in base_files:
                relative_path = base_file.relative_to(base_ui_dir)
                lang_file = lang_ui_dir / relative_path
                
                if not lang_file.exists():
                    issues[lang_code].append(f"Missing translation file: {relative_path}")
                    continue
                
                # Load and compare JSON files
                try:
                    with open(base_file, 'r', encoding='utf-8') as f:
                        base_data = json.load(f)
                    
                    with open(lang_file, 'r', encoding='utf-8') as f:
                        lang_data = json.load(f)
                    
                    # Check for missing keys
                    missing_keys = set(base_data.keys()) - set(lang_data.keys())
                    if missing_keys:
                        issues[lang_code].append(f"Missing keys in {relative_path}: {missing_keys}")
                    
                    # Check for empty translations
                    empty_translations = [k for k, v in lang_data.items() if not v or v.strip() == ""]
                    if empty_translations:
                        issues[lang_code].append(f"Empty translations in {relative_path}: {empty_translations}")
                    
                    # Check terminology consistency
                    self.check_terminology_consistency(lang_code, lang_data, relative_path, issues)
                    
                except json.JSONDecodeError as e:
                    issues[lang_code].append(f"Invalid JSON in {relative_path}: {e}")
                except Exception as e:
                    issues[lang_code].append(f"Error checking {relative_path}: {e}")
        
        return dict(issues)
    
    def check_terminology_consistency(self, lang_code: str, translations: Dict, file_path: Path, issues: Dict):
        """Check if translations use consistent terminology."""
        for key, value in translations.items():
            if not isinstance(value, str):
                continue
                
            # Check technical terms
            for term, expected in self.terminology.get("technical_terms", {}).items():
                if term.lower() in value.lower():
                    if isinstance(expected, dict) and lang_code in expected:
                        expected_term = expected[lang_code]
                        if expected_term.lower() not in value.lower():
                            issues[lang_code].append(
                                f"Terminology inconsistency in {file_path}, key '{key}': "
                                f"Expected '{expected_term}' for '{term}'"
                            )
    
    def check_documentation_translations(self) -> Dict[str, List[str]]:
        """Check documentation translation files."""
        logger.info("Checking documentation translations...")
        
        issues = defaultdict(list)
        
        # Find all markdown files in base language
        base_docs = []
        for root_dir in ["docs", "."]:
            root_path = Path(root_dir)
            if root_path.exists():
                base_docs.extend(root_path.glob("*.md"))
        
        for lang_dir in self.lang_dir.iterdir():
            if not lang_dir.is_dir() or lang_dir.name == self.base_language:
                continue
                
            lang_code = lang_dir.name
            
            # Check for translated documentation
            for base_doc in base_docs:
                if base_doc.name == "README.md":
                    continue  # Skip README as it has special handling
                    
                translated_doc = lang_dir / base_doc.name
                if not translated_doc.exists():
                    issues[lang_code].append(f"Missing translation: {base_doc.name}")
                else:
                    # Check file size (very small files might be incomplete)
                    if translated_doc.stat().st_size < 100:
                        issues[lang_code].append(f"Suspiciously small translation: {base_doc.name}")
        
        return dict(issues)
    
    def generate_quality_report(self) -> str:
        """Generate a comprehensive quality report."""
        logger.info("Generating quality report...")
        
        ui_issues = self.check_ui_translations()
        doc_issues = self.check_documentation_translations()
        
        report = ["# Translation Quality Report", ""]
        report.append(f"Generated: {Path(__file__).stat().st_mtime}")
        report.append("")
        
        # Summary
        total_languages = len([d for d in self.lang_dir.iterdir() if d.is_dir() and d.name != self.base_language])
        languages_with_ui_issues = len(ui_issues)
        languages_with_doc_issues = len(doc_issues)
        
        report.extend([
            "## Summary",
            "",
            f"- Total languages: {total_languages}",
            f"- Languages with UI issues: {languages_with_ui_issues}",
            f"- Languages with documentation issues: {languages_with_doc_issues}",
            ""
        ])
        
        # UI Issues
        if ui_issues:
            report.extend(["## UI Translation Issues", ""])
            for lang, issues in ui_issues.items():
                report.append(f"### {lang.upper()}")
                for issue in issues:
                    report.append(f"- {issue}")
                report.append("")
        
        # Documentation Issues
        if doc_issues:
            report.extend(["## Documentation Translation Issues", ""])
            for lang, issues in doc_issues.items():
                report.append(f"### {lang.upper()}")
                for issue in issues:
                    report.append(f"- {issue}")
                report.append("")
        
        return "\n".join(report)
    
    def fix_common_issues(self):
        """Automatically fix common translation issues."""
        logger.info("Fixing common translation issues...")
        
        # Create missing UI directories and files
        base_ui_dir = self.lang_dir / self.base_language / "ui"
        if base_ui_dir.exists():
            base_files = list(base_ui_dir.rglob("*.json"))
            
            for lang_dir in self.lang_dir.iterdir():
                if not lang_dir.is_dir() or lang_dir.name == self.base_language:
                    continue
                    
                lang_code = lang_dir.name
                lang_ui_dir = lang_dir / "ui"
                
                # Create UI directory if missing
                lang_ui_dir.mkdir(parents=True, exist_ok=True)
                
                # Create missing translation files
                for base_file in base_files:
                    relative_path = base_file.relative_to(base_ui_dir)
                    lang_file = lang_ui_dir / relative_path
                    
                    if not lang_file.exists():
                        # Create directory structure
                        lang_file.parent.mkdir(parents=True, exist_ok=True)
                        
                        # Copy base file as template
                        with open(base_file, 'r', encoding='utf-8') as f:
                            base_data = json.load(f)
                        
                        # Mark as needing translation
                        translated_data = {}
                        for key, value in base_data.items():
                            translated_data[key] = f"[NEEDS_TRANSLATION] {value}"
                        
                        with open(lang_file, 'w', encoding='utf-8') as f:
                            json.dump(translated_data, f, indent=2, ensure_ascii=False)
                        
                        logger.info(f"Created template translation file: {lang_file}")

def main():
    """Main function to run translation quality checks."""
    checker = TranslationQualityChecker()
    
    # Generate quality report
    report = checker.generate_quality_report()
    
    # Save report
    report_file = Path("lang/translation_quality_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    logger.info(f"Quality report saved to: {report_file}")
    
    # Fix common issues
    checker.fix_common_issues()
    
    print(report)

if __name__ == "__main__":
    main()
