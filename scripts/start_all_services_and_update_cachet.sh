#!/bin/bash

# Start All TurdParty Services and Update Cachet Status
# This script starts all services and registers them with Cachet

set -e

echo "🚀 Starting All TurdParty Services and Updating Cachet"
echo "======================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ] && [ ! -d ".dockerwrapper" ]; then
    print_error "Please run this script from the turdparty project root directory"
    exit 1
fi

# Function to check if a container is running
is_container_running() {
    docker ps --format "{{.Names}}" | grep -q "^$1$"
}

# Function to wait for a service to be healthy
wait_for_service() {
    local service_name=$1
    local max_wait=${2:-60}
    local wait_time=0
    
    print_status "Waiting for $service_name to be ready..."
    
    while [ $wait_time -lt $max_wait ]; do
        if is_container_running "$service_name"; then
            # Check if container has health check
            health_status=$(docker inspect "$service_name" --format='{{.State.Health.Status}}' 2>/dev/null || echo "no-health-check")
            
            if [ "$health_status" = "healthy" ] || [ "$health_status" = "no-health-check" ]; then
                print_success "$service_name is ready"
                return 0
            fi
        fi
        
        sleep 2
        wait_time=$((wait_time + 2))
        echo -n "."
    done
    
    echo ""
    print_warning "$service_name took longer than expected to start"
    return 1
}

# Start services using the dynamic startup script if available
print_status "Starting TurdParty services..."

if [ -f ".dockerwrapper/start-turdparty-dynamic.sh" ]; then
    print_status "Using dynamic startup script..."
    cd .dockerwrapper
    bash start-turdparty-dynamic.sh
    cd ..
elif [ -f ".dockerwrapper/start-turdparty.sh" ]; then
    print_status "Using standard startup script..."
    cd .dockerwrapper
    bash start-turdparty.sh
    cd ..
else
    print_status "Using docker compose directly..."
    if [ -f ".dockerwrapper/docker-compose.yml" ]; then
        docker compose -f .dockerwrapper/docker-compose.yml -p turdparty up -d
    else
        docker compose up -d
    fi
fi

# Wait for core services to be ready
print_status "Waiting for core services to start..."

# Core infrastructure services
wait_for_service "turdparty_postgres" 30
wait_for_service "turdparty_redis" 30
wait_for_service "turdparty_minio" 30

# Application services
wait_for_service "turdparty_api" 60
wait_for_service "turdparty_frontend" 30

# Worker services
wait_for_service "turdparty_celery_default" 30
wait_for_service "turdparty_celery_file_ops" 30
wait_for_service "turdparty_celery_vm_ops" 30
wait_for_service "turdparty_celery_flower" 30

# Check if Cachet is running
if is_container_running "turdparty_cachet"; then
    print_success "Cachet is already running"
else
    print_warning "Cachet is not running - status updates will be skipped"
fi

# Show running services
print_status "Current service status:"
docker ps --format "table {{.Names}}\t{{.Status}}" | grep turdparty

# Update Cachet status if the update script exists
if [ -f "scripts/update_cachet_status.py" ] && is_container_running "turdparty_cachet"; then
    print_status "Updating Cachet service statuses..."
    
    # Use nix-shell to run the Python script with required dependencies
    if command -v nix-shell >/dev/null 2>&1; then
        nix-shell -p python3 -p python3Packages.requests --run "python3 scripts/update_cachet_status.py"
    else
        print_warning "nix-shell not available, trying direct Python execution..."
        python3 scripts/update_cachet_status.py 2>/dev/null || print_warning "Could not update Cachet status - missing dependencies"
    fi
else
    print_warning "Cachet status update script not found or Cachet not running"
fi

# Final status check
print_status "Performing final health checks..."

# Check API health
if curl -s http://localhost:3050/api/v1/health >/dev/null 2>&1; then
    print_success "API is responding"
else
    print_error "API is not responding"
fi

# Check frontend
if curl -s http://localhost:3100 >/dev/null 2>&1; then
    print_success "Frontend is responding"
else
    print_warning "Frontend may not be ready yet"
fi

# Check Cachet
if curl -s http://localhost:3501/api/v1/ping >/dev/null 2>&1; then
    print_success "Cachet is responding"
else
    print_warning "Cachet is not responding"
fi

echo ""
print_success "Service startup complete!"
echo ""
echo "🌐 Access URLs:"
echo "   • API:          http://localhost:3050"
echo "   • Frontend:     http://localhost:3100"
echo "   • Cachet:       http://localhost:3501"
echo "   • Celery Flower: http://localhost:3450"
echo "   • MinIO Console: http://localhost:3301"
echo ""
echo "📊 To update service statuses manually:"
echo "   nix-shell -p python3 -p python3Packages.requests --run 'python3 scripts/update_cachet_status.py'"
echo ""
print_success "All services started and registered with Cachet! 🎉"
