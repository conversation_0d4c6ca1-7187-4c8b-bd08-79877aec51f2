#!/usr/bin/env python3
"""
Translation Manager

Unified script for managing all translations in the TurdParty project.
Combines documentation and UI translations with quality checking.
"""

import os
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Optional
import subprocess
import sys

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TranslationManager:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.lang_dir = self.project_root / "lang"
        self.config_file = self.project_root / "config" / "translation_config.json"
        self.base_language = "en_GB"
        
        # Ensure directories exist
        self.lang_dir.mkdir(exist_ok=True)
        (self.project_root / "config").mkdir(exist_ok=True)
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        if self.config_file.exists():
            with open(self.config_file, 'r') as f:
                config = json.load(f)
                return config.get("languages", {}).get("supported", [])
        
        # Fallback: scan existing language directories
        languages = []
        for item in self.lang_dir.iterdir():
            if item.is_dir() and item.name != self.base_language:
                languages.append(item.name)
        return sorted(languages)
    
    def check_translation_status(self) -> Dict[str, Dict[str, str]]:
        """Check the status of all translations."""
        logger.info("Checking translation status...")
        
        status = {}
        languages = self.get_supported_languages()
        
        for lang in languages:
            lang_dir = self.lang_dir / lang
            status[lang] = {
                "ui_translations": "missing",
                "doc_translations": "missing",
                "completeness": "0%"
            }
            
            if lang_dir.exists():
                # Check UI translations
                ui_dir = lang_dir / "ui"
                if ui_dir.exists() and list(ui_dir.rglob("*.json")):
                    status[lang]["ui_translations"] = "present"
                
                # Check documentation translations
                doc_files = list(lang_dir.glob("*.md"))
                if doc_files:
                    status[lang]["doc_translations"] = "present"
                
                # Calculate rough completeness
                base_ui_files = list((self.lang_dir / self.base_language / "ui").rglob("*.json")) if (self.lang_dir / self.base_language / "ui").exists() else []
                lang_ui_files = list(ui_dir.rglob("*.json")) if ui_dir.exists() else []
                
                if base_ui_files:
                    completeness = (len(lang_ui_files) / len(base_ui_files)) * 100
                    status[lang]["completeness"] = f"{completeness:.0f}%"
        
        return status
    
    def run_quality_check(self) -> bool:
        """Run translation quality checks."""
        logger.info("Running translation quality checks...")
        
        try:
            quality_script = self.project_root / "scripts" / "translation_quality_checker.py"
            if quality_script.exists():
                result = subprocess.run([sys.executable, str(quality_script)], 
                                      capture_output=True, text=True, cwd=self.project_root)
                
                if result.returncode == 0:
                    logger.info("Quality check completed successfully")
                    return True
                else:
                    logger.error(f"Quality check failed: {result.stderr}")
                    return False
            else:
                logger.warning("Quality checker script not found")
                return False
        except Exception as e:
            logger.error(f"Error running quality check: {e}")
            return False
    
    def translate_ui(self, target_language: Optional[str] = None) -> bool:
        """Translate UI components."""
        logger.info(f"Translating UI components for {target_language or 'all languages'}...")
        
        try:
            enhanced_script = self.project_root / "scripts" / "enhanced_translate.py"
            if enhanced_script.exists():
                cmd = [sys.executable, str(enhanced_script), "--type", "ui"]
                if target_language:
                    cmd.extend(["--language", target_language])
                
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
                
                if result.returncode == 0:
                    logger.info("UI translation completed successfully")
                    return True
                else:
                    logger.error(f"UI translation failed: {result.stderr}")
                    return False
            else:
                logger.warning("Enhanced translate script not found")
                return False
        except Exception as e:
            logger.error(f"Error translating UI: {e}")
            return False
    
    def translate_docs(self, target_language: Optional[str] = None) -> bool:
        """Translate documentation."""
        logger.info(f"Translating documentation for {target_language or 'all languages'}...")
        
        try:
            docs_script = self.project_root / "scripts" / "translate_docs.py"
            if docs_script.exists():
                result = subprocess.run([sys.executable, str(docs_script)], 
                                      capture_output=True, text=True, cwd=self.project_root)
                
                if result.returncode == 0:
                    logger.info("Documentation translation completed successfully")
                    return True
                else:
                    logger.error(f"Documentation translation failed: {result.stderr}")
                    return False
            else:
                logger.warning("Documentation translate script not found")
                return False
        except Exception as e:
            logger.error(f"Error translating documentation: {e}")
            return False
    
    def fix_missing_translations(self) -> bool:
        """Fix missing translations by creating templates."""
        logger.info("Fixing missing translations...")
        
        try:
            quality_script = self.project_root / "scripts" / "translation_quality_checker.py"
            if quality_script.exists():
                # Import and run the fix function
                sys.path.insert(0, str(self.project_root / "scripts"))
                from translation_quality_checker import TranslationQualityChecker
                
                checker = TranslationQualityChecker()
                checker.fix_common_issues()
                
                logger.info("Missing translations fixed")
                return True
            else:
                logger.warning("Quality checker script not found")
                return False
        except Exception as e:
            logger.error(f"Error fixing missing translations: {e}")
            return False
    
    def generate_status_report(self) -> str:
        """Generate a comprehensive status report."""
        logger.info("Generating translation status report...")
        
        status = self.check_translation_status()
        
        report = ["# Translation Status Report", ""]
        report.append(f"Generated by Translation Manager")
        report.append("")
        
        # Summary
        total_languages = len(status)
        ui_complete = sum(1 for s in status.values() if s["ui_translations"] == "present")
        doc_complete = sum(1 for s in status.values() if s["doc_translations"] == "present")
        
        report.extend([
            "## Summary",
            "",
            f"- Total languages: {total_languages}",
            f"- Languages with UI translations: {ui_complete}/{total_languages}",
            f"- Languages with documentation translations: {doc_complete}/{total_languages}",
            ""
        ])
        
        # Detailed status
        report.extend(["## Detailed Status", ""])
        report.append("| Language | UI Translations | Documentation | Completeness |")
        report.append("|----------|----------------|---------------|--------------|")
        
        for lang, info in sorted(status.items()):
            ui_status = "✅" if info["ui_translations"] == "present" else "❌"
            doc_status = "✅" if info["doc_translations"] == "present" else "❌"
            report.append(f"| {lang.upper()} | {ui_status} | {doc_status} | {info['completeness']} |")
        
        report.append("")
        
        # Recommendations
        report.extend(["## Recommendations", ""])
        
        missing_ui = [lang for lang, info in status.items() if info["ui_translations"] == "missing"]
        missing_docs = [lang for lang, info in status.items() if info["doc_translations"] == "missing"]
        
        if missing_ui:
            report.append(f"- **Missing UI translations**: {', '.join(missing_ui)}")
        if missing_docs:
            report.append(f"- **Missing documentation translations**: {', '.join(missing_docs)}")
        
        if not missing_ui and not missing_docs:
            report.append("- All translations appear to be present! ✅")
        
        report.extend([
            "",
            "## Next Steps",
            "",
            "1. Run quality checks: `python scripts/translation_manager.py --check-quality`",
            "2. Fix missing translations: `python scripts/translation_manager.py --fix-missing`",
            "3. Update specific language: `python scripts/translation_manager.py --translate --language <lang>`",
            ""
        ])
        
        return "\n".join(report)
    
    def clean_translations(self) -> bool:
        """Clean up translation artifacts and temporary files."""
        logger.info("Cleaning translation artifacts...")
        
        try:
            # Remove temporary files
            temp_dir = self.lang_dir / "temp"
            if temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir)
                logger.info("Removed temporary files")
            
            # Remove cache files
            cache_file = self.lang_dir / ".translation_cache.json"
            if cache_file.exists():
                cache_file.unlink()
                logger.info("Removed translation cache")
            
            return True
        except Exception as e:
            logger.error(f"Error cleaning translations: {e}")
            return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Translation Manager for TurdParty")
    parser.add_argument("--status", action="store_true", help="Show translation status")
    parser.add_argument("--check-quality", action="store_true", help="Run quality checks")
    parser.add_argument("--translate", action="store_true", help="Run translations")
    parser.add_argument("--translate-ui", action="store_true", help="Translate UI only")
    parser.add_argument("--translate-docs", action="store_true", help="Translate documentation only")
    parser.add_argument("--fix-missing", action="store_true", help="Fix missing translations")
    parser.add_argument("--clean", action="store_true", help="Clean translation artifacts")
    parser.add_argument("--language", help="Target specific language")
    parser.add_argument("--report", help="Generate status report to file")
    
    args = parser.parse_args()
    
    manager = TranslationManager()
    
    if args.status:
        status = manager.check_translation_status()
        print("\nTranslation Status:")
        for lang, info in sorted(status.items()):
            print(f"  {lang.upper()}: UI={info['ui_translations']}, Docs={info['doc_translations']}, Complete={info['completeness']}")
    
    elif args.check_quality:
        success = manager.run_quality_check()
        sys.exit(0 if success else 1)
    
    elif args.translate:
        ui_success = manager.translate_ui(args.language)
        doc_success = manager.translate_docs(args.language)
        sys.exit(0 if ui_success and doc_success else 1)
    
    elif args.translate_ui:
        success = manager.translate_ui(args.language)
        sys.exit(0 if success else 1)
    
    elif args.translate_docs:
        success = manager.translate_docs(args.language)
        sys.exit(0 if success else 1)
    
    elif args.fix_missing:
        success = manager.fix_missing_translations()
        sys.exit(0 if success else 1)
    
    elif args.clean:
        success = manager.clean_translations()
        sys.exit(0 if success else 1)
    
    elif args.report:
        report = manager.generate_status_report()
        with open(args.report, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"Status report saved to: {args.report}")
    
    else:
        # Default: show status and generate report
        report = manager.generate_status_report()
        print(report)
        
        # Save report
        report_file = manager.lang_dir / "translation_status_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\nDetailed report saved to: {report_file}")

if __name__ == "__main__":
    main()
