#!/usr/bin/env python3
"""
Comprehensive Test Suite Runner for TurdParty
Runs all tests including health checks, API tests, UI tests, and integration tests.
"""

import subprocess
import sys
import os
import time
import json
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional

# Colors for output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

def print_header(text: str):
    """Print a formatted header."""
    print(f"\n{Colors.CYAN}{'='*80}{Colors.NC}")
    print(f"{Colors.WHITE}{text.center(80)}{Colors.NC}")
    print(f"{Colors.CYAN}{'='*80}{Colors.NC}\n")

def print_section(text: str):
    """Print a formatted section header."""
    print(f"\n{Colors.BLUE}{'─'*60}{Colors.NC}")
    print(f"{Colors.YELLOW}🔍 {text}{Colors.NC}")
    print(f"{Colors.BLUE}{'─'*60}{Colors.NC}")

def print_success(text: str):
    """Print success message."""
    print(f"{Colors.GREEN}✅ {text}{Colors.NC}")

def print_warning(text: str):
    """Print warning message."""
    print(f"{Colors.YELLOW}⚠️  {text}{Colors.NC}")

def print_error(text: str):
    """Print error message."""
    print(f"{Colors.RED}❌ {text}{Colors.NC}")

def print_info(text: str):
    """Print info message."""
    print(f"{Colors.BLUE}ℹ️  {text}{Colors.NC}")

def run_command(cmd: List[str], cwd: str = None, timeout: int = 300) -> Dict[str, Any]:
    """Run a command and return the result."""
    try:
        print_info(f"Running: {' '.join(cmd)}")
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=cwd
        )
        return {
            "success": result.returncode == 0,
            "returncode": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "returncode": -1,
            "stdout": "",
            "stderr": f"Command timed out after {timeout} seconds"
        }
    except Exception as e:
        return {
            "success": False,
            "returncode": -1,
            "stdout": "",
            "stderr": str(e)
        }

def check_service_health() -> Dict[str, bool]:
    """Check the health of all services."""
    print_section("Service Health Checks")
    
    services = {
        "API": "http://localhost:3050/api/v1/health",
        "Frontend": "http://localhost:3100",
        "Cachet": "http://localhost:3501/api/v1/ping",
        "MinIO": "http://localhost:3300/minio/health/live",
        "Celery Flower": "http://localhost:3450",
        "Redis": "redis://localhost:3400"
    }
    
    health_status = {}
    
    for service, url in services.items():
        try:
            if service == "Redis":
                # Special check for Redis
                result = run_command(["redis-cli", "-h", "localhost", "-p", "3400", "ping"])
                health_status[service] = result["success"] and "PONG" in result["stdout"]
            else:
                response = requests.get(url, timeout=10)
                health_status[service] = response.status_code == 200
            
            if health_status[service]:
                print_success(f"{service} is healthy")
            else:
                print_error(f"{service} is unhealthy")
                
        except Exception as e:
            health_status[service] = False
            print_error(f"{service} health check failed: {str(e)}")
    
    return health_status

def run_api_tests() -> Dict[str, Any]:
    """Run API tests."""
    print_section("API Tests")
    
    # Check if we're in a container or need to use nix-shell
    test_commands = [
        # Health check
        ["python3", "scripts/check_health.py", "--json"],
        
        # API unit tests
        ["python", "-m", "pytest", "api/tests/test_models.py", "-v"],
        ["python", "-m", "pytest", "api/tests/test_repositories.py", "-v"],
        ["python", "-m", "pytest", "api/tests/test_services.py", "-v"],
        
        # API integration tests
        ["python", "-m", "pytest", "api/tests/test_api_endpoints.py", "-v"],
        ["python", "-m", "pytest", "api/tests/test_file_upload_api.py", "-v"],
        ["python", "-m", "pytest", "api/tests/test_minio_integration.py", "-v"],
    ]
    
    results = {}
    
    for cmd in test_commands:
        test_name = cmd[-2] if len(cmd) > 2 else " ".join(cmd)
        print_info(f"Running {test_name}")
        
        # Try running directly first, then with nix-shell if needed
        result = run_command(cmd)
        
        if not result["success"] and "command not found" in result["stderr"].lower():
            # Try with nix-shell
            nix_cmd = ["nix-shell", "-p", "python3", "-p", "python3Packages.pytest", "-p", "python3Packages.requests", "--run", " ".join(cmd)]
            result = run_command(nix_cmd)
        
        results[test_name] = result
        
        if result["success"]:
            print_success(f"{test_name} passed")
        else:
            print_error(f"{test_name} failed")
            if result["stderr"]:
                print(f"  Error: {result['stderr'][:200]}...")
    
    return results

def run_ui_tests() -> Dict[str, Any]:
    """Run UI/Playwright tests."""
    print_section("UI/Playwright Tests")
    
    # Check if Playwright container is available
    playwright_check = run_command(["docker", "ps", "--filter", "name=turdparty_playwright", "--format", "{{.Names}}"])
    
    if "turdparty_playwright" not in playwright_check["stdout"]:
        print_warning("Playwright container not running, starting it...")
        start_result = run_command(["docker", "compose", "-f", ".dockerwrapper/docker-compose.playwright.yml", "up", "-d"])
        if not start_result["success"]:
            print_error("Failed to start Playwright container")
            return {"playwright_setup": start_result}
        
        # Wait for container to be ready
        time.sleep(10)
    
    # Run Playwright tests
    ui_tests = [
        "tests/accessibility.spec.js",
        "tests/file-upload-e2e.test.js", 
        "tests/navigation-flow.spec.js",
        "tests/form-inputs.spec.js",
        "tests/vm-operations.spec.js",
        "tests/performance.spec.js",
        "tests/security.spec.js"
    ]
    
    results = {}
    
    for test_file in ui_tests:
        test_name = os.path.basename(test_file)
        print_info(f"Running UI test: {test_name}")
        
        # Copy test file to container if it exists
        if os.path.exists(test_file):
            copy_result = run_command(["docker", "cp", test_file, "turdparty_playwright:/app/tests/"])
            if not copy_result["success"]:
                print_warning(f"Could not copy {test_file} to container")
        
        # Run the test
        cmd = ["docker", "exec", "turdparty_playwright", "npx", "playwright", "test", f"tests/{test_name}", "--reporter=list"]
        result = run_command(cmd, timeout=180)
        
        results[test_name] = result
        
        if result["success"]:
            print_success(f"{test_name} passed")
        else:
            print_error(f"{test_name} failed")
            if result["stderr"]:
                print(f"  Error: {result['stderr'][:200]}...")
    
    return results

def run_integration_tests() -> Dict[str, Any]:
    """Run integration tests."""
    print_section("Integration Tests")
    
    integration_tests = [
        # MinIO integration
        ["python", "-m", "pytest", "api/tests/test_minio_e2e.py", "-v"],
        
        # File upload integration
        ["python", "-m", "pytest", "api/tests/test_file_upload_service.py", "-v"],
        
        # VM operations integration
        ["python", "-m", "pytest", "api/tests/test_vagrant_vm_api.py", "-v"],
        
        # Database integration
        ["python", "-m", "pytest", "api/tests/test_database_integration.py", "-v"],
    ]
    
    results = {}
    
    for cmd in integration_tests:
        test_name = cmd[-2] if len(cmd) > 2 else " ".join(cmd)
        print_info(f"Running integration test: {test_name}")
        
        # Try running directly first, then with nix-shell if needed
        result = run_command(cmd)
        
        if not result["success"] and "command not found" in result["stderr"].lower():
            # Try with nix-shell
            nix_cmd = ["nix-shell", "-p", "python3", "-p", "python3Packages.pytest", "-p", "python3Packages.requests", "--run", " ".join(cmd)]
            result = run_command(nix_cmd)
        
        results[test_name] = result
        
        if result["success"]:
            print_success(f"{test_name} passed")
        else:
            print_error(f"{test_name} failed")
            if result["stderr"]:
                print(f"  Error: {result['stderr'][:200]}...")
    
    return results

def run_cachet_tests() -> Dict[str, Any]:
    """Test Cachet customizations and status monitoring."""
    print_section("Cachet Tests")
    
    results = {}
    
    # Test Cachet API
    try:
        response = requests.get("http://localhost:3501/api/v1/components", timeout=10)
        results["cachet_api"] = {
            "success": response.status_code == 200,
            "data": response.json() if response.status_code == 200 else None
        }
        
        if results["cachet_api"]["success"]:
            print_success("Cachet API is working")
            components = results["cachet_api"]["data"].get("data", [])
            print_info(f"Found {len(components)} components registered")
        else:
            print_error("Cachet API failed")
            
    except Exception as e:
        results["cachet_api"] = {"success": False, "error": str(e)}
        print_error(f"Cachet API test failed: {str(e)}")
    
    # Test dark theme CSS
    try:
        response = requests.get("http://localhost:3501/css/dark-theme.css", timeout=10)
        results["dark_theme"] = {"success": response.status_code == 200}
        
        if results["dark_theme"]["success"]:
            print_success("Dark theme CSS is accessible")
        else:
            print_error("Dark theme CSS not accessible")
            
    except Exception as e:
        results["dark_theme"] = {"success": False, "error": str(e)}
        print_error(f"Dark theme test failed: {str(e)}")
    
    # Test service icons JS
    try:
        response = requests.get("http://localhost:3501/js/service-icons.js", timeout=10)
        results["service_icons"] = {"success": response.status_code == 200}
        
        if results["service_icons"]["success"]:
            print_success("Service icons JS is accessible")
        else:
            print_error("Service icons JS not accessible")
            
    except Exception as e:
        results["service_icons"] = {"success": False, "error": str(e)}
        print_error(f"Service icons test failed: {str(e)}")
    
    # Test status update script
    cmd = ["python3", "scripts/cachet_service_manager.py"]
    result = run_command(cmd)
    
    if not result["success"] and "command not found" in result["stderr"].lower():
        nix_cmd = ["nix-shell", "-p", "python3", "-p", "python3Packages.requests", "--run", " ".join(cmd)]
        result = run_command(nix_cmd)
    
    results["status_monitoring"] = result
    
    if result["success"]:
        print_success("Status monitoring script works")
    else:
        print_error("Status monitoring script failed")
    
    return results

def generate_test_report(all_results: Dict[str, Any]) -> str:
    """Generate a comprehensive test report."""
    print_section("Test Report Generation")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"test_results/full_test_report_{timestamp}.md"
    
    # Ensure directory exists
    os.makedirs("test_results", exist_ok=True)
    
    # Calculate overall statistics
    total_tests = 0
    passed_tests = 0
    
    for category, tests in all_results.items():
        if isinstance(tests, dict):
            for test_name, result in tests.items():
                total_tests += 1
                if isinstance(result, dict) and result.get("success", False):
                    passed_tests += 1
    
    pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    # Generate report
    report_content = f"""# TurdParty Full Test Suite Report

**Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## Summary

- **Total Tests:** {total_tests}
- **Passed:** {passed_tests}
- **Failed:** {total_tests - passed_tests}
- **Pass Rate:** {pass_rate:.1f}%

## Test Results by Category

"""
    
    for category, tests in all_results.items():
        report_content += f"\n### {category.replace('_', ' ').title()}\n\n"
        
        if isinstance(tests, dict):
            for test_name, result in tests.items():
                if isinstance(result, dict):
                    status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
                    report_content += f"- **{test_name}:** {status}\n"
                    
                    if not result.get("success", False) and result.get("stderr"):
                        report_content += f"  - Error: `{result['stderr'][:100]}...`\n"
        else:
            status = "✅ PASS" if tests else "❌ FAIL"
            report_content += f"- **{category}:** {status}\n"
    
    report_content += f"""

## Recommendations

"""
    
    if pass_rate < 80:
        report_content += "- ⚠️ Pass rate is below 80%. Review failed tests and fix issues.\n"
    
    if not all_results.get("health_checks", {}).get("API", False):
        report_content += "- ❌ API health check failed. Check API service status.\n"
    
    if not all_results.get("health_checks", {}).get("Frontend", False):
        report_content += "- ❌ Frontend health check failed. Check frontend service status.\n"
    
    if pass_rate >= 90:
        report_content += "- 🎉 Excellent pass rate! System is working well.\n"
    
    report_content += f"""

---
*Report generated by TurdParty Test Suite Runner*
"""
    
    # Write report
    with open(report_file, "w") as f:
        f.write(report_content)
    
    print_success(f"Test report generated: {report_file}")
    return report_file

def main():
    """Run the full test suite."""
    print_header("TurdParty Full Test Suite")
    
    start_time = time.time()
    all_results = {}
    
    try:
        # 1. Service Health Checks
        all_results["health_checks"] = check_service_health()
        
        # 2. API Tests
        all_results["api_tests"] = run_api_tests()
        
        # 3. UI Tests
        all_results["ui_tests"] = run_ui_tests()
        
        # 4. Integration Tests
        all_results["integration_tests"] = run_integration_tests()
        
        # 5. Cachet Tests
        all_results["cachet_tests"] = run_cachet_tests()
        
        # 6. Generate Report
        report_file = generate_test_report(all_results)
        
        # Final Summary
        end_time = time.time()
        duration = end_time - start_time
        
        print_header("Test Suite Complete")
        print_info(f"Total execution time: {duration:.1f} seconds")
        print_info(f"Detailed report: {report_file}")
        
        # Calculate overall success
        health_ok = all(all_results["health_checks"].values())
        
        if health_ok:
            print_success("🎉 All critical services are healthy!")
            return 0
        else:
            print_warning("⚠️ Some services have issues. Check the report for details.")
            return 1
            
    except KeyboardInterrupt:
        print_error("\n🛑 Test suite interrupted by user")
        return 130
    except Exception as e:
        print_error(f"💥 Test suite failed with error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
