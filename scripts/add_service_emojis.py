#!/usr/bin/env python3
"""
Add emojis to Cachet service names for better visual appeal.
"""

import subprocess
import sys

# Service emoji mapping
SERVICE_EMOJIS = {
    "API": "🚀 API",
    "Frontend": "🌐 Frontend",
    "Redis": "⚡ Redis",
    "Celery Default": "⚙️ Celery Default",
    "Celery File Ops": "📁 Celery File Ops", 
    "Celery VM Ops": "🖥️ Celery VM Ops",
    "Celery Flower": "🌸 Celery Flower",
    "PostgreSQL": "🗄️ PostgreSQL",
    "MinIO": "📦 MinIO"
}

def run_sql_command(sql: str) -> tuple:
    """Run a SQL command in the Cachet database."""
    try:
        result = subprocess.run([
            "docker", "exec", "turdparty_postgres_cachet",
            "psql", "-U", "postgres", "-d", "cachet",
            "-c", sql
        ], capture_output=True, text=True, timeout=10)
        
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def update_component_names():
    """Update component names to include emojis."""
    print("🎨 Adding emojis to service names...")
    
    success_count = 0
    total_count = len(SERVICE_EMOJIS)
    
    for old_name, new_name in SERVICE_EMOJIS.items():
        sql = f"UPDATE chq_components SET name = '{new_name}' WHERE name = '{old_name}';"
        
        success, stdout, stderr = run_sql_command(sql)
        
        if success:
            print(f"✅ Updated: {old_name} → {new_name}")
            success_count += 1
        else:
            print(f"❌ Failed to update {old_name}: {stderr}")
    
    return success_count, total_count

def update_group_names():
    """Update group names to ensure they have emojis."""
    print("🎯 Updating group names...")
    
    group_updates = [
        (1, "🌐 Frontend Services"),
        (2, "⚙️ Worker Services"), 
        (3, "🗄️ Backend Services")
    ]
    
    success_count = 0
    
    for group_id, group_name in group_updates:
        sql = f"UPDATE chq_component_groups SET name = '{group_name}' WHERE id = {group_id};"
        
        success, stdout, stderr = run_sql_command(sql)
        
        if success:
            print(f"✅ Updated group {group_id}: {group_name}")
            success_count += 1
        else:
            print(f"❌ Failed to update group {group_id}: {stderr}")
    
    return success_count, len(group_updates)

def main():
    """Main function to add emojis to service names."""
    print("🎨 Adding Emojis to Cachet Service Names")
    print("=" * 45)
    
    # Check if Cachet database is accessible
    success, stdout, stderr = run_sql_command("SELECT 1;")
    if not success:
        print("❌ Cannot connect to Cachet database")
        return 1
    
    print("✅ Connected to Cachet database")
    
    # Update component names
    comp_success, comp_total = update_component_names()
    
    # Update group names
    group_success, group_total = update_group_names()
    
    print(f"\n📊 Update Summary:")
    print(f"   🎯 Components: {comp_success}/{comp_total} updated")
    print(f"   📁 Groups: {group_success}/{group_total} updated")
    
    total_success = comp_success + group_success
    total_items = comp_total + group_total
    
    if total_success == total_items:
        print("🎉 All service names updated with emojis!")
        print("🌐 Refresh http://localhost:3501 to see the changes")
        return 0
    else:
        print("⚠️  Some updates failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
