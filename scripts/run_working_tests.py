#!/usr/bin/env python3
"""
Focused Test Suite for Working TurdParty Services
Tests only the services that are currently operational.
"""

import subprocess
import sys
import os
import time
import json
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional

# Colors for output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

def print_header(text: str):
    """Print a formatted header."""
    print(f"\n{Colors.CYAN}{'='*80}{Colors.NC}")
    print(f"{Colors.WHITE}{text.center(80)}{Colors.NC}")
    print(f"{Colors.CYAN}{'='*80}{Colors.NC}\n")

def print_section(text: str):
    """Print a formatted section header."""
    print(f"\n{Colors.BLUE}{'─'*60}{Colors.NC}")
    print(f"{Colors.YELLOW}🔍 {text}{Colors.NC}")
    print(f"{Colors.BLUE}{'─'*60}{Colors.NC}")

def print_success(text: str):
    """Print success message."""
    print(f"{Colors.GREEN}✅ {text}{Colors.NC}")

def print_warning(text: str):
    """Print warning message."""
    print(f"{Colors.YELLOW}⚠️  {text}{Colors.NC}")

def print_error(text: str):
    """Print error message."""
    print(f"{Colors.RED}❌ {text}{Colors.NC}")

def print_info(text: str):
    """Print info message."""
    print(f"{Colors.BLUE}ℹ️  {text}{Colors.NC}")

def test_service_connectivity() -> Dict[str, Any]:
    """Test connectivity to all services."""
    print_section("Service Connectivity Tests")
    
    services = {
        "Frontend": {
            "url": "http://localhost:3100",
            "expected_content": "TurdParty",
            "timeout": 10
        },
        "Cachet Status Page": {
            "url": "http://localhost:3501",
            "expected_content": "Status",
            "timeout": 10
        },
        "Cachet API": {
            "url": "http://localhost:3501/api/v1/ping",
            "expected_json": {"data": "Pong!"},
            "timeout": 10
        },
        "Cachet Components": {
            "url": "http://localhost:3501/api/v1/components",
            "expected_json_key": "data",
            "timeout": 10
        },
        "MinIO Console": {
            "url": "http://localhost:3301",
            "expected_status": 200,
            "timeout": 10
        },
        "Celery Flower": {
            "url": "http://localhost:3450",
            "expected_status": 200,
            "timeout": 10
        },
        "Redis": {
            "url": "redis://localhost:3400",
            "test_type": "redis",
            "timeout": 5
        }
    }
    
    results = {}
    
    for service_name, config in services.items():
        print_info(f"Testing {service_name}...")
        
        try:
            if config.get("test_type") == "redis":
                # Special Redis test
                result = subprocess.run(
                    ["redis-cli", "-h", "localhost", "-p", "3400", "ping"],
                    capture_output=True, text=True, timeout=config["timeout"]
                )
                success = result.returncode == 0 and "PONG" in result.stdout
                results[service_name] = {
                    "success": success,
                    "response": result.stdout.strip() if success else result.stderr
                }
            else:
                # HTTP test
                response = requests.get(config["url"], timeout=config["timeout"])
                success = response.status_code == 200
                
                # Additional checks
                if success and "expected_content" in config:
                    success = config["expected_content"] in response.text
                
                if success and "expected_json" in config:
                    try:
                        json_data = response.json()
                        success = json_data == config["expected_json"]
                    except:
                        success = False
                
                if success and "expected_json_key" in config:
                    try:
                        json_data = response.json()
                        success = config["expected_json_key"] in json_data
                    except:
                        success = False
                
                results[service_name] = {
                    "success": success,
                    "status_code": response.status_code,
                    "response_size": len(response.text) if hasattr(response, 'text') else 0
                }
            
            if results[service_name]["success"]:
                print_success(f"{service_name} is working")
            else:
                print_error(f"{service_name} failed")
                
        except Exception as e:
            results[service_name] = {
                "success": False,
                "error": str(e)
            }
            print_error(f"{service_name} error: {str(e)}")
    
    return results

def test_cachet_customizations() -> Dict[str, Any]:
    """Test Cachet dark theme and customizations."""
    print_section("Cachet Customization Tests")
    
    tests = {
        "Dark Theme CSS": "http://localhost:3501/css/dark-theme.css",
        "Service Icons JS": "http://localhost:3501/js/service-icons.js",
        "Component Groups": "http://localhost:3501/api/v1/components/groups",
        "Components with Emojis": "http://localhost:3501/api/v1/components"
    }
    
    results = {}
    
    for test_name, url in tests.items():
        print_info(f"Testing {test_name}...")
        
        try:
            response = requests.get(url, timeout=10)
            success = response.status_code == 200
            
            # Special checks for specific tests
            if test_name == "Dark Theme CSS" and success:
                success = "dark-bg-primary" in response.text
            
            if test_name == "Service Icons JS" and success:
                success = "serviceIcons" in response.text
            
            if test_name == "Components with Emojis" and success:
                try:
                    data = response.json()
                    components = data.get("data", [])
                    emoji_count = sum(1 for comp in components if any(ord(char) > 127 for char in comp.get("name", "")))
                    success = emoji_count > 0
                    results[test_name] = {
                        "success": success,
                        "emoji_components": emoji_count,
                        "total_components": len(components)
                    }
                except:
                    success = False
            
            if test_name not in results:
                results[test_name] = {
                    "success": success,
                    "status_code": response.status_code,
                    "content_length": len(response.text)
                }
            
            if success:
                print_success(f"{test_name} is working")
            else:
                print_error(f"{test_name} failed")
                
        except Exception as e:
            results[test_name] = {
                "success": False,
                "error": str(e)
            }
            print_error(f"{test_name} error: {str(e)}")
    
    return results

def test_docker_containers() -> Dict[str, Any]:
    """Test Docker container health."""
    print_section("Docker Container Health Tests")
    
    results = {}
    
    try:
        # Get container status
        result = subprocess.run(
            ["docker", "ps", "--format", "{{.Names}}\t{{.Status}}\t{{.Image}}"],
            capture_output=True, text=True, timeout=10
        )
        
        if result.returncode == 0:
            containers = {}
            for line in result.stdout.strip().split('\n'):
                if 'turdparty_' in line:
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        name = parts[0]
                        status = parts[1]
                        image = parts[2] if len(parts) > 2 else "unknown"
                        
                        is_healthy = "healthy" in status.lower() or "up" in status.lower()
                        containers[name] = {
                            "status": status,
                            "image": image,
                            "healthy": is_healthy
                        }
                        
                        if is_healthy:
                            print_success(f"{name}: {status}")
                        else:
                            print_warning(f"{name}: {status}")
            
            results["containers"] = containers
            results["success"] = len(containers) > 0
            
        else:
            results = {"success": False, "error": result.stderr}
            print_error("Failed to get container status")
            
    except Exception as e:
        results = {"success": False, "error": str(e)}
        print_error(f"Container health check error: {str(e)}")
    
    return results

def test_file_operations() -> Dict[str, Any]:
    """Test basic file operations that don't require API."""
    print_section("File System Tests")
    
    results = {}
    
    # Test if key directories exist
    directories = [
        "api",
        "turdparty-app", 
        ".dockerwrapper",
        "scripts",
        "docs"
    ]
    
    for directory in directories:
        exists = os.path.exists(directory)
        results[f"Directory: {directory}"] = {"success": exists}
        
        if exists:
            print_success(f"Directory {directory} exists")
        else:
            print_warning(f"Directory {directory} missing")
    
    # Test if key files exist
    files = [
        "docker-compose.yml",
        ".dockerwrapper/docker-compose.yml",
        "scripts/run_full_test_suite.py",
        "scripts/cachet_service_manager.py",
        "scripts/update_cachet_status.py"
    ]
    
    for file_path in files:
        exists = os.path.exists(file_path)
        results[f"File: {file_path}"] = {"success": exists}
        
        if exists:
            print_success(f"File {file_path} exists")
        else:
            print_warning(f"File {file_path} missing")
    
    return results

def run_status_monitoring_test() -> Dict[str, Any]:
    """Test the Cachet status monitoring script."""
    print_section("Status Monitoring Test")
    
    try:
        print_info("Running Cachet service manager...")
        result = subprocess.run(
            ["nix-shell", "-p", "python3", "-p", "python3Packages.requests", 
             "--run", "python3 scripts/cachet_service_manager.py"],
            capture_output=True, text=True, timeout=30
        )
        
        success = result.returncode == 0
        
        if success:
            print_success("Status monitoring script executed successfully")
            # Count services mentioned in output
            service_count = result.stdout.count("✅") + result.stdout.count("⚠️") + result.stdout.count("❌")
            return {
                "success": True,
                "services_checked": service_count,
                "output": result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout
            }
        else:
            print_error("Status monitoring script failed")
            return {
                "success": False,
                "error": result.stderr[:200] + "..." if len(result.stderr) > 200 else result.stderr
            }
            
    except Exception as e:
        print_error(f"Status monitoring test error: {str(e)}")
        return {"success": False, "error": str(e)}

def generate_focused_report(all_results: Dict[str, Any]) -> str:
    """Generate a focused test report."""
    print_section("Test Report Generation")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"test_results/focused_test_report_{timestamp}.md"
    
    # Ensure directory exists
    os.makedirs("test_results", exist_ok=True)
    
    # Calculate statistics
    total_tests = 0
    passed_tests = 0
    
    for category, tests in all_results.items():
        if isinstance(tests, dict):
            for test_name, result in tests.items():
                total_tests += 1
                if isinstance(result, dict) and result.get("success", False):
                    passed_tests += 1
    
    pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    # Generate report
    report_content = f"""# TurdParty Focused Test Report

**Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## Summary

- **Total Tests:** {total_tests}
- **Passed:** {passed_tests}
- **Failed:** {total_tests - passed_tests}
- **Pass Rate:** {pass_rate:.1f}%

## Test Results

"""
    
    for category, tests in all_results.items():
        report_content += f"\n### {category.replace('_', ' ').title()}\n\n"
        
        if isinstance(tests, dict):
            for test_name, result in tests.items():
                if isinstance(result, dict):
                    status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
                    report_content += f"- **{test_name}:** {status}\n"
                    
                    # Add specific details
                    if "emoji_components" in result:
                        report_content += f"  - Emoji components: {result['emoji_components']}/{result['total_components']}\n"
                    
                    if "services_checked" in result:
                        report_content += f"  - Services checked: {result['services_checked']}\n"
                    
                    if not result.get("success", False) and result.get("error"):
                        report_content += f"  - Error: `{result['error'][:100]}...`\n"
    
    # Working services summary
    working_services = []
    if "connectivity_tests" in all_results:
        for service, result in all_results["connectivity_tests"].items():
            if result.get("success", False):
                working_services.append(service)
    
    report_content += f"""

## Working Services

{', '.join(working_services) if working_services else 'None detected'}

## Recommendations

"""
    
    if pass_rate >= 80:
        report_content += "- 🎉 Good pass rate! Most services are working well.\n"
    else:
        report_content += "- ⚠️ Some services need attention.\n"
    
    if "Frontend" in working_services:
        report_content += "- ✅ Frontend is accessible at http://localhost:3100\n"
    
    if "Cachet Status Page" in working_services:
        report_content += "- ✅ Cachet status page is accessible at http://localhost:3501\n"
    
    report_content += """

---
*Report generated by TurdParty Focused Test Suite*
"""
    
    # Write report
    with open(report_file, "w") as f:
        f.write(report_content)
    
    print_success(f"Test report generated: {report_file}")
    return report_file

def main():
    """Run the focused test suite."""
    print_header("TurdParty Focused Test Suite")
    print_info("Testing only operational services and features")
    
    start_time = time.time()
    all_results = {}
    
    try:
        # 1. Service Connectivity Tests
        all_results["connectivity_tests"] = test_service_connectivity()
        
        # 2. Cachet Customization Tests
        all_results["cachet_customizations"] = test_cachet_customizations()
        
        # 3. Docker Container Health
        all_results["container_health"] = test_docker_containers()
        
        # 4. File System Tests
        all_results["file_system"] = test_file_operations()
        
        # 5. Status Monitoring Test
        all_results["status_monitoring"] = run_status_monitoring_test()
        
        # 6. Generate Report
        report_file = generate_focused_report(all_results)
        
        # Final Summary
        end_time = time.time()
        duration = end_time - start_time
        
        print_header("Focused Test Suite Complete")
        print_info(f"Total execution time: {duration:.1f} seconds")
        print_info(f"Detailed report: {report_file}")
        
        # Calculate overall success
        working_services = sum(1 for result in all_results.get("connectivity_tests", {}).values() 
                             if result.get("success", False))
        
        if working_services >= 3:
            print_success(f"🎉 {working_services} services are working!")
            return 0
        else:
            print_warning(f"⚠️ Only {working_services} services are working")
            return 1
            
    except KeyboardInterrupt:
        print_error("\n🛑 Test suite interrupted by user")
        return 130
    except Exception as e:
        print_error(f"💥 Test suite failed with error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
