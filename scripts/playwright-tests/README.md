# TurdParty UI Testing

This directory contains comprehensive UI tests for the TurdParty application using Playwright. The tests cover various aspects of the UI functionality, including component rendering, form validation, user flows, accessibility, and more.

## Test Structure

The test suite is organized into several files, each focusing on a specific aspect of UI testing:

- **simple.spec.js**: Basic UI accessibility checks to ensure fundamental components are visible
- **ui-components.spec.js**: Tests rendering of individual UI components across pages
- **form-inputs.spec.js**: Validates form inputs, validation, and submission behavior
- **vm-operations.spec.js**: Tests VM-specific operations like status checks and VM actions
- **navigation-flow.spec.js**: Tests navigation between pages and complete user workflows
- **enhanced-ui.spec.js**: More advanced UI tests including responsiveness and error handling
- **accessibility.spec.js**: Tests for accessibility compliance using axe-core

## Running Tests

The tests can be run using npm scripts defined in the root package.json:

```bash
# Run all tests
npm run test:all

# Run specific test suites
npm run test:ui          # UI component tests
npm run test:forms       # Form validation tests
npm run test:vm          # VM operations tests
npm run test:nav         # Navigation flow tests
npm run test:enhanced    # Enhanced UI tests
npm run test:a11y        # Accessibility tests

# Run a single test file directly
npx playwright test scripts/playwright-tests/ui-components.spec.js
```

## Test Screenshots

The tests generate screenshots in the `test_screenshots` directory, which can be used to verify visual aspects of the UI. These screenshots are particularly useful for:

- Checking component rendering
- Verifying responsive behavior
- Examining error states
- Reviewing accessibility issues

## Configuring Tests

The tests use the frontend URL defined in each test file. By default, this is set to `http://**********:3000`, which is the Docker container address. If needed, update this URL in the test files to match your environment.

## Accessibility Testing

The accessibility tests use the @axe-core/playwright library to check for WCAG compliance. These tests verify:

- Proper labeling of form elements
- Correct ARIA attributes
- Color contrast
- Keyboard navigation
- Focus management
- Heading structure

## Adding New Tests

When adding new tests, follow these conventions:

1. Create a descriptive test name that explains what is being tested
2. Use the `test.describe` and `test` functions to organize tests
3. Take screenshots at key points to document the test
4. Add appropriate assertions to validate behavior
5. Log helpful information using `console.log`
6. Handle cases where elements might not be found

## Test Environment

The tests are designed to run against the TurdParty application running in Docker containers. Make sure the application is running before executing the tests.

```bash
# Start the application (if needed)
docker-compose up -d
``` 