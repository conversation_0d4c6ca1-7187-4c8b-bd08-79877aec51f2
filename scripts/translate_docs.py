
#!/usr/bin/env python
"""
Script to translate documentation files to multiple languages.
"""
import os
import glob
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
import json
from google.cloud import translate_v2 as translate
from babel.messages.catalog import Catalog
from babel.messages.pofile import write_po, read_po
from babel.messages.mofile import write_mo

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define target languages
LANGUAGES = {
    'af': 'Afrikaans',
    'en_GB': 'English (UK)',
    'de': 'German',
    'ro': 'Romanian',
    'zu': 'isiZulu'
}

# Map Babel language codes to Google Translate language codes
LANGUAGE_CODE_MAP = {
    'af': 'af',      # Afrikaans
    'bg': 'bg',      # Bulgarian
    'cs': 'cs',      # Czech
    'da': 'da',      # Danish
    'de': 'de',      # German
    'el': 'el',      # Greek
    'en_GB': 'en',   # British English (Google Translate doesn't distinguish UK English)
    'es': 'es',      # Spanish
    'et': 'et',      # Estonian
    'fi': 'fi',      # Finnish
    'fr': 'fr',      # French
    'gsw': 'de',     # Swiss German (fallback to German)
    'hu': 'hu',      # Hungarian
    'it': 'it',      # Italian
    'ja': 'ja',      # Japanese
    'lt': 'lt',      # Lithuanian
    'lv': 'lv',      # Latvian
    'nl': 'nl',      # Dutch
    'pl': 'pl',      # Polish
    'pt': 'pt',      # Portuguese
    'ro': 'ro',      # Romanian
    'ru': 'ru',      # Russian
    'sk': 'sk',      # Slovak
    'sl': 'sl',      # Slovenian
    'sv': 'sv',      # Swedish
    'tr': 'tr',      # Turkish
    'uk': 'uk',      # Ukrainian
    'zh': 'zh',      # Chinese
    'zu': 'zu'       # Zulu
}

# Define source and target directories
SOURCE_DIRS = ['docs/', '.']
SOURCE_FILES = ['README.md']
TARGET_DIR = 'lang/'

def find_markdown_files(source_dirs: List[str], specific_files: Optional[List[str]] = None) -> List[str]:
    """
    Find all markdown files in the specified directories.
    
    Args:
        source_dirs: List of directories to search
        specific_files: Optional list of specific files to include
        
    Returns:
        List of file paths
    """
    files = []
    
    # Add specific files if provided
    if specific_files:
        for file in specific_files:
            if os.path.exists(file) and file.endswith('.md'):
                files.append(file)
    
    # Find all markdown files in source directories
    for directory in source_dirs:
        if os.path.exists(directory):
            md_files = glob.glob(f"{directory}/**/*.md", recursive=True)
            files.extend(md_files)
    
    return list(set(files))  # Remove duplicates

def extract_content(file_path: str) -> str:
    """
    Extract content from a markdown file.
    
    Args:
        file_path: Path to the markdown file
        
    Returns:
        Content of the file
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return ""

def create_pot_file(file_path: str, output_dir: str) -> str:
    """
    Create a POT file from a markdown file.
    
    Args:
        file_path: Path to the markdown file
        output_dir: Directory to store the POT file
        
    Returns:
        Path to the created POT file
    """
    content = extract_content(file_path)
    if not content:
        return ""
    
    # Create a catalog
    catalog = Catalog(
        project="Documentation Translation",
        version="1.0",
        copyright_holder="Replit Project"
    )
    
    # Add the whole content as a single message
    catalog.add(content, context=file_path)
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Create POT file name from the original file path
    file_name = os.path.basename(file_path)
    pot_path = os.path.join(output_dir, f"{os.path.splitext(file_name)[0]}.pot")
    
    # Write POT file
    with open(pot_path, 'wb') as f:
        write_po(f, catalog)
    
    logger.info(f"Created POT file: {pot_path}")
    return pot_path

def create_po_files(pot_path: str, languages: Dict[str, str]) -> Dict[str, str]:
    """
    Create PO files for each language.
    
    Args:
        pot_path: Path to the POT file
        languages: Dictionary of language codes and names
        
    Returns:
        Dictionary of language codes and PO file paths
    """
    if not pot_path or not os.path.exists(pot_path):
        return {}
    
    po_files = {}
    pot_dir = os.path.dirname(pot_path)
    pot_name = os.path.basename(pot_path)
    base_name = os.path.splitext(pot_name)[0]
    
    # Read the catalog from the POT file
    with open(pot_path, 'rb') as f:
        catalog = read_po(f)
    
    for lang_code, lang_name in languages.items():
        # Create language directory
        lang_dir = os.path.join(pot_dir, lang_code)
        os.makedirs(lang_dir, exist_ok=True)
        
        # Create PO file
        po_path = os.path.join(lang_dir, f"{base_name}.po")
        
        # Create a new catalog with language information
        lang_catalog = Catalog(
            project=catalog.project,
            version=catalog.version,
            copyright_holder=catalog.copyright_holder,
            locale=lang_code
        )
        
        # Copy messages from the original catalog
        for message in catalog:
            if message.id:  # Skip header
                lang_catalog.add(
                    message.id,
                    context=message.context,
                    flags=message.flags
                )
        
        # Write PO file
        with open(po_path, 'wb') as f:
            write_po(f, lang_catalog)
        
        po_files[lang_code] = po_path
        logger.info(f"Created PO file for {lang_name}: {po_path}")
    
    return po_files

def get_translation_client() -> Any:
    """
    Get a Google Cloud Translation API client.
    
    Returns:
        A Translation API client
    """
    try:
        return translate.Client()
    except Exception as e:
        logger.error(f"Error creating translation client: {e}")
        logger.error("Make sure you've set up GOOGLE_APPLICATION_CREDENTIALS environment variable")
        return None

def translate_text(text: str, target_language: str, client: Any) -> str:
    """
    Translate text to the target language.
    
    Args:
        text: Text to translate
        target_language: Target language code
        client: Translation API client
        
    Returns:
        Translated text
    """
    if not client:
        return f"[Translation failed] {text}"
    
    try:
        logger.info(f"Translating text to {target_language} (first 50 chars): {text[:50]}...")
        result = client.translate(text, target_language=target_language)
        logger.info(f"Translation successful for {target_language}")
        return result['translatedText']
    except Exception as e:
        logger.error(f"Error translating text: {e}")
        return f"[Translation failed] {text}"

def translate_po_files(po_files: Dict[str, str]) -> None:
    """
    Translate content in PO files using Google Cloud Translation API.
    
    Args:
        po_files: Dictionary of language codes and PO file paths
    """
    # Get translation client
    client = get_translation_client()
    if not client:
        logger.error("Failed to create translation client, using simulation instead")
        simulate_translations(po_files)
        return
    
    for lang_code, po_path in po_files.items():
        if not os.path.exists(po_path):
            continue
        
        # Get Google Translate language code
        target_language = LANGUAGE_CODE_MAP.get(lang_code)
        if not target_language:
            logger.warning(f"No mapping for language code {lang_code}, skipping translation")
            continue
        
        # Read the PO file
        with open(po_path, 'rb') as f:
            catalog = read_po(f)
        
        # Translate each message
        total_messages = 0
        translated_messages = 0
        
        for message in catalog:
            if message.id:  # Skip header
                total_messages += 1
                try:
                    # Special handling for UK English - just replace specific words
                    if lang_code == 'en_GB':
                        message.string = message.id.replace('color', 'colour').replace('Color', 'Colour')
                        translated_messages += 1
                    else:
                        message.string = translate_text(message.id, target_language, client)
                        if message.string and not message.string.startswith('[Translation failed]'):
                            translated_messages += 1
                except Exception as e:
                    logger.error(f"Error translating message: {e}")
                    message.string = f"[Translation failed] {message.id}"
        
        # Write updated PO file
        with open(po_path, 'wb') as f:
            write_po(f, catalog)
        
        logger.info(f"Translated {translated_messages}/{total_messages} messages for {LANGUAGES[lang_code]}")

def simulate_translations(po_files: Dict[str, str]) -> None:
    """
    Simulates translations by adding placeholder translations to PO files.
    This is used as a fallback when translation API is not available.
    
    Args:
        po_files: Dictionary of language codes and PO file paths
    """
    logger.warning("Using simulated translations as fallback")
    
    for lang_code, po_path in po_files.items():
        if not os.path.exists(po_path):
            continue
        
        # Read the PO file
        with open(po_path, 'rb') as f:
            catalog = read_po(f)
        
        # Add simulated translations
        for message in catalog:
            if message.id:  # Skip header
                if lang_code == 'af':
                    # Afrikaans translation simulation
                    message.string = f"[AF] {message.id}"
                elif lang_code == 'en_GB':
                    # UK English translation simulation (minimal changes)
                    message.string = message.id.replace('color', 'colour').replace('Color', 'Colour')
                elif lang_code == 'de':
                    # German translation simulation
                    message.string = f"[DE] {message.id}"
                elif lang_code == 'ro':
                    # Romanian translation simulation
                    message.string = f"[RO] {message.id}"
                elif lang_code == 'zu':
                    # isiZulu translation simulation
                    message.string = f"[ZU] {message.id}"
        
        # Write updated PO file
        with open(po_path, 'wb') as f:
            write_po(f, catalog)
        
        logger.info(f"Added simulated translations to {po_path}")

def compile_and_create_md(po_files: Dict[str, str], target_dir: str) -> None:
    """
    Compile PO files to MO files and create markdown files.
    
    Args:
        po_files: Dictionary of language codes and PO file paths
        target_dir: Directory to store the translated markdown files
    """
    for lang_code, po_path in po_files.items():
        if not os.path.exists(po_path):
            continue
        
        # Read the PO file
        with open(po_path, 'rb') as f:
            catalog = read_po(f)
        
        # Create language directory in target directory
        lang_dir = os.path.join(target_dir, lang_code)
        os.makedirs(lang_dir, exist_ok=True)
        
        # Create MO file
        mo_path = os.path.join(os.path.dirname(po_path), f"{os.path.splitext(os.path.basename(po_path))[0]}.mo")
        with open(mo_path, 'wb') as f:
            write_mo(f, catalog)
        
        # For each message, create a markdown file
        for message in catalog:
            if message.id and message.string and message.context:
                # Get original file context and create corresponding translated file
                original_path = message.context
                relative_path = os.path.relpath(original_path, '.')
                translated_path = os.path.join(lang_dir, relative_path)
                
                # Create directory structure
                os.makedirs(os.path.dirname(translated_path), exist_ok=True)
                
                # Write translated content
                with open(translated_path, 'w', encoding='utf-8') as f:
                    f.write(message.string)
                
                logger.info(f"Created translated file: {translated_path}")

def check_credentials() -> bool:
    """
    Check if Google Cloud credentials are available.
    
    Returns:
        True if credentials are available, False otherwise
    """
    if os.environ.get('GOOGLE_APPLICATION_CREDENTIALS'):
        return True
    
    # Check if we can create a client without explicit credentials
    try:
        translate.Client()
        return True
    except Exception:
        return False

def main():
    """Main function to orchestrate the translation process."""
    logger.info("Starting documentation translation process")
    
    # Dictionary to track translation status
    translation_status = {lang: {"total": 0, "translated": 0} for lang in LANGUAGES}
    
    # Check for credentials
    using_real_translations = check_credentials()
    if not using_real_translations:
        logger.warning("Google Cloud credentials not found. You can:")
        logger.warning("1. Set GOOGLE_APPLICATION_CREDENTIALS environment variable")
        logger.warning("   or")
        logger.warning("2. Run with --simulate to use simulated translations")
        logger.warning("Using simulated translations for now...")
    else:
        logger.info("Using Google Cloud Translation API for translations")
    
    # Find markdown files
    files = find_markdown_files(SOURCE_DIRS, SOURCE_FILES)
    logger.info(f"Found {len(files)} markdown files to translate")
    
    # Create temp directory for PO/POT files
    temp_dir = os.path.join(TARGET_DIR, 'temp')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Process each file
    for file_path in files:
        logger.info(f"Processing file: {file_path}")
        
        # Create POT file
        pot_path = create_pot_file(file_path, temp_dir)
        if not pot_path:
            logger.warning(f"Failed to create POT file for {file_path}")
            continue
        
        # Create PO files
        po_files = create_po_files(pot_path, LANGUAGES)
        if not po_files:
            logger.warning(f"Failed to create PO files for {pot_path}")
            continue
        
        # Translate PO files or simulate translations
        if using_real_translations:
            translate_po_files(po_files)
        else:
            simulate_translations(po_files)
        
        # Update translation status
        for lang_code, po_path in po_files.items():
            if os.path.exists(po_path):
                with open(po_path, 'rb') as f:
                    catalog = read_po(f)
                    total = sum(1 for m in catalog if m.id)
                    translated = sum(1 for m in catalog if m.id and m.string)
                    translation_status[lang_code]["total"] += total
                    translation_status[lang_code]["translated"] += translated
        
        # Compile and create translated markdown files
        compile_and_create_md(po_files, TARGET_DIR)
    
    # Print translation status summary
    logger.info("\nTranslation Status Summary:")
    for lang_code, status in translation_status.items():
        total = status["total"]
        translated = status["translated"]
        percentage = (translated / total * 100) if total > 0 else 0
        logger.info(f"{LANGUAGES[lang_code]}: {translated}/{total} messages ({percentage:.1f}%)")
    
    logger.info("\nDocumentation translation process completed")
    
    # Write status to a file
    status_file = os.path.join(TARGET_DIR, 'translation_status.json')
    with open(status_file, 'w', encoding='utf-8') as f:
        json.dump({
            "timestamp": str(Path.ctime(Path())),
            "using_real_translations": using_real_translations,
            "languages": {lang_code: {
                "name": LANGUAGES[lang_code],
                "total": status["total"],
                "translated": status["translated"],
                "percentage": (status["translated"] / status["total"] * 100) if status["total"] > 0 else 0
            } for lang_code, status in translation_status.items()}
        }, f, indent=2)
    logger.info(f"Translation status written to {status_file}")

if __name__ == "__main__":
    main()
