#!/usr/bin/env python3
"""
<PERSON>ript to update Docker container names to use the turdparty_ prefix.
"""
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple

def update_docker_compose_files(verbose: bool = False, dry_run: bool = False) -> Dict[str, int]:
    """Update Docker Compose files to use turdparty_ prefix for container names."""
    # Get the project root directory
    project_root = Path(__file__).parent.parent.absolute()
    
    # Find all Docker Compose files
    docker_compose_files = list(project_root.glob("**/*docker-compose*.yml"))
    docker_compose_files.extend(project_root.glob("**/*docker-compose*.yaml"))
    
    # Regular expressions for finding container names
    container_name_pattern = re.compile(r'container_name:\s*(["\']?)dockerwrapper[-_]([^"\'\n]+)(["\']?)')
    network_name_pattern = re.compile(r'name:\s*(["\']?)dockerwrapper[-_]([^"\'\n]+)(["\']?)')
    
    # Track changes
    changes_made = 0
    files_changed = set()
    
    # Process each file
    for file_path in docker_compose_files:
        if verbose:
            print(f"Processing {file_path}...")
        
        # Read the file content
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Update container names
        new_content, container_changes = re.subn(
            container_name_pattern,
            r'container_name: \1turdparty_\2\3',
            content
        )
        
        # Update network names
        new_content, network_changes = re.subn(
            network_name_pattern,
            r'name: \1turdparty_\2\3',
            new_content
        )
        
        # If changes were made, write the file
        if container_changes > 0 or network_changes > 0:
            changes_made += container_changes + network_changes
            files_changed.add(str(file_path))
            
            if verbose:
                print(f"  Made {container_changes} container name changes and {network_changes} network name changes")
            
            if not dry_run:
                with open(file_path, 'w') as f:
                    f.write(new_content)
    
    return {
        "files_changed": files_changed,
        "changes_made": changes_made
    }

def update_shell_scripts(verbose: bool = False, dry_run: bool = False) -> Dict[str, int]:
    """Update shell scripts to use turdparty_ prefix for container names."""
    # Get the project root directory
    project_root = Path(__file__).parent.parent.absolute()
    
    # Find all shell scripts
    shell_scripts = list(project_root.glob("**/*.sh"))
    
    # Regular expressions for finding container references
    container_ref_pattern = re.compile(r'(docker\s+(?:exec|logs|stop|rm|inspect|ps|run)\s+(?:-\w+\s+)*)(dockerwrapper[-_][^\s]+)')
    grep_pattern = re.compile(r'(grep\s+(?:-\w+\s+)*["\']?)(dockerwrapper[-_][^\s"\']+)(["\']?)')
    
    # Track changes
    changes_made = 0
    files_changed = set()
    
    # Process each file
    for file_path in shell_scripts:
        if verbose:
            print(f"Processing {file_path}...")
        
        # Read the file content
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Update container references
        new_content, container_changes = re.subn(
            container_ref_pattern,
            lambda m: f"{m.group(1)}turdparty_{m.group(2).replace('dockerwrapper-', '').replace('dockerwrapper_', '')}",
            content
        )
        
        # Update grep patterns
        new_content, grep_changes = re.subn(
            grep_pattern,
            lambda m: f"{m.group(1)}turdparty_{m.group(2).replace('dockerwrapper-', '').replace('dockerwrapper_', '')}{m.group(3)}",
            new_content
        )
        
        # If changes were made, write the file
        if container_changes > 0 or grep_changes > 0:
            changes_made += container_changes + grep_changes
            files_changed.add(str(file_path))
            
            if verbose:
                print(f"  Made {container_changes} container reference changes and {grep_changes} grep pattern changes")
            
            if not dry_run:
                with open(file_path, 'w') as f:
                    f.write(new_content)
    
    return {
        "files_changed": files_changed,
        "changes_made": changes_made
    }

def update_python_files(verbose: bool = False, dry_run: bool = False) -> Dict[str, int]:
    """Update Python files to use turdparty_ prefix for container names."""
    # Get the project root directory
    project_root = Path(__file__).parent.parent.absolute()
    
    # Find all Python files
    python_files = list(project_root.glob("**/*.py"))
    
    # Regular expressions for finding container references
    container_ref_pattern = re.compile(r'(["\'])dockerwrapper[-_]([^"\'\n]+)(["\'])')
    
    # Track changes
    changes_made = 0
    files_changed = set()
    
    # Process each file
    for file_path in python_files:
        # Skip this script
        if file_path.name == "update_docker_container_names.py":
            continue
            
        if verbose:
            print(f"Processing {file_path}...")
        
        # Read the file content
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Update container references
        new_content, container_changes = re.subn(
            container_ref_pattern,
            r'\1turdparty_\2\3',
            content
        )
        
        # If changes were made, write the file
        if container_changes > 0:
            changes_made += container_changes
            files_changed.add(str(file_path))
            
            if verbose:
                print(f"  Made {container_changes} container reference changes")
            
            if not dry_run:
                with open(file_path, 'w') as f:
                    f.write(new_content)
    
    return {
        "files_changed": files_changed,
        "changes_made": changes_made
    }

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Update Docker container names to use turdparty_ prefix.')
    parser.add_argument('--verbose', action='store_true', help='Show verbose output')
    parser.add_argument('--dry-run', action='store_true', help='Dry run (no changes will be made)')
    args = parser.parse_args()
    
    # Update Docker Compose files
    print("Updating Docker Compose files...")
    compose_results = update_docker_compose_files(verbose=args.verbose, dry_run=args.dry_run)
    
    # Update shell scripts
    print("Updating shell scripts...")
    script_results = update_shell_scripts(verbose=args.verbose, dry_run=args.dry_run)
    
    # Update Python files
    print("Updating Python files...")
    python_results = update_python_files(verbose=args.verbose, dry_run=args.dry_run)
    
    # Print summary
    print("\nSummary:")
    print(f"Docker Compose files changed: {len(compose_results['files_changed'])}")
    print(f"Shell scripts changed: {len(script_results['files_changed'])}")
    print(f"Python files changed: {len(python_results['files_changed'])}")
    print(f"Total changes made: {compose_results['changes_made'] + script_results['changes_made'] + python_results['changes_made']}")
    
    # List changed files
    if args.verbose:
        print("\nChanged files:")
        all_changed_files = compose_results['files_changed'] | script_results['files_changed'] | python_results['files_changed']
        for file_path in sorted(all_changed_files):
            print(f"  {file_path}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
