#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the entire transition process.
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List, <PERSON>ple

def run_transition_process(verbose: bool = False, dry_run: bool = False) -> Dict[str, int]:
    """Run the entire transition process."""
    # Get the project root directory
    project_root = Path(__file__).parent.parent.absolute()

    # Change to the project root directory
    os.chdir(project_root)

    # Define the steps to run
    steps = [
        {
            "name": "Update import statements",
            "command": ["python", "scripts/update_imports.py", "api"]
        },
        {
            "name": "Update import statements in db directory",
            "command": ["python", "scripts/update_imports.py", "db"]
        },
        {
            "name": "Update import statements in utils directory",
            "command": ["python", "scripts/update_imports.py", "utils"]
        },
        {
            "name": "Update import statements in tests directory",
            "command": ["python", "scripts/update_imports.py", "tests"]
        },
        {
            "name": "Update Docker Compose files",
            "command": ["python", "scripts/update_docker_compose.py", "docker"]
        },
        {
            "name": "Update Docker container names to use turdparty_ prefix",
            "command": ["python", "scripts/update_docker_container_names.py"]
        },
        {
            "name": "Update deployment scripts",
            "command": ["python", "scripts/update_deployment_scripts.py", "scripts"]
        },
        {
            "name": "Update GitHub workflows",
            "command": ["python", "scripts/update_github_workflows.py", ".github"]
        },
        {
            "name": "Update documentation",
            "command": ["python", "scripts/update_documentation.py", "docs"]
        },
        {
            "name": "Run all tests",
            "command": ["python", "scripts/run_all_tests.py"]
        },
        {
            "name": "Generate test report",
            "command": ["python", "scripts/generate_test_report.py", "--run-tests"]
        },
        {
            "name": "Remove symbolic links",
            "command": ["python", "scripts/remove_symlinks.py"]
        },
        {
            "name": "Remove original files",
            "command": ["python", "scripts/remove_original_files.py"]
        },
        {
            "name": "Verify transition",
            "command": ["python", "scripts/verify_transition.py"]
        },
        {
            "name": "Run all transition reports",
            "command": ["python", "scripts/run_all_transition_reports.py"]
        },
        {
            "name": "Generate commit message",
            "command": ["python", "scripts/generate_commit_message.py"]
        },
        {
            "name": "Generate PR description",
            "command": ["python", "scripts/generate_pr_description.py"]
        }
    ]

    # Run the steps
    results = {}
    for step in steps:
        print(f"Running {step['name']}...")
        cmd = step["command"]

        if verbose:
            print(f"Running command: {' '.join(cmd)}")

        if dry_run:
            print(f"Would run command: {' '.join(cmd)}")
            results[step["name"]] = 0
        else:
            result = subprocess.run(cmd)
            results[step["name"]] = result.returncode

    # Create pull request
    if not dry_run:
        print("Creating pull request...")
        subprocess.run(["python", "scripts/create_pull_request.py"])
    else:
        print("Would create pull request")

    return results

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Run the entire transition process.')
    parser.add_argument('--verbose', action='store_true', help='Show verbose output')
    parser.add_argument('--dry-run', action='store_true', help='Dry run (no changes will be made)')
    args = parser.parse_args()

    results = run_transition_process(verbose=args.verbose, dry_run=args.dry_run)

    # Print summary
    print("\nTransition Process Results:")
    for step, returncode in results.items():
        status = "✅ Passed" if returncode == 0 else "❌ Failed"
        print(f"{step}: {status}")

    # Return success if all steps pass
    return 0 if all(returncode == 0 for returncode in results.values()) else 1

if __name__ == "__main__":
    sys.exit(main())
