#!/bin/bash

# Setup script for Inspektor Gadget ML Integration
# This script sets up the complete environment for binary analysis

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "🚀 Setting up Inspektor Gadget ML Integration Environment"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check Vagrant
    if ! command -v vagrant &> /dev/null; then
        log_warning "Vagrant is not installed. VM templates will not be available."
        log_warning "Please install Vagrant to use VM analysis features."
    fi
    
    # Check VirtualBox
    if ! command -v vboxmanage &> /dev/null; then
        log_warning "VirtualBox is not installed. VM templates will not be available."
        log_warning "Please install VirtualBox to use VM analysis features."
    fi
    
    log_success "Prerequisites check completed"
}

# Setup ELK stack
setup_elk_stack() {
    log_info "Setting up ELK stack..."
    
    cd "$PROJECT_ROOT/.dockerwrapper"
    
    # Create necessary directories
    mkdir -p logs/inspektor-gadget
    mkdir -p config/elasticsearch
    mkdir -p config/kibana/dashboards
    
    # Set proper permissions for Elasticsearch
    sudo chown -R 1000:1000 config/elasticsearch 2>/dev/null || true
    
    # Start ELK stack
    log_info "Starting ELK stack services..."
    docker-compose -f services/elk-stack.yml up -d
    
    # Wait for services to be ready
    log_info "Waiting for ELK stack to be ready..."
    
    # Wait for Elasticsearch
    for i in {1..30}; do
        if curl -s http://localhost:9200/_cluster/health &>/dev/null; then
            log_success "Elasticsearch is ready"
            break
        fi
        echo -n "."
        sleep 10
    done
    
    # Wait for Kibana
    for i in {1..30}; do
        if curl -s http://localhost:5601/api/status &>/dev/null; then
            log_success "Kibana is ready"
            break
        fi
        echo -n "."
        sleep 10
    done
    
    log_success "ELK stack setup completed"
}

# Setup Kibana dashboards
setup_kibana_dashboards() {
    log_info "Setting up Kibana dashboards..."
    
    # Create index patterns
    curl -X POST "localhost:5601/api/saved_objects/index-pattern/inspektor-gadget-*" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "attributes": {
                "title": "inspektor-gadget-*",
                "timeFieldName": "@timestamp"
            }
        }' 2>/dev/null || log_warning "Index pattern may already exist"
    
    # Import dashboard configuration
    cat > /tmp/kibana_dashboard.json << 'EOF'
{
    "version": "8.11.0",
    "objects": [
        {
            "id": "inspektor-gadget-overview",
            "type": "dashboard",
            "attributes": {
                "title": "Inspektor Gadget Analysis Overview",
                "description": "Overview dashboard for binary analysis results",
                "panelsJSON": "[]",
                "timeRestore": false,
                "version": 1
            }
        }
    ]
}
EOF
    
    curl -X POST "localhost:5601/api/saved_objects/_import" \
        -H "kbn-xsrf: true" \
        -F "file=@/tmp/kibana_dashboard.json" 2>/dev/null || log_warning "Dashboard import may have failed"
    
    rm -f /tmp/kibana_dashboard.json
    
    log_success "Kibana dashboards setup completed"
}

# Setup VM templates
setup_vm_templates() {
    log_info "Setting up VM templates..."
    
    if ! command -v vagrant &> /dev/null; then
        log_warning "Vagrant not available, skipping VM template setup"
        return
    fi
    
    cd "$PROJECT_ROOT/vagrant_templates"
    
    # Download and add Vagrant boxes
    local templates=("ubuntu/jammy64" "centos/stream9" "debian/bookworm64" "fedora/39-cloud-base" "generic/alpine319")
    local template_names=("ubuntu-22.04" "centos-stream-9" "debian-12" "fedora-39" "alpine-3.19")
    
    for i in "${!templates[@]}"; do
        local box="${templates[$i]}"
        local name="${template_names[$i]}"
        
        log_info "Setting up VM template: $name"
        
        # Add box if not already added
        if ! vagrant box list | grep -q "$box"; then
            log_info "Downloading Vagrant box: $box"
            vagrant box add "$box" --provider virtualbox || log_warning "Failed to add box $box"
        fi
        
        # Test VM template
        cd "$name"
        if [ -f "Vagrantfile" ]; then
            log_info "Testing VM template: $name"
            vagrant validate || log_warning "VM template $name validation failed"
        fi
        cd ..
    done
    
    log_success "VM templates setup completed"
}

# Setup Python dependencies
setup_python_dependencies() {
    log_info "Setting up Python dependencies..."
    
    cd "$PROJECT_ROOT"
    
    # Install analysis service dependencies
    if command -v pip3 &> /dev/null; then
        pip3 install -r requirements.txt
        pip3 install elasticsearch kibana-api
    else
        log_warning "pip3 not available, please install Python dependencies manually"
    fi
    
    log_success "Python dependencies setup completed"
}

# Create test data
create_test_data() {
    log_info "Creating test data..."
    
    mkdir -p "$PROJECT_ROOT/test_data"
    
    # Create test binary
    cat > "$PROJECT_ROOT/test_data/test_binary.sh" << 'EOF'
#!/bin/bash
# Test binary for Inspektor Gadget analysis

echo "Starting test binary execution"

# File operations
echo "Test data" > /tmp/test_file.txt
cat /tmp/test_file.txt
ls -la /tmp/
rm /tmp/test_file.txt

# Network operations
ping -c 1 ******* 2>/dev/null || echo "Ping failed"
nslookup google.com 2>/dev/null || echo "DNS lookup failed"

# Process operations
ps aux | head -5
whoami
id

echo "Test binary execution completed"
exit 0
EOF
    
    chmod +x "$PROJECT_ROOT/test_data/test_binary.sh"
    
    # Create malicious test binary (harmless simulation)
    cat > "$PROJECT_ROOT/test_data/suspicious_binary.sh" << 'EOF'
#!/bin/bash
# Suspicious binary simulation for testing

echo "Suspicious activity simulation"

# Simulate suspicious file operations
touch /tmp/.hidden_file
echo "sensitive_data" > /tmp/.hidden_file

# Simulate network scanning
for port in 22 23 80 443; do
    timeout 1 bash -c "</dev/tcp/127.0.0.1/$port" 2>/dev/null && echo "Port $port open"
done

# Simulate privilege escalation attempt
sudo -n whoami 2>/dev/null || echo "Privilege escalation failed"

# Cleanup
rm -f /tmp/.hidden_file

echo "Suspicious simulation completed"
EOF
    
    chmod +x "$PROJECT_ROOT/test_data/suspicious_binary.sh"
    
    log_success "Test data created"
}

# Verify installation
verify_installation() {
    log_info "Verifying installation..."
    
    # Check ELK stack
    if curl -s http://localhost:9200/_cluster/health | grep -q '"status":"green\|yellow"'; then
        log_success "Elasticsearch is healthy"
    else
        log_error "Elasticsearch is not healthy"
    fi
    
    if curl -s http://localhost:5601/api/status | grep -q '"overall":{"level":"available"}'; then
        log_success "Kibana is available"
    else
        log_warning "Kibana may not be fully ready"
    fi
    
    if curl -s http://localhost:9600 &>/dev/null; then
        log_success "Logstash is running"
    else
        log_warning "Logstash may not be ready"
    fi
    
    # Check API endpoints
    if curl -s http://localhost:8000/api/v1/analysis/ &>/dev/null; then
        log_success "Analysis API is available"
    else
        log_warning "Analysis API may not be ready"
    fi
    
    log_success "Installation verification completed"
}

# Print usage information
print_usage() {
    echo ""
    echo "🎉 Inspektor Gadget ML Integration Setup Complete!"
    echo "================================================"
    echo ""
    echo "Services available:"
    echo "  • Elasticsearch: http://localhost:9200"
    echo "  • Kibana: http://localhost:5601"
    echo "  • Logstash: http://localhost:9600"
    echo "  • Analysis API: http://localhost:8000/api/v1/analysis/"
    echo ""
    echo "Next steps:"
    echo "  1. Upload a binary via the API or UI"
    echo "  2. Start an analysis with your preferred VM template"
    echo "  3. Monitor progress in real-time"
    echo "  4. View results in Kibana dashboards"
    echo ""
    echo "Test the setup:"
    echo "  • Run integration tests: pytest tests/integration/"
    echo "  • Run UI tests: npx playwright test tests/playwright/"
    echo "  • Use test binaries in: test_data/"
    echo ""
    echo "For troubleshooting, check logs:"
    echo "  • docker-compose -f .dockerwrapper/services/elk-stack.yml logs"
    echo ""
}

# Main execution
main() {
    check_prerequisites
    setup_elk_stack
    setup_kibana_dashboards
    setup_vm_templates
    setup_python_dependencies
    create_test_data
    
    # Wait a bit for services to stabilize
    log_info "Waiting for services to stabilize..."
    sleep 30
    
    verify_installation
    print_usage
}

# Handle script arguments
case "${1:-}" in
    "elk")
        setup_elk_stack
        ;;
    "vm")
        setup_vm_templates
        ;;
    "test")
        create_test_data
        ;;
    "verify")
        verify_installation
        ;;
    *)
        main
        ;;
esac
