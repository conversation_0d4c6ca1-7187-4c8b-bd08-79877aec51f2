#!/usr/bin/env python3
"""
Customize Cachet with dark theme and service icons.
This script injects custom CSS and JavaScript into the Cachet container.
"""

import subprocess
import sys
import os
from typing import Dict, Any

# Service icon mapping
SERVICE_ICONS = {
    "API": "api",
    "Frontend": "frontend", 
    "Redis": "redis",
    "Celery Default": "celery",
    "Celery File Ops": "celery",
    "Celery VM Ops": "celery",
    "Celery Flower": "flower",
    "PostgreSQL": "postgres",
    "MinIO": "minio"
}

def run_docker_command(command: list) -> tuple:
    """Run a docker command and return result."""
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=30)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def inject_custom_css():
    """Inject custom dark theme CSS into Cachet."""
    print("🎨 Injecting dark theme CSS...")

    # Read the custom CSS file
    css_file = "scripts/cachet_dark_theme.css"
    if not os.path.exists(css_file):
        print(f"❌ CSS file not found: {css_file}")
        return False

    with open(css_file, 'r') as f:
        css_content = f.read()

    # Create the CSS directory if it doesn't exist
    success, stdout, stderr = run_docker_command([
        "docker", "exec", "turdparty_cachet",
        "mkdir", "-p", "/var/www/html/public/css"
    ])

    if not success:
        print(f"❌ Failed to create CSS directory: {stderr}")
        return False

    # Copy the CSS file to the container
    with open("/tmp/dark-theme.css", "w") as f:
        f.write(css_content)

    success, stdout, stderr = run_docker_command([
        "docker", "cp", "/tmp/dark-theme.css", "turdparty_cachet:/var/www/html/public/css/dark-theme.css"
    ])

    if success:
        print("✅ Dark theme CSS injected successfully")
        return True
    else:
        print(f"❌ Failed to inject CSS: {stderr}")
        return False

def inject_service_icons():
    """Inject service icons into Cachet."""
    print("🎯 Adding service icons...")
    
    # Create JavaScript to add icons to components
    js_content = """
// TurdParty Service Icons for Cachet
(function() {
    'use strict';
    
    // Service icon mapping
    const serviceIcons = {
        'API': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 2L2 7L12 12L22 7L12 2Z"/><path d="M2 17L12 22L22 17"/><path d="M2 12L12 17L22 12"/></svg>',
        'Frontend': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="2" y="3" width="20" height="14" rx="2" ry="2"/><line x1="8" y1="21" x2="16" y2="21"/><line x1="12" y1="17" x2="12" y2="21"/></svg>',
        'Redis': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M4 6H20V18H4V6Z"/><path d="M4 12H20"/><path d="M8 8V16"/><path d="M16 8V16"/></svg>',
        'Celery Default': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M12 1V3"/><path d="M12 21V23"/><path d="M4.22 4.22L5.64 5.64"/><path d="M18.36 18.36L19.78 19.78"/><path d="M1 12H3"/><path d="M21 12H23"/><path d="M4.22 19.78L5.64 18.36"/><path d="M18.36 5.64L19.78 4.22"/></svg>',
        'Celery File Ops': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M12 1V3"/><path d="M12 21V23"/><path d="M4.22 4.22L5.64 5.64"/><path d="M18.36 18.36L19.78 19.78"/><path d="M1 12H3"/><path d="M21 12H23"/><path d="M4.22 19.78L5.64 18.36"/><path d="M18.36 5.64L19.78 4.22"/></svg>',
        'Celery VM Ops': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M12 1V3"/><path d="M12 21V23"/><path d="M4.22 4.22L5.64 5.64"/><path d="M18.36 18.36L19.78 19.78"/><path d="M1 12H3"/><path d="M21 12H23"/><path d="M4.22 19.78L5.64 18.36"/><path d="M18.36 5.64L19.78 4.22"/></svg>',
        'Celery Flower': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z"/><circle cx="12" cy="12" r="3"/></svg>',
        'PostgreSQL': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M21 12C21 13.66 16.97 15 12 15S3 13.66 3 12"/><path d="M3 5V19C3 20.66 7.03 22 12 22S21 20.66 21 19V5"/></svg>',
        'MinIO': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/></svg>'
    };
    
    function addServiceIcons() {
        // Find all component names and add icons
        const components = document.querySelectorAll('.component-name, .component h4, [class*="component"] h4');
        
        components.forEach(component => {
            const text = component.textContent.trim();
            const icon = serviceIcons[text];
            
            if (icon && !component.querySelector('.service-icon')) {
                const iconSpan = document.createElement('span');
                iconSpan.className = 'service-icon';
                iconSpan.innerHTML = icon;
                iconSpan.style.marginRight = '8px';
                iconSpan.style.color = '#4a9eff';
                iconSpan.style.verticalAlign = 'middle';
                
                component.insertBefore(iconSpan, component.firstChild);
            }
        });
    }
    
    // Add icons when page loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addServiceIcons);
    } else {
        addServiceIcons();
    }
    
    // Re-add icons if content changes (for dynamic updates)
    const observer = new MutationObserver(addServiceIcons);
    observer.observe(document.body, { childList: true, subtree: true });
    
})();
"""
    
    # Create the JS directory if it doesn't exist
    success, _, stderr = run_docker_command([
        "docker", "exec", "turdparty_cachet",
        "mkdir", "-p", "/var/www/html/public/js"
    ])

    if not success:
        print(f"❌ Failed to create JS directory: {stderr}")
        return False

    # Copy the JS file to the container
    with open("/tmp/service-icons.js", "w") as f:
        f.write(js_content)

    success, _, stderr = run_docker_command([
        "docker", "cp", "/tmp/service-icons.js", "turdparty_cachet:/var/www/html/public/js/service-icons.js"
    ])

    if success:
        print("✅ Service icons JavaScript injected successfully")
        return True
    else:
        print(f"❌ Failed to inject icons: {stderr}")
        return False

def update_master_layout():
    """Update the master layout to include our custom CSS and JS."""
    print("🔧 Updating master layout...")

    # First, backup the original layout
    success, _, stderr = run_docker_command([
        "docker", "exec", "turdparty_cachet",
        "cp", "/var/www/html/resources/views/layout/master.blade.php",
        "/var/www/html/resources/views/layout/master.blade.php.backup"
    ])

    if not success:
        print(f"⚠️  Could not backup master layout: {stderr}")

    # Create a custom head injection file
    custom_head_content = '''    <!-- TurdParty Custom Dark Theme -->
    <link rel="stylesheet" href="{{ asset('css/dark-theme.css') }}">
    <script src="{{ asset('js/service-icons.js') }}" defer></script>
    <style>
        /* Ensure dark theme is applied immediately */
        body.status-page {
            background-color: #1a1a1a !important;
            color: #ffffff !important;
        }
    </style>'''

    # Write the custom content to a temp file
    with open("/tmp/custom-head.txt", "w") as f:
        f.write(custom_head_content)

    # Copy to container and inject
    success, _, stderr = run_docker_command([
        "docker", "cp", "/tmp/custom-head.txt", "turdparty_cachet:/tmp/custom-head.txt"
    ])

    if not success:
        print(f"❌ Failed to copy custom head content: {stderr}")
        return False

    # Use sed to insert before </head>
    success, _, stderr = run_docker_command([
        "docker", "exec", "turdparty_cachet",
        "bash", "-c", "sed -i '/<\\/head>/e cat /tmp/custom-head.txt' /var/www/html/resources/views/layout/master.blade.php"
    ])

    if success:
        print("✅ Master layout updated successfully")
        return True
    else:
        print(f"❌ Failed to update master layout: {stderr}")
        return False

def clear_cache():
    """Clear Cachet cache to ensure changes take effect."""
    print("🧹 Clearing Cachet cache...")
    
    success, stdout, stderr = run_docker_command([
        "docker", "exec", "turdparty_cachet",
        "php", "/var/www/html/artisan", "cache:clear"
    ])
    
    if success:
        print("✅ Cache cleared successfully")
    else:
        print(f"⚠️  Could not clear cache: {stderr}")
    
    # Also clear view cache
    success, stdout, stderr = run_docker_command([
        "docker", "exec", "turdparty_cachet",
        "php", "/var/www/html/artisan", "view:clear"
    ])
    
    if success:
        print("✅ View cache cleared successfully")
    else:
        print(f"⚠️  Could not clear view cache: {stderr}")

def main():
    """Main function to customize Cachet theme."""
    print("🎨 Customizing Cachet with Dark Theme and Service Icons")
    print("=" * 60)
    
    # Check if Cachet container is running
    success, stdout, stderr = run_docker_command([
        "docker", "ps", "--filter", "name=turdparty_cachet", "--format", "{{.Names}}"
    ])
    
    if not success or "turdparty_cachet" not in stdout:
        print("❌ Cachet container is not running")
        return 1
    
    print("✅ Cachet container found")
    
    # Apply customizations
    steps = [
        ("Injecting dark theme CSS", inject_custom_css),
        ("Adding service icons", inject_service_icons),
        ("Updating master layout", update_master_layout),
        ("Clearing cache", clear_cache)
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if step_func():
            success_count += 1
        else:
            print(f"⚠️  {step_name} failed, but continuing...")
    
    print(f"\n📊 Customization Summary:")
    print(f"   ✅ Successful steps: {success_count}/{len(steps)}")
    
    if success_count >= 3:  # At least CSS, icons, and layout
        print("🎉 Cachet customization completed successfully!")
        print("🌐 Refresh http://localhost:3501 to see the dark theme")
        return 0
    else:
        print("⚠️  Some customizations failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
