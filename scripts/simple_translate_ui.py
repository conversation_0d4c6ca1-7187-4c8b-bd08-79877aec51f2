#!/usr/bin/env python3
"""
Simple UI Translation Script

Creates proper translations for UI components using the terminology dictionary.
This is a simplified version that doesn't require external API dependencies.
"""

import os
import json
import logging
import argparse
from pathlib import Path
from typing import Dict, Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleUITranslator:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.lang_dir = self.project_root / "lang"
        self.base_language = "en_GB"
        self.terminology = self.load_terminology()
    
    def load_terminology(self) -> Dict:
        """Load terminology dictionary."""
        terminology_file = self.lang_dir / "terminology.json"
        if terminology_file.exists():
            with open(terminology_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def get_translation(self, text: str, target_lang: str) -> str:
        """Get translation using terminology dictionary."""
        # Check if we have a direct translation in terminology
        for category in self.terminology.values():
            if isinstance(category, dict):
                for term, translations in category.items():
                    if text.strip() == term:
                        if isinstance(translations, dict) and target_lang in translations:
                            return translations[target_lang]
                        elif isinstance(translations, str):
                            return translations
        
        # Apply terminology to longer texts
        result = text
        for category in self.terminology.values():
            if isinstance(category, dict):
                for term, translations in category.items():
                    if isinstance(translations, dict) and target_lang in translations:
                        if term.lower() in text.lower():
                            result = result.replace(term, translations[target_lang])
        
        # If no translation found, mark it for manual translation
        if result == text:
            return f"[NEEDS_TRANSLATION] {text}"
        
        return result
    
    def translate_ui_file(self, source_file: Path, target_file: Path, target_lang: str):
        """Translate a UI JSON file."""
        logger.info(f"Translating UI file: {source_file} -> {target_file}")
        
        if not source_file.exists():
            logger.warning(f"Source file does not exist: {source_file}")
            return
        
        with open(source_file, 'r', encoding='utf-8') as f:
            source_data = json.load(f)
        
        translated_data = {}
        
        for key, value in source_data.items():
            if isinstance(value, str):
                translation = self.get_translation(value, target_lang)
                translated_data[key] = translation
                
                if translation.startswith("[NEEDS_TRANSLATION]"):
                    logger.warning(f"Manual translation needed for {key}: {value}")
                else:
                    logger.debug(f"Translated {key}: {value} -> {translation}")
            else:
                translated_data[key] = value
        
        # Ensure target directory exists
        target_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Save translated file
        with open(target_file, 'w', encoding='utf-8') as f:
            json.dump(translated_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved translated file: {target_file}")
    
    def translate_language(self, target_lang: str):
        """Translate all UI files for a specific language."""
        logger.info(f"Translating UI for language: {target_lang}")
        
        base_ui_dir = self.lang_dir / self.base_language / "ui"
        if not base_ui_dir.exists():
            logger.error(f"Base UI directory not found: {base_ui_dir}")
            return
        
        target_ui_dir = self.lang_dir / target_lang / "ui"
        
        # Find all JSON files in base UI directory
        for ui_file in base_ui_dir.rglob("*.json"):
            relative_path = ui_file.relative_to(base_ui_dir)
            target_file = target_ui_dir / relative_path
            
            self.translate_ui_file(ui_file, target_file, target_lang)
    
    def translate_all_languages(self):
        """Translate UI for all supported languages."""
        logger.info("Translating UI for all languages...")
        
        # Get all language directories
        for lang_dir in self.lang_dir.iterdir():
            if lang_dir.is_dir() and lang_dir.name != self.base_language and lang_dir.name != "temp":
                self.translate_language(lang_dir.name)
    
    def update_german_translations(self):
        """Specifically update German translations with better quality."""
        logger.info("Updating German translations with improved quality...")
        
        # Custom German translations for better quality
        german_translations = {
            "app.title": "TurdParty Anwendung",
            "app.description": "Eine umfassende Anwendung für Datei-Upload, VM-Verwaltung und Injektionsoperationen",
            "app.welcome": "Willkommen bei TurdParty, {name}!",
            "app.items": "{count, plural, one {# Element} other {# Elemente}}",
            "app.version": "Version {version}",
            "app.copyright": "© 2025 TurdParty Projekt",
            "nav.home": "Startseite",
            "nav.files": "Dateien",
            "nav.vms": "Virtuelle Maschinen",
            "nav.injections": "Injektionen",
            "nav.status": "VM-Status",
            "nav.docs": "Dokumentation",
            "nav.settings": "Einstellungen",
            "nav.help": "Hilfe",
            "nav.about": "Über",
            "button.create": "Erstellen",
            "button.edit": "Bearbeiten",
            "button.delete": "Löschen",
            "button.cancel": "Abbrechen",
            "button.save": "Speichern",
            "button.refresh": "Aktualisieren",
            "button.upload": "Hochladen",
            "button.download": "Herunterladen",
            "button.select": "Auswählen",
            "button.start": "Starten",
            "button.stop": "Stoppen",
            "button.restart": "Neustarten",
            "button.pause": "Pausieren",
            "button.resume": "Fortsetzen",
            "button.configure": "Konfigurieren",
            "button.view": "Anzeigen",
            "button.close": "Schließen",
            "button.submit": "Absenden",
            "button.reset": "Zurücksetzen",
            "button.apply": "Anwenden",
            "button.confirm": "Bestätigen",
            "button.retry": "Wiederholen",
            "status.loading": "Wird geladen...",
            "status.success": "Erfolg",
            "status.error": "Fehler",
            "status.warning": "Warnung",
            "status.info": "Information",
            "status.pending": "Ausstehend",
            "status.completed": "Abgeschlossen",
            "status.failed": "Fehlgeschlagen",
            "status.cancelled": "Abgebrochen",
            "status.running": "Läuft",
            "status.stopped": "Gestoppt",
            "status.healthy": "Gesund",
            "status.unhealthy": "Ungesund",
            "status.unknown": "Unbekannt",
            "form.required": "Erforderlich",
            "form.optional": "Optional",
            "form.invalid": "Ungültige Eingabe",
            "form.save_success": "Erfolgreich gespeichert",
            "form.save_error": "Speichern fehlgeschlagen",
            "form.validation_error": "Bitte überprüfen Sie Ihre Eingabe",
            "table.no_data": "Keine Daten verfügbar",
            "table.loading": "Daten werden geladen...",
            "table.rows_per_page": "Zeilen pro Seite",
            "table.page_of": "Seite {current} von {total}",
            "table.sort_by": "Sortieren nach {column}",
            "dialog.confirm_delete": "Sind Sie sicher, dass Sie dieses Element löschen möchten?",
            "dialog.unsaved_changes": "Sie haben ungespeicherte Änderungen. Möchten Sie fortfahren?",
            "dialog.operation_success": "Operation erfolgreich abgeschlossen",
            "dialog.operation_failed": "Operation fehlgeschlagen: {error}",
            "time.just_now": "Gerade eben",
            "time.minutes_ago": "{count, plural, one {vor # Minute} other {vor # Minuten}}",
            "time.hours_ago": "{count, plural, one {vor # Stunde} other {vor # Stunden}}",
            "time.days_ago": "{count, plural, one {vor # Tag} other {vor # Tagen}}",
            "file.size_bytes": "{size} Bytes",
            "file.size_kb": "{size} KB",
            "file.size_mb": "{size} MB",
            "file.size_gb": "{size} GB",
            "error.network": "Netzwerkfehler. Bitte überprüfen Sie Ihre Verbindung.",
            "error.server": "Serverfehler. Bitte versuchen Sie es später erneut.",
            "error.unauthorized": "Sie sind nicht berechtigt, diese Aktion auszuführen.",
            "error.not_found": "Die angeforderte Ressource wurde nicht gefunden.",
            "error.validation": "Bitte überprüfen Sie Ihre Eingabe und versuchen Sie es erneut.",
            "error.unknown": "Ein unerwarteter Fehler ist aufgetreten."
        }
        
        # Update German common.json
        german_common_file = self.lang_dir / "de" / "ui" / "components" / "common.json"
        german_common_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(german_common_file, 'w', encoding='utf-8') as f:
            json.dump(german_translations, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Updated German common translations: {german_common_file}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Simple UI Translation Script")
    parser.add_argument("--language", help="Target language code")
    parser.add_argument("--update-german", action="store_true", help="Update German translations with better quality")
    
    args = parser.parse_args()
    
    translator = SimpleUITranslator()
    
    if args.update_german:
        translator.update_german_translations()
    elif args.language:
        translator.translate_language(args.language)
    else:
        translator.translate_all_languages()
    
    logger.info("Translation completed!")

if __name__ == "__main__":
    main()
