#!/usr/bin/env python3
"""
Update Cachet component statuses by directly updating the database.
This script reads the actual service health and updates Cachet accordingly.
"""

import subprocess
import sys
import json
import requests
from datetime import datetime
from typing import Dict, Any

# Import the service manager for health checking
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from cachet_service_manager import (
    SERVICES_CONFIG, 
    get_api_health, 
    determine_service_status,
    CACHET_STATUS
)

def update_component_in_database(component_id: int, status: str) -> bool:
    """Update component status directly in the Cachet database."""
    status_code = CACHET_STATUS[status]
    
    sql_command = f"""
    UPDATE chq_components 
    SET status = {status_code}, updated_at = NOW() 
    WHERE id = {component_id};
    """
    
    try:
        result = subprocess.run([
            "docker", "exec", "turdparty_postgres_cachet", 
            "psql", "-U", "postgres", "-d", "cachet", 
            "-c", sql_command
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ Updated component {component_id} to {status}")
            return True
        else:
            print(f"❌ Failed to update component {component_id}: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error updating component {component_id}: {e}")
        return False

def create_incident_if_needed(component_id: int, component_name: str, status: str) -> bool:
    """Create an incident if a service is down."""
    if status in ["major_outage", "partial_outage"]:
        incident_name = f"{component_name} - Service Issue"
        
        if status == "major_outage":
            incident_status = 4  # Identified
            message = f"{component_name} is currently experiencing a major outage."
        else:
            incident_status = 2  # Investigating  
            message = f"{component_name} is experiencing performance issues."
        
        # Check if incident already exists
        check_sql = f"""
        SELECT id FROM chq_incidents 
        WHERE name = '{incident_name}' 
        AND status IN (1, 2, 3, 4)
        ORDER BY created_at DESC LIMIT 1;
        """
        
        try:
            result = subprocess.run([
                "docker", "exec", "turdparty_postgres_cachet",
                "psql", "-U", "postgres", "-d", "cachet",
                "-t", "-c", check_sql
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                print(f"📋 Incident already exists for {component_name}")
                return True
            
            # Create new incident
            create_sql = f"""
            INSERT INTO chq_incidents (name, message, status, visible, component_id, created_at, updated_at)
            VALUES ('{incident_name}', '{message}', {incident_status}, true, {component_id}, NOW(), NOW());
            """
            
            result = subprocess.run([
                "docker", "exec", "turdparty_postgres_cachet",
                "psql", "-U", "postgres", "-d", "cachet",
                "-c", create_sql
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"📋 Created incident for {component_name}")
                return True
            else:
                print(f"❌ Failed to create incident for {component_name}: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error creating incident for {component_name}: {e}")
            return False
    
    return True

def resolve_incidents_if_operational(component_id: int, component_name: str, status: str) -> bool:
    """Resolve incidents if service is operational."""
    if status == "operational":
        # Find open incidents for this component
        resolve_sql = f"""
        UPDATE chq_incidents 
        SET status = 4, updated_at = NOW()
        WHERE component_id = {component_id} 
        AND status IN (1, 2, 3);
        """
        
        try:
            result = subprocess.run([
                "docker", "exec", "turdparty_postgres_cachet",
                "psql", "-U", "postgres", "-d", "cachet",
                "-c", resolve_sql
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"✅ Resolved incidents for {component_name}")
                return True
            else:
                print(f"❌ Failed to resolve incidents for {component_name}: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error resolving incidents for {component_name}: {e}")
            return False
    
    return True

def main():
    """Main function to update all service statuses in Cachet."""
    print("🔄 Updating Cachet Service Statuses")
    print("=" * 40)
    
    # Get current health status
    api_health = get_api_health()
    
    updates_successful = 0
    total_updates = 0
    
    # Process each service group
    for group_key, group_config in SERVICES_CONFIG.items():
        group_name = group_config["group_name"]
        print(f"\n{group_name}")
        print("-" * len(group_name))
        
        for service_key, service_config in group_config["services"].items():
            total_updates += 1
            service_name = service_config["name"]
            component_id = service_config["id"]
            
            # Determine current status
            status = determine_service_status(service_config, api_health)
            
            # Status icon
            if status == "operational":
                status_icon = "✅"
            elif status == "performance_issues":
                status_icon = "⚠️"
            elif status == "partial_outage":
                status_icon = "🔶"
            else:  # major_outage
                status_icon = "❌"
            
            print(f"  {status_icon} {service_name}: {status}")
            
            # Update component status
            if update_component_in_database(component_id, status):
                updates_successful += 1
                
                # Handle incidents
                if status in ["major_outage", "partial_outage"]:
                    create_incident_if_needed(component_id, service_name, status)
                else:
                    resolve_incidents_if_operational(component_id, service_name, status)
    
    print(f"\n📊 Update Summary:")
    print(f"   ✅ Successful: {updates_successful}/{total_updates}")
    print(f"   ❌ Failed: {total_updates - updates_successful}/{total_updates}")
    
    if updates_successful == total_updates:
        print("🎉 All statuses updated successfully!")
        print(f"🌐 View status page: http://localhost:3501")
        return 0
    else:
        print("⚠️  Some updates failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
