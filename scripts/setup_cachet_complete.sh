#!/bin/bash

# Complete Cachet Setup Script for TurdParty
# This script sets up <PERSON>ache<PERSON> with dark theme, service icons, and real-time status monitoring

set -e

echo "🎨 Complete Cachet Setup for TurdParty"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ] && [ ! -d ".dockerwrapper" ]; then
    print_error "Please run this script from the turdparty project root directory"
    exit 1
fi

# Function to check if a container is running
is_container_running() {
    docker ps --format "{{.Names}}" | grep -q "^$1$"
}

# Step 1: Start all services
print_step "Starting all TurdParty services..."
if [ -f "scripts/start_all_services_and_update_cachet.sh" ]; then
    print_status "Using comprehensive startup script..."
    bash scripts/start_all_services_and_update_cachet.sh
else
    print_status "Starting services manually..."
    if [ -f ".dockerwrapper/start-turdparty-dynamic.sh" ]; then
        cd .dockerwrapper
        bash start-turdparty-dynamic.sh
        cd ..
    else
        print_warning "No startup script found, using docker compose..."
        docker compose up -d
    fi
fi

# Wait for Cachet to be ready
print_step "Waiting for Cachet to be ready..."
max_wait=60
wait_time=0

while [ $wait_time -lt $max_wait ]; do
    if is_container_running "turdparty_cachet"; then
        if curl -s http://localhost:3501/api/v1/ping >/dev/null 2>&1; then
            print_success "Cachet is ready!"
            break
        fi
    fi
    
    sleep 2
    wait_time=$((wait_time + 2))
    echo -n "."
done

if [ $wait_time -ge $max_wait ]; then
    print_error "Cachet did not start within $max_wait seconds"
    exit 1
fi

# Step 2: Apply dark theme and icons
print_step "Applying dark theme and service icons..."

if [ -f "scripts/customize_cachet_theme.py" ]; then
    print_status "Applying custom theme..."
    if command -v nix-shell >/dev/null 2>&1; then
        nix-shell -p python3 --run "python3 scripts/customize_cachet_theme.py" || print_warning "Theme application had some issues"
    else
        python3 scripts/customize_cachet_theme.py || print_warning "Theme application had some issues"
    fi
    
    # Manual injection of CSS and JS links (backup method)
    print_status "Ensuring theme files are linked..."
    docker exec turdparty_cachet sed -i '/<\/head>/i\    <link rel="stylesheet" href="{{ asset('"'"'css/dark-theme.css'"'"') }}">' /var/www/html/resources/views/layout/master.blade.php 2>/dev/null || true
    docker exec turdparty_cachet sed -i '/<\/head>/i\    <script src="{{ asset('"'"'js/service-icons.js'"'"') }}" defer></script>' /var/www/html/resources/views/layout/master.blade.php 2>/dev/null || true
else
    print_warning "Theme customization script not found"
fi

# Step 3: Add emojis to service names
print_step "Adding emojis to service names..."

if [ -f "scripts/add_service_emojis.py" ]; then
    print_status "Adding emojis to services..."
    if command -v nix-shell >/dev/null 2>&1; then
        nix-shell -p python3 --run "python3 scripts/add_service_emojis.py" || print_warning "Emoji addition had some issues"
    else
        python3 scripts/add_service_emojis.py || print_warning "Emoji addition had some issues"
    fi
else
    print_warning "Emoji script not found"
fi

# Step 4: Update service statuses
print_step "Updating service statuses in Cachet..."

if [ -f "scripts/update_cachet_status.py" ]; then
    print_status "Updating service statuses..."
    if command -v nix-shell >/dev/null 2>&1; then
        nix-shell -p python3 -p python3Packages.requests --run "python3 scripts/update_cachet_status.py" || print_warning "Status update had some issues"
    else
        python3 scripts/update_cachet_status.py || print_warning "Status update had some issues"
    fi
else
    print_warning "Status update script not found"
fi

# Step 5: Clear caches
print_step "Clearing Cachet caches..."
docker exec turdparty_cachet php /var/www/html/artisan cache:clear >/dev/null 2>&1 || print_warning "Could not clear cache"
docker exec turdparty_cachet php /var/www/html/artisan view:clear >/dev/null 2>&1 || print_warning "Could not clear view cache"

# Step 6: Final verification
print_step "Performing final verification..."

# Check services
services_ok=0
total_services=0

services=(
    "turdparty_api:3050"
    "turdparty_frontend:3100"
    "turdparty_cachet:3501"
    "turdparty_celery_flower:3450"
    "turdparty_minio:3301"
)

for service_port in "${services[@]}"; do
    service=$(echo $service_port | cut -d: -f1)
    port=$(echo $service_port | cut -d: -f2)
    total_services=$((total_services + 1))
    
    if is_container_running "$service"; then
        if curl -s "http://localhost:$port" >/dev/null 2>&1; then
            print_success "$service is running and accessible"
            services_ok=$((services_ok + 1))
        else
            print_warning "$service is running but not accessible on port $port"
        fi
    else
        print_error "$service is not running"
    fi
done

# Final summary
echo ""
print_success "🎉 Cachet Setup Complete!"
echo ""
echo "📊 Service Status: $services_ok/$total_services services accessible"
echo ""
echo "🌐 Access URLs:"
echo "   • 🎨 Cachet Status Page:  http://localhost:3501"
echo "   • 🚀 API:                http://localhost:3050"
echo "   • 🌐 Frontend:           http://localhost:3100"
echo "   • 🌸 Celery Flower:      http://localhost:3450"
echo "   • 📦 MinIO Console:      http://localhost:3301"
echo ""
echo "✨ Features Enabled:"
echo "   • 🌙 Dark mode theme"
echo "   • 🎯 Service icons"
echo "   • 😊 Emoji service names"
echo "   • 📊 Real-time status monitoring"
echo "   • 📱 Responsive design"
echo ""
echo "🔄 To update statuses manually:"
echo "   nix-shell -p python3 -p python3Packages.requests --run 'python3 scripts/update_cachet_status.py'"
echo ""

if [ $services_ok -eq $total_services ]; then
    print_success "All services are running perfectly! 🚀"
    exit 0
else
    print_warning "Some services may need attention"
    exit 1
fi
