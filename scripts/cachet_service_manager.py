#!/usr/bin/env python3
"""
Cachet Service Manager for TurdParty
Manages service registration and status updates in Cachet status page.
"""

import requests
import json
import sys
import time
import subprocess
from typing import Dict, Any, Optional, List
from datetime import datetime

# Configuration
CACHET_BASE_URL = "http://localhost:3501"
API_BASE_URL = "http://localhost:3050"

# Cachet status codes
CACHET_STATUS = {
    "operational": 1,
    "performance_issues": 2,
    "partial_outage": 3,
    "major_outage": 4
}

# Service definitions with their groups and details
SERVICES_CONFIG = {
    "frontend_services": {
        "group_name": "🌐 Frontend Services",
        "group_id": 1,
        "order": 1,
        "services": {
            "api": {
                "id": 1,
                "name": "API",
                "description": "Main API service for TurdParty",
                "link": "http://turdparty_api:8000/api/v1/health",
                "container": "turdparty_api",
                "health_check": "api"
            },
            "frontend": {
                "id": 2,
                "name": "Frontend",
                "description": "Frontend web interface",
                "link": "http://turdparty_frontend:80",
                "container": "turdparty_frontend",
                "health_check": "http"
            }
        }
    },
    "worker_services": {
        "group_name": "⚙️ Worker Services",
        "group_id": 2,
        "order": 2,
        "services": {
            "redis": {
                "id": 3,
                "name": "Redis",
                "description": "Cache and message broker",
                "container": "turdparty_redis",
                "health_check": "container"
            },
            "celery_default": {
                "id": 4,
                "name": "Celery Default",
                "description": "Default task worker",
                "container": "turdparty_celery_default",
                "health_check": "container"
            },
            "celery_file_ops": {
                "id": 5,
                "name": "Celery File Ops",
                "description": "File operations task worker",
                "container": "turdparty_celery_file_ops",
                "health_check": "container"
            },
            "celery_vm_ops": {
                "id": 6,
                "name": "Celery VM Ops",
                "description": "VM operations task worker",
                "container": "turdparty_celery_vm_ops",
                "health_check": "container"
            },
            "celery_flower": {
                "id": 7,
                "name": "Celery Flower",
                "description": "Celery monitoring tool",
                "link": "http://localhost:3450",
                "container": "turdparty_celery_flower",
                "health_check": "http"
            }
        }
    },
    "backend_services": {
        "group_name": "🗄️ Backend Services",
        "group_id": 3,
        "order": 3,
        "services": {
            "postgres": {
                "id": 8,
                "name": "PostgreSQL",
                "description": "Database",
                "container": "turdparty_postgres",
                "health_check": "container"
            },
            "minio": {
                "id": 9,
                "name": "MinIO",
                "description": "Object storage",
                "link": "http://turdparty_minio:9000/minio/health/live",
                "container": "turdparty_minio",
                "health_check": "api"
            }
        }
    }
}

def get_api_health() -> Dict[str, Any]:
    """Get health status from the TurdParty API."""
    try:
        response = requests.get(f"{API_BASE_URL}/api/v1/system/health", timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"⚠️  API health check failed: {response.status_code}")
            return {"status": "error", "services": {}}
    except Exception as e:
        print(f"❌ Error getting API health: {e}")
        return {"status": "error", "services": {}}

def check_container_health(container_name: str) -> str:
    """Check Docker container health status."""
    try:
        # First try to get health status
        result = subprocess.run(
            ["docker", "inspect", container_name, "--format", "{{.State.Health.Status}}"],
            capture_output=True, text=True, timeout=5
        )
        
        if result.returncode == 0 and result.stdout.strip():
            health = result.stdout.strip()
            if health == "healthy":
                return "operational"
            elif health == "unhealthy":
                return "partial_outage"
            else:
                return "performance_issues"
        
        # If no health check, check if container is running
        result = subprocess.run(
            ["docker", "inspect", container_name, "--format", "{{.State.Status}}"],
            capture_output=True, text=True, timeout=5
        )
        
        if result.returncode == 0:
            status = result.stdout.strip()
            return "operational" if status == "running" else "major_outage"
        else:
            return "major_outage"
            
    except Exception as e:
        print(f"❌ Error checking container {container_name}: {e}")
        return "major_outage"

def check_http_endpoint(url: str) -> str:
    """Check HTTP endpoint health."""
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            return "operational"
        elif response.status_code < 500:
            return "performance_issues"
        else:
            return "partial_outage"
    except Exception:
        return "major_outage"

def determine_service_status(service_config: Dict[str, Any], api_health: Dict[str, Any]) -> str:
    """Determine the status of a service based on various health checks."""
    service_name = service_config.get("name", "").lower()
    health_check_type = service_config.get("health_check", "container")
    
    # Check API health first if available
    api_services = api_health.get("services", {})
    
    if health_check_type == "api":
        if "api" in service_name:
            api_status = api_services.get("api", "error")
        elif "minio" in service_name:
            api_status = api_services.get("minio", "error")
        else:
            api_status = "unknown"
        
        if api_status == "ok":
            status = "operational"
        elif api_status == "error":
            status = "partial_outage"
        else:
            status = "performance_issues"
    
    elif health_check_type == "http":
        link = service_config.get("link")
        if link:
            status = check_http_endpoint(link)
        else:
            status = "operational"  # Default if no link
    
    else:  # container health check
        container = service_config.get("container")
        if container:
            status = check_container_health(container)
        else:
            status = "operational"  # Default
    
    # Override with container status if it's worse
    container = service_config.get("container")
    if container:
        container_status = check_container_health(container)
        if container_status == "major_outage":
            status = "major_outage"
        elif container_status == "partial_outage" and status == "operational":
            status = "partial_outage"
    
    return status

def update_cachet_component_status(component_id: int, status: str) -> bool:
    """Update component status in Cachet (read-only for now)."""
    # For now, we'll just log what we would update
    # In the future, this could use API authentication to actually update
    status_code = CACHET_STATUS[status]
    print(f"📊 Component {component_id}: {status} (code: {status_code})")
    return True

def main():
    """Main function to check and update all service statuses."""
    print("🔍 TurdParty Service Status Monitor")
    print("=" * 50)
    
    # Get API health data
    api_health = get_api_health()
    overall_status = api_health.get("status", "unknown")
    
    print(f"🌐 Overall API Status: {overall_status}")
    print()
    
    # Check each service group
    total_services = 0
    operational_services = 0
    
    for group_key, group_config in SERVICES_CONFIG.items():
        group_name = group_config["group_name"]
        print(f"{group_name}")
        print("-" * len(group_name))
        
        for service_key, service_config in group_config["services"].items():
            total_services += 1
            service_name = service_config["name"]
            
            # Determine service status
            status = determine_service_status(service_config, api_health)
            
            if status == "operational":
                operational_services += 1
                status_icon = "✅"
            elif status == "performance_issues":
                status_icon = "⚠️"
            elif status == "partial_outage":
                status_icon = "🔶"
            else:  # major_outage
                status_icon = "❌"
            
            print(f"  {status_icon} {service_name}: {status}")
            
            # Update Cachet (currently just logs)
            component_id = service_config["id"]
            update_cachet_component_status(component_id, status)
        
        print()
    
    # Summary
    print("📈 Summary")
    print("-" * 10)
    print(f"✅ Operational: {operational_services}/{total_services}")
    print(f"⚠️  Issues: {total_services - operational_services}/{total_services}")
    
    if operational_services == total_services:
        print("🎉 All services are operational!")
        return 0
    else:
        print("⚠️  Some services have issues")
        return 1

if __name__ == "__main__":
    sys.exit(main())
