#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to verify that the transition is complete and everything works correctly.
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple

def check_directory_structure() -> bool:
    """Check if the directory structure is correct."""
    # Define the expected directory structure
    expected_directories = [
        "api/v1/routes",
        "config",
        "data",
        "db/migrations",
        "db/models",
        "db/repositories",
        "db/schemas",
        "db/services",
        "docker",
        "docs/images",
        "logs",
        "nix",
        "scripts",
        "static/css",
        "static/html",
        "static/js",
        "tests/e2e",
        "tests/fixtures",
        "tests/integration",
        "tests/scripts",
        "tests/unit",
        "utils",
        "vagrant",
    ]

    # Check if the directories exist
    missing_directories = []
    for directory in expected_directories:
        if not os.path.isdir(directory):
            missing_directories.append(directory)

    if missing_directories:
        print("❌ The following directories are missing:")
        for directory in missing_directories:
            print(f"  - {directory}")
        return False
    else:
        print("✅ All expected directories exist")
        return True

def check_key_files() -> bool:
    """Check if key files exist in the expected locations."""
    # Define the expected key files
    expected_files = [
        "main.py",
        "api/v1/application.py",
        "api/v1/routes/__init__.py",
        "db/models/__init__.py",
        "db/repositories/__init__.py",
        "db/schemas/__init__.py",
        "db/services/__init__.py",
        "utils/__init__.py",
        "utils/config.py",
        "utils/logging_config.py",
        "utils/security.py",
        "utils/auth.py",
        "utils/exceptions.py",
        "utils/dependencies.py",
        "tests/__init__.py",
        "tests/unit/__init__.py",
        "tests/integration/__init__.py",
    ]

    # Check if the files exist
    missing_files = []
    for file in expected_files:
        if not os.path.isfile(file):
            missing_files.append(file)

    if missing_files:
        print("❌ The following key files are missing:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    else:
        print("✅ All expected key files exist")
        return True

def check_original_files() -> bool:
    """Check if original files have been removed."""
    # Define the original directories to check
    original_dirs = [
        "api/core",
        "api/db",
        "api/schemas",
        "api/services",
        "api/routes",
        "api/static",
        "api/middleware",
        "api/tests",
    ]

    # Define the original files to check
    original_files = [
        "api/application.py",
    ]

    # Check if the directories exist
    existing_dirs = []
    for dir_path in original_dirs:
        if os.path.isdir(dir_path):
            existing_dirs.append(dir_path)

    # Check if the files exist
    existing_files = []
    for file_path in original_files:
        if os.path.isfile(file_path):
            existing_files.append(file_path)

    if existing_dirs or existing_files:
        if existing_dirs:
            print("❌ The following original directories still exist:")
            for directory in existing_dirs:
                print(f"  - {directory}")
        if existing_files:
            print("❌ The following original files still exist:")
            for file in existing_files:
                print(f"  - {file}")
        return False
    else:
        print("✅ All original files have been removed")
        return True

def check_symbolic_links() -> bool:
    """Check if symbolic links have been removed."""
    # Define the symbolic links to check
    symlinks = [
        "api/core/utils",
        "api/schemas/schemas",
        "api/services/services",
        "api/db/repositories/repositories",
        "api/static/static",
    ]

    # Check if the symbolic links exist
    existing_symlinks = []
    for symlink in symlinks:
        if os.path.islink(symlink):
            existing_symlinks.append(symlink)

    if existing_symlinks:
        print("❌ The following symbolic links still exist:")
        for symlink in existing_symlinks:
            print(f"  - {symlink}")
        return False
    else:
        print("✅ All symbolic links have been removed")
        return True

def check_docker_container_names() -> bool:
    """Check if Docker container names use the turdparty_ prefix."""
    print("Checking Docker container names...")

    # Find all Docker Compose files
    docker_compose_files = list(Path(".").glob("**/*docker-compose*.yml"))
    docker_compose_files.extend(Path(".").glob("**/*docker-compose*.yaml"))

    # Check each file for container names
    incorrect_names = []
    for file_path in docker_compose_files:
        with open(file_path, 'r') as f:
            content = f.read()

        # Check for dockerwrapper_ or dockerwrapper- prefixes
        if "container_name: dockerwrapper" in content or "name: dockerwrapper" in content:
            incorrect_names.append(str(file_path))

    if incorrect_names:
        print("❌ The following Docker Compose files still use dockerwrapper prefix:")
        for file_path in incorrect_names:
            print(f"  - {file_path}")
        return False
    else:
        print("✅ All Docker container names use turdparty_ prefix")
        return True

def check_application_runs() -> bool:
    """Check if the application runs correctly."""
    print("Checking if the application runs correctly...")

    # Start the application
    app_process = subprocess.Popen(
        ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )

    # Wait for the application to start
    import time
    time.sleep(5)

    # Test the application
    test_cmd = ["curl", "-s", "http://localhost:8000/api/v1/health"]
    result = subprocess.run(test_cmd, capture_output=True, text=True)

    # Stop the application
    app_process.terminate()
    app_process.wait()

    # Check the result
    if "status" in result.stdout and "ok" in result.stdout:
        print("✅ Application runs correctly")
        return True
    else:
        print("❌ Application fails to run correctly")
        print(f"Output: {result.stdout}")
        print(f"Error: {result.stderr}")
        return False

def verify_transition() -> Dict[str, bool]:
    """Verify that the transition is complete and everything works correctly."""
    # Get the project root directory
    project_root = Path(__file__).parent.parent.absolute()

    # Change to the project root directory
    os.chdir(project_root)

    # Check directory structure
    directory_structure_ok = check_directory_structure()

    # Check key files
    key_files_ok = check_key_files()

    # Check original files
    original_files_ok = check_original_files()

    # Check symbolic links
    symbolic_links_ok = check_symbolic_links()

    # Check Docker container names
    docker_container_names_ok = check_docker_container_names()

    # Check application runs
    application_runs_ok = check_application_runs()

    return {
        "directory_structure": directory_structure_ok,
        "key_files": key_files_ok,
        "original_files": original_files_ok,
        "symbolic_links": symbolic_links_ok,
        "docker_container_names": docker_container_names_ok,
        "application_runs": application_runs_ok
    }

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Verify that the transition is complete and everything works correctly.')
    args = parser.parse_args()

    results = verify_transition()

    # Print summary
    print("\nVerification Results:")
    for check, result in results.items():
        status = "✅ Passed" if result else "❌ Failed"
        print(f"{check.replace('_', ' ').title()}: {status}")

    # Return success if all checks pass
    return 0 if all(results.values()) else 1

if __name__ == "__main__":
    sys.exit(main())
