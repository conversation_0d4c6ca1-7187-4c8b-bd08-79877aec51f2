feat: Implement Celery integration and Docker namespacing

This commit adds Celery integration for asynchronous task processing and
updates Docker container naming to use the turdparty_ prefix for better
namespacing.

Key changes:
- Added Celery workers for file operations, VM lifecycle, VM injection, and monitoring
- Implemented task status tracking and monitoring
- Updated all Docker containers to use turdparty_ prefix
- Created dedicated Docker networks for each environment
- Added comprehensive documentation for Celery and Docker namespacing
- Fixed integration tests to work with the new container naming
- Removed transition documentation (no longer needed)

See CHANGELOG.md for more details.
