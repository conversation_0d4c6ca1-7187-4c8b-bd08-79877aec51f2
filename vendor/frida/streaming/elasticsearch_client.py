"""
Elasticsearch Client for Frida Analysis

Handles real-time streaming of analysis events to Elasticsearch with:
- Index management and rotation
- Bulk operations for performance
- Error handling and retry logic
- Schema validation and mapping
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from elasticsearch import AsyncElasticsearch
from elasticsearch.helpers import async_bulk
import uuid

logger = logging.getLogger(__name__)


class ElasticsearchStreamer:
    """Streams Frida analysis events to Elasticsearch."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Elasticsearch streamer."""
        self.config = config
        self.es_config = config.get('storage', {}).get('elasticsearch', {})
        
        # Initialize Elasticsearch client
        self.client = AsyncElasticsearch(
            hosts=self.es_config.get('hosts', ['localhost:9200']),
            timeout=30,
            max_retries=3,
            retry_on_timeout=True
        )
        
        # Index configuration
        self.index_prefix = self.es_config.get('index_prefix', 'frida-runtime')
        self.index_pattern = self.es_config.get('index_pattern', 'frida-runtime-{YYYY.MM.DD}')
        self.shards = self.es_config.get('shards', 3)
        self.replicas = self.es_config.get('replicas', 1)
        
        # Bulk operations
        self.bulk_size = self.es_config.get('bulk_size', 100)
        self.bulk_timeout = self.es_config.get('bulk_timeout', 5)
        self.pending_events: List[Dict[str, Any]] = []
        self.last_bulk_time = datetime.utcnow()
        
        # Background task for bulk operations
        self._bulk_task: Optional[asyncio.Task] = None
        self._shutdown = False
        
        logger.info("Elasticsearch streamer initialized")
    
    async def start(self) -> None:
        """Start the streamer and background tasks."""
        try:
            # Verify connection
            await self.client.cluster.health()
            
            # Setup index templates
            await self._setup_index_template()
            
            # Start bulk processing task
            self._bulk_task = asyncio.create_task(self._bulk_processor())
            
            logger.info("Elasticsearch streamer started")
            
        except Exception as e:
            logger.error(f"Failed to start Elasticsearch streamer: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the streamer and flush pending events."""
        try:
            self._shutdown = True
            
            # Cancel bulk task
            if self._bulk_task:
                self._bulk_task.cancel()
                try:
                    await self._bulk_task
                except asyncio.CancelledError:
                    pass
            
            # Flush remaining events
            if self.pending_events:
                await self._flush_bulk_events()
            
            # Close client
            await self.client.close()
            
            logger.info("Elasticsearch streamer stopped")
            
        except Exception as e:
            logger.error(f"Error stopping Elasticsearch streamer: {e}")
    
    async def stream_event(self, event: Dict[str, Any]) -> None:
        """Stream a single event to Elasticsearch."""
        try:
            # Add metadata
            event['@timestamp'] = datetime.utcnow().isoformat()
            event['event_id'] = str(uuid.uuid4())
            
            # Add to bulk queue
            self.pending_events.append(event)
            
            # Check if we should flush immediately
            if len(self.pending_events) >= self.bulk_size:
                await self._flush_bulk_events()
                
        except Exception as e:
            logger.error(f"Failed to stream event: {e}")
    
    async def stream_events(self, events: List[Dict[str, Any]]) -> None:
        """Stream multiple events to Elasticsearch."""
        try:
            # Add metadata to all events
            timestamp = datetime.utcnow().isoformat()
            for event in events:
                event['@timestamp'] = timestamp
                event['event_id'] = str(uuid.uuid4())
            
            # Add to bulk queue
            self.pending_events.extend(events)
            
            # Check if we should flush
            if len(self.pending_events) >= self.bulk_size:
                await self._flush_bulk_events()
                
        except Exception as e:
            logger.error(f"Failed to stream events: {e}")
    
    async def _bulk_processor(self) -> None:
        """Background task for processing bulk operations."""
        while not self._shutdown:
            try:
                await asyncio.sleep(1)  # Check every second
                
                # Check if we should flush based on time
                time_since_last_bulk = (datetime.utcnow() - self.last_bulk_time).total_seconds()
                
                if (self.pending_events and 
                    (time_since_last_bulk >= self.bulk_timeout or 
                     len(self.pending_events) >= self.bulk_size)):
                    await self._flush_bulk_events()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in bulk processor: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _flush_bulk_events(self) -> None:
        """Flush pending events to Elasticsearch."""
        if not self.pending_events:
            return
        
        try:
            # Prepare bulk operations
            actions = []
            current_date = datetime.utcnow().strftime('%Y.%m.%d')
            index_name = f"{self.index_prefix}-{current_date}"
            
            for event in self.pending_events:
                action = {
                    '_index': index_name,
                    '_source': event
                }
                actions.append(action)
            
            # Execute bulk operation
            success_count, failed_items = await async_bulk(
                self.client,
                actions,
                chunk_size=self.bulk_size,
                request_timeout=30
            )
            
            # Log results
            if failed_items:
                logger.warning(f"Bulk operation had {len(failed_items)} failures")
                for item in failed_items:
                    logger.error(f"Failed to index: {item}")
            
            logger.debug(f"Successfully indexed {success_count} events to {index_name}")
            
            # Clear pending events
            self.pending_events.clear()
            self.last_bulk_time = datetime.utcnow()
            
        except Exception as e:
            logger.error(f"Failed to flush bulk events: {e}")
            # Keep events for retry
    
    async def _setup_index_template(self) -> None:
        """Setup Elasticsearch index template for Frida events."""
        try:
            template_name = f"{self.index_prefix}-template"
            
            template = {
                "index_patterns": [f"{self.index_prefix}-*"],
                "settings": {
                    "number_of_shards": self.shards,
                    "number_of_replicas": self.replicas,
                    "index.refresh_interval": "5s",
                    "index.codec": "best_compression"
                },
                "mappings": {
                    "properties": {
                        "@timestamp": {
                            "type": "date"
                        },
                        "event_id": {
                            "type": "keyword"
                        },
                        "session_id": {
                            "type": "keyword"
                        },
                        "binary_uuid": {
                            "type": "keyword"
                        },
                        "event_type": {
                            "type": "keyword"
                        },
                        "api_category": {
                            "type": "keyword"
                        },
                        "api_function": {
                            "type": "keyword"
                        },
                        "process_id": {
                            "type": "integer"
                        },
                        "thread_id": {
                            "type": "integer"
                        },
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "param_name": {
                                    "type": "text",
                                    "fields": {
                                        "keyword": {
                                            "type": "keyword",
                                            "ignore_above": 256
                                        }
                                    }
                                },
                                "param_value": {
                                    "type": "text",
                                    "fields": {
                                        "keyword": {
                                            "type": "keyword",
                                            "ignore_above": 256
                                        }
                                    }
                                },
                                "param_type": {
                                    "type": "keyword"
                                }
                            }
                        },
                        "return_value": {
                            "type": "text",
                            "fields": {
                                "keyword": {
                                    "type": "keyword",
                                    "ignore_above": 256
                                }
                            }
                        },
                        "file_operations": {
                            "properties": {
                                "path": {
                                    "type": "keyword"
                                },
                                "operation": {
                                    "type": "keyword"
                                },
                                "size": {
                                    "type": "long"
                                },
                                "hash": {
                                    "type": "keyword"
                                }
                            }
                        },
                        "network_operations": {
                            "properties": {
                                "protocol": {
                                    "type": "keyword"
                                },
                                "destination_ip": {
                                    "type": "ip"
                                },
                                "destination_port": {
                                    "type": "integer"
                                },
                                "data_size": {
                                    "type": "long"
                                }
                            }
                        },
                        "registry_operations": {
                            "properties": {
                                "key_path": {
                                    "type": "keyword"
                                },
                                "operation": {
                                    "type": "keyword"
                                },
                                "value_name": {
                                    "type": "keyword"
                                },
                                "value_data": {
                                    "type": "text",
                                    "fields": {
                                        "keyword": {
                                            "type": "keyword",
                                            "ignore_above": 256
                                        }
                                    }
                                }
                            }
                        },
                        "tags": {
                            "type": "keyword"
                        },
                        "source_system": {
                            "type": "keyword"
                        },
                        "vm_id": {
                            "type": "keyword"
                        },
                        "analysis_profile": {
                            "type": "keyword"
                        }
                    }
                }
            }
            
            # Create or update template
            await self.client.indices.put_index_template(
                name=template_name,
                body=template
            )
            
            logger.info(f"Created Elasticsearch index template: {template_name}")
            
        except Exception as e:
            logger.error(f"Failed to setup index template: {e}")
            raise
    
    async def search_events(
        self,
        query: Dict[str, Any],
        session_id: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        size: int = 100,
        sort: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """Search for events in Elasticsearch."""
        try:
            # Build search query
            search_body = {
                "query": {
                    "bool": {
                        "must": [query] if query else [{"match_all": {}}],
                        "filter": []
                    }
                },
                "size": size,
                "sort": sort or [{"@timestamp": {"order": "desc"}}]
            }
            
            # Add session filter
            if session_id:
                search_body["query"]["bool"]["filter"].append({
                    "term": {"session_id": session_id}
                })
            
            # Add time range filter
            if start_time or end_time:
                time_filter = {"range": {"@timestamp": {}}}
                if start_time:
                    time_filter["range"]["@timestamp"]["gte"] = start_time.isoformat()
                if end_time:
                    time_filter["range"]["@timestamp"]["lte"] = end_time.isoformat()
                search_body["query"]["bool"]["filter"].append(time_filter)
            
            # Execute search
            response = await self.client.search(
                index=f"{self.index_prefix}-*",
                body=search_body
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to search events: {e}")
            raise
    
    async def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """Get summary statistics for a session."""
        try:
            # Aggregation query
            agg_body = {
                "query": {
                    "term": {"session_id": session_id}
                },
                "size": 0,
                "aggs": {
                    "event_types": {
                        "terms": {"field": "event_type", "size": 50}
                    },
                    "api_functions": {
                        "terms": {"field": "api_function", "size": 100}
                    },
                    "api_categories": {
                        "terms": {"field": "api_category", "size": 20}
                    },
                    "timeline": {
                        "date_histogram": {
                            "field": "@timestamp",
                            "fixed_interval": "30s"
                        }
                    },
                    "total_events": {
                        "value_count": {"field": "event_id"}
                    }
                }
            }
            
            response = await self.client.search(
                index=f"{self.index_prefix}-*",
                body=agg_body
            )
            
            return {
                "session_id": session_id,
                "total_events": response["aggregations"]["total_events"]["value"],
                "event_types": response["aggregations"]["event_types"]["buckets"],
                "api_functions": response["aggregations"]["api_functions"]["buckets"],
                "api_categories": response["aggregations"]["api_categories"]["buckets"],
                "timeline": response["aggregations"]["timeline"]["buckets"]
            }
            
        except Exception as e:
            logger.error(f"Failed to get session summary: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check of Elasticsearch connection."""
        try:
            cluster_health = await self.client.cluster.health()
            
            return {
                "status": "healthy",
                "cluster_status": cluster_health["status"],
                "number_of_nodes": cluster_health["number_of_nodes"],
                "pending_events": len(self.pending_events),
                "bulk_size": self.bulk_size,
                "index_prefix": self.index_prefix
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "pending_events": len(self.pending_events)
            }
