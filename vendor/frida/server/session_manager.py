"""
Analysis Session Manager

Manages the lifecycle of Frida analysis sessions including:
- Session creation and tracking
- State management and persistence
- Resource allocation and cleanup
- Session metadata and reporting
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)


class SessionStatus(Enum):
    """Analysis session status enumeration."""
    PENDING = "pending"
    PROVISIONING_VM = "provisioning_vm"
    TRANSFERRING_BINARY = "transferring_binary"
    STARTING_FRIDA = "starting_frida"
    RUNNING = "running"
    STOPPING = "stopping"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


@dataclass
class SessionMetadata:
    """Session metadata structure."""
    session_id: str
    binary_uuid: str
    analysis_type: str
    profile: str
    vm_template: str
    duration: int
    user_id: str
    status: SessionStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    vm_id: Optional[str] = None
    vm_ip: Optional[str] = None
    frida_pid: Optional[int] = None
    error_message: Optional[str] = None
    artifacts_count: int = 0
    events_count: int = 0
    stop_reason: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with datetime serialization."""
        data = asdict(self)
        # Convert datetime objects to ISO strings
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
            elif isinstance(value, SessionStatus):
                data[key] = value.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SessionMetadata':
        """Create from dictionary with datetime deserialization."""
        # Convert ISO strings back to datetime objects
        datetime_fields = ['created_at', 'started_at', 'completed_at']
        for field in datetime_fields:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        # Convert status string to enum
        if 'status' in data:
            data['status'] = SessionStatus(data['status'])
        
        return cls(**data)


class AnalysisSessionManager:
    """Manages analysis sessions and their lifecycle."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize session manager."""
        self.config = config
        self.sessions: Dict[str, SessionMetadata] = {}
        self.active_sessions: Set[str] = set()
        self.session_locks: Dict[str, asyncio.Lock] = {}
        
        # Session limits and timeouts
        self.max_concurrent_sessions = config.get('frida', {}).get('max_sessions', 10)
        self.default_timeout = config.get('analysis', {}).get('default_duration', 300)
        self.max_timeout = config.get('analysis', {}).get('max_duration', 1800)
        
        # Persistence
        self.persistence_enabled = config.get('session_manager', {}).get('persistence', True)
        self.session_file = Path(config.get('session_manager', {}).get('session_file', 'sessions.json'))
        
        # Load existing sessions
        if self.persistence_enabled:
            self._load_sessions()
        
        logger.info(f"Session manager initialized with max {self.max_concurrent_sessions} concurrent sessions")
    
    async def create_session(
        self,
        binary_uuid: str,
        analysis_type: str = "runtime",
        profile: str = "default",
        vm_template: str = "ubuntu_22",
        duration: Optional[int] = None,
        user_id: str = "system",
        config: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new analysis session."""
        try:
            # Check session limits
            if len(self.active_sessions) >= self.max_concurrent_sessions:
                raise RuntimeError(f"Maximum concurrent sessions ({self.max_concurrent_sessions}) reached")
            
            # Generate session ID
            session_id = str(uuid.uuid4())
            
            # Validate and set duration
            if duration is None:
                duration = self.default_timeout
            elif duration > self.max_timeout:
                duration = self.max_timeout
            
            # Create session metadata
            metadata = SessionMetadata(
                session_id=session_id,
                binary_uuid=binary_uuid,
                analysis_type=analysis_type,
                profile=profile,
                vm_template=vm_template,
                duration=duration,
                user_id=user_id,
                status=SessionStatus.PENDING,
                created_at=datetime.utcnow(),
                config=config or {}
            )
            
            # Store session
            self.sessions[session_id] = metadata
            self.active_sessions.add(session_id)
            self.session_locks[session_id] = asyncio.Lock()
            
            # Persist session
            if self.persistence_enabled:
                await self._save_sessions()
            
            logger.info(f"Created session {session_id} for binary {binary_uuid}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create session: {e}")
            raise
    
    async def update_session_status(
        self,
        session_id: str,
        status: SessionStatus,
        **kwargs
    ) -> None:
        """Update session status and metadata."""
        try:
            if session_id not in self.sessions:
                raise ValueError(f"Session {session_id} not found")
            
            async with self.session_locks[session_id]:
                metadata = self.sessions[session_id]
                metadata.status = status
                
                # Update timestamps
                if status == SessionStatus.RUNNING and not metadata.started_at:
                    metadata.started_at = datetime.utcnow()
                elif status in [SessionStatus.COMPLETED, SessionStatus.FAILED, SessionStatus.TIMEOUT, SessionStatus.CANCELLED]:
                    metadata.completed_at = datetime.utcnow()
                    if session_id in self.active_sessions:
                        self.active_sessions.remove(session_id)
                
                # Update additional fields
                for key, value in kwargs.items():
                    if hasattr(metadata, key):
                        setattr(metadata, key, value)
                
                # Persist changes
                if self.persistence_enabled:
                    await self._save_sessions()
                
                logger.debug(f"Updated session {session_id} status to {status.value}")
                
        except Exception as e:
            logger.error(f"Failed to update session {session_id}: {e}")
            raise
    
    async def get_session(self, session_id: str) -> Optional[SessionMetadata]:
        """Get session metadata."""
        return self.sessions.get(session_id)
    
    async def get_session_dict(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session metadata as dictionary."""
        metadata = self.sessions.get(session_id)
        return metadata.to_dict() if metadata else None
    
    async def list_sessions(
        self,
        user_id: Optional[str] = None,
        status: Optional[SessionStatus] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """List sessions with optional filtering."""
        sessions = []
        
        for metadata in self.sessions.values():
            # Apply filters
            if user_id and metadata.user_id != user_id:
                continue
            if status and metadata.status != status:
                continue
            
            sessions.append(metadata.to_dict())
        
        # Sort by creation time (newest first)
        sessions.sort(key=lambda x: x['created_at'], reverse=True)
        
        # Apply limit
        if limit:
            sessions = sessions[:limit]
        
        return sessions
    
    async def list_active_sessions(self) -> List[Dict[str, Any]]:
        """List all active sessions."""
        return [
            self.sessions[session_id].to_dict()
            for session_id in self.active_sessions
            if session_id in self.sessions
        ]
    
    async def cancel_session(self, session_id: str, reason: str = "manual") -> bool:
        """Cancel a running session."""
        try:
            if session_id not in self.sessions:
                return False
            
            metadata = self.sessions[session_id]
            
            if metadata.status in [SessionStatus.COMPLETED, SessionStatus.FAILED, SessionStatus.CANCELLED]:
                return False
            
            await self.update_session_status(
                session_id,
                SessionStatus.CANCELLED,
                stop_reason=reason
            )
            
            logger.info(f"Cancelled session {session_id} (reason: {reason})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel session {session_id}: {e}")
            return False
    
    async def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions."""
        cleaned_count = 0
        current_time = datetime.utcnow()
        
        for session_id, metadata in list(self.sessions.items()):
            # Check if session has expired
            if metadata.status == SessionStatus.RUNNING:
                if metadata.started_at:
                    elapsed = (current_time - metadata.started_at).total_seconds()
                    if elapsed > metadata.duration:
                        await self.update_session_status(
                            session_id,
                            SessionStatus.TIMEOUT,
                            stop_reason="timeout"
                        )
                        cleaned_count += 1
            
            # Clean up old completed sessions (older than 24 hours)
            elif metadata.status in [SessionStatus.COMPLETED, SessionStatus.FAILED, SessionStatus.CANCELLED]:
                if metadata.completed_at:
                    age = (current_time - metadata.completed_at).total_seconds()
                    if age > 86400:  # 24 hours
                        await self._remove_session(session_id)
                        cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} expired sessions")
        
        return cleaned_count
    
    async def get_session_statistics(self) -> Dict[str, Any]:
        """Get session statistics."""
        stats = {
            'total_sessions': len(self.sessions),
            'active_sessions': len(self.active_sessions),
            'status_counts': {},
            'analysis_type_counts': {},
            'profile_counts': {},
            'average_duration': 0,
            'success_rate': 0
        }
        
        if not self.sessions:
            return stats
        
        # Count by status
        for metadata in self.sessions.values():
            status = metadata.status.value
            stats['status_counts'][status] = stats['status_counts'].get(status, 0) + 1
            
            # Count by analysis type
            analysis_type = metadata.analysis_type
            stats['analysis_type_counts'][analysis_type] = stats['analysis_type_counts'].get(analysis_type, 0) + 1
            
            # Count by profile
            profile = metadata.profile
            stats['profile_counts'][profile] = stats['profile_counts'].get(profile, 0) + 1
        
        # Calculate success rate
        completed = stats['status_counts'].get('completed', 0)
        total_finished = sum(
            stats['status_counts'].get(status, 0)
            for status in ['completed', 'failed', 'timeout', 'cancelled']
        )
        
        if total_finished > 0:
            stats['success_rate'] = completed / total_finished
        
        # Calculate average duration for completed sessions
        completed_sessions = [
            m for m in self.sessions.values()
            if m.status == SessionStatus.COMPLETED and m.started_at and m.completed_at
        ]
        
        if completed_sessions:
            total_duration = sum(
                (m.completed_at - m.started_at).total_seconds()
                for m in completed_sessions
            )
            stats['average_duration'] = total_duration / len(completed_sessions)
        
        return stats
    
    async def _remove_session(self, session_id: str) -> None:
        """Remove session from memory and persistence."""
        if session_id in self.sessions:
            del self.sessions[session_id]
        
        if session_id in self.active_sessions:
            self.active_sessions.remove(session_id)
        
        if session_id in self.session_locks:
            del self.session_locks[session_id]
        
        if self.persistence_enabled:
            await self._save_sessions()
    
    def _load_sessions(self) -> None:
        """Load sessions from persistence file."""
        try:
            if self.session_file.exists():
                with open(self.session_file, 'r') as f:
                    data = json.load(f)
                
                for session_data in data.get('sessions', []):
                    metadata = SessionMetadata.from_dict(session_data)
                    self.sessions[metadata.session_id] = metadata
                    
                    # Restore active sessions (but mark as failed if they were running)
                    if metadata.status in [SessionStatus.RUNNING, SessionStatus.PROVISIONING_VM, 
                                         SessionStatus.TRANSFERRING_BINARY, SessionStatus.STARTING_FRIDA]:
                        metadata.status = SessionStatus.FAILED
                        metadata.error_message = "Session interrupted by restart"
                        metadata.completed_at = datetime.utcnow()
                    elif metadata.status == SessionStatus.PENDING:
                        self.active_sessions.add(metadata.session_id)
                    
                    self.session_locks[metadata.session_id] = asyncio.Lock()
                
                logger.info(f"Loaded {len(self.sessions)} sessions from persistence")
                
        except Exception as e:
            logger.error(f"Failed to load sessions: {e}")
    
    async def _save_sessions(self) -> None:
        """Save sessions to persistence file."""
        try:
            data = {
                'sessions': [metadata.to_dict() for metadata in self.sessions.values()],
                'saved_at': datetime.utcnow().isoformat()
            }
            
            # Ensure directory exists
            self.session_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Write to temporary file first, then rename (atomic operation)
            temp_file = self.session_file.with_suffix('.tmp')
            with open(temp_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            temp_file.rename(self.session_file)
            
        except Exception as e:
            logger.error(f"Failed to save sessions: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check of session manager."""
        return {
            'status': 'healthy',
            'total_sessions': len(self.sessions),
            'active_sessions': len(self.active_sessions),
            'max_concurrent_sessions': self.max_concurrent_sessions,
            'persistence_enabled': self.persistence_enabled,
            'session_file_exists': self.session_file.exists() if self.persistence_enabled else None
        }
