"""
Frida Analysis Server

Main server controller for Frida-based binary analysis.
Integrates with <PERSON> for comprehensive analysis.
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

import frida
from elasticsearch import AsyncElasticsearch
from minio import Minio

from .session_manager import AnalysisSessionManager
from .vm_coordinator import VMCoordinator
from ..streaming.elasticsearch_client import ElasticsearchStreamer
from ..streaming.data_formatter import DataFormatter
from ..streaming.sanitizer import DataSanitizer
from ..extractors.file_extractor import FileExtractor
from ..extractors.registry_extractor import RegistryExtractor
from ..extractors.network_extractor import NetworkExtractor

logger = logging.getLogger(__name__)


class FridaAnalysisServer:
    """Main Frida analysis server."""
    
    def __init__(self, config_path: str = "vendor/frida/config/frida_config.json"):
        """Initialize the Frida analysis server."""
        self.config = self._load_config(config_path)
        self.session_manager = AnalysisSessionManager(self.config)
        self.vm_coordinator = VMCoordinator(self.config)
        
        # Initialize storage clients
        self._init_storage_clients()
        
        # Initialize data processing components
        self.data_formatter = DataFormatter(self.config)
        self.data_sanitizer = DataSanitizer(self.config)
        self.elasticsearch_streamer = ElasticsearchStreamer(self.config)
        
        # Initialize extractors
        self.file_extractor = FileExtractor(self.config)
        self.registry_extractor = RegistryExtractor(self.config)
        self.network_extractor = NetworkExtractor(self.config)
        
        # Active sessions
        self.active_sessions: Dict[str, Dict] = {}
        
        logger.info("Frida Analysis Server initialized")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load config from {config_path}: {e}")
            raise
    
    def _init_storage_clients(self):
        """Initialize MinIO and Elasticsearch clients."""
        # MinIO client
        minio_config = self.config['storage']['minio']
        self.minio_client = Minio(
            minio_config['endpoint'],
            access_key=minio_config['access_key'],
            secret_key=minio_config['secret_key'],
            secure=minio_config['secure']
        )
        
        # Elasticsearch client
        es_config = self.config['storage']['elasticsearch']
        self.elasticsearch_client = AsyncElasticsearch(
            hosts=es_config['hosts']
        )
        
        logger.info("Storage clients initialized")
    
    async def start_analysis(
        self,
        binary_uuid: str,
        analysis_type: str = "runtime",
        profile: str = "default",
        vm_template: str = "ubuntu_22",
        duration: Optional[int] = None,
        user_id: str = "system"
    ) -> str:
        """Start a new analysis session."""
        try:
            # Generate session ID
            session_id = str(uuid.uuid4())
            
            # Load analysis profile
            profile_config = self._load_analysis_profile(profile)
            
            # Set duration
            if duration is None:
                duration = profile_config.get('duration', self.config['analysis']['default_duration'])
            
            # Create session metadata
            session_metadata = {
                'session_id': session_id,
                'binary_uuid': binary_uuid,
                'analysis_type': analysis_type,
                'profile': profile,
                'vm_template': vm_template,
                'duration': duration,
                'user_id': user_id,
                'status': 'starting',
                'started_at': datetime.utcnow().isoformat(),
                'config': profile_config
            }
            
            # Store session metadata
            self.active_sessions[session_id] = session_metadata
            
            # Start VM and analysis
            await self._start_analysis_session(session_id, session_metadata)
            
            logger.info(f"Started analysis session {session_id} for binary {binary_uuid}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to start analysis: {e}")
            raise
    
    async def _start_analysis_session(self, session_id: str, metadata: Dict[str, Any]):
        """Start the actual analysis session."""
        try:
            # Update status
            metadata['status'] = 'provisioning_vm'
            
            # Provision VM
            vm_info = await self.vm_coordinator.provision_vm(
                template=metadata['vm_template'],
                session_id=session_id
            )
            
            metadata['vm_id'] = vm_info['vm_id']
            metadata['vm_ip'] = vm_info['ip_address']
            metadata['status'] = 'transferring_binary'
            
            # Transfer binary to VM
            await self._transfer_binary_to_vm(
                metadata['binary_uuid'],
                vm_info
            )
            
            metadata['status'] = 'starting_frida'
            
            # Start Frida instrumentation
            await self._start_frida_instrumentation(session_id, metadata, vm_info)
            
            metadata['status'] = 'running'
            
            # Schedule analysis completion
            asyncio.create_task(
                self._schedule_analysis_completion(session_id, metadata['duration'])
            )
            
        except Exception as e:
            logger.error(f"Failed to start analysis session {session_id}: {e}")
            metadata['status'] = 'failed'
            metadata['error'] = str(e)
            raise
    
    async def _transfer_binary_to_vm(self, binary_uuid: str, vm_info: Dict[str, Any]):
        """Transfer binary from MinIO to VM."""
        try:
            # Download binary from MinIO
            bucket_name = self.config['storage']['minio']['bucket']
            binary_data = self.minio_client.get_object(bucket_name, binary_uuid)
            
            # Transfer to VM using VM coordinator
            await self.vm_coordinator.transfer_file_to_vm(
                vm_info['vm_id'],
                binary_data.read(),
                f"/tmp/analysis_binary_{binary_uuid}"
            )
            
            logger.info(f"Transferred binary {binary_uuid} to VM {vm_info['vm_id']}")
            
        except Exception as e:
            logger.error(f"Failed to transfer binary {binary_uuid}: {e}")
            raise
    
    async def _start_frida_instrumentation(
        self,
        session_id: str,
        metadata: Dict[str, Any],
        vm_info: Dict[str, Any]
    ):
        """Start Frida instrumentation on the target VM."""
        try:
            # Connect to Frida server on VM
            device = frida.get_device_manager().add_remote_device(vm_info['ip_address'])
            
            # Load instrumentation scripts based on profile
            scripts = self._load_instrumentation_scripts(metadata['config'])
            
            # Start binary execution with instrumentation
            binary_path = f"/tmp/analysis_binary_{metadata['binary_uuid']}"
            
            # Spawn process with Frida
            pid = device.spawn([binary_path])
            session = device.attach(pid)
            
            # Load and enable scripts
            for script_name, script_code in scripts.items():
                script = session.create_script(script_code)
                script.on('message', lambda message, data: self._handle_frida_message(
                    session_id, script_name, message, data
                ))
                script.load()
            
            # Resume execution
            device.resume(pid)
            
            # Store session info
            metadata['frida_session'] = session
            metadata['frida_pid'] = pid
            metadata['frida_device'] = device
            
            logger.info(f"Started Frida instrumentation for session {session_id}")
            
        except Exception as e:
            logger.error(f"Failed to start Frida instrumentation: {e}")
            raise
    
    def _load_analysis_profile(self, profile_name: str) -> Dict[str, Any]:
        """Load analysis profile configuration."""
        try:
            profiles_path = "vendor/frida/config/analysis_profiles.json"
            with open(profiles_path, 'r') as f:
                profiles = json.load(f)
            
            if profile_name not in profiles['profiles']:
                logger.warning(f"Profile {profile_name} not found, using default")
                profile_name = 'default'
            
            return profiles['profiles'][profile_name]
            
        except Exception as e:
            logger.error(f"Failed to load analysis profile {profile_name}: {e}")
            # Return minimal default profile
            return {
                'name': 'Minimal Default',
                'duration': 300,
                'api_hooks': ['file_io', 'network', 'process'],
                'monitoring': {'screenshots': False, 'network_capture': True}
            }
    
    def _load_instrumentation_scripts(self, profile_config: Dict[str, Any]) -> Dict[str, str]:
        """Load Frida instrumentation scripts based on profile."""
        scripts = {}
        
        # Load core scripts based on API hooks
        for hook_type in profile_config.get('api_hooks', []):
            script_path = f"vendor/frida/scripts/api_hooks/{hook_type}.js"
            try:
                with open(script_path, 'r') as f:
                    scripts[hook_type] = f.read()
            except FileNotFoundError:
                logger.warning(f"Script not found: {script_path}")
        
        return scripts
    
    async def _handle_frida_message(
        self,
        session_id: str,
        script_name: str,
        message: Dict[str, Any],
        data: Optional[bytes]
    ):
        """Handle messages from Frida scripts."""
        try:
            if message['type'] == 'send':
                # Format data for Elasticsearch
                formatted_data = self.data_formatter.format_frida_event(
                    session_id=session_id,
                    script_name=script_name,
                    payload=message['payload'],
                    raw_data=data
                )
                
                # Apply sanitization
                sanitized_data = self.data_sanitizer.sanitize_event(formatted_data)
                
                # Stream to Elasticsearch
                await self.elasticsearch_streamer.stream_event(sanitized_data)
                
                # Also send to Logstash for Inspector Gadget correlation
                if self.config['integration']['inspector_gadget']['enabled']:
                    await self._send_to_logstash(sanitized_data)
                
        except Exception as e:
            logger.error(f"Failed to handle Frida message: {e}")
    
    async def _send_to_logstash(self, event_data: Dict[str, Any]):
        """Send event data to Logstash for correlation with Inspector Gadget."""
        try:
            # Add Frida-specific tags
            event_data['tags'] = event_data.get('tags', []) + ['frida', 'dynamic-analysis']
            event_data['source_system'] = 'frida'
            
            # Send to Logstash HTTP input
            logstash_endpoint = self.config['integration']['elk_stack']['logstash_endpoint']
            
            # Use aiohttp to send data
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.post(logstash_endpoint, json=event_data) as response:
                    if response.status != 200:
                        logger.warning(f"Failed to send to Logstash: {response.status}")
                        
        except Exception as e:
            logger.error(f"Failed to send data to Logstash: {e}")
    
    async def _schedule_analysis_completion(self, session_id: str, duration: int):
        """Schedule analysis completion after specified duration."""
        await asyncio.sleep(duration)
        await self.stop_analysis(session_id, reason="timeout")
    
    async def stop_analysis(self, session_id: str, reason: str = "manual"):
        """Stop an analysis session."""
        try:
            if session_id not in self.active_sessions:
                raise ValueError(f"Session {session_id} not found")
            
            metadata = self.active_sessions[session_id]
            
            # Stop Frida instrumentation
            if 'frida_session' in metadata:
                metadata['frida_session'].detach()
            
            # Extract artifacts
            await self._extract_analysis_artifacts(session_id, metadata)
            
            # Cleanup VM
            if 'vm_id' in metadata:
                await self.vm_coordinator.cleanup_vm(metadata['vm_id'])
            
            # Update session status
            metadata['status'] = 'completed'
            metadata['completed_at'] = datetime.utcnow().isoformat()
            metadata['stop_reason'] = reason
            
            # Generate final report
            await self._generate_analysis_report(session_id, metadata)
            
            logger.info(f"Stopped analysis session {session_id} (reason: {reason})")
            
        except Exception as e:
            logger.error(f"Failed to stop analysis session {session_id}: {e}")
            raise
    
    async def _extract_analysis_artifacts(self, session_id: str, metadata: Dict[str, Any]):
        """Extract analysis artifacts from VM."""
        try:
            vm_id = metadata.get('vm_id')
            if not vm_id:
                return
            
            # Extract different types of artifacts based on analysis type
            if metadata['analysis_type'] == 'install_footprint':
                # Extract file system changes
                file_artifacts = await self.file_extractor.extract_file_changes(vm_id)
                await self._store_artifacts(session_id, 'file_changes', file_artifacts)
                
                # Extract registry changes (Windows)
                if 'windows' in metadata['vm_template']:
                    registry_artifacts = await self.registry_extractor.extract_registry_changes(vm_id)
                    await self._store_artifacts(session_id, 'registry_changes', registry_artifacts)
            
            # Extract network artifacts
            network_artifacts = await self.network_extractor.extract_network_data(vm_id)
            await self._store_artifacts(session_id, 'network_data', network_artifacts)
            
            logger.info(f"Extracted artifacts for session {session_id}")
            
        except Exception as e:
            logger.error(f"Failed to extract artifacts for session {session_id}: {e}")
    
    async def _store_artifacts(self, session_id: str, artifact_type: str, artifacts: List[Dict]):
        """Store artifacts in MinIO."""
        try:
            bucket_name = self.config['storage']['minio']['bucket']
            
            for i, artifact in enumerate(artifacts):
                # Generate artifact filename
                artifact_filename = f"{session_id}/{artifact_type}_{i:04d}.json"
                
                # Convert to JSON
                artifact_json = json.dumps(artifact, indent=2)
                
                # Upload to MinIO
                self.minio_client.put_object(
                    bucket_name,
                    artifact_filename,
                    data=artifact_json.encode('utf-8'),
                    length=len(artifact_json),
                    content_type='application/json'
                )
            
            logger.info(f"Stored {len(artifacts)} {artifact_type} artifacts for session {session_id}")
            
        except Exception as e:
            logger.error(f"Failed to store artifacts: {e}")
    
    async def _generate_analysis_report(self, session_id: str, metadata: Dict[str, Any]):
        """Generate final analysis report."""
        try:
            # Query Elasticsearch for session events
            query = {
                "query": {
                    "term": {"session_id": session_id}
                },
                "aggs": {
                    "event_types": {
                        "terms": {"field": "event_type"}
                    },
                    "api_calls": {
                        "terms": {"field": "api_function"}
                    }
                }
            }
            
            # Execute query
            response = await self.elasticsearch_client.search(
                index=f"frida-runtime-*",
                body=query
            )
            
            # Generate report
            report = {
                'session_id': session_id,
                'binary_uuid': metadata['binary_uuid'],
                'analysis_summary': {
                    'total_events': response['hits']['total']['value'],
                    'event_types': response['aggregations']['event_types']['buckets'],
                    'api_calls': response['aggregations']['api_calls']['buckets']
                },
                'metadata': metadata
            }
            
            # Store report
            await self._store_artifacts(session_id, 'analysis_report', [report])
            
            logger.info(f"Generated analysis report for session {session_id}")
            
        except Exception as e:
            logger.error(f"Failed to generate analysis report: {e}")
    
    async def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """Get status of an analysis session."""
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
        
        return self.active_sessions[session_id]
    
    async def list_active_sessions(self) -> List[Dict[str, Any]]:
        """List all active analysis sessions."""
        return list(self.active_sessions.values())
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check of all components."""
        health = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'components': {}
        }
        
        try:
            # Check Elasticsearch
            es_health = await self.elasticsearch_client.cluster.health()
            health['components']['elasticsearch'] = {
                'status': es_health['status'],
                'nodes': es_health['number_of_nodes']
            }
        except Exception as e:
            health['components']['elasticsearch'] = {'status': 'error', 'error': str(e)}
            health['status'] = 'degraded'
        
        try:
            # Check MinIO
            self.minio_client.bucket_exists(self.config['storage']['minio']['bucket'])
            health['components']['minio'] = {'status': 'healthy'}
        except Exception as e:
            health['components']['minio'] = {'status': 'error', 'error': str(e)}
            health['status'] = 'degraded'
        
        # Check VM coordinator
        vm_health = await self.vm_coordinator.health_check()
        health['components']['vm_coordinator'] = vm_health
        
        if vm_health['status'] != 'healthy':
            health['status'] = 'degraded'
        
        return health
