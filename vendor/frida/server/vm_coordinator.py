"""
VM Coordinator

Manages virtual machine lifecycle for Frida analysis including:
- VM provisioning and configuration
- Template management and selection
- Network isolation and security
- File transfer and communication
- VM cleanup and resource management
"""

import asyncio
import json
import logging
import subprocess
import tempfile
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import paramiko
import docker
from fabric import Connection

logger = logging.getLogger(__name__)


class VMCoordinator:
    """Coordinates VM lifecycle for analysis sessions."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize VM coordinator."""
        self.config = config
        self.vm_config = config.get('vm', {})
        self.templates = self.vm_config.get('templates', {})
        self.isolation_config = self.vm_config.get('isolation', {})
        
        # Active VMs tracking
        self.active_vms: Dict[str, Dict[str, Any]] = {}
        self.vm_locks: Dict[str, asyncio.Lock] = {}
        
        # Docker client for container-based VMs
        self.docker_client = docker.from_env()
        
        # VM limits
        self.max_concurrent_vms = config.get('vm', {}).get('max_concurrent', 5)
        
        logger.info("VM Coordinator initialized")
    
    async def provision_vm(
        self,
        template: str,
        session_id: str,
        custom_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Provision a new VM for analysis."""
        try:
            # Check VM limits
            if len(self.active_vms) >= self.max_concurrent_vms:
                raise RuntimeError(f"Maximum concurrent VMs ({self.max_concurrent_vms}) reached")
            
            # Get template configuration
            if template not in self.templates:
                raise ValueError(f"Unknown VM template: {template}")
            
            template_config = self.templates[template].copy()
            if custom_config:
                template_config.update(custom_config)
            
            # Generate VM ID
            vm_id = f"frida-analysis-{session_id[:8]}-{uuid.uuid4().hex[:8]}"
            
            # Create VM lock
            self.vm_locks[vm_id] = asyncio.Lock()
            
            async with self.vm_locks[vm_id]:
                # Provision based on template type
                if template.startswith('docker_'):
                    vm_info = await self._provision_docker_vm(vm_id, template_config, session_id)
                else:
                    vm_info = await self._provision_vagrant_vm(vm_id, template_config, session_id)
                
                # Store VM info
                vm_info.update({
                    'vm_id': vm_id,
                    'session_id': session_id,
                    'template': template,
                    'created_at': datetime.utcnow().isoformat(),
                    'status': 'running'
                })
                
                self.active_vms[vm_id] = vm_info
                
                logger.info(f"Provisioned VM {vm_id} for session {session_id}")
                return vm_info
                
        except Exception as e:
            logger.error(f"Failed to provision VM for session {session_id}: {e}")
            # Cleanup on failure
            if vm_id in self.vm_locks:
                del self.vm_locks[vm_id]
            raise
    
    async def _provision_docker_vm(
        self,
        vm_id: str,
        config: Dict[str, Any],
        session_id: str
    ) -> Dict[str, Any]:
        """Provision a Docker-based analysis environment."""
        try:
            # Prepare Docker configuration
            image = config.get('image', 'ubuntu:22.04')
            memory_limit = config.get('memory', '2g')
            cpu_limit = config.get('cpus', 2)
            
            # Create isolated network
            network_name = f"frida-net-{session_id[:8]}"
            try:
                network = self.docker_client.networks.create(
                    network_name,
                    driver="bridge",
                    internal=self.isolation_config.get('air_gapped', True),
                    options={
                        "com.docker.network.bridge.enable_icc": "false",
                        "com.docker.network.bridge.enable_ip_masquerade": "false"
                    }
                )
            except docker.errors.APIError as e:
                if "already exists" in str(e):
                    network = self.docker_client.networks.get(network_name)
                else:
                    raise
            
            # Prepare container environment
            environment = {
                'FRIDA_SESSION_ID': session_id,
                'ANALYSIS_MODE': 'isolated',
                'DEBIAN_FRONTEND': 'noninteractive'
            }
            
            # Create and start container
            container = self.docker_client.containers.run(
                image,
                name=vm_id,
                detach=True,
                mem_limit=memory_limit,
                cpu_count=cpu_limit,
                network=network_name,
                environment=environment,
                cap_drop=['ALL'],
                cap_add=['SYS_PTRACE'],  # Required for Frida
                security_opt=['no-new-privileges:true'],
                read_only=False,
                tmpfs={'/tmp': 'noexec,nosuid,size=100m'},
                command=['sleep', 'infinity']  # Keep container running
            )
            
            # Wait for container to be ready
            await asyncio.sleep(2)
            
            # Install Frida in container
            await self._setup_frida_in_container(container)
            
            # Get container IP
            container.reload()
            networks = container.attrs['NetworkSettings']['Networks']
            ip_address = networks[network_name]['IPAddress']
            
            return {
                'type': 'docker',
                'container_id': container.id,
                'container_name': vm_id,
                'network_name': network_name,
                'ip_address': ip_address,
                'ssh_port': None,  # Docker exec used instead
                'frida_port': 27042
            }
            
        except Exception as e:
            logger.error(f"Failed to provision Docker VM {vm_id}: {e}")
            # Cleanup on failure
            try:
                if 'container' in locals():
                    container.remove(force=True)
                if 'network' in locals():
                    network.remove()
            except:
                pass
            raise
    
    async def _provision_vagrant_vm(
        self,
        vm_id: str,
        config: Dict[str, Any],
        session_id: str
    ) -> Dict[str, Any]:
        """Provision a Vagrant-based VM."""
        try:
            # Create temporary Vagrantfile
            vm_dir = Path(f"/tmp/frida-vm-{vm_id}")
            vm_dir.mkdir(parents=True, exist_ok=True)
            
            vagrantfile_content = self._generate_vagrantfile(vm_id, config)
            
            with open(vm_dir / "Vagrantfile", "w") as f:
                f.write(vagrantfile_content)
            
            # Start VM
            process = await asyncio.create_subprocess_exec(
                'vagrant', 'up',
                cwd=vm_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                raise RuntimeError(f"Vagrant up failed: {stderr.decode()}")
            
            # Get VM IP
            ip_process = await asyncio.create_subprocess_exec(
                'vagrant', 'ssh-config',
                cwd=vm_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            ip_stdout, _ = await ip_process.communicate()
            ssh_config = ip_stdout.decode()
            
            # Parse SSH config for IP
            ip_address = self._parse_vagrant_ip(ssh_config)
            ssh_port = self._parse_vagrant_port(ssh_config)
            
            # Setup Frida on VM
            await self._setup_frida_on_vagrant_vm(vm_dir, config)
            
            return {
                'type': 'vagrant',
                'vm_dir': str(vm_dir),
                'ip_address': ip_address,
                'ssh_port': ssh_port,
                'frida_port': 27042
            }
            
        except Exception as e:
            logger.error(f"Failed to provision Vagrant VM {vm_id}: {e}")
            # Cleanup on failure
            if 'vm_dir' in locals() and vm_dir.exists():
                try:
                    await asyncio.create_subprocess_exec(
                        'vagrant', 'destroy', '-f',
                        cwd=vm_dir
                    )
                except:
                    pass
            raise
    
    async def _setup_frida_in_container(self, container) -> None:
        """Install and configure Frida in Docker container."""
        try:
            # Update package lists
            exec_result = container.exec_run(['apt-get', 'update'])
            if exec_result.exit_code != 0:
                raise RuntimeError(f"Failed to update packages: {exec_result.output}")
            
            # Install Python and pip
            exec_result = container.exec_run([
                'apt-get', 'install', '-y', 'python3', 'python3-pip', 'curl'
            ])
            if exec_result.exit_code != 0:
                raise RuntimeError(f"Failed to install Python: {exec_result.output}")
            
            # Install Frida
            exec_result = container.exec_run(['pip3', 'install', 'frida-tools'])
            if exec_result.exit_code != 0:
                raise RuntimeError(f"Failed to install Frida: {exec_result.output}")
            
            # Start Frida server
            container.exec_run([
                'frida-server', '-l', '0.0.0.0:27042'
            ], detach=True)
            
            logger.info(f"Frida setup completed in container {container.name}")
            
        except Exception as e:
            logger.error(f"Failed to setup Frida in container: {e}")
            raise
    
    async def _setup_frida_on_vagrant_vm(self, vm_dir: Path, config: Dict[str, Any]) -> None:
        """Install and configure Frida on Vagrant VM."""
        try:
            # Create setup script
            setup_script = """#!/bin/bash
set -e

# Update system
sudo apt-get update

# Install Python and dependencies
sudo apt-get install -y python3 python3-pip curl wget

# Install Frida
pip3 install frida-tools

# Download and start Frida server
wget -O /tmp/frida-server https://github.com/frida/frida/releases/latest/download/frida-server-linux-x86_64
chmod +x /tmp/frida-server
nohup /tmp/frida-server -l 0.0.0.0:27042 > /tmp/frida-server.log 2>&1 &

echo "Frida setup completed"
"""
            
            script_path = vm_dir / "setup_frida.sh"
            with open(script_path, "w") as f:
                f.write(setup_script)
            
            # Execute setup script
            process = await asyncio.create_subprocess_exec(
                'vagrant', 'ssh', '-c', f'bash /vagrant/setup_frida.sh',
                cwd=vm_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                raise RuntimeError(f"Frida setup failed: {stderr.decode()}")
            
            logger.info(f"Frida setup completed on Vagrant VM")
            
        except Exception as e:
            logger.error(f"Failed to setup Frida on Vagrant VM: {e}")
            raise
    
    def _generate_vagrantfile(self, vm_id: str, config: Dict[str, Any]) -> str:
        """Generate Vagrantfile for VM."""
        box = config.get('box', 'ubuntu/jammy64')
        memory = config.get('memory', 2048)
        cpus = config.get('cpus', 2)
        
        return f"""
Vagrant.configure("2") do |config|
  config.vm.box = "{box}"
  config.vm.hostname = "{vm_id}"
  
  config.vm.provider "libvirt" do |vb|
    vb.memory = {memory}
    vb.cpus = {cpus}
  end
  
  # Disable default shared folder
  config.vm.synced_folder ".", "/vagrant", type: "rsync"
  
  # Network configuration for isolation
  config.vm.network "private_network", type: "dhcp"
  
  # Provision basic tools
  config.vm.provision "shell", inline: <<-SHELL
    apt-get update
    apt-get install -y curl wget python3 python3-pip
  SHELL
end
"""
    
    def _parse_vagrant_ip(self, ssh_config: str) -> str:
        """Parse IP address from Vagrant SSH config."""
        for line in ssh_config.split('\n'):
            if 'HostName' in line:
                return line.split()[-1]
        raise RuntimeError("Could not parse VM IP address")
    
    def _parse_vagrant_port(self, ssh_config: str) -> int:
        """Parse SSH port from Vagrant SSH config."""
        for line in ssh_config.split('\n'):
            if 'Port' in line:
                return int(line.split()[-1])
        return 22
    
    async def transfer_file_to_vm(
        self,
        vm_id: str,
        file_data: bytes,
        destination_path: str
    ) -> None:
        """Transfer file to VM."""
        try:
            if vm_id not in self.active_vms:
                raise ValueError(f"VM {vm_id} not found")
            
            vm_info = self.active_vms[vm_id]
            
            if vm_info['type'] == 'docker':
                await self._transfer_file_to_docker(vm_info, file_data, destination_path)
            else:
                await self._transfer_file_to_vagrant(vm_info, file_data, destination_path)
            
            logger.info(f"Transferred file to VM {vm_id}: {destination_path}")
            
        except Exception as e:
            logger.error(f"Failed to transfer file to VM {vm_id}: {e}")
            raise
    
    async def _transfer_file_to_docker(
        self,
        vm_info: Dict[str, Any],
        file_data: bytes,
        destination_path: str
    ) -> None:
        """Transfer file to Docker container."""
        container = self.docker_client.containers.get(vm_info['container_id'])
        
        # Create temporary file
        with tempfile.NamedTemporaryFile() as temp_file:
            temp_file.write(file_data)
            temp_file.flush()
            
            # Copy to container
            with open(temp_file.name, 'rb') as f:
                container.put_archive(Path(destination_path).parent, f.read())
    
    async def _transfer_file_to_vagrant(
        self,
        vm_info: Dict[str, Any],
        file_data: bytes,
        destination_path: str
    ) -> None:
        """Transfer file to Vagrant VM."""
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(file_data)
            temp_file_path = temp_file.name
        
        try:
            # Use SCP to transfer file
            process = await asyncio.create_subprocess_exec(
                'vagrant', 'scp', temp_file_path, f'default:{destination_path}',
                cwd=vm_info['vm_dir'],
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                raise RuntimeError(f"SCP transfer failed: {stderr.decode()}")
                
        finally:
            # Cleanup temporary file
            Path(temp_file_path).unlink(missing_ok=True)
    
    async def cleanup_vm(self, vm_id: str) -> None:
        """Cleanup and destroy VM."""
        try:
            if vm_id not in self.active_vms:
                logger.warning(f"VM {vm_id} not found for cleanup")
                return
            
            vm_info = self.active_vms[vm_id]
            
            async with self.vm_locks.get(vm_id, asyncio.Lock()):
                if vm_info['type'] == 'docker':
                    await self._cleanup_docker_vm(vm_info)
                else:
                    await self._cleanup_vagrant_vm(vm_info)
                
                # Remove from tracking
                del self.active_vms[vm_id]
                if vm_id in self.vm_locks:
                    del self.vm_locks[vm_id]
                
                logger.info(f"Cleaned up VM {vm_id}")
                
        except Exception as e:
            logger.error(f"Failed to cleanup VM {vm_id}: {e}")
    
    async def _cleanup_docker_vm(self, vm_info: Dict[str, Any]) -> None:
        """Cleanup Docker-based VM."""
        try:
            # Remove container
            container = self.docker_client.containers.get(vm_info['container_id'])
            container.remove(force=True)
            
            # Remove network
            try:
                network = self.docker_client.networks.get(vm_info['network_name'])
                network.remove()
            except docker.errors.NotFound:
                pass  # Network already removed
                
        except Exception as e:
            logger.error(f"Failed to cleanup Docker VM: {e}")
    
    async def _cleanup_vagrant_vm(self, vm_info: Dict[str, Any]) -> None:
        """Cleanup Vagrant-based VM."""
        try:
            vm_dir = Path(vm_info['vm_dir'])
            
            # Destroy VM
            process = await asyncio.create_subprocess_exec(
                'vagrant', 'destroy', '-f',
                cwd=vm_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await process.communicate()
            
            # Remove VM directory
            import shutil
            shutil.rmtree(vm_dir, ignore_errors=True)
            
        except Exception as e:
            logger.error(f"Failed to cleanup Vagrant VM: {e}")
    
    async def get_vm_status(self, vm_id: str) -> Optional[Dict[str, Any]]:
        """Get VM status and information."""
        return self.active_vms.get(vm_id)
    
    async def list_active_vms(self) -> List[Dict[str, Any]]:
        """List all active VMs."""
        return list(self.active_vms.values())
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check of VM coordinator."""
        return {
            'status': 'healthy',
            'active_vms': len(self.active_vms),
            'max_concurrent_vms': self.max_concurrent_vms,
            'available_templates': list(self.templates.keys()),
            'docker_available': self._check_docker_availability(),
            'vagrant_available': self._check_vagrant_availability()
        }
    
    def _check_docker_availability(self) -> bool:
        """Check if Docker is available."""
        try:
            self.docker_client.ping()
            return True
        except:
            return False
    
    def _check_vagrant_availability(self) -> bool:
        """Check if Vagrant is available."""
        try:
            result = subprocess.run(['vagrant', '--version'], capture_output=True)
            return result.returncode == 0
        except:
            return False
