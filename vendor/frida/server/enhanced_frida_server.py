"""
Enhanced Frida Analysis Server with Inspector Gadget Integration

This enhanced version integrates Frida dynamic analysis with Inspector Gadget
eBPF monitoring for comprehensive binary behavior analysis.
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

import frida
from elasticsearch import AsyncElasticsearch
from minio import Minio

from .session_manager import AnalysisSessionManager
from .vm_coordinator import VMCoordinator
from ..streaming.elasticsearch_client import ElasticsearchStreamer
from ..streaming.data_formatter import DataFormatter
from ..streaming.sanitizer import DataSanitizer
from ..extractors.file_extractor import FileExtractor
from ..extractors.registry_extractor import RegistryExtractor
from ..extractors.network_extractor import NetworkExtractor
from ..integrations.inspector_gadget import InspectorGadgetIntegration

logger = logging.getLogger(__name__)


class EnhancedFridaAnalysisServer:
    """Enhanced Frida analysis server with Inspector Gadget integration."""
    
    def __init__(self, config_path: str = "vendor/frida/config/frida_config.json"):
        """Initialize the enhanced Frida analysis server."""
        self.config = self._load_config(config_path)
        self.session_manager = AnalysisSessionManager(self.config)
        self.vm_coordinator = VMCoordinator(self.config)
        
        # Initialize storage clients
        self._init_storage_clients()
        
        # Initialize data processing components
        self.data_formatter = DataFormatter(self.config)
        self.data_sanitizer = DataSanitizer(self.config)
        self.elasticsearch_streamer = ElasticsearchStreamer(self.config)
        
        # Initialize extractors
        self.file_extractor = FileExtractor(self.config)
        self.registry_extractor = RegistryExtractor(self.config)
        self.network_extractor = NetworkExtractor(self.config)
        
        # Initialize Inspector Gadget integration
        self.inspector_gadget = InspectorGadgetIntegration(self.config)
        
        # Active sessions with enhanced metadata
        self.active_sessions: Dict[str, Dict] = {}
        
        logger.info("Enhanced Frida Analysis Server with Inspector Gadget initialized")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # Ensure Inspector Gadget configuration exists
            if 'inspector_gadget' not in config:
                config['inspector_gadget'] = {
                    'enabled': True,
                    'default_gadgets': [
                        'trace_exec', 'trace_tcp', 'trace_open', 
                        'trace_dns', 'trace_capabilities'
                    ],
                    'correlation_enabled': True
                }
            
            return config
        except Exception as e:
            logger.error(f"Failed to load config from {config_path}: {e}")
            raise
    
    def _init_storage_clients(self):
        """Initialize MinIO and Elasticsearch clients."""
        # MinIO client
        minio_config = self.config['storage']['minio']
        self.minio_client = Minio(
            minio_config['endpoint'],
            access_key=minio_config['access_key'],
            secret_key=minio_config['secret_key'],
            secure=minio_config['secure']
        )
        
        # Elasticsearch client
        es_config = self.config['storage']['elasticsearch']
        self.elasticsearch_client = AsyncElasticsearch(
            hosts=es_config['hosts']
        )
        
        logger.info("Storage clients initialized")
    
    async def start_enhanced_analysis(
        self,
        binary_uuid: str,
        analysis_type: str = "runtime",
        profile: str = "default",
        vm_template: str = "ubuntu_22",
        duration: Optional[int] = None,
        user_id: str = "system",
        enable_inspector_gadget: bool = True,
        gadgets: Optional[List[str]] = None
    ) -> str:
        """Start an enhanced analysis session with Inspector Gadget integration."""
        try:
            # Generate session ID
            session_id = str(uuid.uuid4())
            
            # Load analysis profile
            profile_config = self._load_analysis_profile(profile)
            
            # Set duration
            if duration is None:
                duration = profile_config.get('duration', self.config['analysis']['default_duration'])
            
            # Set Inspector Gadget gadgets
            if gadgets is None:
                gadgets = self.config['inspector_gadget']['default_gadgets']
            
            # Create enhanced session metadata
            session_metadata = {
                'session_id': session_id,
                'binary_uuid': binary_uuid,
                'analysis_type': analysis_type,
                'profile': profile,
                'vm_template': vm_template,
                'duration': duration,
                'user_id': user_id,
                'status': 'starting',
                'started_at': datetime.utcnow().isoformat(),
                'config': profile_config,
                'inspector_gadget': {
                    'enabled': enable_inspector_gadget,
                    'gadgets': gadgets,
                    'status': 'pending'
                },
                'correlation_data': {
                    'frida_events': [],
                    'ig_events': [],
                    'correlations': []
                }
            }
            
            # Store session metadata
            self.active_sessions[session_id] = session_metadata
            
            # Start enhanced analysis session
            await self._start_enhanced_analysis_session(session_id, session_metadata)
            
            logger.info(f"Started enhanced analysis session {session_id} for binary {binary_uuid}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to start enhanced analysis: {e}")
            raise
    
    async def _start_enhanced_analysis_session(self, session_id: str, metadata: Dict[str, Any]):
        """Start the enhanced analysis session with Inspector Gadget."""
        try:
            # Update status
            metadata['status'] = 'provisioning_vm'
            
            # Provision VM
            vm_info = await self.vm_coordinator.provision_vm(
                template=metadata['vm_template'],
                session_id=session_id
            )
            
            metadata['vm_id'] = vm_info['vm_id']
            metadata['vm_ip'] = vm_info['ip_address']
            metadata['status'] = 'transferring_binary'
            
            # Transfer binary to VM
            await self._transfer_binary_to_vm(
                metadata['binary_uuid'],
                vm_info
            )
            
            # Start Inspector Gadget monitoring if enabled
            if metadata['inspector_gadget']['enabled']:
                metadata['status'] = 'starting_inspector_gadget'
                await self._start_inspector_gadget_monitoring(session_id, metadata, vm_info)
            
            metadata['status'] = 'starting_frida'
            
            # Start Frida instrumentation
            await self._start_frida_instrumentation(session_id, metadata, vm_info)
            
            metadata['status'] = 'running'
            
            # Schedule analysis completion
            asyncio.create_task(
                self._schedule_analysis_completion(session_id, metadata['duration'])
            )
            
        except Exception as e:
            logger.error(f"Failed to start enhanced analysis session {session_id}: {e}")
            metadata['status'] = 'failed'
            metadata['error'] = str(e)
            raise
    
    async def _start_inspector_gadget_monitoring(
        self,
        session_id: str,
        metadata: Dict[str, Any],
        vm_info: Dict[str, Any]
    ):
        """Start Inspector Gadget monitoring."""
        try:
            ig_config = metadata['inspector_gadget']
            
            # Start Inspector Gadget monitoring
            ig_result = await self.inspector_gadget.start_monitoring(
                session_id=session_id,
                vm_id=vm_info['vm_id'],
                gadgets=ig_config['gadgets'],
                correlation_id=metadata['binary_uuid']
            )
            
            # Update metadata with Inspector Gadget status
            ig_config['status'] = ig_result.get('status', 'unknown')
            ig_config['gadget_results'] = ig_result.get('gadget_results', {})
            
            logger.info(f"Started Inspector Gadget monitoring for session {session_id}")
            
        except Exception as e:
            logger.error(f"Failed to start Inspector Gadget monitoring: {e}")
            metadata['inspector_gadget']['status'] = 'failed'
            metadata['inspector_gadget']['error'] = str(e)
            # Don't fail the entire analysis if Inspector Gadget fails
    
    async def _transfer_binary_to_vm(self, binary_uuid: str, vm_info: Dict[str, Any]):
        """Transfer binary from MinIO to VM."""
        try:
            # Download binary from MinIO
            bucket_name = self.config['storage']['minio']['bucket']
            binary_data = self.minio_client.get_object(bucket_name, binary_uuid)
            
            # Transfer to VM using VM coordinator
            await self.vm_coordinator.transfer_file_to_vm(
                vm_info['vm_id'],
                binary_data.read(),
                f"/tmp/analysis_binary_{binary_uuid}"
            )
            
            logger.info(f"Transferred binary {binary_uuid} to VM {vm_info['vm_id']}")
            
        except Exception as e:
            logger.error(f"Failed to transfer binary {binary_uuid}: {e}")
            raise
    
    async def _start_frida_instrumentation(
        self,
        session_id: str,
        metadata: Dict[str, Any],
        vm_info: Dict[str, Any]
    ):
        """Start Frida instrumentation on the target VM."""
        try:
            # Connect to Frida server on VM
            device = frida.get_device_manager().add_remote_device(vm_info['ip_address'])
            
            # Load instrumentation scripts based on profile
            scripts = self._load_instrumentation_scripts(metadata['config'])
            
            # Start binary execution with instrumentation
            binary_path = f"/tmp/analysis_binary_{metadata['binary_uuid']}"
            
            # Spawn process with Frida
            pid = device.spawn([binary_path])
            session = device.attach(pid)
            
            # Load and enable scripts
            for script_name, script_code in scripts.items():
                script = session.create_script(script_code)
                script.on('message', lambda message, data, sn=script_name: 
                         asyncio.create_task(self._handle_enhanced_frida_message(
                             session_id, sn, message, data
                         )))
                script.load()
            
            # Resume execution
            device.resume(pid)
            
            # Store session info
            metadata['frida_session'] = session
            metadata['frida_pid'] = pid
            metadata['frida_device'] = device
            
            logger.info(f"Started Frida instrumentation for session {session_id}")
            
        except Exception as e:
            logger.error(f"Failed to start Frida instrumentation: {e}")
            raise
    
    def _load_analysis_profile(self, profile_name: str) -> Dict[str, Any]:
        """Load analysis profile configuration."""
        try:
            profiles_path = "vendor/frida/config/analysis_profiles.json"
            with open(profiles_path, 'r') as f:
                profiles = json.load(f)
            
            if profile_name not in profiles['profiles']:
                logger.warning(f"Profile {profile_name} not found, using default")
                profile_name = 'default'
            
            return profiles['profiles'][profile_name]
            
        except Exception as e:
            logger.error(f"Failed to load analysis profile {profile_name}: {e}")
            # Return minimal default profile
            return {
                'name': 'Minimal Default',
                'duration': 300,
                'api_hooks': ['file_io', 'network', 'process'],
                'monitoring': {'screenshots': False, 'network_capture': True}
            }
    
    def _load_instrumentation_scripts(self, profile_config: Dict[str, Any]) -> Dict[str, str]:
        """Load Frida instrumentation scripts based on profile."""
        scripts = {}
        
        # Load core scripts based on API hooks
        for hook_type in profile_config.get('api_hooks', []):
            script_path = f"vendor/frida/scripts/api_hooks/{hook_type}.js"
            try:
                with open(script_path, 'r') as f:
                    scripts[hook_type] = f.read()
            except FileNotFoundError:
                logger.warning(f"Script not found: {script_path}")
        
        return scripts
    
    async def _handle_enhanced_frida_message(
        self,
        session_id: str,
        script_name: str,
        message: Dict[str, Any],
        data: Optional[bytes]
    ):
        """Handle messages from Frida scripts with Inspector Gadget correlation."""
        try:
            if message['type'] == 'send':
                # Format data for Elasticsearch
                formatted_data = self.data_formatter.format_frida_event(
                    session_id=session_id,
                    script_name=script_name,
                    payload=message['payload'],
                    raw_data=data
                )
                
                # Apply sanitization
                sanitized_data = self.data_sanitizer.sanitize_event(formatted_data)
                
                # Store Frida event for correlation
                if session_id in self.active_sessions:
                    self.active_sessions[session_id]['correlation_data']['frida_events'].append(
                        sanitized_data
                    )
                
                # Stream to Elasticsearch
                await self.elasticsearch_streamer.stream_event(sanitized_data)
                
                # Send to Logstash for Inspector Gadget correlation
                await self._send_to_logstash(sanitized_data)
                
                # Perform real-time correlation if enabled
                if (self.config['inspector_gadget'].get('correlation_enabled', True) and
                    session_id in self.active_sessions and
                    self.active_sessions[session_id]['inspector_gadget']['enabled']):
                    
                    await self._perform_realtime_correlation(session_id, sanitized_data)
                
        except Exception as e:
            logger.error(f"Failed to handle enhanced Frida message: {e}")
    
    async def _perform_realtime_correlation(self, session_id: str, frida_event: Dict[str, Any]):
        """Perform real-time correlation between Frida and Inspector Gadget events."""
        try:
            # Get recent Inspector Gadget data
            ig_data = await self.inspector_gadget.get_session_data(session_id)
            
            if ig_data.get('status') == 'success':
                # Find correlations for this specific event
                correlations = await self.inspector_gadget.correlate_with_frida(
                    session_id, [frida_event]
                )
                
                # Store correlations
                if correlations.get('status') == 'success':
                    session_metadata = self.active_sessions[session_id]
                    session_metadata['correlation_data']['correlations'].extend(
                        correlations.get('correlations', [])
                    )
                    
                    # Log significant correlations
                    for correlation in correlations.get('correlations', []):
                        if correlation.get('correlation_score', 0) > 0.7:
                            logger.info(
                                f"High correlation found in session {session_id}: "
                                f"Frida {correlation['frida_event'].get('api_function')} "
                                f"<-> IG events (score: {correlation['correlation_score']:.2f})"
                            )
            
        except Exception as e:
            logger.error(f"Failed to perform real-time correlation: {e}")
    
    async def _send_to_logstash(self, event_data: Dict[str, Any]):
        """Send event data to Logstash for correlation with Inspector Gadget."""
        try:
            # Add Frida-specific tags
            event_data['tags'] = event_data.get('tags', []) + ['frida', 'dynamic-analysis']
            event_data['source_system'] = 'frida'
            
            # Send to Logstash HTTP input
            logstash_endpoint = self.config.get('integration', {}).get('elk_stack', {}).get('logstash_endpoint')
            
            if logstash_endpoint:
                # Use aiohttp to send data
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    async with session.post(logstash_endpoint, json=event_data) as response:
                        if response.status != 200:
                            logger.warning(f"Failed to send to Logstash: {response.status}")
                            
        except Exception as e:
            logger.error(f"Failed to send data to Logstash: {e}")
    
    async def _schedule_analysis_completion(self, session_id: str, duration: int):
        """Schedule analysis completion after specified duration."""
        await asyncio.sleep(duration)
        await self.stop_enhanced_analysis(session_id, reason="timeout")
    
    async def stop_enhanced_analysis(self, session_id: str, reason: str = "manual"):
        """Stop an enhanced analysis session."""
        try:
            if session_id not in self.active_sessions:
                raise ValueError(f"Session {session_id} not found")
            
            metadata = self.active_sessions[session_id]
            
            # Stop Inspector Gadget monitoring
            if metadata['inspector_gadget']['enabled']:
                await self.inspector_gadget.stop_monitoring(session_id)
            
            # Stop Frida instrumentation
            if 'frida_session' in metadata:
                metadata['frida_session'].detach()
            
            # Perform final correlation analysis
            await self._perform_final_correlation(session_id, metadata)
            
            # Extract artifacts
            await self._extract_analysis_artifacts(session_id, metadata)
            
            # Cleanup VM
            if 'vm_id' in metadata:
                await self.vm_coordinator.cleanup_vm(metadata['vm_id'])
            
            # Cleanup Inspector Gadget session
            if metadata['inspector_gadget']['enabled']:
                await self.inspector_gadget.cleanup_session(session_id)
            
            # Update session status
            metadata['status'] = 'completed'
            metadata['completed_at'] = datetime.utcnow().isoformat()
            metadata['stop_reason'] = reason
            
            # Generate enhanced analysis report
            await self._generate_enhanced_analysis_report(session_id, metadata)
            
            logger.info(f"Stopped enhanced analysis session {session_id} (reason: {reason})")
            
        except Exception as e:
            logger.error(f"Failed to stop enhanced analysis session {session_id}: {e}")
            raise
