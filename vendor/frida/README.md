# Frida Analysis Engine

This directory contains the Frida-based binary analysis engine for TurdParty's malware analysis capabilities.

## Overview

The Frida Analysis Engine provides comprehensive dynamic analysis of binary files through:

1. **Install Footprint Analysis**: Captures all file system and registry changes during binary installation
2. **Runtime Behavior Analysis**: Real-time monitoring of API calls, network activity, and system interactions
3. **Artifact Extraction**: Individual storage of analysis artifacts in MinIO with UUID linking
4. **Real-time Streaming**: Live data streaming to Elasticsearch for threat hunting

## Directory Structure

```
vendor/frida/
├── README.md                    # This file
├── requirements.txt             # Python dependencies
├── config/                      # Configuration files
│   ├── frida_config.json       # Main Frida configuration
│   ├── analysis_profiles.json  # Analysis profiles for different malware types
│   └── sanitization_rules.json # Data sanitization configuration
├── scripts/                     # Frida JavaScript instrumentation scripts
│   ├── core/                   # Core instrumentation modules
│   ├── api_hooks/              # API hooking scripts by category
│   ├── install_monitor/        # Installation footprint monitoring
│   └── utils/                  # Utility scripts and helpers
├── server/                      # Frida server management
│   ├── frida_server.py         # Main server controller
│   ├── session_manager.py      # Analysis session management
│   └── vm_coordinator.py       # VM lifecycle coordination
├── extractors/                  # Artifact extraction modules
│   ├── file_extractor.py       # File system change extraction
│   ├── registry_extractor.py   # Windows registry change extraction
│   └── network_extractor.py    # Network activity extraction
├── streaming/                   # Real-time data streaming
│   ├── elasticsearch_client.py # Elasticsearch integration
│   ├── data_formatter.py       # Data formatting for ES
│   └── sanitizer.py            # Data sanitization engine
└── tests/                       # Test suite
    ├── unit/                   # Unit tests
    ├── integration/            # Integration tests
    └── fixtures/               # Test fixtures and sample binaries
```

## Features

### Supported Analysis Types
- **Windows PE Analysis**: Full API hooking and registry monitoring
- **Linux ELF Analysis**: System call monitoring and file tracking
- **Cross-platform**: Unified analysis framework for multiple architectures

### API Categories Monitored
- **File I/O**: CreateFile, ReadFile, WriteFile, DeleteFile, etc.
- **Network**: Socket operations, HTTP/HTTPS requests, DNS queries
- **Process**: Process creation, thread management, DLL loading
- **Registry**: Key creation, value modification, permission changes
- **Cryptography**: Encryption/decryption operations, key generation

### Data Sanitization Levels
1. **Basic**: Remove absolute paths, mask IPs, hash sensitive values
2. **Enhanced**: Anonymize all identifiable information with consistent mapping
3. **Maximum**: Replace all strings with semantic tokens for external sharing

## Configuration

### Main Configuration (`config/frida_config.json`)
```json
{
  "version": "1.0.0",
  "frida": {
    "version": "latest",
    "mode": "auto",
    "timeout": 300,
    "max_sessions": 10
  },
  "analysis": {
    "default_duration": 300,
    "max_duration": 1800,
    "capture_screenshots": true,
    "memory_dumps": false
  },
  "storage": {
    "minio_bucket": "frida-artifacts",
    "elasticsearch_index": "frida-runtime",
    "retention_days": -1
  },
  "security": {
    "air_gapped": true,
    "sanitization_level": 1,
    "audit_logging": true
  }
}
```

## Installation

### Prerequisites
- Python 3.8+
- Frida (latest stable)
- Node.js (for JavaScript script development)
- Docker (for VM management)

### Setup
```bash
# Install Python dependencies
pip install -r vendor/frida/requirements.txt

# Install Frida
pip install frida-tools

# Verify installation
frida --version
```

## Usage

### Basic Analysis
```python
from vendor.frida.server.frida_server import FridaAnalysisServer

# Initialize server
server = FridaAnalysisServer()

# Start analysis session
session_id = server.start_analysis(
    binary_uuid="uuid-here",
    analysis_type="runtime",
    duration=300
)

# Monitor progress
status = server.get_session_status(session_id)
```

### Install Footprint Analysis
```python
# Start install monitoring
session_id = server.start_analysis(
    binary_uuid="uuid-here",
    analysis_type="install_footprint",
    vm_template="windows_10_clean"
)
```

## Integration Points

### TurdParty API Integration
- Integrates with existing VM management system
- Uses current authentication and authorization
- Follows established API patterns and conventions

### Storage Integration
- **MinIO**: Individual artifact storage with metadata
- **PostgreSQL**: Session metadata and analysis results
- **Elasticsearch**: Real-time behavior data and search

### Security Integration
- Air-gapped analysis VMs
- Comprehensive audit logging
- Configurable data sanitization
- Role-based access control

## Development

### Adding New API Hooks
1. Create script in `scripts/api_hooks/[category]/`
2. Follow existing hook patterns
3. Add tests in `tests/unit/api_hooks/`
4. Update configuration if needed

### Extending Analysis Profiles
1. Add profile to `config/analysis_profiles.json`
2. Create corresponding scripts if needed
3. Test with sample binaries
4. Document new capabilities

## Monitoring and Logging

### Health Checks
- Frida server status
- VM availability
- Storage connectivity
- Elasticsearch cluster health

### Performance Metrics
- Analysis completion rate
- Average processing time
- Resource utilization
- Error rates

## Security Considerations

### VM Isolation
- Complete network isolation (air-gapped)
- Snapshot-based VM reversion
- Resource limitations and monitoring
- Escape attempt detection

### Data Protection
- Encrypted artifact storage
- Secure data transmission
- Audit trail for all operations
- Configurable data retention

## Troubleshooting

### Common Issues
1. **Frida Connection Failed**: Check VM network configuration
2. **Analysis Timeout**: Increase timeout or check VM resources
3. **Storage Errors**: Verify MinIO connectivity and permissions
4. **Script Errors**: Check Frida script syntax and compatibility

### Debug Mode
```python
server = FridaAnalysisServer(debug=True)
```

## Contributing

### Code Standards
- Follow PEP 8 for Python code
- Use ESLint for JavaScript scripts
- Comprehensive test coverage
- Clear documentation

### Testing
```bash
# Run unit tests
python -m pytest vendor/frida/tests/unit/

# Run integration tests
python -m pytest vendor/frida/tests/integration/
```

---

**Version**: 1.0.0  
**Last Updated**: 2024-12-19  
**Maintainer**: TurdParty Development Team
