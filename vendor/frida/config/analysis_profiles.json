{"version": "1.0.0", "profiles": {"default": {"name": "Default Analysis", "description": "Standard comprehensive analysis for unknown binaries", "duration": 300, "api_hooks": ["file_io", "network", "process", "registry", "crypto"], "monitoring": {"screenshots": true, "memory_dumps": false, "network_capture": true, "file_changes": true}, "inspector_gadget": {"enabled": true, "gadgets": ["trace_exec", "trace_tcp", "trace_dns", "trace_open", "trace_capabilities"]}}, "malware_analysis": {"name": "Malware Analysis", "description": "Intensive analysis for suspected malware", "duration": 600, "api_hooks": ["file_io", "network", "process", "registry", "crypto", "memory", "injection"], "monitoring": {"screenshots": true, "memory_dumps": true, "network_capture": true, "file_changes": true, "registry_changes": true}, "inspector_gadget": {"enabled": true, "gadgets": ["trace_exec", "trace_tcp", "trace_dns", "trace_open", "trace_capabilities", "trace_mount", "audit_seccomp", "trace_signal"]}, "security": {"enhanced_monitoring": true, "behavior_analysis": true, "threat_detection": true}}, "quick_scan": {"name": "<PERSON>", "description": "Fast analysis for initial assessment", "duration": 120, "api_hooks": ["file_io", "network", "process"], "monitoring": {"screenshots": false, "memory_dumps": false, "network_capture": true, "file_changes": true}, "inspector_gadget": {"enabled": true, "gadgets": ["trace_exec", "trace_tcp", "trace_open"]}}, "network_analysis": {"name": "Network Analysis", "description": "Focus on network behavior and communications", "duration": 300, "api_hooks": ["network", "crypto", "process"], "monitoring": {"screenshots": false, "memory_dumps": false, "network_capture": true, "file_changes": false}, "inspector_gadget": {"enabled": true, "gadgets": ["trace_tcp", "trace_dns", "trace_bind", "trace_ssl", "top_tcp"]}, "network": {"deep_packet_inspection": true, "protocol_analysis": true, "dns_monitoring": true}}, "file_analysis": {"name": "File System Analysis", "description": "Focus on file system operations and changes", "duration": 300, "api_hooks": ["file_io", "process"], "monitoring": {"screenshots": false, "memory_dumps": false, "network_capture": false, "file_changes": true, "registry_changes": true}, "inspector_gadget": {"enabled": true, "gadgets": ["trace_open", "trace_fsslower", "top_file", "trace_mount"]}, "filesystem": {"detailed_monitoring": true, "permission_tracking": true, "access_patterns": true}}, "install_footprint": {"name": "Install Footprint Analysis", "description": "Comprehensive analysis of installation artifacts", "duration": 900, "api_hooks": ["file_io", "registry", "process"], "monitoring": {"screenshots": true, "memory_dumps": false, "network_capture": false, "file_changes": true, "registry_changes": true}, "inspector_gadget": {"enabled": true, "gadgets": ["trace_exec", "trace_open", "trace_mount", "trace_capabilities"]}, "install_monitoring": {"pre_install_snapshot": true, "post_install_comparison": true, "artifact_extraction": true, "change_tracking": true}}, "runtime_behavior": {"name": "Runtime Behavior Analysis", "description": "Real-time behavior monitoring and analysis", "duration": 300, "api_hooks": ["file_io", "network", "process", "registry", "crypto", "memory"], "monitoring": {"screenshots": true, "memory_dumps": false, "network_capture": true, "file_changes": true, "registry_changes": true}, "inspector_gadget": {"enabled": true, "gadgets": ["trace_exec", "trace_tcp", "trace_dns", "trace_open", "trace_capabilities", "trace_signal", "top_process"]}, "behavior": {"api_call_patterns": true, "execution_flow": true, "resource_usage": true, "anomaly_detection": true}}}, "platform_specific": {"windows": {"additional_hooks": ["registry", "wmi", "com", "services"], "monitoring": {"registry_changes": true, "service_changes": true, "wmi_queries": true}}, "linux": {"additional_hooks": ["syscalls", "elf_loading", "capabilities"], "monitoring": {"syscall_tracing": true, "capability_checks": true, "elf_analysis": true}}}}