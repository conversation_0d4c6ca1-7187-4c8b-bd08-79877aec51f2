{"version": "1.0.0", "sanitization_levels": {"1": {"name": "Basic Sanitization", "description": "Remove absolute paths, mask IPs, hash sensitive values", "rules": {"file_paths": {"action": "relativize", "preserve_structure": true, "mask_user_dirs": true}, "ip_addresses": {"action": "mask_last_octet", "preserve_ranges": true, "whitelist": ["127.0.0.1", "localhost"]}, "registry_values": {"action": "hash_sensitive", "preserve_keys": true, "sensitive_patterns": ["password", "key", "token", "secret"]}, "api_parameters": {"action": "preserve", "mask_sensitive": true, "preserve_types": true}}}, "2": {"name": "Enhanced Sanitization", "description": "Anonymize all identifiable information with consistent mapping", "rules": {"file_paths": {"action": "anonymize", "consistent_mapping": true, "preserve_extensions": true}, "ip_addresses": {"action": "anonymize", "preserve_internal_external": true, "consistent_mapping": true}, "registry_values": {"action": "hash_all", "preserve_structure": true, "consistent_mapping": true}, "api_parameters": {"action": "hash_values", "preserve_types": true, "preserve_patterns": true}, "process_names": {"action": "anonymize", "consistent_mapping": true, "preserve_system_processes": true}}}, "3": {"name": "Maximum Sanitization", "description": "Replace all strings with semantic tokens for external sharing", "rules": {"file_paths": {"action": "tokenize", "semantic_tokens": true, "preserve_hierarchy": false}, "ip_addresses": {"action": "tokenize", "semantic_tokens": true, "preserve_nothing": true}, "registry_values": {"action": "tokenize", "semantic_tokens": true, "preserve_structure": false}, "api_parameters": {"action": "tokenize", "preserve_types_only": true, "remove_values": true}, "process_names": {"action": "tokenize", "semantic_tokens": true, "preserve_nothing": true}, "network_data": {"action": "remove", "preserve_patterns_only": true}}}}, "field_mappings": {"file_operations": {"filename": "sanitize_path", "full_path": "sanitize_path", "directory": "sanitize_path"}, "network_operations": {"src_ip": "sanitize_ip", "dst_ip": "sanitize_ip", "hostname": "sanitize_hostname", "url": "sanitize_url"}, "process_operations": {"command_line": "sanitize_cmdline", "process_name": "sanitize_process", "working_directory": "sanitize_path"}, "registry_operations": {"key_path": "sanitize_registry_key", "value_name": "sanitize_registry_value", "value_data": "sanitize_registry_data"}}, "sanitization_functions": {"sanitize_path": {"level_1": "remove_absolute_prefix", "level_2": "anonymize_with_mapping", "level_3": "tokenize_semantic"}, "sanitize_ip": {"level_1": "mask_last_octet", "level_2": "anonymize_with_mapping", "level_3": "tokenize_semantic"}, "sanitize_hostname": {"level_1": "mask_domain", "level_2": "anonymize_with_mapping", "level_3": "tokenize_semantic"}, "sanitize_url": {"level_1": "mask_domain_and_params", "level_2": "anonymize_with_mapping", "level_3": "tokenize_semantic"}, "sanitize_cmdline": {"level_1": "mask_sensitive_args", "level_2": "anonymize_paths_and_args", "level_3": "tokenize_semantic"}, "sanitize_process": {"level_1": "preserve", "level_2": "anonymize_with_mapping", "level_3": "tokenize_semantic"}, "sanitize_registry_key": {"level_1": "mask_user_specific", "level_2": "anonymize_with_mapping", "level_3": "tokenize_semantic"}, "sanitize_registry_value": {"level_1": "hash_if_sensitive", "level_2": "hash_all", "level_3": "tokenize_semantic"}, "sanitize_registry_data": {"level_1": "hash_if_sensitive", "level_2": "hash_all", "level_3": "remove"}}, "preservation_rules": {"behavioral_patterns": {"preserve": true, "description": "Maintain behavioral signatures for analysis"}, "timing_information": {"preserve": true, "description": "Keep timing data for sequence analysis"}, "api_call_sequences": {"preserve": true, "description": "Maintain API call patterns"}, "file_operation_patterns": {"preserve": true, "description": "Keep file access patterns"}, "network_communication_patterns": {"preserve": true, "description": "Maintain network behavior patterns"}}, "sensitive_patterns": {"passwords": ["password", "passwd", "pwd", "pass"], "keys": ["key", "apikey", "api_key", "secret", "token"], "personal_info": ["email", "phone", "ssn", "credit", "card"], "system_info": ["username", "hostname", "computername", "macaddress"]}, "anonymization_seeds": {"use_session_specific": true, "use_file_uuid": true, "consistent_across_analysis": true}}