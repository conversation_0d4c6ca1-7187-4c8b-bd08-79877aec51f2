"""
Inspector Gadget Integration for Frida Analysis

This module provides integration between Frida dynamic analysis and Inspector G<PERSON>get
eBPF-based system monitoring for comprehensive binary behavior analysis.
"""

import asyncio
import json
import logging
import subprocess
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Set
from pathlib import Path
import tempfile

logger = logging.getLogger(__name__)


class InspectorGadgetIntegration:
    """Integrates Inspector <PERSON><PERSON><PERSON> eBPF monitoring with Frida analysis."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Inspector Gadget integration."""
        self.config = config
        self.gadget_config = config.get('inspector_gadget', {})
        self.enabled = self.gadget_config.get('enabled', True)
        
        # Active gadget sessions
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.gadget_processes: Dict[str, subprocess.Popen] = {}
        
        # Supported gadgets
        self.supported_gadgets = {
            'trace_exec': 'Process execution monitoring',
            'trace_tcp': 'TCP connection monitoring', 
            'trace_dns': 'DNS query monitoring',
            'trace_open': 'File operation monitoring',
            'trace_capabilities': 'Capability check monitoring',
            'trace_mount': 'Mount operation monitoring',
            'trace_fsslower': 'Slow filesystem operation monitoring',
            'trace_bind': 'Socket binding monitoring',
            'trace_ssl': 'SSL/TLS monitoring',
            'trace_signal': 'Signal monitoring',
            'audit_seccomp': 'Seccomp policy monitoring',
            'top_process': 'Process activity monitoring',
            'top_file': 'File activity monitoring',
            'top_tcp': 'TCP activity monitoring',
            'snapshot_process': 'Process snapshot',
            'snapshot_socket': 'Socket snapshot'
        }
        
        logger.info(f"Inspector Gadget integration initialized (enabled: {self.enabled})")
    
    async def start_monitoring(
        self,
        session_id: str,
        vm_id: str,
        gadgets: List[str],
        correlation_id: str
    ) -> Dict[str, Any]:
        """Start Inspector Gadget monitoring for a Frida analysis session."""
        if not self.enabled:
            logger.warning("Inspector Gadget integration is disabled")
            return {'status': 'disabled'}
        
        try:
            # Validate gadgets
            invalid_gadgets = [g for g in gadgets if g not in self.supported_gadgets]
            if invalid_gadgets:
                raise ValueError(f"Unsupported gadgets: {invalid_gadgets}")
            
            # Create session configuration
            session_config = {
                'session_id': session_id,
                'vm_id': vm_id,
                'gadgets': gadgets,
                'correlation_id': correlation_id,
                'started_at': datetime.utcnow().isoformat(),
                'status': 'starting'
            }
            
            # Start each gadget
            gadget_results = {}
            for gadget in gadgets:
                try:
                    result = await self._start_gadget(session_id, vm_id, gadget, correlation_id)
                    gadget_results[gadget] = result
                except Exception as e:
                    logger.error(f"Failed to start gadget {gadget}: {e}")
                    gadget_results[gadget] = {'status': 'failed', 'error': str(e)}
            
            session_config['gadget_results'] = gadget_results
            session_config['status'] = 'running'
            
            # Store session
            self.active_sessions[session_id] = session_config
            
            logger.info(f"Started Inspector Gadget monitoring for session {session_id}")
            return session_config
            
        except Exception as e:
            logger.error(f"Failed to start Inspector Gadget monitoring: {e}")
            raise
    
    async def _start_gadget(
        self,
        session_id: str,
        vm_id: str,
        gadget: str,
        correlation_id: str
    ) -> Dict[str, Any]:
        """Start a specific Inspector Gadget."""
        try:
            # Prepare gadget command
            cmd = self._build_gadget_command(gadget, vm_id, correlation_id)
            
            # Create output file for gadget data
            output_dir = Path(f"/tmp/inspector-gadget/{session_id}")
            output_dir.mkdir(parents=True, exist_ok=True)
            output_file = output_dir / f"{gadget}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
            
            # Start gadget process
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=output_dir
            )
            
            # Store process reference
            process_key = f"{session_id}_{gadget}"
            self.gadget_processes[process_key] = process
            
            return {
                'status': 'running',
                'pid': process.pid,
                'output_file': str(output_file),
                'command': ' '.join(cmd)
            }
            
        except Exception as e:
            logger.error(f"Failed to start gadget {gadget}: {e}")
            raise
    
    def _build_gadget_command(self, gadget: str, vm_id: str, correlation_id: str) -> List[str]:
        """Build Inspector Gadget command for specific gadget type."""
        base_cmd = ['ig', gadget]
        
        # Add common parameters
        base_cmd.extend([
            '--output', 'json',
            '--timeout', '0',  # Run indefinitely
            '--annotation', f'session={correlation_id}',
            '--annotation', f'vm={vm_id}'
        ])
        
        # Add gadget-specific parameters
        gadget_params = {
            'trace_exec': ['--follow-forks'],
            'trace_tcp': ['--all-namespaces'],
            'trace_dns': ['--all-namespaces'],
            'trace_open': ['--follow-forks'],
            'trace_capabilities': ['--all-namespaces'],
            'trace_mount': ['--all-namespaces'],
            'trace_fsslower': ['--min-latency', '1ms'],
            'trace_bind': ['--all-namespaces'],
            'trace_ssl': ['--all-namespaces'],
            'trace_signal': ['--follow-forks'],
            'audit_seccomp': ['--all-namespaces'],
            'top_process': ['--interval', '5s'],
            'top_file': ['--interval', '5s'],
            'top_tcp': ['--interval', '5s'],
            'snapshot_process': [],
            'snapshot_socket': []
        }
        
        if gadget in gadget_params:
            base_cmd.extend(gadget_params[gadget])
        
        return base_cmd
    
    async def stop_monitoring(self, session_id: str) -> Dict[str, Any]:
        """Stop Inspector Gadget monitoring for a session."""
        if not self.enabled:
            return {'status': 'disabled'}
        
        try:
            if session_id not in self.active_sessions:
                logger.warning(f"No active Inspector Gadget session found: {session_id}")
                return {'status': 'not_found'}
            
            session_config = self.active_sessions[session_id]
            
            # Stop all gadget processes for this session
            stopped_gadgets = []
            for gadget in session_config.get('gadgets', []):
                process_key = f"{session_id}_{gadget}"
                if process_key in self.gadget_processes:
                    try:
                        process = self.gadget_processes[process_key]
                        process.terminate()
                        
                        # Wait for process to terminate
                        try:
                            await asyncio.wait_for(process.wait(), timeout=10)
                        except asyncio.TimeoutError:
                            process.kill()
                            await process.wait()
                        
                        stopped_gadgets.append(gadget)
                        del self.gadget_processes[process_key]
                        
                    except Exception as e:
                        logger.error(f"Failed to stop gadget {gadget}: {e}")
            
            # Update session status
            session_config['status'] = 'stopped'
            session_config['stopped_at'] = datetime.utcnow().isoformat()
            session_config['stopped_gadgets'] = stopped_gadgets
            
            logger.info(f"Stopped Inspector Gadget monitoring for session {session_id}")
            return session_config
            
        except Exception as e:
            logger.error(f"Failed to stop Inspector Gadget monitoring: {e}")
            raise
    
    async def get_session_data(self, session_id: str) -> Dict[str, Any]:
        """Get Inspector Gadget data for a session."""
        if not self.enabled:
            return {'status': 'disabled'}
        
        try:
            if session_id not in self.active_sessions:
                return {'status': 'not_found'}
            
            session_config = self.active_sessions[session_id]
            
            # Collect data from all gadgets
            gadget_data = {}
            for gadget in session_config.get('gadgets', []):
                try:
                    data = await self._collect_gadget_data(session_id, gadget)
                    gadget_data[gadget] = data
                except Exception as e:
                    logger.error(f"Failed to collect data for gadget {gadget}: {e}")
                    gadget_data[gadget] = {'error': str(e)}
            
            return {
                'session_id': session_id,
                'status': session_config['status'],
                'gadget_data': gadget_data,
                'metadata': {
                    'started_at': session_config.get('started_at'),
                    'stopped_at': session_config.get('stopped_at'),
                    'vm_id': session_config.get('vm_id'),
                    'correlation_id': session_config.get('correlation_id')
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get session data: {e}")
            raise
    
    async def _collect_gadget_data(self, session_id: str, gadget: str) -> Dict[str, Any]:
        """Collect data from a specific gadget."""
        try:
            # Find gadget output files
            output_dir = Path(f"/tmp/inspector-gadget/{session_id}")
            gadget_files = list(output_dir.glob(f"{gadget}_*.json"))
            
            if not gadget_files:
                return {'status': 'no_data', 'events': []}
            
            # Read and parse gadget data
            all_events = []
            for file_path in gadget_files:
                try:
                    with open(file_path, 'r') as f:
                        for line in f:
                            line = line.strip()
                            if line:
                                try:
                                    event = json.loads(line)
                                    all_events.append(event)
                                except json.JSONDecodeError:
                                    continue
                except Exception as e:
                    logger.error(f"Failed to read gadget file {file_path}: {e}")
            
            return {
                'status': 'success',
                'events': all_events,
                'event_count': len(all_events),
                'files': [str(f) for f in gadget_files]
            }
            
        except Exception as e:
            logger.error(f"Failed to collect gadget data: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def correlate_with_frida(
        self,
        session_id: str,
        frida_events: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Correlate Inspector Gadget data with Frida events."""
        if not self.enabled:
            return {'status': 'disabled'}
        
        try:
            # Get Inspector Gadget data
            ig_data = await self.get_session_data(session_id)
            
            if ig_data.get('status') != 'success':
                return {'status': 'no_ig_data', 'ig_status': ig_data.get('status')}
            
            # Perform correlation analysis
            correlations = []
            
            for frida_event in frida_events:
                frida_timestamp = frida_event.get('timestamp')
                frida_pid = frida_event.get('pid')
                frida_api = frida_event.get('api_function')
                
                if not all([frida_timestamp, frida_pid, frida_api]):
                    continue
                
                # Find matching Inspector Gadget events
                matching_events = self._find_matching_ig_events(
                    ig_data['gadget_data'],
                    frida_timestamp,
                    frida_pid,
                    frida_api
                )
                
                if matching_events:
                    correlations.append({
                        'frida_event': frida_event,
                        'ig_events': matching_events,
                        'correlation_score': self._calculate_correlation_score(
                            frida_event, matching_events
                        )
                    })
            
            return {
                'status': 'success',
                'session_id': session_id,
                'correlations': correlations,
                'correlation_count': len(correlations),
                'frida_event_count': len(frida_events),
                'ig_event_count': sum(
                    data.get('event_count', 0) 
                    for data in ig_data['gadget_data'].values()
                )
            }
            
        except Exception as e:
            logger.error(f"Failed to correlate with Frida: {e}")
            raise
    
    def _find_matching_ig_events(
        self,
        gadget_data: Dict[str, Any],
        frida_timestamp: str,
        frida_pid: int,
        frida_api: str
    ) -> List[Dict[str, Any]]:
        """Find Inspector Gadget events that match Frida event criteria."""
        matching_events = []
        
        # Convert timestamp for comparison
        try:
            frida_time = datetime.fromisoformat(frida_timestamp.replace('Z', '+00:00'))
        except:
            return matching_events
        
        # Search through all gadget data
        for gadget_name, gadget_info in gadget_data.items():
            if gadget_info.get('status') != 'success':
                continue
            
            for event in gadget_info.get('events', []):
                # Check PID match
                event_pid = event.get('pid')
                if event_pid and event_pid == frida_pid:
                    # Check timestamp proximity (within 1 second)
                    try:
                        event_time = datetime.fromisoformat(
                            event.get('timestamp', '').replace('Z', '+00:00')
                        )
                        time_diff = abs((event_time - frida_time).total_seconds())
                        
                        if time_diff <= 1.0:  # Within 1 second
                            matching_events.append({
                                'gadget': gadget_name,
                                'event': event,
                                'time_diff': time_diff
                            })
                    except:
                        continue
        
        return matching_events
    
    def _calculate_correlation_score(
        self,
        frida_event: Dict[str, Any],
        ig_events: List[Dict[str, Any]]
    ) -> float:
        """Calculate correlation score between Frida and Inspector Gadget events."""
        if not ig_events:
            return 0.0
        
        score = 0.0
        
        # Base score for having matching events
        score += 0.3
        
        # Score for PID match
        frida_pid = frida_event.get('pid')
        for ig_event in ig_events:
            if ig_event['event'].get('pid') == frida_pid:
                score += 0.2
                break
        
        # Score for timestamp proximity
        min_time_diff = min(ig_event.get('time_diff', 1.0) for ig_event in ig_events)
        if min_time_diff <= 0.1:  # Very close in time
            score += 0.3
        elif min_time_diff <= 0.5:
            score += 0.2
        elif min_time_diff <= 1.0:
            score += 0.1
        
        # Score for API function correlation
        frida_api = frida_event.get('api_function', '').lower()
        for ig_event in ig_events:
            gadget_type = ig_event.get('gadget', '')
            if self._api_gadget_correlation(frida_api, gadget_type):
                score += 0.2
                break
        
        return min(score, 1.0)  # Cap at 1.0
    
    def _api_gadget_correlation(self, frida_api: str, gadget_type: str) -> bool:
        """Check if Frida API correlates with Inspector Gadget type."""
        correlations = {
            'createfile': ['trace_open', 'trace_fsslower'],
            'openfile': ['trace_open', 'trace_fsslower'],
            'readfile': ['trace_open', 'trace_fsslower'],
            'writefile': ['trace_open', 'trace_fsslower'],
            'connect': ['trace_tcp', 'trace_bind'],
            'socket': ['trace_tcp', 'trace_bind'],
            'bind': ['trace_bind'],
            'listen': ['trace_bind'],
            'accept': ['trace_tcp'],
            'send': ['trace_tcp'],
            'recv': ['trace_tcp'],
            'createprocess': ['trace_exec'],
            'terminateprocess': ['trace_exec'],
            'openprocess': ['trace_exec'],
        }
        
        for api_pattern, gadgets in correlations.items():
            if api_pattern in frida_api and gadget_type in gadgets:
                return True
        
        return False
    
    async def cleanup_session(self, session_id: str) -> None:
        """Cleanup Inspector Gadget session data."""
        try:
            # Stop monitoring if still active
            if session_id in self.active_sessions:
                await self.stop_monitoring(session_id)
            
            # Remove session data
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
            
            # Cleanup output files
            output_dir = Path(f"/tmp/inspector-gadget/{session_id}")
            if output_dir.exists():
                import shutil
                shutil.rmtree(output_dir, ignore_errors=True)
            
            logger.info(f"Cleaned up Inspector Gadget session {session_id}")
            
        except Exception as e:
            logger.error(f"Failed to cleanup session {session_id}: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check of Inspector Gadget integration."""
        try:
            # Check if Inspector Gadget is available
            process = await asyncio.create_subprocess_exec(
                'ig', '--version',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                version = stdout.decode().strip()
                ig_available = True
            else:
                version = None
                ig_available = False
            
            return {
                'status': 'healthy' if self.enabled and ig_available else 'degraded',
                'enabled': self.enabled,
                'ig_available': ig_available,
                'ig_version': version,
                'active_sessions': len(self.active_sessions),
                'supported_gadgets': len(self.supported_gadgets),
                'gadget_processes': len(self.gadget_processes)
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'enabled': self.enabled,
                'error': str(e),
                'active_sessions': len(self.active_sessions)
            }
