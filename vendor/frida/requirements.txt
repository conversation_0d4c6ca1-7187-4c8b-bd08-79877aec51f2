# Frida Analysis Engine Dependencies
# Core Frida components
frida>=16.1.0
frida-tools>=12.2.0

# Async and networking
aiohttp>=3.8.0
aiofiles>=23.0.0
asyncio-mqtt>=0.13.0
websockets>=11.0.0

# Data processing and analysis
pandas>=2.0.0
numpy>=1.24.0
psutil>=5.9.0
python-magic>=0.4.27

# Storage and database
elasticsearch>=8.10.0
elasticsearch-dsl>=8.9.0
minio>=7.1.0
sqlalchemy>=2.0.0
alembic>=1.12.0

# Cryptography and hashing
cryptography>=41.0.0
pycryptodome>=3.18.0

# Configuration and serialization
pydantic>=2.0.0
pyyaml>=6.0.0
toml>=0.10.2
python-dotenv>=1.0.0

# Logging and monitoring
structlog>=23.1.0
prometheus-client>=0.17.0

# VM and container management
docker>=6.1.0
paramiko>=3.3.0
fabric>=3.2.0

# Testing and development
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
pytest-cov>=4.1.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# Windows-specific dependencies (conditional)
pywin32>=306; sys_platform == "win32"
wmi>=1.5.1; sys_platform == "win32"

# Linux-specific dependencies (conditional)
python-ptrace>=0.9.8; sys_platform == "linux"

# Performance and optimization
uvloop>=0.17.0; sys_platform != "win32"
orjson>=3.9.0
msgpack>=1.0.0

# HTTP and API clients
httpx>=0.24.0
requests>=2.31.0
urllib3>=2.0.0

# File format analysis
pefile>=2023.2.7
pyelftools>=0.29

# Network analysis
scapy>=2.5.0
dpkt>=1.9.8
